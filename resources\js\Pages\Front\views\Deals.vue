<script setup>

import feather from 'feather-icons';
import DealList from '../components/shared/DealList.vue';
import { onMounted , onUpdated } from 'vue';
import App from '../App.vue';
import { ref } from 'vue';
import { Head, usePage } from '@inertiajs/vue3';
const pageTitle = ref('Deals');

const {deals, searchField} = usePage().props;

// Run feather.replace() on mount and update
onMounted(() => {
	feather.replace();
});

onUpdated(() => {
	feather.replace();
});
</script>

<style scoped>
/* Your scoped styles here */
</style>


<template>
	<Head :title="pageTitle" />
	<App>
		<div>

		<div class="container mx-auto">
			<DealList :deals="deals" :searchField="searchField"/>
		</div>
	</div>
	</App>
	
</template>
