<?php

use App\Http\Controllers\Agent\AgentManageController;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\ChatbotController;
use App\Http\Controllers\Chatcontroller;
use App\Http\Controllers\Chats\IntentController;
use App\Http\Controllers\Chats\PhrasesController;
use App\Http\Controllers\Chats\VendorApiController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\Complaincontroller;
use App\Http\Controllers\DealsController;
use App\Http\Controllers\dtadmin\CategoryController;
use App\Http\Controllers\dtadmin\ManageUserController;
use App\Http\Controllers\Front\ContactController;
use App\Http\Controllers\Front\FrontHomecontroller;
use App\Http\Controllers\Homecontroller;
use App\Http\Controllers\OAuthController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\Usercontroller;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Route::get('/get-domain-data', [FrontHomecontroller::class, 'getSessionDomain'])->name('get-domain-data');

Route::get('/oauth/login', [FrontHomecontroller::class, 'LoginAuth'])->name('oauth/login');

Route::get('/global-chatbot-service/{companyID}', [FrontHomecontroller::class, 'CompaniesBotsGlobal'])->name('front-global-chatbot-service');

Route::middleware('guest')->group(function () {

    Route::post('/store-guest-complain', [Complaincontroller::class, 'storeguestComplain'])->name('guestcomplaints.store');

    Route::post('send-contact-message', [ContactController::class, 'sendQuery'])->name('send-contact-message');

    Route::post('auth', [AuthenticatedSessionController::class, 'userAuth'])->name('auth');
    Route::post('send-auth-otp', [AuthenticatedSessionController::class, 'sendAuthOtp'])->name('send-auth-otp');

    // Route::get('/generate-qr', [FrontHomecontroller::class, 'generateQRcode'])->name('generate-qr');

    Route::get('/agent-account', [AgentManageController::class, 'ResetAgentPassword'])->name('agent-account');
    Route::post('/reset-password-agent', [AgentManageController::class, 'resetCustomPassword'])->name('reset-password-agent');

    Route::get('/', [FrontHomecontroller::class, 'index'])->name('home');
    Route::get('/companies', [FrontHomecontroller::class, 'Companies'])->name('front-companies');
    Route::get('/search-company', [FrontHomecontroller::class, 'SearchCompanies'])->name('search-company');
    Route::get('/search-deals', [FrontHomecontroller::class, 'SearchDeals'])->name('search-deals');

    Route::get('/company/{id}', [FrontHomecontroller::class, 'ComplanyDetail'])->name('company-detail');
    Route::get('/contact', [FrontHomecontroller::class, 'Contact'])->name('contact');
    Route::get('/deals', [FrontHomecontroller::class, 'Deals'])->name('front-deals');
    Route::get('/deal/{id}', [FrontHomecontroller::class, 'DealDetail'])->name('front-deal-detail');
    Route::get('/login', [FrontHomecontroller::class, 'Login'])->name('user-login');
    Route::post('/web-google-login', [FrontHomecontroller::class, 'webGoogleLogin'])->name('web-google-login');
    Route::get('/signup', [FrontHomecontroller::class, 'Signup'])->name('user-signup');
    Route::get('/signup-business', [FrontHomecontroller::class, 'SignupBusiness'])->name('user-signup-business');

    Route::post('send-message-guest', [ChatbotController::class, 'sendGuestMessage']);
    Route::post('send-guest-msg', [ChatbotController::class, 'sendJavaGuestMessage']);

});

Route::middleware('auth')->group(function () {
    Route::get('/chatbot-json', [ChatbotController::class, 'chatbotJson'])->name('chatbot-json');
    Route::get('/get-user-detail', [ChatbotController::class, 'getUserDetail'])->name('get-user-detail');
    Route::get('/notifications/fetch', [ChatbotController::class, 'fetchNotifications']);
});

Route::group(['prefix' => 'dtadmin'], function () {

    Route::middleware('auth')->group(function () {

        Route::get('/getlast_message/{uid}/{agenttype}', [ChatbotController::class, 'getLastMessage'])->name('get-lastmessage');

        // Route::post('/company', [CompanyController::class, 'update'])->name('company.update');
        Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
        Route::post('/profile', [ProfileController::class, 'update'])->name('profile.update');
        Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

        /* new routes */

        Route::get('/searchuser', [Usercontroller::class, 'search'])->name('searchuser');
        Route::get('/searchauthuser', [Usercontroller::class, 'searchauthuser'])->name('searchauthuser');

        Route::get('/chattest', [Chatcontroller::class, 'chattest'])->name('chattest');
        Route::get('messages', [Chatcontroller::class, 'messages'])->name('messages');
        Route::post('/messages', [Chatcontroller::class, 'messageStore'])->name('messageStore');

        Route::get('/search-company-user', [ChatbotController::class, 'searchcompanyUser'])->name('search-company-user');

        Route::middleware(['checkRole:user'])->group(function () {

            Route::post('send-message', [ChatbotController::class, 'send']);
            Route::get('/chatbot', [ChatbotController::class, 'chatbot'])->name('chatbot');

        });

        Route::middleware(['checkRole:agent,company'])->group(function () {
            Route::post('send-agent-message', [ChatbotController::class, 'sendbyAgent']);
            Route::get('get-agent-last-conversation', [ChatbotController::class, 'botConversation']);
            Route::post('cancel-agent-conversaction', [ChatbotController::class, 'cancelbotConversation']);
        });

        Route::middleware(['checkRole:admin,company'])->group(function () {
            Route::group(['prefix' => 'companies'], function () {
                // Route::get('/', [CompanyController::class, 'index'])->name('companies');
                // Route::get('/add', [CompanyController::class, 'create'])->name('companies.add');
                // Route::get('/edit/{id}', [CompanyController::class, 'edit'])->name('companies.edit');
                Route::post('/store', [CompanyController::class, 'store'])->name('companies.store');
                Route::post('/address-store', [CompanyController::class, 'addressStore'])->name('companies.address.store');
                Route::post('/address-save', [CompanyController::class, 'addressSave'])->name('companies.address.save');
                Route::post('/save', [CompanyController::class, 'save'])->name('companies.save');
                Route::delete('delete/{id}', [CompanyController::class, 'destroy'])->name('companies.delete');
            });
        });

        Route::middleware(['checkRole:admin'])->group(function () {
            Route::group(['prefix' => 'contacts'], function () {
                Route::get('/', [ContactController::class, 'index'])->name('contacts');
                Route::delete('delete/{id}', [ContactController::class, 'destroy'])->name('contacts.delete');
            });

            Route::group(['prefix' => 'manage-users'], function () {
                Route::get('/', [ManageUserController::class, 'index'])->name('manage-users');
                Route::get('/edit/{id}', [ManageUserController::class, 'edit'])->name('manage-users.edit');
                Route::post('/update/{id}', [ManageUserController::class, 'update'])->name('manage-users.update');
                Route::get('/access-portal/{id}', [ManageUserController::class, 'accessPortal'])->name('manage-users.access-portal');
                Route::delete('/delete/{id}', [ManageUserController::class, 'destroy'])->name('manage-users.delete');
            });

            Route::group(['prefix' => 'category'], function () {
                Route::get('/list', [CategoryController::class, 'index'])->name('category.list');
                Route::get('/add', [CategoryController::class, 'create'])->name('category.create');
                Route::post('/store', [CategoryController::class, 'store'])->name('category.store');
                Route::get('/edit/{id}', [CategoryController::class, 'edit'])->name('category.edit');
                Route::post('/update', [CategoryController::class, 'update'])->name('category.update');
                Route::delete('/delete/{id}', [CategoryController::class, 'destroy'])->name('category.delete');
            });
        });

        Route::middleware(['checkRole:company'])->group(function () {

            Route::group(['prefix' => 'manage-agent'], function () {
                Route::get('/list', [AgentManageController::class, 'index'])->name('manage-agent.list');
                Route::get('/add', [AgentManageController::class, 'create'])->name('manage-agent.add');
                Route::get('/edit/{id}', [AgentManageController::class, 'edit'])->name('manage-agent.edit');
                Route::delete('delete/{id}', [AgentManageController::class, 'destroy'])->name('manage-agent.delete');
                Route::post('/store', [AgentManageController::class, 'store'])->name('manage-agent.store');
                Route::post('/update/{id}', [AgentManageController::class, 'update'])->name('manage-agent.update');
            });

            Route::group(['prefix' => 'settings'], function () {

                Route::post('/company', [CompanyController::class, 'update'])->name('company.update');
                Route::post('/updateoauth', [OAuthController::class, 'updateOauth'])->name('company.oauth.update');

                Route::get('/chatbots', [SettingController::class, 'index'])->name('chatbots.setting');

                Route::prefix('training')->middleware('auth')->group(function () {
                    Route::resource('intents', IntentController::class);
                    Route::resource('phrases', PhrasesController::class);
                    Route::resource('vendor-apis', VendorApiController::class);
                    Route::get('/search-api', [VendorApiController::class, 'searchApi'])->name('vendorapi.search');
                });

                Route::group(['prefix' => 'chatbot'], function () {
                    Route::get('/add', [SettingController::class, 'create'])->name('chatbots.add');
                    Route::get('/edit/{id}', [SettingController::class, 'edit'])->name('chatbots.edit');
                    Route::delete('delete/{id}', [SettingController::class, 'destroy'])->name('chatbots.delete');
                    Route::post('/store', [SettingController::class, 'store'])->name('chatbots.store');
                    Route::post('/update', [SettingController::class, 'update'])->name('chatbots.update');
                });

            });

        });

        /* new route over */

        Route::get('/dashboard', [Homecontroller::class, 'dashboard'])->name('dashboard');

        Route::middleware(['checkRole:agent,company'])->group(function () {
            Route::get('/chats', [ChatbotController::class, 'index'])->name('chats');
            Route::get('/userdetail/{uid}', [ChatbotController::class, 'userDetail'])->name('user-details');
        });

        Route::get('/addcomplain', [Complaincontroller::class, 'addComplain'])->name('addcomplain');

        Route::group(['prefix' => 'complaints'], function () {

            Route::get('/get-complaint-details', [Complaincontroller::class, 'getComplaintDetails'])->name('complaints.get-details');
            Route::get('/', [Complaincontroller::class, 'index'])->name('complaints');
            Route::get('/show/{id}', [Complaincontroller::class, 'show'])->name('complaints.view');
            Route::post('/store', [Complaincontroller::class, 'store'])->name('complaints.store');
            Route::post('/update/{id}', [Complaincontroller::class, 'update'])->name('complaints.update');
            Route::post('/update-progress-status', [Complaincontroller::class, 'updateProgressStatus'])->name('complaints.update-progress-status');

            Route::middleware(['checkRole:admin,company'])->group(function () {
                Route::get('/edit/{id}', [Complaincontroller::class, 'edit'])->name('complaints.edit');
            });

            Route::post('/send-complain-message', [Complaincontroller::class, 'sendComplainMessage'])->name('complaints.send-complain-message');
        });

        Route::middleware(['checkRole:company,admin'])->group(function () {

            Route::group(['prefix' => 'deals'], function () {
                Route::get('/', [DealsController::class, 'index'])->name('deals');
                Route::get('/show/{id}', [DealsController::class, 'view'])->name('deals.show');
                Route::get('/add', [DealsController::class, 'create'])->name('deal.add');
                Route::get('/edit/{id}', [DealsController::class, 'edit'])->name('deal.edit');
                Route::post('/store', [DealsController::class, 'store'])->name('deal.store');
                Route::post('/update', [DealsController::class, 'update'])->name('deal.update');
                Route::delete('delete/{id}', [DealsController::class, 'destroy'])->name('deals.delete');
            });

        });

    });

    require __DIR__.'/auth.php';

    Route::get('/agent', function () {
        return Inertia::render('Agent');
    })->name('agent');

});
