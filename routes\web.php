<?php

use App\Http\Controllers\Admin\PaymentGatewayController;
use App\Http\Controllers\Admin\SubscriptionController;
use App\Http\Controllers\Admin\SubscriptionPlanController;
use App\Http\Controllers\Company\SubscriptionController as CompanySubscriptionController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ComplaintController;
use App\Http\Controllers\PasswordController;
use App\Http\Controllers\ProfileController;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'login'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'register'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
    Route::get('/forgot-password', [PasswordController::class, 'forgotPassword'])->name('password.request');
    Route::post('/forgot-password', [PasswordController::class, 'sendResetLinkEmail'])->name('password.email');
    Route::get('/reset-password/{token}', [PasswordController::class, 'resetPassword'])->name('password.reset');
    Route::post('/reset-password', [PasswordController::class, 'reset'])->name('password.update');
});

Route::middleware('auth')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

    Route::resource('complains', ComplaintController::class)
        ->middleware('role:admin,company,support_agent,end_user');

    Route::post('/complains/{complain}/messages', [ComplaintController::class, 'storeMessage'])
        ->name('complains.messages.store')
        ->middleware('role:admin,company,support_agent,end_user');

    // API Routes
    Route::get('/api/complains/{status?}', [ComplaintController::class, 'apiIndex'])
        ->name('complains.api')
        ->middleware('role:admin,company,support_agent,end_user');
    Route::get('/api/complains-filters', [ComplaintController::class, 'getFilterOptions'])
        ->name('complains.filter-options')
        ->middleware('role:admin,company,support_agent,end_user');
});

Route::middleware('auth')->group(function () {
    Route::get('/dashboard', function () {
        return Inertia::render('Dashboard');
    })->name('dashboard');
    Route::get('/profile', function () {
        return Inertia::render('Profile', [
            'user' => Auth::user(),
        ]);
    })->name('profile');
    Route::put('/profile', [ProfileController::class, 'update'])->name('profile.update');
    // API Management Routes
    Route::get('/apis', function () {
        return Inertia::render('Apis/index');
    })->name('apis.index');
    Route::get('/apis/create', function () {
        return Inertia::render('Apis/Create');
    })->name('apis.create');
    Route::get('/apis/{id}/edit', function ($id) {
        return Inertia::render('Apis/Edit', [
            'api' => [
                'id' => $id,
                'name' => 'User Authentication API',
                'method' => 'POST',
                'url' => 'https://api.example.com/v1/auth',
                'status' => 'active',
                'description' => 'Authenticates users and returns JWT token',
                'version' => '1.0',
                'owner' => 'Auth Team',
                'requestParams' => [
                    ['label' => 'Username', 'parameter' => 'username', 'type' => 'String'],
                    ['label' => 'Password', 'parameter' => 'password', 'type' => 'String']
                ],
                'responseParams' => [
                    ['label' => 'Token', 'parameter' => 'token', 'type' => 'String'],
                    ['label' => 'Expiry', 'parameter' => 'expires_at', 'type' => 'String']
                ]
            ]
        ]);
    })->name('apis.edit');
    Route::get('/chats', function () {
        return Inertia::render('Chats');
    })->name('chats');
    Route::get('/intents', function () {
        return Inertia::render('Intents');
    })->name('intents');
    Route::get('/intents/create', function () {
        return Inertia::render('IntentCreate');
    })->name('intents.create');
    Route::get('/deals', function () {
        return Inertia::render('Deals/Index');
    })->name('deals.index');
    Route::get('/deals/create', function () {
        return Inertia::render('Deals/Create');
    })->name('deals.create');

    // Agent Management Routes
    Route::get('/agents', function () {
        return Inertia::render('Agents/index');
    })->name('agents.index');
    Route::get('/agents/create', function () {
        return Inertia::render('Agents/Create');
    })->name('agents.create');
    Route::get('/agents/{id}/edit', function ($id) {
        return Inertia::render('Agents/Edit', [
            'agent' => [
                'id' => $id,
                'name' => 'Sarah Johnson',
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'role' => 'Senior Support Agent',
                'department' => 'Customer Support',
                'status' => 'active',
                'skills' => ['Technical Support', 'Customer Service', 'Product Knowledge'],
                'bio' => 'Experienced support agent with 5+ years in customer service and technical troubleshooting.',
                'emergency_contact' => 'John Johnson',
                'emergency_phone' => '+****************',
                'start_date' => '2024-01-15',
                'salary' => '65000',
                'employee_id' => 'EMP001',
                'manager_id' => '',
                'work_schedule' => 'full_time',
                'permissions' => [
                    'can_view_all_tickets' => true,
                    'can_assign_tickets' => true,
                    'can_delete_tickets' => false,
                    'can_manage_users' => false,
                    'can_view_reports' => true,
                    'can_export_data' => false
                ]
            ]
        ]);
    })->name('agents.edit');

    // Admin Routes
    Route::middleware('role:admin')->prefix('admin')->name('admin.')->group(function () {
        // Payment Gateway Management
        Route::get('/payment-gateways', [PaymentGatewayController::class, 'index'])
            ->name('payment-gateways.index');
        Route::get('/payment-gateways/{paymentGateway}/edit', [PaymentGatewayController::class, 'edit'])
            ->name('payment-gateways.edit');
        Route::put('/payment-gateways/{paymentGateway}', [PaymentGatewayController::class, 'update'])
            ->name('payment-gateways.update');
        Route::post('/payment-gateways/{paymentGateway}/toggle-status', [PaymentGatewayController::class, 'toggleStatus'])
            ->name('payment-gateways.toggle-status');
        Route::post('/payment-gateways/{paymentGateway}/toggle-test-mode', [PaymentGatewayController::class, 'toggleTestMode'])
            ->name('payment-gateways.toggle-test-mode');
        Route::post('/payment-gateways/{paymentGateway}/test-connection', [PaymentGatewayController::class, 'testConnection'])
            ->name('payment-gateways.test-connection');

        // Stripe Connect routes
        Route::post('/stripe/connect', [\App\Http\Controllers\Admin\StripeConnectController::class, 'connect'])
            ->name('stripe.connect');
        Route::get('/stripe/callback', [\App\Http\Controllers\Admin\StripeConnectController::class, 'callback'])
            ->name('stripe.callback');
        Route::post('/stripe/disconnect', [\App\Http\Controllers\Admin\StripeConnectController::class, 'disconnect'])
            ->name('stripe.disconnect');

        // Reports routes
        Route::get('/reports', [\App\Http\Controllers\Admin\ReportsController::class, 'index'])
            ->name('reports.index');
        Route::get('/reports/subscription-metrics', [\App\Http\Controllers\Admin\ReportsController::class, 'subscriptionMetrics'])
            ->name('reports.subscription-metrics');
        Route::get('/reports/revenue-metrics', [\App\Http\Controllers\Admin\ReportsController::class, 'revenueMetrics'])
            ->name('reports.revenue-metrics');
        Route::get('/reports/churn-analysis', [\App\Http\Controllers\Admin\ReportsController::class, 'churnAnalysis'])
            ->name('reports.churn-analysis');

        // Subscription Plan Management
        Route::resource('subscription-plans', SubscriptionPlanController::class);
        Route::post('/subscription-plans/{subscriptionPlan}/toggle-status', [SubscriptionPlanController::class, 'toggleStatus'])
            ->name('subscription-plans.toggle-status');

        // Subscription Management
        Route::get('/subscriptions', [SubscriptionController::class, 'index'])
            ->name('subscriptions.index');
        Route::get('/subscriptions/{subscription}', [SubscriptionController::class, 'show'])
            ->name('subscriptions.show');
        Route::post('/subscriptions/{subscription}/cancel', [SubscriptionController::class, 'cancel'])
            ->name('subscriptions.cancel');
        Route::post('/subscriptions/{subscription}/resume', [SubscriptionController::class, 'resume'])
            ->name('subscriptions.resume');
    });

    // Company Subscription Routes
    Route::middleware('role:company')->prefix('company')->name('company.')->group(function () {
        Route::get('/subscription/pricing', [CompanySubscriptionController::class, 'pricing'])
            ->name('subscription.pricing');
        Route::get('/subscription/dashboard', [CompanySubscriptionController::class, 'dashboard'])
            ->name('subscription.dashboard');
        Route::post('/subscription/subscribe', [CompanySubscriptionController::class, 'subscribe'])
            ->name('subscription.subscribe');
        Route::get('/subscription/invoices', [CompanySubscriptionController::class, 'invoices'])
            ->name('subscription.invoices');
        Route::get('/subscription/invoices/{invoice}/download', [CompanySubscriptionController::class, 'downloadInvoice'])
            ->name('subscription.invoices.download');
        Route::get('/subscription/payment-history', [CompanySubscriptionController::class, 'paymentHistory'])
            ->name('subscription.payment-history');
        Route::post('/subscription/cancel', [CompanySubscriptionController::class, 'cancel'])
            ->name('subscription.cancel');
    });
});

Route::get('/', function () {
    return Inertia::render('Public/welcome', [
        'user' => User::first(),
    ]);
});

Route::get('/companies', function () {
    return Inertia::render('Public/Companies');
})->name('companies');

Route::get('/today-deals', function () {
    return Inertia::render('Public/PublicDeals');
})->name('deals.public');

Route::get('/contact', function () {
    return Inertia::render('Public/Contact');
})->name('contact');

// Webhook routes (no auth required)
Route::post('/webhooks/stripe', [\App\Http\Controllers\WebhookController::class, 'stripe'])
    ->name('webhooks.stripe');
Route::post('/webhooks/paypal', [\App\Http\Controllers\WebhookController::class, 'paypal'])
    ->name('webhooks.paypal');