<script setup>
import feather from 'feather-icons';
import ResetForm from '../components/login/ResetForm.vue';
import App from '../App.vue';
import { ref } from 'vue';
import { Head, usePage } from '@inertiajs/vue3';
const pageTitle = ref('Reset Password');
feather.replace();

const {searchField} = usePage().props;
</script>

<template>
	<Head :title="pageTitle" />
	<App>

		<div class="container mx-auto flex flex-col-reverse md:flex-row py-5 md:py-10 md:mt-10">
	  <!-- Register form -->
	  <ResetForm :fromData="searchField"/>
  
	  <!-- Contact details -->
	</div>

	</App>
	
  </template>
  

  