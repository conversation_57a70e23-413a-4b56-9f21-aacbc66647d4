<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, Link, useForm, usePage } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import TextInput from '@/Components/TextInput.vue';
import TextAreaInput from '@/Components/TextAreaInput.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import DefaultCard from '@/Components/Forms/DefaultCard.vue';
import SelectInput from '@/Components/SelectInput.vue';
import FileInput from '@/Components/FileInput.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import { Inertia } from '@inertiajs/inertia';
import BreadcrumbDefault from '@/Components/Breadcrumbs/BreadcrumbDefault.vue';
import PageHeading from '@/Components/Global/PageHeading.vue';

const pageTitle = ref('Add Category')


const { companyList } = usePage().props;
var baseurl = window.location.origin;

const form = useForm({
  catPicture: null,
  catName: ''
});

const onFileChange = (event) => {
  const file = event.target.files[0];
  form.catPicture = file;
};


const resetForm = () => {
  form.catPicture = null;
  form.catName = '';

  // Clear file input by replacing it with a new one
  const fileInput = document.getElementById('catPicture');
  fileInput.value = ''; // Clear the value for better browser compatibility
  const newFileInput = document.createElement('input');
  newFileInput.type = 'file';
  newFileInput.id = 'catPicture';
  newFileInput.classList.add('mt-1', 'block', 'w-full');
  newFileInput.addEventListener('change', onFileChange); // Reattach the change event listener
  fileInput.parentNode.replaceChild(newFileInput, fileInput);
};


const submitForm = () => {
  form.post(route('category.store'), {
    onSuccess: (response) => {
      resetForm();

    },
    onError: (errors) => {
    }
  });
};


</script>

<template>

  <Head :title="pageTitle" />

  <AuthenticatedLayout>
    <PageHeading :title="pageTitle"></PageHeading>

    <form @submit.prevent="submitForm" enctype="multipart/form-data" class="form-main-body">

      <div class="add-form-main-bcls">

        <div class="flex flex-col gap-4 col-span-12 xl:col-span-12">
          <!-- Contact Form Start -->
          <DefaultCard cardTitle="Add Category">

            <div class="p-6">


              <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
                leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
                <p v-if="$page.props.flash?.message" :class="$page.props.flash.class">{{ $page.props.flash.message }}
                </p>
              </Transition>


              <div class="mt-0 mb-4">
                <InputLabel for="catName" value="Name" />
                <TextInput id="catName" name="catName" type="text" placeholder="Title" class="block mt-1 w-full"
                  v-model="form.catName" />
                <InputError :message="form.errors.catName" class="mt-2" />
              </div>

              <div class="mt-2 mb-4">
                <InputLabel for="catPicture" value="Upload Image" />
                <FileInput id="catPicture" type="file" class="block mt-1 w-full" v-model="form.catPicture"
                  @input="onFileChange" autocomplete="catPicture" />
                <InputError class="mt-2" :message="form.errors.catPicture" />
              </div>






              <button class="dk-update-btn">
                Create </button>

              <ResponsiveNavLink :href="route('category.list')" class="dk-cancle-btn">
                Cancel
              </ResponsiveNavLink>


            </div>

          </DefaultCard>

          <!-- Contact Form End -->
        </div>

      </div>
    </form>

  </AuthenticatedLayout>
</template>
