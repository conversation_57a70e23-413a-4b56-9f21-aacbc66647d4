<?php

namespace App\Models;

use App\Events\ChatAgentMessage;
use Exception;
use Google\Auth\Credentials\ServiceAccountCredentials;
use Google\Cloud\Dialogflow\V2\QueryInput;
use Google\Cloud\Dialogflow\V2\SessionsClient;
use Google\Cloud\Dialogflow\V2\TextInput;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Kreait\Firebase\Contract\Database;

class FirebaseFunction extends Model
{
    use HasFactory;

    protected $database;

    protected $tableName;

    public function __construct(Database $database)
    {
        $this->database  = $database;
        $this->tableName = 'chats';
    }

    public function getBotConversation($userId, $agentType)
    {

        $chatData     = [];
        $path         = $this->tableName.'/user_id_'.$userId.'/agent_'.$agentType;
        $userChatsRef = $this->database->getReference($path);
        $chats        = $userChatsRef->getValue();
        if ($chats) {

            $chat_source     = $agentType;
            $chat_uid        = base64_encode($userId);
            $status_fallback = $chats['status_fallback'];

            $agentTypes    = explode('_', $agentType);
            $companyID     = $agentTypes[1];
            $companyDetail = Company::find($companyID);
            $companyUserID = $companyDetail->userId;

            $botconQry = BotConversation::where('user_id', $userId)->where('company_user_id', $companyUserID)->where('status_ticket', 1)->first();
            if ($botconQry) {
                $status_fallback = 0;
            }

            foreach ($chats as $chatId => $chat) {
                if (is_array($chat)) {
                    $res['chat_source']     = $chat_source ?? ''; // Assuming $chat_source is defined elsewhere
                    $res['chat_userid']     = $userId      ?? '';
                    $res['message']         = $chat['message'];
                    $res['chat_uid']        = $chat_uid    ?? '';
                    $res['id']              = $chat['id'];
                    $res['timestamp']       = isset($chat['timestamp']) ? $chat['timestamp'] : '';
                    $res['status_fallback'] = isset($status_fallback)
                        ? ($status_fallback === true ? 1 : ($status_fallback === false ? 0 : $status_fallback))
                        : 0;
                    $chatData[] = $res;
                }
            }
        }

        return $chatData;
    }

    //    public function getBotConversation($userId, $agentType)
    //    {
    //
    //        $chatData     = [];
    //        $path         = $this->tableName.'/user_id_'.$userId.'/agent_'.$agentType;
    //        $userChatsRef = $this->database->getReference($path);
    //        $chats        = $userChatsRef->getValue();
    //        if ($chats) {
    //
    //            $chat_source     = base64_encode($agentType);
    //            $chat_uid        = base64_encode($userId);
    //            $status_fallback = $chats['status_fallback'];
    //
    //            $botdetail = Bots::where('chatbot_project_id', $agentType)->first();
    //            if ($botdetail) {
    //                $botconQry = BotConversation::where('user_id', $userId)->where('bot_id', $botdetail->id)->where('status_ticket', 1)->first();
    //                if ($botconQry) {
    //                    $status_fallback = 0;
    //                }
    //            }
    //
    //            foreach ($chats as $chatId => $chat) {
    //                if (is_array($chat)) {
    //                    $res['chat_source']     = $chat_source ?? ''; // Assuming $chat_source is defined elsewhere
    //                    $res['chat_uid']        = $chat_uid    ?? '';       // Assuming $chat_uid is defined elsewhere
    //                    $res['id']              = $chat['id'];
    //                    $res['message']         = $chat['message'];
    //                    $res['suggession']      = (isset($chat['suggession']) && $chat['suggession'] != '') ? json_decode($chat['suggession']) : '';
    //                    $res['timestamp']       = isset($chat['timestamp']) ? $chat['timestamp'] : '';
    //                    $res['status_fallback'] = isset($status_fallback) ? $status_fallback : 0;
    //                    $chatData[]             = $res;
    //                }
    //            }
    //        }
    //
    //        return $chatData;
    //    }

    public function getUserIdsByTypes(array $types)
    {
        // Reference the 'chats' node in Firebase
        $reference = $this->database->getReference($this->tableName);

        // Get all chat data from Firebase
        $chatData = $reference->getValue();

        // Initialize an array to hold matching user IDs
        $userIds = [];

        // Iterate over each user_id_* node
        foreach ($chatData as $userId => $chats) {
            // Extract the numeric part from 'user_id_*'
            $userIdNumeric = (int) str_replace('user_id_', '', $userId);

            // Check if any of the $types exist in the user's chat record
            foreach ($types as $type) {
                if (isset($chats[$type])) {
                    // Add numeric user ID to the result if a match is found
                    $userIds[] = $userIdNumeric;
                    break; // Exit inner loop once a match is found for this user
                }
            }
        }

        return $userIds;
    }

    public function javaChatAgent($userMessage, $currentUser, $companyuid, $status_fallback)
    {

        if ($status_fallback == 1) {
            return response()->json(['message' => 'Please wait our agent will get back to you soon.', 'status_fallback' => 1]);
        }
        $baseURl        = env('BOTBASE_URL');
        $queryparameter = '?userId='.$currentUser.'&companyId='.$companyuid.'&text='.urlencode($userMessage); // Encode the user message
        $finalUrl       = $baseURl.$queryparameter;

        try {
            $client   = new Client;
            $response = $client->get($finalUrl);

            if ($response->getStatusCode() == 200) {
                $result = $response->getBody()->getContents();

                $data            = json_decode($result, true);
                $receiverMessage = $data['message']     ?? 'No message received.';
                $fallBackStatus  = $data['is_fallback'] ?? 0;

                $agentId = $companyuid;

                $allAgent          = User::where('parent_id', $companyuid)->get();
                $checkonlinestatus = 0;
                foreach ($allAgent as $inneragentdetail) {
                    $userStatusResponse = User::getUserStatus($inneragentdetail->id);
                    if ($userStatusResponse->getData()->status && $userStatusResponse->getData()->is_online) {
                        $agentId           = $inneragentdetail->id;
                        $checkonlinestatus = 1;
                    }
                }

                if ($checkonlinestatus == 0) {
                    $companyUser        = User::where('id', $companyuid)->first();
                    $userStatusResponse = User::getUserStatus($companyUser->id);
                    if ($userStatusResponse->getData()->status && $userStatusResponse->getData()->is_online) {
                        $agentId           = $companyUser->id;
                        $checkonlinestatus = 1;
                    }
                }

                $this->storeUserAgentMessage($currentUser, $companyuid, $agentId, $userMessage, $checkonlinestatus, $receiverMessage, $fallBackStatus);

                if ($fallBackStatus == 1) {

                    $userStatusResponse = User::getUserStatus($agentId);

                    if ($userStatusResponse->getData()->status && $userStatusResponse->getData()->is_online) {
                        $agentOnline = 1;
                    } else {
                        $agentOnline = 0;
                    }

                    if ($agentOnline == 1) {
                        $fallBackStatus  = 1;
                        $receiverMessage = 'Please wait for a customer service agent to assist you.';

                        $checkBotConversation = BotConversation::where('user_id', $currentUser)->where('company_user_id', $companyuid)->first();
                        if ($checkBotConversation) {
                            $checkBotConversation->user_id         = $currentUser;
                            $checkBotConversation->company_user_id = $companyuid;
                            $checkBotConversation->lastmessage     = $userMessage;
                            $checkBotConversation->status          = 0;
                            $checkBotConversation->last_msg_type   = 'user';
                            $checkBotConversation->agent_id        = $agentId;
                            $checkBotConversation->status_ticket   = 0;
                            $checkBotConversation->save();
                        } else {
                            $saveBotcon                  = new BotConversation;
                            $saveBotcon->user_id         = $currentUser;
                            $saveBotcon->company_user_id = $companyuid;
                            $saveBotcon->lastmessage     = $userMessage;
                            $saveBotcon->status          = 0;
                            $saveBotcon->last_msg_type   = 'user';
                            $saveBotcon->agent_id        = $agentId;
                            $saveBotcon->status_ticket   = 0;
                            $saveBotcon->save();
                        }

                    } else {
                        $fallBackStatus  = 0;
                        $receiverMessage = 'Unfortunately, our service agent is currently unavailable. Would you like to create a support ticket? Please reply with "Yes" or "No".';

                        $checkBotConversation = BotConversation::where('user_id', $currentUser)->where('company_user_id', $companyuid)->first();
                        if ($checkBotConversation) {
                            $checkBotConversation->user_id         = $currentUser;
                            $checkBotConversation->company_user_id = $companyuid;
                            $checkBotConversation->lastmessage     = $userMessage;
                            $checkBotConversation->status          = 0;
                            $checkBotConversation->last_msg_type   = 'user';
                            $checkBotConversation->agent_id        = $agentId;
                            $checkBotConversation->status_ticket   = 1;
                            $checkBotConversation->save();
                        } else {
                            $saveBotcon                  = new BotConversation;
                            $saveBotcon->user_id         = $currentUser;
                            $saveBotcon->company_user_id = $companyuid;
                            $saveBotcon->lastmessage     = $userMessage;
                            $saveBotcon->status          = 0;
                            $saveBotcon->last_msg_type   = 'user';
                            $saveBotcon->agent_id        = $agentId;
                            $saveBotcon->status_ticket   = 1;
                            $saveBotcon->save();
                        }
                        $status_fallback = 0;

                        $senderMessage    = '';
                        $triggerBroadCast = 1; // (trigger brod carst to 1 otherwise 0;)
                        $this->AgentLiveChatsend($currentUser, $companyuid, $receiverMessage, $triggerBroadCast, $senderMessage, $agentId, $status_fallback);

                    }

                }

                return response()->json(['message' => $receiverMessage, 'status_fallback' => $fallBackStatus, 'status_ticket' => 0]);

            } else {
                // Handle non-200 status codes (e.g., log an error, return a default message)
                Log::error('javaChatAgent: HTTP status code '.$response->getStatusCode());

                return response()->json(['message' => 'Error retrieving data from the chatbot'], 500);
            }
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            // Handle network errors or other exceptions
            Log::error('javaChatAgent: Request exception: '.$e->getMessage());

            return response()->json(['message' => 'Failed to connect to the chatbot'], 500);
        } catch (Exception $e) {
            Log::error('javaChatAgent: General exception: '.$e->getMessage());

            return response()->json(['message' => 'An unexpected error occurred.'], 500);
        }
    }

    public function storeUserAgentMessage($currentUser, $companyuid, $agentId, $userMessage, $checkonlinestatus, $receiverMessage, $status_fallback)
    {

        BotConversation::checkNotification($agentId);

        $senderMessage    = $userMessage;
        $triggerBroadCast = 1; // (trigger brod carst to 1 otherwise 0;)

        $this->AgentLiveChatsend($currentUser, $companyuid, $receiverMessage, $triggerBroadCast, $senderMessage, $agentId, $status_fallback);

    }

    public function liveagentChat($userMessage, $currentUser, $companyuid, $status_fallback)
    {

        $defaultStatus = $status_fallback;

        try {

            $allAgent          = User::where('parent_id', $companyuid)->get();
            $checkonlinestatus = 0;
            foreach ($allAgent as $inneragentdetail) {
                $userStatusResponse = User::getUserStatus($inneragentdetail->id);
                if ($userStatusResponse->getData()->status && $userStatusResponse->getData()->is_online) {
                    $agentId           = $inneragentdetail->id;
                    $checkonlinestatus = 1;
                }
            }

            if ($checkonlinestatus == 0) {
                $companyUser        = User::where('id', $companyuid)->first();
                $userStatusResponse = User::getUserStatus($companyUser->id);
                if ($userStatusResponse->getData()->status && $userStatusResponse->getData()->is_online) {
                    $agentId           = $companyUser->id;
                    $checkonlinestatus = 1;
                }
            }

            $ticketFinalMEssage = '';
            $checkBot           = BotConversation::where('user_id', $currentUser)->where('company_user_id', $companyuid)->first();
            if ($checkBot) {

                if ($checkBot->status_ticket == 1) {

                    $checkStatus = array('yes', 'no', 'Yes', 'No','YES,','NO');
                    
                    if ($checkBot->ticket_status == '') {

                        if (in_array($userMessage, $checkStatus)) {

                            $statusConfirmTicket = strtolower($userMessage);
                            $status_fallback = 0;

                            if ($statusConfirmTicket == 'yes') {
                                $receiverMessage = 'Great! Could you please provide the title of your ticket?';
                                $checkBot->ticket_status = $statusConfirmTicket;
                                $checkBot->save();
                                return response()->json(['message' => $receiverMessage, 'status_fallback' => $status_fallback]);
                            } else {
                                $checkBot->status = 0;
                                $checkBot->status_ticket = 0;
                                $checkBot->ticket_title = '';
                                $checkBot->ticket_status = '';
                                $checkBot->save();
                                $receiverMessage = 'Thank you for your response. Our service agent is currently unavailable. Please wait while we connect you.';
                                return response()->json(['message' => $receiverMessage, 'status_fallback' => $status_fallback]);
                            }

                        } else {
                            $receiverMessage = 'Please respond with "Yes" or "No" to proceed.';
                            $status_fallback = 0;
                            return response()->json(['message' => $receiverMessage, 'status_fallback' => $status_fallback]);
                        }

                    }

                    if ($checkBot->ticket_title == '') {

                        $status_fallback = 0;
                        $receiverMessage = 'Thank you. Now, please describe the issue or details related to your ticket.';
                        $checkBot->ticket_title = $userMessage;
                        $checkBot->save();
                        return response()->json(['message' => $receiverMessage, 'status_fallback' => $status_fallback]);

                    }

                    $ticketFinalMEssage      = 'Thank you! Your ticket has been successfully submitted. Our service agent will contact you shortly.';
                    $complaint_title  = $checkBot->ticket_title;
                    $checkBot->status_ticket = 2;
                    $checkBot->status        = 2;
                    $checkBot->ticket_title = '';
                    $checkBot->ticket_status = '';
                    $checkBot->lastmessage   = $userMessage;
                    $checkBot->save();

                    $priority         = 2;
                    
                    $complaint_detail = $userMessage;

                    $companyDetail = Company::where('userId', $companyuid)->first();

                    $saveData                   = new Complaint;
                    $saveData->companyId        = $companyDetail->id;
                    $saveData->assignedToUserId = $currentUser;
                    $saveData->priority         = $priority;
                    $saveData->complainTitle    = $complaint_title;
                    $saveData->complainDetail   = $complaint_detail;
                    $saveData->save();

                    $cmpID            = $saveData->id;
                    $curentYear       = date('Y');
                    $finalString      = 'CP'.$curentYear.$cmpID;
                    $saveData->cmp_id = $finalString;
                    $saveData->update();

                    $userMessage = '<div><h1>New Ticket Received</h1></div><div><strong>'.$complaint_title.'</strong></div><div>'.$complaint_detail.'</div>';
             
                }

                if($checkBot->status_ticket == 2){
                    $checkBot->status = 0;
                    $checkBot->status_ticket = 0;
                    $checkBot->save();
                }

                if ($checkonlinestatus == 0) {
                    $checkBot->agent_id = $companyuid;
                } else {
                    $checkBot->agent_id = $agentId;
                }
                $checkBot->lastmessage = $userMessage;

                if($checkBot->status == 2){
                    $checkBot->status = 1;
                }
                $checkBot->last_msg_type = 'user';
                


                $checkBot->save();

            } else {

                $checkBot                  = new BotConversation;
                $checkBot->user_id         = $currentUser;
                $checkBot->company_user_id = $companyuid;
                if ($checkonlinestatus == 0) {
                    $checkBot->agent_id = $companyuid;
                } else {
                    $checkBot->agent_id = $agentId;
                }
                $checkBot->lastmessage = $userMessage;
                $checkBot->save();

            }

            $agentId = $checkBot->agent_id != 0 ? $checkBot->agent_id : $companyuid;

            $userStatusResponse = User::getUserStatus($agentId);

            if ($userStatusResponse->getData()->status && $userStatusResponse->getData()->is_online) {
                $agentOnline = 1;
            } else {
                $agentOnline = 0;
            }

            if ($agentOnline == 1) {
                $statusfallback  = 0;
                $status_fallback = 0;
                $receiverMessage = '';
            } else {
                $statusfallback  = 1;
                $status_fallback = 0;
                $receiverMessage = 'Unfortunately, our service agent is currently unavailable. Would you like to create a support ticket? Please reply with "Yes" or "No".';
                $checkBot->status_ticket = 1;
                $checkBot->save();
            }

            BotConversation::checkNotification($agentId);

            if ($defaultStatus == 1 && $status_fallback == 1) {
                $userMessage = '';
            }
            if (trim($ticketFinalMEssage) != '') {
                $receiverMessage = $ticketFinalMEssage;
                $status_fallback = 0;
                $checkBot->status_ticket = 2;
                $checkBot->save();
            }

           

            $senderMessage    = $userMessage;
            $triggerBroadCast = 1; // (trigger brod carst to 1 otherwise 0;)
            $this->AgentLiveChatsend($currentUser, $companyuid, $receiverMessage, $triggerBroadCast, $senderMessage, $agentId, $status_fallback);

            return response()->json(['message' => $receiverMessage, 'status_fallback' => $status_fallback, 'status_ticket' => $statusfallback]);

        } catch (Exception $ex) {
            return response()->json(['message' => $ex->getMessage()], 500);
        }

    }

    public function AgentLiveChatsend($currentUser, $companyuid, $receiverMessage, $triggerBroadCast, $senderMessage, $agentId, $status_fallback)
    {

        try {

            $checkCompany = Company::where('userId', $companyuid)->first();
            $types        = $checkCompany->userId.'_'.$checkCompany->id;

            $postData1 = [
                'id'         => $currentUser,
                'message'    => $senderMessage,
                'suggession' => '',
                'timestamp'  => time(),
            ];
            $postData2 = [
                'id'         => $types,
                'message'    => $receiverMessage,
                'suggession' => '',
                'timestamp'  => time(),
            ];

            $path      = $this->tableName.'/user_id_'.$currentUser.'/agent_'.$types;
            $reference = $this->database->getReference($path);

            // Update status_fallback without altering existing data
            $reference->update(['status_fallback' => $status_fallback]);

            // Push new messages
            if (trim($senderMessage) != '') {
                $reference->push($postData1);
            }
            if (trim($receiverMessage) != '') {
                $reference->push($postData2);
            }

            if ($triggerBroadCast == 1) {
                $chat_source = $types;
                $chat_uid    = base64_encode($currentUser);

                $response   = [];
                $response[] = [
                    'chat_source' => $chat_source,
                    'chat_uid'    => $chat_uid,
                    'bot_user_id' => $agentId,
                    'user_id'     => Auth::id(),
                    'text'        => $senderMessage,
                    'sender'      => 'user',
                    'agent_type'  => $types,

                ];
                $response[] = [
                    'chat_source' => $chat_source,
                    'chat_uid'    => $chat_uid,
                    'bot_user_id' => $agentId,
                    'user_id'     => Auth::id(),
                    'text'        => $receiverMessage,
                    'sender'      => 'server',
                    'agent_type'  => $types,
                ];
                broadcast(new ChatAgentMessage($response))->toOthers();

            }

        } catch (Exception $ex) {
            return response()->json(['message' => $ex->getMessage()], 500);
        }
    }

    public function chatbotWork($request, $agentDetail, $types)
    {

        $payloadArray = [];

        $checkBot = BotConversation::where('user_id', Auth::id())->where('bot_id', $agentDetail->id)->where('status', 1)->first();
        if ($checkBot) {

            $userInput = $request->message;

            $saveBotcon                = BotConversation::find($checkBot->id);
            $saveBotcon->lastmessage   = $userInput;
            $saveBotcon->last_msg_type = 'user';
            $saveBotcon->save();
            $userId    = $checkBot->user_id;
            $postData1 = [
                'id'        => Auth::id(),
                'message'   => $userInput,
                'timestamp' => time(),
            ];

            /* remove after testing */

            $chat_source = base64_encode($types);
            $chat_uid    = base64_encode($userId);

            $response   = [];
            $response[] = [
                'chat_source'     => $chat_source,
                'chat_uid'        => $chat_uid,
                'bot_user_id'     => $agentDetail->user_id,
                'user_id'         => Auth::id(),
                'text'            => $request->message,
                'sender'          => 'server',
                'agent_type'      => $types,
                'status_fallback' => 0,
            ];
            broadcast(new ChatAgentMessage($response))->toOthers();

            /* remove after testing */

            $path      = $this->tableName.'/user_id_'.$userId.'/agent_'.$types;
            $reference = $this->database->getReference($path);

            // Push new messages
            
            $receiverMessage = '';

            $pathnew      = $this->tableName.'/user_id_'.$userId.'/agent_'.$types;
            $newreference = $this->database->getReference($pathnew);

            // Update status_fallback without altering existing data
            if ($checkBot->status_ticket == 1) {

                $checkStatus = array('yes', 'no', 'Yes', 'No','YES,','NO');
                $userMessage  = $userInput;
                
                if ($checkBot->ticket_status == '') {

                    if (in_array($userMessage, $checkStatus)) {

                        $statusConfirmTicket = strtolower($userMessage);
                        $status_fallback = 0;

                        if ($statusConfirmTicket == 'yes') {
                            $receiverMessage = 'Great! Could you please provide the title of your ticket?';
                            $checkBot->ticket_status = $statusConfirmTicket;
                            $checkBot->save();
                            return response()->json(['message' => $receiverMessage, 'status_fallback' => $status_fallback]);
                        } else {
                            $checkBot->status = 0;
                            $checkBot->status_ticket = 0;
                            $checkBot->ticket_title = '';
                            $checkBot->ticket_status = '';
                            $checkBot->save();
                            $receiverMessage = 'Thank you for your response. Our service agent is currently unavailable. Please wait while we connect you.';
                            return response()->json(['message' => $receiverMessage, 'status_fallback' => $status_fallback]);
                        }

                    } else {
                        $receiverMessage = 'Please respond with "Yes" or "No" to proceed.';
                        $status_fallback = 0;
                        return response()->json(['message' => $receiverMessage, 'status_fallback' => $status_fallback]);
                    }

                }

                if ($checkBot->ticket_title == '') {

                    $status_fallback = 0;
                    $receiverMessage = 'Thank you. Now, please describe the issue or details related to your ticket.';
                    $checkBot->ticket_title = $userMessage;
                    $checkBot->save();
                    return response()->json(['message' => $receiverMessage, 'status_fallback' => $status_fallback]);

                }

                $receiverMessage         = 'Thank you! Your ticket has been successfully submitted. Our service agent will contact you shortly.';
                $complaint_title  = $checkBot->ticket_title;

                $checkBot->status_ticket = 2;
                $checkBot->status        = 2;
                $checkBot->ticket_title = '';
                $checkBot->ticket_status = '';
                $checkBot->save();

                $botQry           = Bots::find($checkBot->bot_id);
                $company_id       = $botQry->company_id;
                $priority         = 2;
                
                $complaint_detail = $userInput;

                $saveData                   = new Complaint;
                $saveData->companyId        = $company_id;
                $saveData->assignedToUserId = $userId;
                $saveData->priority         = $priority;
                $saveData->complainTitle    = $complaint_title;
                $saveData->complainDetail   = $complaint_detail;
                $saveData->save();

                $cmpID            = $saveData->id;
                $curentYear       = date('Y');
                $finalString      = 'CP'.$curentYear.$cmpID;
                $saveData->cmp_id = $finalString;
                $saveData->update();

                $ticketMessage = '<div><h1>New Ticket Received</h1></div><div><strong>'.$complaint_title.'</strong></div><div>'.$complaint_detail.'</div>';
                $postData1 = [
                    'id'        => Auth::id(),
                    'message'   => $ticketMessage,
                    'timestamp' => time(),
                ];
                $reference->push($postData1);
                $newreference->update(['status_fallback' => 1]);
                $status_fallback = 1;

            } else {

                
                $reference->push($postData1);
                $newreference->update(['status_fallback' => 0]);
                $status_fallback = 0;
            }

            return response()->json(['message' => $receiverMessage, 'status_fallback' => $status_fallback]);

        } else {

            $privateKeyString    = str_replace('\n', "\n", $agentDetail->chatbot_private_key);
            $formattedPrivateKey = "-----BEGIN PRIVATE KEY-----\n".$privateKeyString."\n-----END PRIVATE KEY-----";

            $jsonData = [
                'type'                        => 'service_account',
                'project_id'                  => $agentDetail->chatbot_project_id,
                'private_key_id'              => $agentDetail->chatbot_private_key_id,
                'private_key'                 => $formattedPrivateKey,
                'client_email'                => $agentDetail->chatbot_client_email,
                'client_id'                   => $agentDetail->chatbot_client_id,
                'auth_uri'                    => 'https://accounts.google.com/o/oauth2/auth',
                'token_uri'                   => 'https://oauth2.googleapis.com/token',
                'auth_provider_x509_cert_url' => 'https://www.googleapis.com/oauth2/v1/certs',
                'client_x509_cert_url'        => 'https://www..com/robot/v1/metadata/x509/'.urlencode($agentDetail->chatbot_client_email),
                'universe_domain'             => 'googleapis.com',
            ];

            $credentials = new ServiceAccountCredentials(
                'https://www.googleapis.com/auth/cloud-platform',
                $jsonData
            );

            $sessionsClient = new SessionsClient([
                'credentials' => $credentials,
            ]);

            $userInput = $request->message;

            try {
                $session = $sessionsClient->sessionName($agentDetail->chatbot_project_id, $agentDetail->chatbot_client_id);

                $textInput = new TextInput;
                $textInput->setText($userInput);
                $textInput->setLanguageCode('en-US');

                $queryInput = new QueryInput;
                $queryInput->setText($textInput);

                $response    = $sessionsClient->detectIntent($session, $queryInput);
                $queryResult = $response->getQueryResult();

                $intent          = $queryResult->getIntent();
                $status_fallback = 0;

                if ($intent->getDisplayName() === 'Default Fallback Intent') {
                    $status_fallback = 1;
                }

                $receiverMessage = $queryResult->getFulfillmentText().PHP_EOL;

                // Get fulfillment messages (this includes custom payloads)
                $fulfillmentMessages = $queryResult->getFulfillmentMessages();

                foreach ($fulfillmentMessages as $message) {
                    // Check if the message contains a custom payload
                    if ($message->hasPayload()) {
                        $payload      = $message->getPayload(); // This is the custom payload
                        $payloadArray = json_decode($payload->serializeToJsonString(), true); // Convert payload to array

                    }
                }

                $agentId = 0;
                if ($status_fallback == 1) {

                    $checkBot = BotConversation::where('user_id', Auth::id())->where('bot_id', $agentDetail->id)->first();
                    if ($checkBot) {
                        $saveBotcon = BotConversation::find($checkBot->id);
                        $agentId    = ($checkBot->agent_id != 0) ? $checkBot->agent_id : $agentDetail->user_id;

                    } else {
                        $saveBotcon = new BotConversation;
                    }

                    if ($agentId == 0) {
                        // NOTE : here find all agent who is online if not then get any one agent id
                        $allAgent          = User::where('parent_id', $agentDetail->user_id)->get();
                        $checkonlinestatus = 0;
                        foreach ($allAgent as $inneragentdetail) {
                            $userStatusResponse = User::getUserStatus($inneragentdetail->id);
                            if ($userStatusResponse->getData()->status && $userStatusResponse->getData()->is_online) {
                                $agentId           = $inneragentdetail->id;
                                $checkonlinestatus = 1;
                            }
                        }
                        if ($checkonlinestatus == 0) {
                            $agentIdqry = User::where('id', $agentDetail->user_id)->first();
                            if ($agentIdqry) {
                                $agentId = $agentIdqry->id;
                            } else {

                                $status_fallback = 0;
                                $statusfallback  = 1;
                                $receiverMessage = 'Unfortunately, our service agent is currently unavailable. Would you like to create a support ticket? Please reply with "Yes" or "No".';

                                return response()->json(['message' => $receiverMessage, 'status_fallback' => $status_fallback, 'status_ticket' => $statusfallback]);

                            }

                        }

                    }

                    $userStatusResponse = User::getUserStatus($agentId);

                    if ($userStatusResponse->getData()->status && $userStatusResponse->getData()->is_online) {
                        $agentOnline = 1;
                    } else {
                        $agentOnline = 0;
                    }

                    if ($agentOnline == 1) {
                        $statusfallback = 0;
                        $continueStatus = 0;

                    } else {
                        $statusfallback  = 1;
                        $status_fallback = 0;
                        $continueStatus  = 1;
                        $receiverMessage = 'Unfortunately, our service agent is currently unavailable. Would you like to create a support ticket? Please reply with "Yes" or "No".';
                    }

                    $saveBotcon->user_id         = Auth::id();
                    $saveBotcon->bot_id          = $agentDetail->id;
                    $saveBotcon->company_user_id = $agentDetail->user_id;
                    $saveBotcon->lastmessage     = $userInput;
                    $saveBotcon->status          = $continueStatus;
                    $saveBotcon->status_ticket   = $statusfallback; /* 1 value if agent not online otherwise 0 */
                    $saveBotcon->last_msg_type   = 'user';
                    $saveBotcon->save();

                    BotConversation::checkNotification($agentDetail->user_id);

                    $triggerBroadCast = 1; // (trigger brod carst to 1 otherwise 0;)
                    $this->AgentWorkSend($request, $agentDetail, $types, $receiverMessage, $triggerBroadCast);

                    return response()->json(['message' => $receiverMessage, 'status_fallback' => $status_fallback, 'status_ticket' => $statusfallback]);

                } else {

                    $userId    = Auth::id();
                    $postData1 = [
                        'id'         => Auth::id(),
                        'message'    => $request->message,
                        'suggession' => '',
                        'timestamp'  => time(),
                    ];

                    $postData2 = [
                        'id'         => $types,
                        'message'    => $receiverMessage,
                        'suggession' => ! empty($payloadArray) ? json_encode($payloadArray, true) : '',
                        'timestamp'  => time(),
                    ];

                    /* remove after testing */

                    // $chat_source = base64_encode($types);
                    // $chat_uid = base64_encode($userId);

                    // $response = [];
                    // $response[] = [
                    //     'chat_source' => $chat_source,
                    //     'chat_uid' => $chat_uid,
                    //     'bot_user_id' => $agentDetail->user_id,
                    //     'user_id'=> Auth::id(),
                    //     'text' => $request->message,
                    //     'sender' => 'user',
                    //     'agent_type' => $types,

                    // ];
                    // $response[] = [
                    //     'chat_source' => $chat_source,
                    //     'chat_uid' => $chat_uid,
                    //     'bot_user_id' => $agentDetail->user_id,
                    //     'user_id'=> Auth::id(),
                    //     'text' => $receiverMessage,
                    //     'suggession' => !empty($payloadArray) ? json_encode($payloadArray,true) : '',
                    //     'sender' => 'server',
                    //     'agent_type' => $types,
                    // ];
                    // broadcast(new ChatAgentMessage($response))->toOthers();

                    /* remove after testing */

                    $path      = $this->tableName.'/user_id_'.$userId.'/agent_'.$types;
                    $reference = $this->database->getReference($path);

                    // Update status_fallback without altering existing data
                    $reference->update(['status_fallback' => 0]);

                    // Push new messages
                    $reference->push($postData1);
                    $reference->push($postData2);

                    return response()->json(['message' => $receiverMessage, 'status_fallback' => $status_fallback]);

                }
            } catch (Exception $ex) {
                return response()->json(['message' => $ex->getMessage()], 500);
            }

        }

    }

    public function agentChatbotWorkLive($userInput, $user_reqid, $checkBot, $types, $currenetUser)
    {

        try {

            if ($checkBot) {

                if ($checkBot->status == 1) {

                    if ($checkBot->agent_id == 0) {

                        if ($checkBot->company_user_id == $currenetUser) {

                        } else {
                            return response()->json(['status' => false, 'message' => 'Invalid role authentication'], 500);
                        }

                    } else {

                        if ($checkBot->agent_id == $currenetUser) {
                        } else {
                            $agentDetail = User::find($checkBot->agent_id);

                            return response()->json(['status' => false, 'message' => 'The user is already associated with the service provider ('.$agentDetail->name.')'], 500);
                        }

                    }

                }

                $saveBotcon                = BotConversation::find($checkBot->id);
                $saveBotcon->agent_id      = $currenetUser;
                $saveBotcon->lastmessage   = $userInput;
                $saveBotcon->status        = 1;
                $saveBotcon->last_msg_type = 'agent';
                $saveBotcon->save();
                $userId = $checkBot->user_id;

                /* remove after testing */

                $chat_source = $types;
                $chat_uid    = base64_encode($userId);

                $response   = [];
                $response[] = [
                    'chat_source'     => $chat_source,
                    'chat_uid'        => $chat_uid,
                    'bot_user_id'     => $checkBot->company_user_id,
                    'user_id'         => $currenetUser,
                    'text'            => $userInput,
                    'sender'          => 'user',
                    'agent_type'      => $types,
                    'status_fallback' => 0,
                ];
                broadcast(new ChatAgentMessage($response))->toOthers();

                /* remove after testing */

                $path      = $this->tableName.'/user_id_'.$userId.'/agent_'.$types;
                $reference = $this->database->getReference($path);

                // Update status_fallback without altering existing data
                $reference->update(['status_fallback' => 0]);
                $status_fallback = 0;

                if (trim($userInput != '')) {

                    // Push new messages
                    $postData1 = [
                        'id'        => $currenetUser,
                        'message'   => $userInput,
                        'timestamp' => time(),
                    ];
                    $reference->push($postData1);

                }

                $receiverMessage = '';

                return response()->json(['status' => true, 'message' => $receiverMessage, 'status_fallback' => $status_fallback]);

            } else {
                return response()->json(['status' => false, 'message' => 'invalid request'], 500);
            }

        } catch (Exception $ex) {
            return response()->json(['status' => false, 'message' => $ex->getMessage()], 500);
        }

    }

    //    public function agentChatbotWorkLive($request, $checkBot, $types)
    //    {
    //
    //        $userInput  = $request->message;
    //        $user_reqid = $request->userid;
    //
    //        try {
    //
    //            if ($checkBot) {
    //
    //                if ($checkBot->status == 1) {
    //
    //                    if ($checkBot->agent_id == 0) {
    //
    //                        if ($checkBot->company_user_id == Auth::id()) {
    //
    //                        } else {
    //                            return response()->json(['status' => false, 'message' => 'Invalid role authentication'], 500);
    //                        }
    //
    //                    } else {
    //
    //                        if ($checkBot->agent_id == Auth::id()) {
    //                        } else {
    //                            $agentDetail = User::find($checkBot->agent_id);
    //
    //                            return response()->json(['status' => false, 'message' => 'The user is already associated with the service provider ('.$agentDetail->name.')'], 500);
    //                        }
    //
    //                    }
    //
    //                }
    //
    //                $saveBotcon                = BotConversation::find($checkBot->id);
    //                $saveBotcon->agent_id      = Auth::id();
    //                $saveBotcon->lastmessage   = $userInput;
    //                $saveBotcon->status        = 1;
    //                $saveBotcon->last_msg_type = 'agent';
    //                $saveBotcon->save();
    //                $userId = $checkBot->user_id;
    //
    //                /* remove after testing */
    //
    //                $chat_source = base64_encode($types);
    //                $chat_uid    = base64_encode($userId);
    //
    //                $response   = [];
    //                $response[] = [
    //                    'chat_source'     => $chat_source,
    //                    'chat_uid'        => $chat_uid,
    //                    'bot_user_id'     => $checkBot->company_user_id,
    //                    'user_id'         => Auth::id(),
    //                    'text'            => $request->message,
    //                    'sender'          => 'user',
    //                    'agent_type'      => $types,
    //                    'status_fallback' => 0,
    //                ];
    //                broadcast(new ChatAgentMessage($response))->toOthers();
    //
    //                /* remove after testing */
    //
    //                $path      = $this->tableName.'/user_id_'.$userId.'/agent_'.$types;
    //                $reference = $this->database->getReference($path);
    //
    //                // Update status_fallback without altering existing data
    //                $reference->update(['status_fallback' => 0]);
    //                $status_fallback = 0;
    //
    //                if (trim($userInput != '')) {
    //
    //                    // Push new messages
    //                    $postData1 = [
    //                        'id'        => Auth::id(),
    //                        'message'   => $userInput,
    //                        'timestamp' => time(),
    //                    ];
    //                    $reference->push($postData1);
    //
    //                }
    //
    //                $receiverMessage = '';
    //
    //                return response()->json(['status' => true, 'message' => $receiverMessage, 'status_fallback' => $status_fallback]);
    //
    //            } else {
    //                return response()->json(['status' => false, 'message' => 'invalid request'], 500);
    //            }
    //
    //        } catch (Exception $ex) {
    //            return response()->json(['status' => false, 'message' => $ex->getMessage()], 500);
    //        }
    //
    //    }

    public function agentChatbotWork($request, $agentDetail, $types)
    {

        $userInput  = $request->message;
        $user_reqid = $request->userid;

        try {

            $checkBot = BotConversation::where('user_id', $user_reqid)->where('bot_id', $agentDetail->id)->first();
            if ($checkBot) {

                if ($checkBot->status == 1) {

                    if ($checkBot->agent_id == 0) {

                        if ($checkBot->company_user_id == Auth::id()) {

                        } else {
                            return response()->json(['status' => false, 'message' => 'Invalid role authentication'], 500);
                        }

                    } else {

                        if ($checkBot->agent_id == Auth::id()) {
                        } else {
                            $agentDetail = User::find($checkBot->agent_id);

                            return response()->json(['status' => false, 'message' => 'The user is already associated with the service provider ('.$agentDetail->name.')'], 500);
                        }

                    }

                }

                $saveBotcon                = BotConversation::find($checkBot->id);
                $saveBotcon->agent_id      = Auth::id();
                $saveBotcon->lastmessage   = $userInput;
                $saveBotcon->status        = 1;
                $saveBotcon->last_msg_type = 'agent';
                $saveBotcon->save();
                $userId    = $checkBot->user_id;
                $postData1 = [
                    'id'        => Auth::id(),
                    'message'   => $userInput,
                    'timestamp' => time(),
                ];

                /* remove after testing */

                $chat_source = base64_encode($types);
                $chat_uid    = base64_encode($userId);

                $response   = [];
                $response[] = [
                    'chat_source'     => $chat_source,
                    'chat_uid'        => $chat_uid,
                    'bot_user_id'     => $agentDetail->user_id,
                    'user_id'         => Auth::id(),
                    'text'            => $request->message,
                    'sender'          => 'user',
                    'agent_type'      => $types,
                    'status_fallback' => 0,
                ];
                broadcast(new ChatAgentMessage($response))->toOthers();

                /* remove after testing */

                $path      = $this->tableName.'/user_id_'.$userId.'/agent_'.$types;
                $reference = $this->database->getReference($path);

                // Update status_fallback without altering existing data
                $reference->update(['status_fallback' => 0]);
                $status_fallback = 0;

                // Push new messages
                $reference->push($postData1);
                $receiverMessage = '';

                return response()->json(['status' => true, 'message' => $receiverMessage, 'status_fallback' => $status_fallback]);

            } else {
                return response()->json(['status' => false, 'message' => 'invalid request'], 500);
            }

        } catch (Exception $ex) {
            return response()->json(['status' => false, 'message' => $ex->getMessage()], 500);
        }

    }

    public function AgentWorkSend($request, $agentDetail, $types, $agentMessage, $triggerBroadCast)
    {

        try {

            $userId = Auth::id();

            $postData1 = [
                'id'        => Auth::id(),
                'message'   => $request->message,
                'timestamp' => time(),
            ];

            $postData2 = [
                'id'        => $types,
                'message'   => $agentMessage,
                'timestamp' => time(),
            ];

            $path      = $this->tableName.'/user_id_'.$userId.'/agent_'.$types;
            $reference = $this->database->getReference($path);

            // Update status_fallback without altering existing data
            $reference->update(['status_fallback' => 1]);

            // Push new messages
            $reference->push($postData1);
            $reference->push($postData2);

            if ($triggerBroadCast == 0) {

            } else {

                $chat_source = base64_encode($types);
                $chat_uid    = base64_encode($userId);

                $response   = [];
                $response[] = [
                    'chat_source' => $chat_source,
                    'chat_uid'    => $chat_uid,
                    'bot_user_id' => $agentDetail->user_id,
                    'user_id'     => Auth::id(),
                    'text'        => $request->message,
                    'sender'      => 'user',
                    'agent_type'  => $types,

                ];
                $response[] = [
                    'chat_source' => $chat_source,
                    'chat_uid'    => $chat_uid,
                    'bot_user_id' => $agentDetail->user_id,
                    'user_id'     => Auth::id(),
                    'text'        => $agentMessage,
                    'sender'      => 'server',
                    'agent_type'  => $types,
                ];
                broadcast(new ChatAgentMessage($response))->toOthers();

            }

        } catch (Exception $ex) {
            return response()->json(['message' => $ex->getMessage()], 500);
        }

    }
}
