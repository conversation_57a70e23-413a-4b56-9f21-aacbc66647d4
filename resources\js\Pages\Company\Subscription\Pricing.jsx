import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import Button from '@/Components/Button';

export default function Pricing({ plans, currentSubscription, paymentGateways }) {
    const [selectedPlan, setSelectedPlan] = useState(null);
    const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('stripe');
    const [isSubscribing, setIsSubscribing] = useState(false);

    const handleSubscribe = (plan) => {
        if (currentSubscription) {
            alert('You already have an active subscription. Please cancel your current subscription first.');
            return;
        }

        setIsSubscribing(true);
        
        router.post(route('company.subscription.subscribe'), {
            plan_id: plan.id,
            payment_method: selectedPaymentMethod,
        }, {
            onSuccess: () => {
                setIsSubscribing(false);
            },
            onError: () => {
                setIsSubscribing(false);
            }
        });
    };

    const getBillingIntervalLabel = (interval) => {
        const labels = {
            monthly: 'month',
            quarterly: 'quarter',
            semi_annual: '6 months',
            annual: 'year'
        };
        return labels[interval] || interval;
    };

    const formatFeatures = (features) => {
        if (!features || !Array.isArray(features)) return [];
        return features;
    };

    return (
        <DashboardLayout>
            <Head title="Subscription Pricing" />
            
            <div className="py-6">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                            Choose Your Plan
                        </h2>
                        <p className="mt-4 text-lg text-gray-600">
                            Select the perfect plan for your business needs. All plans include a {plans[0]?.trial_days || 7}-day free trial.
                        </p>
                    </div>

                    {currentSubscription && (
                        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-md p-4">
                            <div className="flex">
                                <div className="flex-shrink-0">
                                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <div className="ml-3">
                                    <h3 className="text-sm font-medium text-blue-800">
                                        Current Subscription
                                    </h3>
                                    <div className="mt-2 text-sm text-blue-700">
                                        <p>You are currently subscribed to the <strong>{currentSubscription.plan.name}</strong> plan.</p>
                                        {currentSubscription.on_trial && (
                                            <p>Your trial ends in {currentSubscription.trial_days_remaining} days.</p>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    <div className="mt-12 space-y-4 sm:mt-16 sm:space-y-0 sm:grid sm:grid-cols-2 sm:gap-6 lg:max-w-4xl lg:mx-auto xl:max-w-none xl:mx-0 xl:grid-cols-4">
                        {plans.map((plan) => (
                            <div
                                key={plan.id}
                                className={`border rounded-lg shadow-sm divide-y divide-gray-200 ${
                                    plan.is_popular 
                                        ? 'border-indigo-500 relative' 
                                        : 'border-gray-200'
                                }`}
                            >
                                {plan.is_popular && (
                                    <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                                        <span className="inline-flex px-4 py-1 rounded-full text-sm font-semibold tracking-wide uppercase bg-indigo-500 text-white">
                                            Most Popular
                                        </span>
                                    </div>
                                )}
                                
                                <div className="p-6">
                                    <h2 className="text-lg leading-6 font-medium text-gray-900">{plan.name}</h2>
                                    <p className="mt-4 text-sm text-gray-500">{plan.description}</p>
                                    <p className="mt-8">
                                        <span className="text-4xl font-extrabold text-gray-900">{plan.formatted_price}</span>
                                        <span className="text-base font-medium text-gray-500">/{getBillingIntervalLabel(plan.billing_interval)}</span>
                                    </p>
                                    
                                    {plan.discount_percentage > 0 && (
                                        <p className="mt-2 text-sm text-green-600 font-medium">
                                            Save {plan.discount_percentage}% compared to monthly
                                        </p>
                                    )}
                                    
                                    <p className="mt-4 text-sm text-gray-500">
                                        {plan.trial_days} days free trial
                                    </p>
                                    
                                    <Button
                                        onClick={() => handleSubscribe(plan)}
                                        disabled={isSubscribing || (currentSubscription && currentSubscription.plan.id === plan.id)}
                                        className={`mt-8 w-full ${
                                            plan.is_popular 
                                                ? 'bg-indigo-500 hover:bg-indigo-600' 
                                                : ''
                                        }`}
                                        variant={plan.is_popular ? 'primary' : 'outline'}
                                    >
                                        {currentSubscription && currentSubscription.plan.id === plan.id
                                            ? 'Current Plan'
                                            : isSubscribing 
                                                ? 'Processing...' 
                                                : 'Start Free Trial'
                                        }
                                    </Button>
                                </div>
                                
                                <div className="pt-6 pb-8 px-6">
                                    <h3 className="text-xs font-medium text-gray-900 tracking-wide uppercase">
                                        What's included
                                    </h3>
                                    <ul className="mt-6 space-y-4">
                                        {formatFeatures(plan.features).map((feature, index) => (
                                            <li key={index} className="flex space-x-3">
                                                <svg className="flex-shrink-0 h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                </svg>
                                                <span className="text-sm text-gray-500">{feature}</span>
                                            </li>
                                        ))}
                                    </ul>
                                    
                                    {plan.limits && Object.keys(plan.limits).length > 0 && (
                                        <div className="mt-6">
                                            <h4 className="text-xs font-medium text-gray-900 tracking-wide uppercase">
                                                Usage Limits
                                            </h4>
                                            <ul className="mt-4 space-y-2">
                                                {Object.entries(plan.limits).map(([key, value]) => (
                                                    <li key={key} className="text-sm text-gray-500">
                                                        {key.replace('max_', '').replace('_', ' ')}: {value === -1 ? 'Unlimited' : value}
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>

                    {!currentSubscription && (
                        <div className="mt-12 bg-gray-50 rounded-lg p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Method</h3>
                            <div className="space-y-4">
                                {paymentGateways.map((gateway) => (
                                    <div key={gateway.id} className="flex items-center">
                                        <input
                                            id={gateway.name}
                                            name="payment_method"
                                            type="radio"
                                            value={gateway.name}
                                            checked={selectedPaymentMethod === gateway.name}
                                            onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                                            className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
                                        />
                                        <label htmlFor={gateway.name} className="ml-3 block text-sm font-medium text-gray-700">
                                            {gateway.display_name}
                                        </label>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    <div className="mt-12 bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-blue-800">
                                    Free Trial Information
                                </h3>
                                <div className="mt-2 text-sm text-blue-700">
                                    <ul className="list-disc pl-5 space-y-1">
                                        <li>All plans include a {plans[0]?.trial_days || 7}-day free trial</li>
                                        <li>No payment required to start your trial</li>
                                        <li>Cancel anytime during the trial period</li>
                                        <li>Full access to all plan features during trial</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    );
}
