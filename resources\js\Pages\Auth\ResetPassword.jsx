import React, { useState } from 'react';
import { useForm } from '@inertiajs/react';
import GuestLayout from '@/Layouts/GuestLayout';
import { Input } from '@/Components/FormElements';
import Button from '@/Components/Button';
import FormError from '@/Components/FormError';

export default function ResetPassword({ token }) {
    const { data, setData, post, processing, errors } = useForm({
        token: token || '',
        email: '',
        password: '',
        password_confirmation: '',
    });
    const [formError, setFormError] = useState(null);
    const [status, setStatus] = useState(null);

    function handleSubmit(e) {
        e.preventDefault();
        post('/reset-password', {
            onSuccess: () => setStatus('Password reset successful!'),
            onError: (err) => setFormError(err.email || err.password || 'Reset failed'),
        });
    }

    return (
        <GuestLayout>
            <h2 className="text-2xl font-bold mb-6 text-center">Reset your password</h2>
            <form onSubmit={handleSubmit}>
                <input type="hidden" name="token" value={data.token} />
                <Input
                    label="Email"
                    name="email"
                    type="email"
                    value={data.email}
                    onChange={e => setData('email', e.target.value)}
                    error={errors.email}
                    autoFocus
                />
                <Input
                    label="New Password"
                    name="password"
                    type="password"
                    value={data.password}
                    onChange={e => setData('password', e.target.value)}
                    error={errors.password}
                />
                <Input
                    label="Confirm Password"
                    name="password_confirmation"
                    type="password"
                    value={data.password_confirmation}
                    onChange={e => setData('password_confirmation', e.target.value)}
                    error={errors.password_confirmation}
                />
                <FormError error={formError || errors.email || errors.password} />
                <Button type="submit" loading={processing}>Reset Password</Button>
            </form>
            {status && <div className="text-green-600 text-sm mt-4 text-center">{status}</div>}
        </GuestLayout>
    );
} 