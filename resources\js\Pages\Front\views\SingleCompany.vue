<script setup>
import { ref } from 'vue';
import App from '../App.vue';
import { Head, usePage } from '@inertiajs/vue3';
import singImage from '../assets/images/profile-banner-2.jpg';
const pageTitle = ref('Company Detail');

const { companyQry, totalActiveDeal } = usePage().props;
var baseurl = window.location.origin;

const fixUrl = (url) => {
	// Check if the url starts with http:// or https://
	if (!url.startsWith('http://') && !url.startsWith('https://')) {
		// If not, add the default prefix (e.g., http://)
		return 'http://' + url;
	}
	return url;
}




const singleCompany = ref({
	logo: singImage,
	name: 'Company Name',
	address: '123 Street, City, Country',
	contact: '+1234567890',
	website: 'www.company1.com',
	activeDeals: 'Deals offer 3'
});

const getFacebookShareLink = () => {
	// Replace URL and text with the actual deal URL and text
	const url = encodeURIComponent(window.location.href);
	return `https://www.facebook.com/sharer/sharer.php?u=${url}`;
};

const getWhatsAppShareLink = () => {
	// Replace URL and text with the actual deal URL and text
	const url = encodeURIComponent(window.location.href);
	return `https://api.whatsapp.com/send?text=${url}`;
};

const getTwitterShareLink = () => {
	// Replace URL and text with the actual deal URL and text
	const url = encodeURIComponent(window.location.href);
	const text = encodeURIComponent('Check out this deal!');
	return `https://twitter.com/intent/tweet?url=${url}&text=${text}`;
};
</script>

<style scoped>
/* Add your scoped styles here */
</style>

<template>

	<Head :title="pageTitle" />
	<App>

		<div class="mx-auto mt-10 sm:mt-20 container">
			<!-- Company header -->
			<div class="text-center">
				<h1 class="font-semibold text-3xl text-slate-800">{{ companyQry.companyName }}</h1>
			</div>

			<!-- Company details -->
			<div class="gap-8 grid grid-cols-1 sm:grid-cols-1 mt-8">
				<!-- Company image -->
				<div class="mx-auto w-full text-center">
					<img v-if="companyQry.profilePicture && companyQry.profilePicture.startsWith('http')"
						:src="companyQry.profilePicture" alt="Company Logo" class="shadow-sm mx-auto rounded-md h-52" />
					<img v-else-if="companyQry.profilePicture" :src="baseurl + '/storage/' + companyQry.profilePicture"
						alt="Company Logo" class="shadow-sm mx-auto rounded-md h-52" />
					<img v-else src="@/assets/images/user/user-profile.jpeg" alt="Company Logo"
						class="shadow-sm mx-auto rounded-md h-52" />
				</div>

				<!-- Company information -->
				<div class="text-center">
					<h2 class="font-semibold text-slate-800 text-xl">Contact Information:</h2>
					<p class="mb-4 text-lg text-ternary-dark">{{ companyQry.address }}</p>
					<p v-if="companyQry.phone != '' && companyQry.phone != 0" class="mb-2 text-lg text-ternary-dark">
						<strong>Contact:</strong> {{ companyQry.phone }}
					</p>
					<p class="mb-2 text-lg text-ternary-dark"><strong>Website:</strong> <a
							:href="fixUrl(companyQry.websiteUrl)" class="text-cfp-500">{{
								fixUrl(companyQry.websiteUrl)
							}}</a></p>
					<p class="mb-2 text-lg text-ternary-dark" v-if="totalActiveDeal > 0"><strong>Active Deals:</strong>
						{{ totalActiveDeal }}</p>


					<!-- Social sharing -->
					<div class="mt-10 max-md:mt-0 pt-10 pb-10 border-t border-cfp-500-light text-center">
						<h2 class="font-semibold text-ternary-dark text-xl">Share Individual Deal</h2>
						<div class="flex justify-center space-x-4 mx-auto mt-4 text-center align-middle">
							<!-- Facebook -->
							<a :href="getFacebookShareLink()"
								class="bg-gray-50 hover:bg-gray-100 shadow-sm px-4 py-2 rounded-md text-gray-400 hover:text-indigo-500 duration-500 cursor-pointer">

								<svg width="24px" height="24px" viewBox="-5 0 20 20" version="1.1"
									xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">

									<title>facebook [#176]</title>
									<desc>Created with Sketch.</desc>
									<defs>

									</defs>
									<g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
										<g id="Dribbble-Light-Preview" transform="translate(-385.000000, -7399.000000)"
											fill="#000000">
											<g id="icons" transform="translate(56.000000, 160.000000)">
												<path
													d="M335.821282,7259 L335.821282,7250 L338.553693,7250 L339,7246 L335.821282,7246 L335.821282,7244.052 C335.821282,7243.022 335.847593,7242 337.286884,7242 L338.744689,7242 L338.744689,7239.14 C338.744689,7239.097 337.492497,7239 336.225687,7239 C333.580004,7239 331.923407,7240.657 331.923407,7243.7 L331.923407,7246 L329,7246 L329,7250 L331.923407,7250 L331.923407,7259 L335.821282,7259 Z"
													id="facebook-[#176]">

												</path>
											</g>
										</g>
									</g>
								</svg>
							</a>
							<!-- WhatsApp -->
							<a :href="getWhatsAppShareLink()"
								class="bg-gray-50 hover:bg-gray-100 shadow-sm px-4 py-2 rounded-md text-gray-400 hover:text-indigo-500 duration-500 cursor-pointer">
								<svg fill="#000000" width="24px" height="24px" viewBox="0 0 16 16"
									xmlns="http://www.w3.org/2000/svg">
									<path
										d="M11.42 9.49c-.19-.09-1.1-.54-1.27-.61s-.29-.09-.42.1-.48.6-.59.73-.21.14-.4 0a5.13 5.13 0 0 1-1.49-.92 5.25 5.25 0 0 1-1-1.29c-.11-.18 0-.28.08-.38s.18-.21.28-.32a1.39 1.39 0 0 0 .18-.31.38.38 0 0 0 0-.33c0-.09-.42-1-.58-1.37s-.3-.32-.41-.32h-.4a.72.72 0 0 0-.5.23 2.1 2.1 0 0 0-.65 1.55A3.59 3.59 0 0 0 5 8.2 8.32 8.32 0 0 0 8.19 11c.44.19.78.3 1.05.39a2.53 2.53 0 0 0 1.17.07 1.93 1.93 0 0 0 1.26-.88 1.67 1.67 0 0 0 .11-.88c-.05-.07-.17-.12-.36-.21z" />
									<path
										d="M13.29 2.68A7.36 7.36 0 0 0 8 .5a7.44 7.44 0 0 0-6.41 11.15l-1 3.85 3.94-1a7.4 7.4 0 0 0 3.55.9H8a7.44 7.44 0 0 0 5.29-12.72zM8 14.12a6.12 6.12 0 0 1-3.15-.87l-.22-.13-**********-2.28-.14-.23a6.18 6.18 0 0 1 9.6-7.65 6.12 6.12 0 0 1 1.81 4.37A6.19 6.19 0 0 1 8 14.12z" />
								</svg>
							</a>
							<!-- Twitter -->
							<a :href="getTwitterShareLink()"
								class="bg-gray-50 hover:bg-gray-100 shadow-sm px-4 py-2 rounded-md text-gray-400 hover:text-indigo-500 duration-500 cursor-pointer">
								<svg width="24px" height="24px" viewBox="0 0 24 24" fill="none"
									xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd" clip-rule="evenodd"
										d="M19.7828 3.91825C20.1313 3.83565 20.3743 3.75444 20.5734 3.66915C20.8524 3.54961 21.0837 3.40641 21.4492 3.16524C21.7563 2.96255 22.1499 2.9449 22.4739 3.11928C22.7979 3.29366 23 3.6319 23 3.99986C23 5.08079 22.8653 5.96673 22.5535 6.7464C22.2911 7.40221 21.9225 7.93487 21.4816 8.41968C21.2954 11.7828 20.3219 14.4239 18.8336 16.4248C17.291 18.4987 15.2386 19.8268 13.0751 20.5706C10.9179 21.3121 8.63863 21.4778 6.5967 21.2267C4.56816 20.9773 2.69304 20.3057 1.38605 19.2892C1.02813 19.0108 0.902313 18.5264 1.07951 18.109C1.25671 17.6916 1.69256 17.4457 2.14144 17.5099C3.42741 17.6936 4.6653 17.4012 5.6832 16.9832C5.48282 16.8742 5.29389 16.7562 5.11828 16.6346C4.19075 15.9925 3.4424 15.1208 3.10557 14.4471C2.96618 14.1684 2.96474 13.8405 3.10168 13.5606C3.17232 13.4161 3.27562 13.293 3.40104 13.1991C2.04677 12.0814 1.49999 10.5355 1.49999 9.49986C1.49999 9.19192 1.64187 8.90115 1.88459 8.71165C1.98665 8.63197 2.10175 8.57392 2.22308 8.53896C2.12174 8.24222 2.0431 7.94241 1.98316 7.65216C1.71739 6.3653 1.74098 4.91284 2.02985 3.75733C2.1287 3.36191 2.45764 3.06606 2.86129 3.00952C3.26493 2.95299 3.6625 3.14709 3.86618 3.50014C4.94369 5.36782 6.93116 6.50943 8.78086 7.18568C9.6505 7.50362 10.4559 7.70622 11.0596 7.83078C11.1899 6.61019 11.5307 5.6036 12.0538 4.80411C12.7439 3.74932 13.7064 3.12525 14.74 2.84698C16.5227 2.36708 18.5008 2.91382 19.7828 3.91825ZM10.7484 9.80845C10.0633 9.67087 9.12171 9.43976 8.09412 9.06408C6.7369 8.56789 5.16088 7.79418 3.84072 6.59571C3.86435 6.81625 3.89789 7.03492 3.94183 7.24766C4.16308 8.31899 4.5742 8.91899 4.94721 9.10549C5.40342 9.3336 5.61484 9.8685 5.43787 10.3469C5.19827 10.9946 4.56809 11.0477 3.99551 10.9046C4.45603 11.595 5.28377 12.2834 6.66439 12.5135C7.14057 12.5929 7.49208 13.0011 7.49986 13.4838C7.50765 13.9665 7.16949 14.3858 6.69611 14.4805L5.82565 14.6546C5.95881 14.7703 6.103 14.8838 6.2567 14.9902C6.95362 15.4727 7.65336 15.6808 8.25746 15.5298C8.70991 15.4167 9.18047 15.6313 9.39163 16.0472C9.60278 16.463 9.49846 16.9696 9.14018 17.2681C8.49626 17.8041 7.74425 18.2342 6.99057 18.5911C6.63675 18.7587 6.24134 18.9241 5.8119 19.0697C6.14218 19.1402 6.48586 19.198 6.84078 19.2417C8.61136 19.4594 10.5821 19.3126 12.4249 18.6792C14.2614 18.0479 15.9589 16.9385 17.2289 15.2312C18.497 13.5262 19.382 11.1667 19.5007 7.96291C19.51 7.71067 19.6144 7.47129 19.7929 7.29281C20.2425 6.84316 20.6141 6.32777 20.7969 5.7143C20.477 5.81403 20.1168 5.90035 19.6878 5.98237C19.3623 6.04459 19.0272 5.94156 18.7929 5.70727C18.0284 4.94274 16.5164 4.43998 15.2599 4.77822C14.6686 4.93741 14.1311 5.28203 13.7274 5.89906C13.3153 6.52904 13 7.51045 13 8.9999C13 9.28288 12.8801 9.5526 12.6701 9.74221C12.1721 10.1917 11.334 9.92603 10.7484 9.80845Z"
										fill="#0F0F0F" />
								</svg>
							</a>
						</div>
					</div>
					<!-- End Social sharing  -->
				</div>
			</div>
		</div>

	</App>

</template>
