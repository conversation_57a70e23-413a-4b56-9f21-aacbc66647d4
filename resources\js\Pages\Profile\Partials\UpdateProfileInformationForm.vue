<script setup>
import DangerButton from '@/Components/DangerButton.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import { Link, useForm, usePage } from '@inertiajs/vue3';
import FileInput from '@/Components/FileInput.vue';
import { ref, watch } from 'vue';

defineProps({
    mustVerifyEmail: {
        type: Boolean,
    },
    status: {
        type: String,
    },
});

const user = usePage().props.auth.user;

const form = useForm({
    name: user.name,
    company_name: user.company_name,
    email: user.email,
    profilePicture: null,
});

const originalForm = { ...form }; // Save original values for resetting
const formChanged = ref(false); // To track if the form is modified
const isResetting = ref(false); // Temporary flag to disable the watche

const onFileChange = (event) => {
    const file = event.target.files[0];
    form.profilePicture = file;
    if (file) {
        form.previewUrl = URL.createObjectURL(file);
    }
};

watch(
    () => ({
        name: form.name,
        company_name: form.company_name,
        email: form.email,
        profilePicture: form.profilePicture,
    }),
    (newVal, oldVal) => {
        if (!isResetting.value) {
            formChanged.value = JSON.stringify(newVal) !== JSON.stringify(originalForm);
        }
    },
    { deep: true }
);



const resetForm = () => {

    isResetting.value = true;

    form.name = originalForm.name;
    form.company_name = originalForm.company_name;
    form.email = originalForm.email;
    form.profilePicture = null;
    form.previewUrl = null;
    const fileInput = document.getElementById('profilePicture');
    if (fileInput) {
        fileInput.value = '';
    }

    formChanged.value = false;
    setTimeout(() => {
        isResetting.value = false;
    }, 0);


};

const submitForm = () => {

    const formData = new FormData();

    formData.append('name', form.name);
    formData.append('company_name', form.company_name);
    formData.append('email', form.email);
    if (form.profilePicture) {
        formData.append('profilePicture', form.profilePicture);
    }

    form.post(route('profile.update'), {
        onSuccess: () => {
            formChanged.value = false;
            const fileInput = document.getElementById('profilePicture');
            fileInput.value = ''; // Clear the file input for better browser compatibility
        },
        forceFormData: true,
    });
};

var baseurl = window.location.origin;



</script>


<template>
    <section class="!max-w-full">
        <header>
            <h2 class="font-medium text-gray-900 text-lg">Profile Information</h2>

            <p class="mt-1 text-gray-600 text-sm">
                Update your account's profile information and email address.
            </p>
        </header>

        <form @submit.prevent="submitForm" class="space-y-6 mt-6" enctype="multipart/form-data">

            <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
                leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
                <p v-if="form.recentlySuccessful" enter-active-class="transition ease-in-out"
                    enter-from-class="opacity-0" leave-active-class="transition ease-in-out" leave-to-class="opacity-0"
                    :class="$page.props.flash?.class">
                    {{ $page.props.flash.message }}</p>
            </Transition>

            <div>
                <InputLabel for="name" value="Name" />

                <TextInput id="name" type="text" class="block mt-1 w-full" v-model="form.name" required autofocus
                    autocomplete="name" />

                <InputError class="mt-2" :message="form.errors.name" />
            </div>

            <div v-if="$page.props.auth.user.profileType == 'company'">
                <InputLabel for="company_name" value="Company Name" />

                <TextInput id="company_name" type="text" class="block mt-1 w-full" v-model="form.company_name" required
                    autofocus autocomplete="company_name" />

                <InputError class="mt-2" :message="form.errors.company_name" />
            </div>

            <!-- <img
                v-if="$page.props.auth.user.profilePicture && $page.props.auth.user.profilePicture.startsWith('http')"
                :src="$page.props.auth.user.profilePicture" alt="Profile Picture"
                class="border-2 border-gray-300 rounded-full w-15 h-15" />
              <img v-else-if="$page.props.auth.user.profilePicture"
                :src="baseurl + '/storage/' + $page.props.auth.user.profilePicture" alt="Profile Picture"
                class="border-2 border-gray-300 rounded-full w-15 h-15" />
              <img v-else src="@/assets/images/user/user-profile.jpeg" alt="Profile Picture"
                class="border-2 border-gray-300 rounded-full w-15 h-15" /> -->

            <div v-if="form.previewUrl" class="mb-4">
                <img :src="form.previewUrl" alt="Profile Preview"
                    class="border-2 border-gray-300 rounded-full w-24 h-24" />
            </div>


            <div class="mb-3" v-if="$page.props.auth.user.profileType == 'company'">
                <InputLabel for="profilePicture" value="Upload Company logo" />
                <FileInput id="profilePicture" type="file" class="block mt-1 w-full" v-model="form.profilePicture"
                    @input="onFileChange" autocomplete="profilePicture" />
                <InputError class="mt-2" :message="form.errors.profilePicture" />
            </div>

            <div class="mb-3" v-if="$page.props.auth.user.profileType == 'agent' || $page.props.auth.user.profileType == 'admin' || $page.props.auth.user.profileType == 'user'">
                <InputLabel for="profilePicture" value="Upload Profile" />
                <FileInput id="profilePicture" type="file" class="block mt-1 w-full" v-model="form.profilePicture"
                    @input="onFileChange" autocomplete="profilePicture" />
                <InputError class="mt-2" :message="form.errors.profilePicture" />
            </div>


            <div>
                <InputLabel for="email" value="Email" />

                <TextInput id="email" type="email" class="!bg-gray-50" disabled v-model="form.email" required
                    autocomplete="username" />

                <InputError class="mt-2" :message="form.errors.email" />
            </div>

            <div v-if="mustVerifyEmail && user.email_verified_at === null">
                <p class="mt-2 text-slate-800 text-sm">
                    Your email address is unverified.
                    <Link :href="route('verification.send')" method="post" as="button"
                        class="rounded-md focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 text-gray-600 text-sm hover:text-gray-900 underline focus:outline-none">
                    Click here to re-send the verification email.
                    </Link>
                </p>

                <div v-show="status === 'verification-link-sent'" class="mt-2 font-medium text-green-600 text-sm">
                    A new verification link has been sent to your email address.
                </div>
            </div>

            <div class="flex items-center gap-2">
                <PrimaryButton :disabled="form.processing" class="">Save</PrimaryButton>

                <DangerButton type="button" :class="{ hidden: !formChanged }" @click="resetForm">Cancel</DangerButton>

                <!-- <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
                    leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
                    <p v-if="form.recentlySuccessful" class="text-gray-600 text-sm">Saved.</p>
                </Transition> -->

            </div>
        </form>
    </section>
</template>
