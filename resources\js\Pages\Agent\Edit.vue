<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, usePage, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';
import InputError from '@/Components/InputError.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import BreadcrumbDefault from '@/Components/Breadcrumbs/BreadcrumbDefault.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import PageHeading from '@/Components/Global/PageHeading.vue';

const pageTitle = ref('Edit Agent Detail')

const { agentDetail } = usePage().props;

const form = useForm({
    name: agentDetail.name || '',
    phone: agentDetail.phone || '',
    email: agentDetail.email || '',
});

</script>

<template>

    <Head :title="pageTitle" />
    <AuthenticatedLayout>
        <!-- <PageHeading :title="pageTitle"></PageHeading> -->

        <div class="flex">
            <div class="flex-1">
                <div class="form-main-body">
                    <div class="border-stroke bg-white border rounded-md">
                        <div class="p-6 text-gray-900">
                            <form @submit.prevent="form.post(route('manage-agent.update', { id: agentDetail.id }))"
                                class="">

                                <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
                                    leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
                                    <p v-if="form.recentlySuccessful" enter-active-class="transition ease-in-out"
                                        enter-from-class="opacity-0" leave-active-class="transition ease-in-out"
                                        leave-to-class="opacity-0" :class="$page.props.flash?.class">
                                        {{ $page.props.flash.message }}</p>
                                </Transition>


                                <div class="form-ttl-gap">
                                    <InputLabel for="name" value="Agent Name" />
                                    <TextInput id="name" name="name" type="text" class="mt-1" v-model="form.name" />
                                    <InputError :message="form.errors.name" class="mt-2" />
                                </div>
                                <div class="form-ttl-gap">
                                    <InputLabel for="phone" value="Phone" />
                                    <TextInput id="phone" name="phone" type="text" class="mt-1" v-model="form.phone" />
                                    <InputError :message="form.errors.phone" class="mt-2" />
                                </div>
                                <div class="form-ttl-gap">
                                    <InputLabel for="email" value="Email" />
                                    <TextInput id="email" name="email" type="text" class="mt-1" v-model="form.email" />
                                    <InputError :message="form.errors.email" class="mt-2" />
                                </div>
                                <div class="flex items-center">
                                    <PrimaryButton :disabled="form.processing">
                                        <template #default>
                                            <span v-if="form.processing">Loading...</span>
                                            <span v-else>Save</span>
                                        </template>
                                    </PrimaryButton>
                                    <div class="relative">
                                        <ResponsiveNavLink :href="route('manage-agent.list')" class="dk-cancle-btn">
                                            Cancel
                                        </ResponsiveNavLink>

                                    </div>
                                </div>
                            </form>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
