<?php

namespace App\Services;

use App\Models\Invoice;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;

class InvoicePdfService
{
    /**
     * Generate PDF for an invoice.
     */
    public function generatePdf(Invoice $invoice): string
    {
        $data = [
            'invoice' => $invoice,
            'subscription' => $invoice->subscription,
            'user' => $invoice->subscription->user,
            'plan' => $invoice->subscription->subscriptionPlan,
            'company' => $invoice->subscription->user->company,
        ];

        $pdf = Pdf::loadView('invoices.pdf', $data);
        
        $filename = "invoice-{$invoice->id}-" . now()->format('Y-m-d') . '.pdf';
        $path = "invoices/{$filename}";
        
        Storage::disk('public')->put($path, $pdf->output());
        
        return $path;
    }

    /**
     * Generate and download PDF for an invoice.
     */
    public function downloadPdf(Invoice $invoice): \Illuminate\Http\Response
    {
        $data = [
            'invoice' => $invoice,
            'subscription' => $invoice->subscription,
            'user' => $invoice->subscription->user,
            'plan' => $invoice->subscription->plan,
            'company' => $invoice->subscription->user->company,
        ];


        $pdf = Pdf::loadView('invoices.pdf', $data);
        
        $filename = "invoice-{$invoice->id}-" . $invoice->created_at->format('Y-m-d') . '.pdf';
        
        return $pdf->download($filename);
    }

    /**
     * Get invoice data for PDF generation.
     */
    public function getInvoiceData(Invoice $invoice): array
    {
        $subscription = $invoice->subscription;
        $user = $subscription->user;
        $plan = $subscription->subscriptionPlan;
        $company = $user->company;

        return [
            'invoice_number' => $this->generateInvoiceNumber($invoice),
            'invoice_date' => $invoice->created_at->format('M d, Y'),
            'due_date' => $invoice->due_date ? $invoice->due_date->format('M d, Y') : null,
            'paid_date' => $invoice->paid_at ? $invoice->paid_at->format('M d, Y') : null,
            'status' => ucfirst($invoice->status),
            'amount' => $invoice->amount,
            'currency' => strtoupper($invoice->currency),
            'tax_amount' => $invoice->tax_amount ?? 0,
            'total_amount' => $invoice->amount + ($invoice->tax_amount ?? 0),
            
            // Customer details
            'customer_name' => $user->name,
            'customer_email' => $user->email,
            'company_name' => $company->companyName ?? null,
            'company_address' => $this->formatCompanyAddress($company),
            
            // Plan details
            'plan_name' => $plan->name,
            'plan_description' => $plan->description,
            'billing_interval' => ucfirst($plan->billing_interval),
            'billing_period' => $this->getBillingPeriod($invoice, $subscription),
            
            // Payment details
            'payment_method' => $this->getPaymentMethod($invoice),
            'transaction_id' => $invoice->stripe_invoice_id ?? $invoice->paypal_sale_id ?? null,
        ];
    }

    /**
     * Generate invoice number.
     */
    private function generateInvoiceNumber(Invoice $invoice): string
    {
        return 'INV-' . str_pad($invoice->id, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Format company address.
     */
    private function formatCompanyAddress($company): ?string
    {
        if (!$company) {
            return null;
        }

        $address = [];
        
        if ($company->companyAddress) {
            $address[] = $company->companyAddress;
        }
        
        $cityStateZip = array_filter([
            $company->city,
            $company->state,
            $company->zipcode ?? $company->zipCode,
        ]);
        
        if ($cityStateZip) {
            $address[] = implode(', ', $cityStateZip);
        }
        
        if ($company->country) {
            $address[] = $company->country;
        }

        return implode("\n", $address);
    }

    /**
     * Get billing period for invoice.
     */
    private function getBillingPeriod(Invoice $invoice, $subscription): string
    {
        $plan = $subscription->subscriptionPlan;
        $startDate = $invoice->period_start ?? $subscription->created_at;
        $endDate = $invoice->period_end ?? $subscription->ends_at;

        if ($startDate && $endDate) {
            return $startDate->format('M d, Y') . ' - ' . $endDate->format('M d, Y');
        }

        return ucfirst($plan->billing_interval) . 'ly subscription';
    }

    /**
     * Get payment method from invoice.
     */
    private function getPaymentMethod(Invoice $invoice): string
    {
        if ($invoice->stripe_invoice_id) {
            return 'Stripe';
        }
        
        if ($invoice->paypal_sale_id) {
            return 'PayPal';
        }

        return 'Unknown';
    }

    /**
     * Generate invoice HTML for preview.
     */
    public function generateHtml(Invoice $invoice): string
    {
        $data = [
            'invoice' => $invoice,
            'subscription' => $invoice->subscription,
            'user' => $invoice->subscription->user,
            'plan' => $invoice->subscription->subscriptionPlan,
            'company' => $invoice->subscription->user->company,
            'invoice_data' => $this->getInvoiceData($invoice),
        ];

        return view('invoices.html', $data)->render();
    }
}
