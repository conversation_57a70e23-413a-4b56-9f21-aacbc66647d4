<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('company_name')->nullable();
            $table->string('phone', 80);
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->enum('profileType', ['user', 'company', 'admin', 'agent'])->default('user');
            $table->enum('verificationStatus', ['not_verified', 'verified'])->default('not_verified');
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->text('profilePicture')->nullable();
            $table->longText('LastMessage')->nullable();
            $table->rememberToken();
            $table->datetime('timeStamp')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->string('otp', 80)->nullable();
            $table->integer('parent_id')->default(0);
            $table->integer('vid')->default(0)->comment('vendor_id');
            $table->integer('uid')->default(0)->comment('userid');
            $table->timestamp('last_activity')->nullable();
            $table->timestamp('last_seen')->nullable();
            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
