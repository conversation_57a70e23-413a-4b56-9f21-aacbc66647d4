<script setup lang="ts">
import { ref } from 'vue'

const checkboxToggle = ref<boolean>(false)
</script>

<template>
  <div>
    <label for="checkboxLabelOne" class="flex items-center cursor-pointer select-none">
      <div class="relative">
        <input type="checkbox" id="checkboxLabelOne" class="sr-only" @change="checkboxToggle = !checkboxToggle" />
        <div :class="checkboxToggle && 'border-cfp-500 bg-gray '"
          class="flex justify-center items-center mr-4 border rounded w-5 h-5">
          <span :class="checkboxToggle && 'bg-cfp-500'" class="rounded-sm w-2.5 h-2.5"></span>
        </div>
      </div>
      Checkbox Text
    </label>
  </div>
</template>
