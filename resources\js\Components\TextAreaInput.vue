<script setup>
import { onMounted, ref } from 'vue';

const model = defineModel({
    type: String,
    required: true,
});

const input = ref(null);

onMounted(() => {
    if (input.value.hasAttribute('autofocus')) {
        input.value.focus();
    }
});

defineExpose({ focus: () => input.value.focus() });
</script>

<template>
    <textarea
        class="border-stroke focus:border-cfp-500 bg-transparent focus-visible:shadow-none mt-1 px-4 py-2 border rounded-md focus:ring-cfp-500/50 w-full h-40 text-slate-800 outline-none"
        v-model="model" ref="input"></textarea>
</template>
