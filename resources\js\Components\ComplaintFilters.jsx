import React from 'react';
import { FiX } from 'react-icons/fi';
import SearchBar from './SearchBar';
import { FiltersSkeleton } from './SkeletonLoader';

export default function ComplaintFilters({
    filters,
    onFilterChange,
    onClearFilters,
    filterOptions,
    loading,
    searchValue,
    onSearchChange,
    onSearchClear
}) {
    const hasActiveFilters = Object.values(filters).some(value => value !== '') || searchValue;

    const handleFilterChange = (key, value) => {
        onFilterChange({
            ...filters,
            [key]: value
        });
    };

    const handleClearFilters = () => {
        onClearFilters();
        onSearchClear();
    };

    if (loading) {
        return <FiltersSkeleton />;
    }

    return (
        <div className="bg-white p-4 rounded-lg shadow mb-6">
            <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Search & Filters</h3>
                {hasActiveFilters && (
                    <button
                        onClick={handleClearFilters}
                        className="flex items-center gap-2 text-sm text-gray-500 hover:text-gray-700"
                    >
                        <FiX className="w-4 h-4" />
                        Clear all
                    </button>
                )}
            </div>

            {/* Search Bar */}
            <div className="mb-4">
                <SearchBar
                    value={searchValue}
                    onChange={onSearchChange}
                    onClear={onSearchClear}
                    placeholder="Search by title, complaint ID, or description..."
                />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Priority Filter */}
                <div>
                    <label htmlFor="priority-filter" className="block text-sm font-medium text-gray-700 mb-2">
                        Priority
                    </label>
                    <select
                        id="priority-filter"
                        value={filters.priority || ''}
                        onChange={(e) => handleFilterChange('priority', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-cf-primary focus:border-transparent"
                    >
                        <option value="">All Priorities</option>
                        {filterOptions.priorities?.map((priority) => (
                            <option key={priority.value} value={priority.value}>
                                {priority.label}
                            </option>
                        ))}
                    </select>
                </div>

                {/* Status Filter */}
                <div>
                    <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700 mb-2">
                        Status
                    </label>
                    <select
                        id="status-filter"
                        value={filters.status || ''}
                        onChange={(e) => handleFilterChange('status', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-cf-primary focus:border-transparent"
                    >
                        <option value="">All Statuses</option>
                        {filterOptions.statuses?.map((status) => (
                            <option key={status.value} value={status.value}>
                                {status.label}
                            </option>
                        ))}
                    </select>
                </div>

                {/* Company Filter */}
                <div>
                    <label htmlFor="company-filter" className="block text-sm font-medium text-gray-700 mb-2">
                        Company
                    </label>
                    <select
                        id="company-filter"
                        value={filters.company_id || ''}
                        onChange={(e) => handleFilterChange('company_id', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-cf-primary focus:border-transparent"
                    >
                        <option value="">All Companies</option>
                        {filterOptions.companies?.map((company) => (
                            <option key={company.id} value={company.id}>
                                {company.companyName}
                            </option>
                        ))}
                    </select>
                </div>

                {/* Assigned User Filter */}
                <div>
                    <label htmlFor="user-filter" className="block text-sm font-medium text-gray-700 mb-2">
                        Assigned User
                    </label>
                    <select
                        id="user-filter"
                        value={filters.assigned_user_id || ''}
                        onChange={(e) => handleFilterChange('assigned_user_id', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-cf-primary focus:border-transparent"
                    >
                        <option value="">All Users</option>
                        {filterOptions.users?.map((user) => (
                            <option key={user.id} value={user.id}>
                                {user.name}
                            </option>
                        ))}
                    </select>
                </div>
            </div>

            {/* Active Filters Display */}
            {hasActiveFilters && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="flex flex-wrap gap-2">
                        <span className="text-sm text-gray-500">Active filters:</span>
                        {searchValue && (
                            <span className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                                Search: "{searchValue}"
                                <button
                                    onClick={onSearchClear}
                                    className="hover:text-gray-600"
                                >
                                    <FiX className="w-3 h-3" />
                                </button>
                            </span>
                        )}
                        {filters.priority && (
                            <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                Priority: {filterOptions.priorities?.find(p => p.value === filters.priority)?.label}
                                <button
                                    onClick={() => handleFilterChange('priority', '')}
                                    className="hover:text-blue-600"
                                >
                                    <FiX className="w-3 h-3" />
                                </button>
                            </span>
                        )}
                        {filters.status && (
                            <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                Status: {filterOptions.statuses?.find(s => s.value === filters.status)?.label}
                                <button
                                    onClick={() => handleFilterChange('status', '')}
                                    className="hover:text-green-600"
                                >
                                    <FiX className="w-3 h-3" />
                                </button>
                            </span>
                        )}
                        {filters.company_id && (
                            <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                                Company: {filterOptions.companies?.find(c => c.id == filters.company_id)?.companyName}
                                <button
                                    onClick={() => handleFilterChange('company_id', '')}
                                    className="hover:text-purple-600"
                                >
                                    <FiX className="w-3 h-3" />
                                </button>
                            </span>
                        )}
                        {filters.assigned_user_id && (
                            <span className="inline-flex items-center gap-1 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                                User: {filterOptions.users?.find(u => u.id == filters.assigned_user_id)?.name}
                                <button
                                    onClick={() => handleFilterChange('assigned_user_id', '')}
                                    className="hover:text-orange-600"
                                >
                                    <FiX className="w-3 h-3" />
                                </button>
                            </span>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
}
