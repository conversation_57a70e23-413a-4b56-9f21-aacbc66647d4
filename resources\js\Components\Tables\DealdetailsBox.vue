<script setup>

import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';

const props = defineProps(['dealDetail']);
let dealDetail = props.dealDetail;
var baseurl = window.location.origin;

const formatDate = (dateString) => {
  const date = new Date(dateString);
  const formattedDate = `${date.getFullYear()}-${('0' + (date.getMonth() + 1)).slice(-2)}-${('0' + date.getDate()).slice(-2)} ${('0' + date.getHours()).slice(-2)}:${('0' + date.getMinutes()).slice(-2)}`;
  return formattedDate;
}

</script>
<template>





  <div class="border-gray-200 p-8 border rounded-md">
    <!-- Header Section -->
    <div class="flex items-center space-x-6">
      <div class="flex-shrink-0">
        <img v-if="dealDetail.profilePicture && dealDetail.profilePicture.startsWith('http')"
          :src="dealDetail.profilePicture" alt="User"
          class="border-gray-300 shadow-sm border rounded-full w-20 h-20 object-cover" />
        <img v-else-if="dealDetail.profilePicture" :src="baseurl + '/storage/' + dealDetail.profilePicture" alt="User"
          class="border-gray-300 shadow-sm border rounded-full w-20 h-20 object-cover" />
        <img v-else src="@/assets/images/user/user-profile.jpeg" alt="User"
          class="border-gray-300 shadow-sm border rounded-full w-20 h-20 object-cover" />
      </div>
      <div>
        <h3 class="font-semibold text-2xl text-slate-800">{{ dealDetail.companyName }}</h3>
        <p class="text-slate-500 text-sm">{{ dealDetail.email }}</p>
        <p class="text-slate-500 text-sm">{{ dealDetail.phone }}</p>
      </div>
    </div>

    <!-- Divider -->
    <div class="border-gray-200 my-6 border-t"></div>

    <!-- Content Section -->
    <div class="gap-6 grid grid-cols-1 md:grid-cols-2">
      <!-- Deal Image -->
      <div v-if="dealDetail.deal_file" class="space-y-2">
        <h4 class="font-semibold text-slate-800 text-sm">Deal Image</h4>
        <img :src="baseurl + '/storage/' + dealDetail.deal_file" alt="Deal"
          class="border-gray-300 shadow-sm border rounded-md w-full h-48 object-cover" />
      </div>

      <!-- Deal Info -->
      <div>
        <h4 class="font-semibold text-slate-800 text-sm">Title</h4>
        <p class="mb-4 text-gray-600 text-sm">{{ dealDetail.dealTitle }}</p>

        <h4 class="font-semibold text-slate-800 text-sm">Detail</h4>
        <p class="mb-4 text-gray-600 text-sm">{{ dealDetail.dealContent }}</p>

        <h4 class="font-semibold text-slate-800 text-sm">Link</h4>
        <p class="text-gray-600 text-sm break-all">{{ dealDetail.deal_link }}</p>
      </div>
    </div>

    <!-- Divider -->
    <div class="border-gray-200 my-6 border-t"></div>

    <!-- Status and Dates -->
    <div class="gap-6 grid grid-cols-1 md:grid-cols-2">
      <!-- Status -->
      <div class="flex items-center">
        <h4 class="font-semibold text-slate-800 text-sm">Status: </h4>
        <span class="inline-block px-4 py-1 rounded-full font-medium text-sm" :class="{
          ' text-yellow-600': dealDetail.deal_status == 0,
          ' text-green-600': dealDetail.deal_status == 1,
          ' text-red-600': dealDetail.deal_status == 2
        }">
          {{ dealDetail.deal_status == 0 ? 'Pending' : dealDetail.deal_status == 1 ? 'Start' : 'End' }}
        </span>
      </div>

      <!-- Dates -->
      <div>
        <h4 class="font-semibold text-slate-800 text-sm">Start On</h4>
        <p class="text-gray-600 text-sm">{{ formatDate(dealDetail.dealStartOn) }}</p>

        <h4 class="mt-4 font-semibold text-slate-800 text-sm">Expire On</h4>
        <p class="text-gray-600 text-sm">{{ formatDate(dealDetail.dealExpiresOn) }}</p>
      </div>
    </div>
  </div>


  <div class="border-stroke hidden bg-white shadow-default px-5 sm:px-7.5 pt-6 pb-2.5 xl:pb-1 border rounded-md">
    <div class="px-4 sm:px-0">
      <div class="flex-shrink-0 my-2">
        <img v-if="dealDetail.profilePicture && dealDetail.profilePicture.startsWith('http')"
          :src="dealDetail.profilePicture" alt="User" class="rounded-full w-15" />
        <img v-else-if="dealDetail.profilePicture" :src="baseurl + '/storage/' + dealDetail.profilePicture" alt="User"
          class="rounded-full w-15" />
        <img v-else src="@/assets/images/user/user-profile.jpeg" alt="User" class="rounded-full w-15" />
      </div>
      <h3 class="font-semibold text-2xl leading-7">{{ dealDetail.companyName }}</h3>
      <p class="mt-1 max-w-2xl text-slate-500 text-sm leading-6">{{ dealDetail.email }} </p>
      <p class="mt-1 max-w-2xl text-slate-500 text-sm leading-6">{{ dealDetail.phone }}</p>
    </div>
    <div class="border-gray-100 mt-6 border-t">
      <dl class="divide-y divide-gray-100">

        <div v-if="dealDetail.deal_file != '' && dealDetail.deal_file != null"
          class="sm:gap-4 sm:grid sm:grid-cols-3 px-4 sm:px-0 py-6">
          <dt class="font-semibold text-gray-900 text-sm leading-6">Image</dt>
          <dd class="sm:col-span-2 mt-1 sm:mt-0 text-slate-800 text-sm leading-6">
            <div class="my-2">
              <img :src="baseurl + '/storage/' + dealDetail.deal_file" alt="Deal" class="rounded-full w-15 h-15" />
            </div>
          </dd>
        </div>

        <div class="sm:gap-4 sm:grid sm:grid-cols-3 px-4 sm:px-0 py-6">
          <dt class="font-semibold text-gray-900 text-sm leading-6">Title</dt>
          <dd class="sm:col-span-2 mt-1 sm:mt-0 text-slate-800 text-sm leading-6">{{ dealDetail.dealTitle }}</dd>
        </div>
        <div class="sm:gap-4 sm:grid sm:grid-cols-3 px-4 sm:px-0 py-6">
          <dt class="font-semibold text-gray-900 text-sm leading-6">Detail</dt>
          <dd class="sm:col-span-2 mt-1 sm:mt-0 text-slate-800 text-sm leading-6">{{ dealDetail.dealContent }}</dd>
        </div>
        <div class="sm:gap-4 sm:grid sm:grid-cols-3 px-4 sm:px-0 py-6">
          <dt class="font-semibold text-gray-900 text-sm leading-6">Link</dt>
          <dd class="sm:col-span-2 mt-1 sm:mt-0 text-slate-800 text-sm leading-6">{{ dealDetail.deal_link }}</dd>
        </div>
        <div class="sm:gap-4 sm:grid sm:grid-cols-3 px-4 sm:px-0 py-6">
          <dt class="font-semibold text-gray-900 text-sm leading-6">Status</dt>
          <template v-if="dealDetail.deal_status == 0">

            <dt class="sm:col-span-2 mt-1 sm:mt-0 text-slate-800 text-sm leading-6">
              <span
                class="bg-warning bg-opacity-10 px-3 py-1 rounded-full font-medium text-sm text-warning">Pending</span>
            </dt>

          </template>
          <template v-else-if="dealDetail.deal_status == 1">

            <dt class="sm:col-span-2 mt-1 sm:mt-0 text-slate-800 text-sm leading-6">
              <span
                class="bg-success bg-opacity-10 px-3 py-1 rounded-full font-medium text-sm text-success">Start</span>
            </dt>

          </template>
          <template v-else-if="dealDetail.deal_status == 2">
            <dt class="sm:col-span-2 mt-1 sm:mt-0 text-slate-800 text-sm leading-6">
              <span class="bg-danger bg-opacity-10 px-3 py-1 rounded-full font-medium text-danger text-sm">End</span>
            </dt>
          </template>


        </div>
        <div class="sm:gap-4 sm:grid sm:grid-cols-3 px-4 sm:px-0 py-6">
          <dt class="font-semibold text-gray-900 text-sm leading-6">Start On</dt>
          <dd class="sm:col-span-2 mt-1 sm:mt-0 text-slate-800 text-sm leading-6">{{ formatDate(dealDetail.dealStartOn)
            }} </dd>
        </div>
        <div class="sm:gap-4 sm:grid sm:grid-cols-3 px-4 sm:px-0 py-6">
          <dt class="font-semibold text-gray-900 text-sm leading-6">Expire On</dt>
          <dd class="sm:col-span-2 mt-1 sm:mt-0 text-slate-800 text-sm leading-6">{{
            formatDate(dealDetail.dealExpiresOn)
          }}
          </dd>
        </div>


      </dl>
    </div>
  </div>

</template>
