<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdatePaymentGatewayRequest;
use App\Models\PaymentGateway;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class PaymentGatewayController extends Controller
{
    /**
     * Display the payment gateway settings.
     */
    public function index(): Response
    {
        $gateways = PaymentGateway::all();

        return Inertia::render('Admin/PaymentGateways/Index', [
            'gateways' => $gateways->map(function ($gateway) {
                return [
                    'id' => $gateway->id,
                    'name' => $gateway->name,
                    'display_name' => $gateway->display_name,
                    'is_active' => $gateway->is_active,
                    'test_mode' => $gateway->test_mode,
                    'is_configured' => $gateway->isConfigured(),
                    'required_fields' => $gateway->getRequiredFields(),
                ];
            }),
        ]);
    }

    /**
     * Show the form for editing a payment gateway.
     */
    public function edit(PaymentGateway $paymentGateway): Response
    {
        return Inertia::render('Admin/PaymentGateways/Edit', [
            'gateway' => [
                'id' => $paymentGateway->id,
                'name' => $paymentGateway->name,
                'display_name' => $paymentGateway->display_name,
                'is_active' => $paymentGateway->is_active,
                'test_mode' => $paymentGateway->test_mode,
                'test_config' => $paymentGateway->test_config ?? [],
                'live_config' => $paymentGateway->live_config ?? [],
                'webhook_secret' => $paymentGateway->webhook_secret,
                'required_fields' => $paymentGateway->getRequiredFields(),
                'is_configured' => $paymentGateway->isConfigured(),
            ],
        ]);
    }

    /**
     * Update the specified payment gateway.
     */
    public function update(UpdatePaymentGatewayRequest $request, PaymentGateway $paymentGateway)
    {
        $validated = $request->validated();

        // Validate required fields based on gateway type
        $requiredFields = $paymentGateway->getRequiredFields();
        $currentConfig = $validated['test_mode'] ?
            ($validated['test_config'] ?? []) :
            ($validated['live_config'] ?? []);

        foreach ($requiredFields as $field) {
            if (empty($currentConfig[$field])) {
                return back()->withErrors([
                    "config.{$field}" => "The {$field} field is required for {$paymentGateway->display_name}."
                ]);
            }
        }

        $paymentGateway->update($validated);

        return redirect()->route('admin.payment-gateways.index')
                        ->with('success', 'Payment gateway updated successfully.');
    }

    /**
     * Toggle the active status of a payment gateway.
     */
    public function toggleStatus(PaymentGateway $paymentGateway)
    {
        if (!$paymentGateway->isConfigured()) {
            return back()->withErrors([
                'gateway' => 'Cannot activate gateway. Please configure all required fields first.'
            ]);
        }

        $paymentGateway->update(['is_active' => !$paymentGateway->is_active]);

        $status = $paymentGateway->is_active ? 'activated' : 'deactivated';

        return redirect()->route('admin.payment-gateways.index')
                        ->with('success', "Payment gateway {$status} successfully.");
    }

    /**
     * Toggle test mode for a payment gateway.
     */
    public function toggleTestMode(PaymentGateway $paymentGateway)
    {
        $paymentGateway->update(['test_mode' => !$paymentGateway->test_mode]);

        $mode = $paymentGateway->test_mode ? 'test' : 'live';

        return redirect()->route('admin.payment-gateways.index')
                        ->with('success', "Payment gateway switched to {$mode} mode.");
    }

    /**
     * Test the connection to a payment gateway.
     */
    public function testConnection(PaymentGateway $paymentGateway)
    {
        if (!$paymentGateway->isConfigured()) {
            return response()->json([
                'success' => false,
                'message' => 'Gateway is not properly configured.'
            ], 400);
        }

        try {
            // Test connection based on gateway type
            $success = match ($paymentGateway->name) {
                'stripe' => $this->testStripeConnection($paymentGateway),
                'paypal' => $this->testPayPalConnection($paymentGateway),
                default => false,
            };

            return response()->json([
                'success' => $success,
                'message' => $success ? 'Connection successful!' : 'Connection failed.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test Stripe connection.
     */
    private function testStripeConnection(PaymentGateway $paymentGateway): bool
    {
        $config = $paymentGateway->getCurrentConfig();

        try {
            \Stripe\Stripe::setApiKey($config['secret_key']);
            \Stripe\Account::retrieve();
            return true;
        } catch (\Exception) {
            return false;
        }
    }

    /**
     * Test PayPal connection.
     */
    private function testPayPalConnection(PaymentGateway $paymentGateway): bool
    {
        // This would implement PayPal API test
        // For now, just return true if config exists
        $config = $paymentGateway->getCurrentConfig();
        return !empty($config['client_id']) && !empty($config['client_secret']);
    }
}
