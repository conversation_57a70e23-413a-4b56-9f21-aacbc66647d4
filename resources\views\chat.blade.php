<div class="chat-box-main">
    {{-- Chatbot Icon (Initially Visible) --}}
    <div id="chatbot-icon" class="chat-icon">
        {{-- Default Icon --}}
        <svg class="default-icon" viewBox="0 0 32 32">
            <path fill="#FFFFFF"
                d="M12.63,26.46H8.83a6.61,6.61,0,0,1-6.65-6.07,89.05,89.05,0,0,1,0-11.2A6.5,6.5,0,0,1,8.23,3.25a121.62,121.62,0,0,1,15.51,0A6.51,6.51,0,0,1,29.8,9.19a77.53,77.53,0,0,1,0,11.2,6.61,6.61,0,0,1-6.66,6.07H19.48L12.63,31V26.46">
            </path>
            <path fill="#337E81" class="icon-secondary"
                d="M19.57,21.68h3.67a2.08,2.08,0,0,0,2.11-1.81,89.86,89.86,0,0,0,0-10.38,1.9,1.9,0,0,0-1.84-1.74,113.15,113.15,0,0,0-15,0A1.9,1.9,0,0,0,6.71,9.49a74.92,74.92,0,0,0-.06,10.38,2,2,0,0,0,2.1,1.81h3.81V26.5Z">
            </path>
            <path fill="#FFFFFF"
                d="M10.1323 14.052C9.61464 13.873 9 14.1779 9 14.6347V16.9776C9 17.3027 9.21685 17.6019 9.57923 17.7403C10.6835 18.1618 13.228 19 16.0001 19C18.7721 19 21.3166 18.1618 22.4209 17.7403C22.7833 17.6019 23 17.3027 23 16.9776V14.6347C23 14.1779 22.3855 13.873 21.8678 14.052C20.5905 14.4935 18.3791 15.1109 16.0001 15.1109C13.621 15.1109 11.4096 14.4935 10.1323 14.052Z">
            </path>
        </svg>

        {{-- Typing Animation SVG --}}
        <svg class="typing-animation" viewBox="0 0 32 32">
            <circle class="dot dot-1" cx="8" cy="16" r="3" fill="#FFFFFF" />
            <circle class="dot dot-2" cx="16" cy="16" r="3" fill="#FFFFFF" />
            <circle class="dot dot-3" cx="24" cy="16" r="3" fill="#FFFFFF" />
        </svg>
    </div>

    {{-- Chatbot Main Window (Initially Hidden) --}}
    <div id="custom-chatbot-wrapper" class="chat-wrapper hidden">
        {{-- Header Section --}}
        <div class="chat-header">
            <div class="company-name">
                <span id="company-title">Company Name</span>
            </div>
            <div class="header-actions">
                <span class="minimize-btn">
                    <svg width="24" height="24" viewBox="0 0 32 32" fill="currentColor">
                        <path
                            d="M24.1818182,21 C24.6336875,21 25,21.4477153 25,22 C25,22.5522847 24.6336875,23 24.1818182,23 L7.81818182,23 C7.36631248,23 7,22.5522847 7,22 C7,21.4477153 7.36631248,21 7.81818182,21 L24.1818182,21 Z">
                        </path>
                    </svg>
                </span>
            </div>
        </div>

        {{-- Messages Section --}}
        <div id="custom-chatbot-messages" class="chat-messages">
            {{-- Messages will be dynamically inserted here --}}
        </div>

        {{-- Input Section --}}
        <div id="custom-chatbot-input-container" class="chat-input">
            <div class="input-group">

                <input id="custom-chatbot-input" type="text" placeholder="Type your message...">


                <button class="send-btn">
                    <svg viewBox="0 0 32 32" fill="currentColor">
                        <path
                            d="M9.05674797,7.10056554 L9.13703813,7.13553157 L25.4390381,15.1015316 L25.5284558,15.1506535 L25.6286153,15.2222405 C25.7452987,15.313793 25.8339182,15.4266828 25.895416,15.5505399 L25.9423517,15.6622033 L25.9751927,15.7773803 L25.9891204,15.8509608 L25.998657,15.9475578 L25.9972397,16.0748669 L25.9800642,16.201216 L25.9701282,16.2435678 C25.9550365,16.3071288 25.9331784,16.3694784 25.9050831,16.4294253 L25.8937351,16.4490792 C25.8488724,16.5422577 25.7878083,16.6290528 25.7112518,16.7055442 L25.609137,16.7931281 L25.539527,16.8424479 L25.4390381,16.8984684 L9.05674797,24.8994345 C8.4880852,25.1179893 7.84373932,24.9716543 7.42618713,24.5298922 C7.02348961,24.1049956 6.89354829,23.48994 7.08502271,22.9526995 L9.44381329,15.9994998 L7.08997091,9.06153122 C6.90991684,8.5560159 7.00409914,7.99707209 7.33051276,7.58090053 L7.4252609,7.47108641 C7.84373932,7.02834566 8.4880852,6.8820107 9.05674797,7.10056554 Z">
                        </path>
                    </svg>
                </button>
            </div>


        </div>
    </div>
</div>
<style>
    .chat-box-main .chat-icon {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: var(--primary-color, #337E81);
        cursor: pointer;
        z-index: 9998;
        transition: transform 0.3s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .chat-box-main .chat-icon svg {
        width: 32px;
        height: 32px;
        transition: transform 0.3s ease-in-out;
    }

    /* Hover animations */
    .chat-box-main .chat-icon:hover {
        transform: scale(1.05);
    }

    .chat-box-main .chat-icon:hover svg {
        transform: scale(0.95) rotate(5deg);
    }

    /* Pulse animation */
    .chat-box-main .chat-icon::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: var(--primary-color, #337E81);
        opacity: 0.4;
        transform: scale(1);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
            opacity: 0.4;
        }

        50% {
            transform: scale(1.2);
            opacity: 0;
        }

        100% {
            transform: scale(1);
            opacity: 0;
        }
    }

    .chat-box-main .chat-wrapper {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 90%;
        max-width: 400px;
        max-height: 550px;
        min-height: 300px;
        border-radius: 16px;
        background-color: #fff;
        box-shadow: rgba(0, 0, 0, 0.05) 0px 0.48px 2.41px -0.38px, rgba(0, 0, 0, 0.17) 0px 4px 20px -0.75px;
        z-index: 9999;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .chat-box-main .chat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 20px;
        background-color: var(--primary-color, #337E81);
        color: white;
    }

    .chat-box-main .header-actions {
        display: flex;
    }

    .chat-box-main .header-actions {
        display: flex;
        align-items: center;
    }

    .chat-box-main .minimize-btn {
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        transition: background-color 0.2s ease;
    }

    .chat-box-main .minimize-btn:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .chat-box-main .minimize-btn svg {
        width: 24px;
        height: 24px;
        color: white;
    }

    .chat-box-main .chat-messages {
        flex: 1;
        padding: 15px;
        overflow-y: auto;
        background-color: #f8f8f8;
        font-size: 14px;
        line-height: normal;
    }

    .chat-box-main .chat-input {
        padding: 15px;
        border-top: 1px solid #dee2e6;
        position: relative;
    }

    .chat-box-main .input-group {
        display: flex;
        align-items: center;
        padding: 4px 15px;
        border: 1px solid #dee2e6;
        border-radius: 16px;
        background-color: #fff;
        overflow: hidden;
    }

    .chat-box-main .emoji-picker-btn {
        display: flex;
        align-items: center;
        padding: 5px;
        cursor: pointer;
        color: #666;
        transition: color 0.2s ease;
    }

    .chat-box-main .emoji-picker-btn:hover {
        color: var(--primary-color, #337E81);
    }

    #custom-chatbot-input {
        flex: 1;
        border: none;
        outline: none;
        padding: 8px;
        font-size: 14px;
        background: transparent;
    }

    .chat-box-main .send-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        background: none;
        border: none;
        padding: 5px;
        cursor: pointer;
        color: var(--primary-color, #337E81);
        transition: transform 0.2s ease;
    }

    .chat-box-main .send-btn:hover {
        transform: scale(1.1);
    }

    .chat-box-main .send-btn svg {
        width: 20px;
        height: 20px;
    }

    /* Emoji Picker Panel */
    .chat-box-main .emoji-picker {
        position: absolute;
        bottom: 100%;
        left: 15px;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        width: calc(100% - 30px);
        max-height: 150px;
        overflow-y: auto;
    }

    .chat-box-main .emoji-picker.hidden {
        display: none;
    }

    .chat-box-main .emoji-list {
        display: grid;
        grid-template-columns: repeat(8, 1fr);
        gap: 8px;
        padding: 8px;
    }

    .chat-box-main .emoji-list span {
        cursor: pointer;
        padding: 5px;
        text-align: center;
        transition: transform 0.2s ease;
    }

    .chat-box-main .emoji-list span:hover {
        transform: scale(1.2);
        background: #f8f9fa;
        border-radius: 5px;
    }

    .chat-box-main .hidden {
        display: none;
    }

    /* Message Styles */
    .chat-box-main .message {
        margin: 15px 0;
        display: flex;
        align-items: flex-start;
    }

    .chat-box-main .user-message {
        justify-content: flex-end;
    }

    .chat-box-main .bot-message {
        justify-content: flex-start;
    }

    .chat-box-main .message-content {
        max-width: 70%;
        padding: 10px;
        border-radius: 15px;
        background: #FFF;
    }

    .chat-box-main .user-message .message-content {
        background-color: var(--primary-color, #337E81);
        color: white;
    }

    .chat-box-main .chat-box-main .bot-message .message-content {
        border-radius: 10px;
        color: #000;
        background-color: #FFF;
        box-shadow: rgba(0, 0, 0, 0.15) 0px 0.6px 0.54px -1.33px, rgba(0, 0, 0, 0.13) 0px 2.29px 2.06px -2.67px, rgba(0, 0, 0, 0.04) 0px 10px 9px -4px;
    }

    .chat-box-main .bot-avatar {
        width: 30px;
        height: 30px;
        margin-right: 10px;
        border-radius: 50%;
    }

    /* Add these animation styles */
    .chat-box-main .chat-icon .typing-animation {
        position: absolute;
        display: none;
        width: 32px;
        height: 32px;
    }

    .chat-box-main .chat-icon:hover .typing-animation {
        display: block;
    }

    .chat-box-main .chat-icon:hover .default-icon {
        display: none;
    }

    /* Typing animation for SVG dots */
    .chat-box-main .chat-icon .dot {
        opacity: 0;
    }

    .chat-box-main .chat-icon:hover .dot {
        animation: typingDot 1s infinite;
    }

    .chat-box-main .chat-icon:hover .dot-2 {
        animation-delay: 0.2s;
    }

    .chat-box-main .chat-icon:hover .dot-3 {
        animation-delay: 0.4s;
    }

    @keyframes typingDot {

        0%,
        100% {
            transform: translateY(0);
            opacity: 0.2;
        }

        50% {
            transform: translateY(-6px);
            opacity: 1;
        }
    }

    .chat-box-main .chat-icon:hover .chat-bubble {
        transform: translateY(-5px);
        transition: transform 0.3s ease;
    }

    .chat-box-main .chat-icon .typing-animation {
        opacity: 0;
    }

    .chat-box-main .chat-icon:hover .typing-animation {
        opacity: 1;
    }

    .chat-box-main .chat-icon:hover .dot {
        animation: typingDot 1s infinite;
    }

    .chat-box-main .chat-icon:hover .dot-2 {
        animation-delay: 0.2s;
    }

    .chat-box-main .chat-icon:hover .dot-3 {
        animation-delay: 0.4s;
    }

    /* Add these styles for the avatar */
    .chat-box-main .message-avatar {
        width: 20px;
        height: 20px;
        margin-right: 8px;
        border-radius: 50%;
        overflow: hidden;
    }

    .chat-box-main .message-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .chat-box-main .bot-message {
        display: flex;
        align-items: flex-start;
    }

    /* Add these styles */
    .chat-box-main .emoji-item {
        cursor: pointer;
        padding: 5px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        transition: all 0.2s ease;
    }

    .chat-box-main .emoji-item:hover {
        background-color: #f0f0f0;
        transform: scale(1.2);
    }
</style>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Configuration object
        const config = {
            companyName: 'Company Name',
            primaryColor: '#337E81',
            botAvatar: '{{ asset('images/chat-icon.png') }}' // Using Laravel's asset helper
        };

        // Apply configurations
        document.documentElement.style.setProperty('--primary-color', config.primaryColor);
        document.getElementById('company-title').textContent = config.companyName;

        // Toggle chat window
        const chatIcon = document.getElementById('chatbot-icon');
        const chatWrapper = document.getElementById('custom-chatbot-wrapper');
        const minimizeBtn = document.querySelector('.minimize-btn');

        // Add sample messages
        const sampleMessages = [{
                type: 'bot',
                text: 'Hello! How can I help you today?'
            },
            {
                type: 'user',
                text: 'Hi! I have a question about your services.'
            },
            {
                type: 'bot',
                text: 'Of course! I\'d be happy to help. What would you like to know?'
            },
            {
                type: 'user',
                text: 'What are your business hours?'
            },
            {
                type: 'bot',
                text: 'We\'re open Monday to Friday, 9 AM to 6 PM EST.'
            },
            {
                type: 'user',
                text: 'Great, thanks!'
            },
            {
                type: 'bot',
                text: 'You\'re welcome! Is there anything else you\'d like to know?'
            },
            {
                type: 'user',
                text: 'No, that\'s all for now.'
            },
            {
                type: 'bot',
                text: 'Feel free to reach out if you have any other questions!'
            },
        ];

        const messagesContainer = document.getElementById('custom-chatbot-messages');

        // Function to add a message
        function addMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${message.type}-message`;

            let html = '<div class="message-content">';
            if (message.type === 'bot') {
                html = `
                    <div class="message-avatar">
                        <img src="${config.botAvatar}" alt="Bot Avatar">
                    </div>
                    ${html}
                `;
            }
            html += message.text + '</div>';
            messageDiv.innerHTML = html;

            document.getElementById('custom-chatbot-messages').appendChild(messageDiv);
        }

        // Modified toggle chat window
        chatIcon.addEventListener('click', () => {
            chatWrapper.classList.remove('hidden');
            setTimeout(() => {
                chatWrapper.classList.add('show');
            }, 50);
            chatIcon.classList.add('hidden');

            // Add sample messages with delay
            sampleMessages.forEach((msg, index) => {
                setTimeout(() => {
                    addMessage(msg);
                }, index * 500);
            });
        });

        minimizeBtn.addEventListener('click', () => {
            chatWrapper.classList.remove('show');
            setTimeout(() => {
                chatWrapper.classList.add('hidden');
            }, 300);
            chatIcon.classList.remove('hidden');
        });

        // Handle new message input
        const input = document.getElementById('custom-chatbot-input');
        const sendBtn = document.querySelector('.send-btn');

        function sendMessage() {
            const text = input.value.trim();
            if (text) {
                addMessage({
                    type: 'user',
                    text
                });
                input.value = '';
            }
        }

        sendBtn.addEventListener('click', sendMessage);
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        const emojiBtn = document.getElementById('emoji-btn');
        const emojiPicker = document.getElementById('emoji-picker');

        // Toggle emoji picker
        emojiBtn.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent click from bubbling
            emojiPicker.classList.toggle('hidden');
        });

        // Convert emoji spans to clickable elements
        const emojiList = document.querySelector('.emoji-list');
        const emojis = emojiList.textContent.trim().split(' ');
        emojiList.innerHTML = emojis
            .filter(emoji => emoji) // Remove empty strings
            .map(emoji => `<span class="emoji-item">${emoji}</span>`)
            .join('');

        // Handle emoji selection
        emojiList.addEventListener('click', (e) => {
            if (e.target.classList.contains('emoji-item')) {
                const emoji = e.target.textContent;
                const cursorPos = input.selectionStart;
                const textBefore = input.value.substring(0, cursorPos);
                const textAfter = input.value.substring(cursorPos);

                // Insert emoji at cursor position
                input.value = textBefore + emoji + textAfter;

                // Move cursor after inserted emoji
                const newCursorPos = cursorPos + emoji.length;
                input.setSelectionRange(newCursorPos, newCursorPos);

                input.focus();
                emojiPicker.classList.add('hidden');
            }
        });

        // Close emoji picker when clicking outside
        document.addEventListener('click', (e) => {
            if (!emojiBtn.contains(e.target) && !emojiPicker.contains(e.target)) {
                emojiPicker.classList.add('hidden');
            }
        });
    });
</script>
