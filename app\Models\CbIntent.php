<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CbIntent extends Model
{
    use SoftDeletes;

    protected $table = 'cb_intents';

    protected $fillable = ['user_id', 'name', 'context', 'description', 'training_phrases', 'responses'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function delete()
    {
        return parent::delete();
    }

    public static function checkOrCreateDetailfIntent($userID)
    {
        $defaultNames = ['welcome', 'fallback'];

        // Fetch existing intents for the user
        $existingIntents = CbIntent::where('user_id', $userID)
            ->whereIn('name', $defaultNames)
            ->pluck('name')
            ->toArray();

        foreach ($defaultNames as $name) {
            if (! in_array($name, $existingIntents)) {
                // Create the intent if it doesn't exist
                $intent              = new CbIntent;
                $intent->user_id     = $userID;
                $intent->name        = $name;
                $intent->context     = '';
                $intent->description = '';
                $intent->save();
            }
        }
    }
}
