<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            // Add new fields if they don't exist
            if (!Schema::hasColumn('companies', 'companyPhone')) {
                $table->string('companyPhone')->nullable();
            }
            if (!Schema::hasColumn('companies', 'companyEmail')) {
                $table->string('companyEmail')->nullable();
            }
            if (!Schema::hasColumn('companies', 'companyAddress')) {
                $table->text('companyAddress')->nullable();
            }
            if (!Schema::hasColumn('companies', 'country')) {
                $table->string('country')->nullable();
            }
            if (!Schema::hasColumn('companies', 'zipcode')) {
                $table->string('zipcode')->nullable();
            }
            if (!Schema::hasColumn('companies', 'status')) {
                $table->boolean('status')->default(true);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            //
        });
    }
};
