<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SubscriptionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'price',
        'billing_interval',
        'billing_interval_count',
        'trial_days',
        'features',
        'limits',
        'is_active',
        'is_popular',
        'sort_order',
        'stripe_price_id',
        'paypal_plan_id',
        'paypal_product_id',
    ];

    protected $casts = [
        'price' => 'integer',
        'billing_interval_count' => 'integer',
        'trial_days' => 'integer',
        'features' => 'array',
        'limits' => 'array',
        'is_active' => 'boolean',
        'is_popular' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the subscriptions for this plan.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the active subscriptions for this plan.
     */
    public function activeSubscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class)->where('status', 'active');
    }

    /**
     * Scope to get only active plans.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get plans ordered by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('price');
    }

    /**
     * Get the formatted price.
     */
    public function getFormattedPriceAttribute(): string
    {
        return '$' . number_format($this->price / 100, 2);
    }

    /**
     * Get the price per month for comparison.
     */
    public function getMonthlyPriceAttribute(): float
    {
        $months = match ($this->billing_interval) {
            'monthly' => 1,
            'quarterly' => 3,
            'semi_annual' => 6,
            'annual' => 12,
            default => 1,
        };

        return ($this->price / 100) / $months;
    }

    /**
     * Get the discount percentage compared to monthly billing.
     */
    public function getDiscountPercentageAttribute(): int
    {
        if ($this->billing_interval === 'monthly') {
            return 0;
        }

        // Assuming monthly base price is $10
        $monthlyPrice = 1000; // $10 in cents
        $totalMonthlyPrice = $monthlyPrice * match ($this->billing_interval) {
            'quarterly' => 3,
            'semi_annual' => 6,
            'annual' => 12,
            default => 1,
        };

        return round((($totalMonthlyPrice - $this->price) / $totalMonthlyPrice) * 100);
    }

    /**
     * Check if the plan has a specific feature.
     */
    public function hasFeature(string $feature): bool
    {
        return in_array($feature, $this->features ?? []);
    }

    /**
     * Get the limit for a specific resource.
     */
    public function getLimit(string $resource): ?int
    {
        return $this->limits[$resource] ?? null;
    }
}
