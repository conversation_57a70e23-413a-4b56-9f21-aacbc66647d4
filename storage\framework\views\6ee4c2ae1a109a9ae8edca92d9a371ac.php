<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice #<?php echo e($invoice->id); ?></title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-info {
            text-align: right;
            float: right;
            width: 50%;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .invoice-title {
            font-size: 28px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        .invoice-meta {
            margin-bottom: 30px;
        }
        
        .invoice-meta table {
            width: 100%;
        }
        
        .invoice-meta td {
            padding: 5px 0;
            vertical-align: top;
        }
        
        .label {
            font-weight: bold;
            width: 120px;
        }
        
        .customer-info {
            background-color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .customer-info h3 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: #1f2937;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .items-table th,
        .items-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .items-table th {
            background-color: #f3f4f6;
            font-weight: bold;
            color: #374151;
        }
        
        .items-table .amount {
            text-align: right;
        }
        
        .totals {
            float: right;
            width: 300px;
            margin-top: 20px;
        }
        
        .totals table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .totals td {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .totals .label {
            text-align: right;
            font-weight: bold;
        }
        
        .totals .amount {
            text-align: right;
            width: 120px;
        }
        
        .total-row {
            background-color: #f3f4f6;
            font-weight: bold;
            font-size: 14px;
        }
        
        .payment-info {
            clear: both;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }
        
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status.paid {
            background-color: #d1fae5;
            color: #065f46;
        }
        
        .status.pending {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        .status.failed {
            background-color: #fee2e2;
            color: #991b1b;
        }
        
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 10px;
            color: #6b7280;
        }
        
        .clearfix::after {
            content: "";
            display: table;
            clear: both;
        }
    </style>
</head>
<body>
    <div class="header clearfix">
        <div class="company-info">
            <div class="company-name"><?php echo e(config('app.name')); ?></div>
            <div>Subscription Services</div>
        </div>
        
        <div class="invoice-title">INVOICE</div>
    </div>

    <div class="invoice-meta">
        <table>
            <tr>
                <td class="label">Invoice Number:</td>
                <td>INV-<?php echo e(str_pad($invoice->id, 6, '0', STR_PAD_LEFT)); ?></td>
                <td class="label">Status:</td>
                <td>
                    <span class="status <?php echo e($invoice->status); ?>"><?php echo e(ucfirst($invoice->status)); ?></span>
                </td>
            </tr>
            <tr>
                <td class="label">Invoice Date:</td>
                <td><?php echo e($invoice->created_at->format('M d, Y')); ?></td>
                <td class="label">Due Date:</td>
                <td><?php echo e($invoice->due_date ? $invoice->due_date->format('M d, Y') : 'Upon receipt'); ?></td>
            </tr>
            <?php if($invoice->paid_at): ?>
            <tr>
                <td class="label">Paid Date:</td>
                <td><?php echo e($invoice->paid_at->format('M d, Y')); ?></td>
                <td></td>
                <td></td>
            </tr>
            <?php endif; ?>
        </table>
    </div>

    <div class="customer-info">
        <h3>Bill To:</h3>
        <div><strong><?php echo e($user->name); ?></strong></div>
        <div><?php echo e($user->email); ?></div>
        <?php if($company && $company->companyName): ?>
            <div><?php echo e($company->companyName); ?></div>
        <?php endif; ?>
        <?php if($company && $company->companyAddress): ?>
            <div><?php echo e($company->companyAddress); ?></div>
            <?php if($company->city || $company->state || $company->zipcode): ?>
                <div>
                    <?php echo e(implode(', ', array_filter([$company->city, $company->state, $company->zipcode]))); ?>

                </div>
            <?php endif; ?>
            <?php if($company->country): ?>
                <div><?php echo e($company->country); ?></div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
        
    <table class="items-table">
        <thead>
            <tr>
                <th>Description</th>
                <th>Period</th>
                <th class="amount">Amount</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    <?php if($plan): ?>
                    <strong><?php echo e($plan->name); ?></strong>
                    <?php if($plan->description): ?>
                        <br><small><?php echo e($plan->description); ?></small>
                    <?php endif; ?>
                    <?php endif; ?>
                </td>
                <td>
                    <?php if($invoice->period_start && $invoice->period_end): ?>
                        <?php echo e($invoice->period_start->format('M d, Y')); ?> - <?php echo e($invoice->period_end->format('M d, Y')); ?>

                    <?php else: ?>
                        <?php if($plan): ?>
                        <?php echo e(ucfirst($plan->billing_interval)); ?>ly subscription
                        <?php endif; ?>
                    <?php endif; ?>
                </td>
                <td class="amount"><?php echo e(strtoupper($invoice->currency)); ?> <?php echo e(number_format($invoice->amount, 2)); ?></td>
            </tr>
        </tbody>
    </table>

    <div class="totals">
        <table>
            <tr>
                <td class="label">Subtotal:</td>
                <td class="amount"><?php echo e(strtoupper($invoice->currency)); ?> <?php echo e(number_format($invoice->amount, 2)); ?></td>
            </tr>
            <?php if($invoice->tax_amount): ?>
            <tr>
                <td class="label">Tax:</td>
                <td class="amount"><?php echo e(strtoupper($invoice->currency)); ?> <?php echo e(number_format($invoice->tax_amount, 2)); ?></td>
            </tr>
            <?php endif; ?>
            <tr class="total-row">
                <td class="label">Total:</td>
                <td class="amount"><?php echo e(strtoupper($invoice->currency)); ?> <?php echo e(number_format($invoice->amount + ($invoice->tax_amount ?? 0), 2)); ?></td>
            </tr>
        </table>
    </div>

    <div class="payment-info">
        <h3>Payment Information</h3>
        <?php if($invoice->stripe_invoice_id): ?>
            <p><strong>Payment Method:</strong> Stripe</p>
            <p><strong>Transaction ID:</strong> <?php echo e($invoice->stripe_invoice_id); ?></p>
        <?php elseif($invoice->paypal_sale_id): ?>
            <p><strong>Payment Method:</strong> PayPal</p>
            <p><strong>Transaction ID:</strong> <?php echo e($invoice->paypal_sale_id); ?></p>
        <?php endif; ?>
        
        <?php if($invoice->status === 'paid'): ?>
            <p><strong>Payment Status:</strong> <span class="status paid">Paid</span></p>
        <?php elseif($invoice->status === 'pending'): ?>
            <p><strong>Payment Status:</strong> <span class="status pending">Pending</span></p>
        <?php else: ?>
            <p><strong>Payment Status:</strong> <span class="status failed"><?php echo e(ucfirst($invoice->status)); ?></span></p>
        <?php endif; ?>
    </div>

    <div class="footer">
        <p>Thank you for your business!</p>
        <p>This is a computer-generated invoice. No signature required.</p>
    </div>
</body>
</html>
<?php /**PATH D:\projects\www\web\resources\views/invoices/pdf.blade.php ENDPATH**/ ?>