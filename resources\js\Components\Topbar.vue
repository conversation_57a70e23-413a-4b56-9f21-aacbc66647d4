<script setup>
import ResponsiveNavLink from './ResponsiveNavLink.vue';
import { ref, onMounted } from 'vue';
import ApplicationLogo from '@/Components/ApplicationLogo.vue';
import { usePage } from '@inertiajs/vue3';
import Dropdown from '@/Components/Dropdown.vue';
import DropdownLink from '@/Components/DropdownLink.vue';
import { Link } from '@inertiajs/vue3';

var baseurl = window.location.origin;

const toggleSidebar = () => {
  const sidebar = document.querySelector('aside');
  sidebar.classList.toggle('hidden');
};

const showNotifications = ref(false);

const notifications = ref(
  Array.isArray(usePage().props?.notifications) ? usePage().props.notifications : []
);

const fetchNotifications = async () => {
  try {
    const response = await fetch('/notifications/fetch');
    if (response.ok) {
      notifications.value = await response.json();
    }
  } catch (e) {}
};

onMounted(() => {
  Echo.private('broadcast-notification').listen('NotificationEvent', (e) => {
    fetchNotifications();
  });
});
</script>

<template>
  <nav class="flex justify-between items-center border-b h-16">
    <div class="flex items-center py-1 pr-6 border-r p-6 h-full">
      <button @click="toggleSidebar" class="lg:hidden mr-4 text-white">
        <i class="fa-bars fas"></i>
      </button>
      <ApplicationLogo class="max-md:hidden w-auto h-6 main-logo-svg" />
    </div>
    <div class="flex justify-between w-full items-center p-4 h-full">
      <!-- Left Side: Title Heading -->
      <h2 v-if="$page.props.title" class="text-lg font-medium text-gray-800">{{ $page.props.title }}</h2>

      <!-- add bello icon for check notification -->
      <div class="relative mr-4">
        <button @click="showNotifications = !showNotifications" class="focus:outline-none">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6 text-gray-600">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
          </svg>
          <span
            v-if="notifications.filter(n => n.status === 'unseen').length > 0"
            class="absolute top-0 right-0 inline-block w-2 h-2 bg-red-600 rounded-full">
          </span>
        </button>
        <div v-if="showNotifications" class="absolute right-0 mt-2 w-64 bg-white border rounded shadow-lg z-50">
          <div class="p-4 border-b font-semibold">Notifications</div>
          <ul>
            <li v-if="!notifications.length" class="px-4 py-2 text-gray-500">No notifications</li>
            <li
              v-for="notification in notifications"
              :key="notification.id"
              :class="{'font-bold bg-gray-100': notification.status === 'unseen'}"
              class="px-4 py-2 hover:bg-gray-100"
            >
              <a
                :href="`/${notification.route.replace(/^\//, '')}`"
                class="block text-inherit"
                style="text-decoration: none;"
              >
                🔔 {{ notification.message }}
                <br>
                <small class="text-xs text-gray-400">{{ new Date(notification.created_at).toLocaleString() }}</small>
              </a>
            </li>
          </ul>
        </div>
      </div>

      <!-- Right Side: Button -->
      <ResponsiveNavLink v-if="$page.props.actionBtn && $page.props.actionBtn.route && $page.props.actionBtn.title"
        class="px-4 py-2 bg-cfp text-white text-sm font-medium rounded-lg shadow hover:text-white transition"
        :href="$page.props.actionBtn.route">
        + <span class="max-md:hidden">{{ $page.props.actionBtn.title }}</span>
      </ResponsiveNavLink>
    </div>


    <div class="flex items-center py-1 border-l h-full p-4">
      <Dropdown align="right" width="48">
        <template #trigger>
          <span class="inline-flex rounded-md">
            <button type="button"
              class="inline-flex items-center border border-transparent rounded-md font-medium text-sm leading-4 transition duration-150 ease-in-out focus:outline-none h-10 w-10">
              <!-- User's Name -->
              <!-- <div class="d-block">{{ $page.props.auth.user.name }} ({{ $page.props.auth.user.profileType }})
                </div> -->
              <img
                v-if="$page.props.auth.user.profilePicture && $page.props.auth.user.profilePicture.startsWith('http')"
                :src="$page.props.auth.user.profilePicture" alt="Profile Picture"
                class="border-2 border-gray-300 rounded-full" />
              <img v-else-if="$page.props.auth.user.profilePicture"
                :src="baseurl + '/storage/' + $page.props.auth.user.profilePicture" alt="Profile Picture"
                class="border-2 border-gray-300 rounded-full" />
              <img v-else src="@/assets/images/user/user-profile.jpeg" alt="Profile Picture"
                class="border-2 border-gray-300 rounded-full" />
            </button>
          </span>
        </template>
        <template #content>
          <div class="flex items-center space-x-2 px-4 py-2 border-b bg-gray-50">
            <img v-if="$page.props.auth.user.profilePicture && $page.props.auth.user.profilePicture.startsWith('http')"
              :src="$page.props.auth.user.profilePicture" alt="Profile Picture"
              class="border-2 border-gray-300 rounded-full w-10 h-10" />
            <img v-else-if="$page.props.auth.user.profilePicture"
              :src="baseurl + '/storage/' + $page.props.auth.user.profilePicture" alt="Profile Picture"
              class="border-2 border-gray-300 rounded-full w-10 h-10" />
            <img v-else src="@/assets/images/user/user-profile.jpeg" alt="Profile Picture"
              class="border-2 border-gray-300 rounded-full w-10 h-10" />
            <div>
              <h2 class=" font-semibold text-gray-800">{{ $page.props.auth.user.name }}</h2>
              <p class="text-[12px] text- text-gray-500 leading-none">{{ $page.props.auth.user.profileType }}</p>
              
              <p v-if="$page.props.auth.company" class="text-[12px] text- text-gray-500 leading-none mt-2">Company : {{ $page.props.auth.company.companyName }}</p>
            </div>
          </div>

          <DropdownLink class="flex items-center gap-2 py-3"
            :href="route('profile.edit')">
            <span class="w-5">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5}
                stroke="currentColor" className="size-6">
                <path strokeLinecap="round" strokeLinejoin="round"
                  d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
              </svg>
            </span>
            Profile
          </DropdownLink>
          <!-- <DropdownLink class="flex items-center gap-2 py-3" v-if="$page.props.auth.user.profileType == 'company'"
            :href="route('intents.index')"> <span class="w-5">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5}
                stroke="currentColor" className="size-6">
                <path strokeLinecap="round" strokeLinejoin="round"
                  d="M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z" />
              </svg>

            </span>
            Training Settings
          </DropdownLink> -->
          <!-- <DropdownLink class="flex items-center gap-2 py-3" v-if="$page.props.auth.user.profileType == 'company'"
            :href="route('vendor-apis.index')"> <span class="w-5">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5}
                stroke="currentColor" className="size-6">
                <path strokeLinecap="round" strokeLinejoin="round"
                  d="M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z" />
              </svg>

            </span>
            API Configuration
          </DropdownLink> -->
          <DropdownLink class="flex items-center gap-2 py-3" v-if="$page.props.auth.user.profileType == 'company'"
            :href="route('manage-agent.list')">
            <span class="w-5">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5}
                stroke="currentColor" className="size-6">
                <path strokeLinecap="round" strokeLinejoin="round"
                  d="M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z" />
              </svg>

            </span>
            Manage Agents
          </DropdownLink>
          <!-- <DropdownLink class="flex items-center gap-2 py-3" v-if="$page.props.auth.user.profileType == 'company'"
            :href="route('companies')">
            <span class="w-5">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5}
                stroke="currentColor" className="size-6">
                <path strokeLinecap="round" strokeLinejoin="round" d="M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                <path strokeLinecap="round" strokeLinejoin="round"
                  d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z" />
              </svg>

            </span>
            Manage Locations
          </DropdownLink> -->
          <DropdownLink class="flex items-center gap-2 py-3" :href="route('logout')" method="post" as="button">
            <span class="w-5">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="size-6">
                <path fillRule="evenodd"
                  d="M16.5 3.75a1.5 1.5 0 0 1 1.5 1.5v13.5a1.5 1.5 0 0 1-1.5 1.5h-6a1.5 1.5 0 0 1-1.5-1.5V15a.75.75 0 0 0-1.5 0v3.75a3 3 0 0 0 3 3h6a3 3 0 0 0 3-3V5.25a3 3 0 0 0-3-3h-6a3 3 0 0 0-3 3V9A.75.75 0 1 0 9 9V5.25a1.5 1.5 0 0 1 1.5-1.5h6ZM5.78 8.47a.75.75 0 0 0-1.06 0l-3 3a.75.75 0 0 0 0 1.06l3 3a.75.75 0 0 0 1.06-1.06l-1.72-1.72H15a.75.75 0 0 0 0-1.5H4.06l1.72-1.72a.75.75 0 0 0 0-1.06Z"
                  clipRule="evenodd" />
              </svg>

            </span>
            Log Out
          </DropdownLink>
        </template>
      </Dropdown>
    </div>

  </nav>
</template>