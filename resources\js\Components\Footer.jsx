import React from 'react';
import { Link } from '@inertiajs/react';

export default function Footer() {
    return (
        <footer className="bg-gray-900 text-white px-6 py-16">
            <div className="max-w-7xl mx-auto">
                {/* Social Media Section */}
                <div className="text-center mb-12">
                    <h3 className="text-2xl font-bold mb-4">Follow Us</h3>
                    <div className="flex justify-center gap-6">
                        {/* Twitter */}
                        <a 
                            href="#" 
                            className="w-12 h-12 bg-gray-800 hover:bg-cf-primary-600 rounded-full flex items-center justify-center transition-colors text-white"
                            aria-label="Follow us on Twitter"
                        >
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        
                        {/* Facebook */}
                        <a 
                            href="#" 
                            className="w-12 h-12 bg-gray-800 hover:bg-cf-primary-600 rounded-full flex items-center justify-center transition-colors text-white"
                            aria-label="Follow us on Facebook"
                        >
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                        
                        {/* LinkedIn */}
                        <a 
                            href="#" 
                            className="w-12 h-12 bg-gray-800 hover:bg-cf-primary-600 rounded-full flex items-center justify-center transition-colors text-white"
                            aria-label="Follow us on LinkedIn"
                        >
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                        
                        {/* Instagram */}
                        <a 
                            href="#" 
                            className="w-12 h-12 bg-gray-800 hover:bg-cf-primary-600 rounded-full flex items-center justify-center transition-colors text-white"
                            aria-label="Follow us on Instagram"
                        >
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.596-3.205-1.529l1.529-1.297c.447.596 1.148.894 1.676.894.745 0 1.297-.447 1.297-1.148 0-.596-.447-1.148-1.297-1.148H7.301V11.54h1.148c.745 0 1.297-.447 1.297-1.148 0-.596-.447-1.148-1.297-1.148-.596 0-1.148.298-1.529.745L5.244 8.692c.745-.894 1.896-1.529 3.205-1.529 2.297 0 4.146 1.849 4.146 4.146 0 1.148-.447 2.148-1.297 2.745.894.596 1.297 1.596 1.297 2.745 0 2.297-1.849 4.189-4.146 4.189zm7.301-1.148h-1.148v-2.297h-2.297v2.297h-1.148V8.692h1.148v2.297h2.297V8.692h1.148v7.148z"/>
                            </svg>
                        </a>
                    </div>
                </div>
                
                {/* Footer Links */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
                    {/* Company Info */}
                    <div>
                        <div className="flex items-center gap-2 mb-4">
                            <div className="w-8 h-8 bg-gradient-to-r from-cf-primary-500 to-cf-primary-600 rounded-lg flex items-center justify-center">
                                <span className="text-white font-bold text-sm">C</span>
                            </div>
                            <span className="text-xl font-bold">ChatHi</span>
                        </div>
                        <p className="text-gray-400">
                            Discover the best deals and offers from top brands worldwide. Connect with companies and find amazing opportunities.
                        </p>
                    </div>
                    
                    {/* Categories */}
                    <div>
                        <h4 className="font-semibold mb-4">Categories</h4>
                        <ul className="space-y-2 text-gray-400">
                            <li><a href="#" className="hover:text-white transition-colors">Fashion</a></li>
                            <li><a href="#" className="hover:text-white transition-colors">Electronics</a></li>
                            <li><a href="#" className="hover:text-white transition-colors">Home & Garden</a></li>
                            <li><a href="#" className="hover:text-white transition-colors">Sports</a></li>
                            <li><a href="#" className="hover:text-white transition-colors">Books</a></li>
                        </ul>
                    </div>
                    
                    {/* Company */}
                    <div>
                        <h4 className="font-semibold mb-4">Company</h4>
                        <ul className="space-y-2 text-gray-400">
                            <li><Link href="/" className="hover:text-white transition-colors">Home</Link></li>
                            <li><Link href="/companies" className="hover:text-white transition-colors">Companies</Link></li>
                            <li><Link href="/deals" className="hover:text-white transition-colors">Deals</Link></li>
                            <li><Link href="/contact" className="hover:text-white transition-colors">Contact</Link></li>
                            <li><a href="#" className="hover:text-white transition-colors">About</a></li>
                        </ul>
                    </div>
                    
                    {/* Support */}
                    <div>
                        <h4 className="font-semibold mb-4">Support</h4>
                        <ul className="space-y-2 text-gray-400">
                            <li><a href="#" className="hover:text-white transition-colors">Help Center</a></li>
                            <li><a href="#" className="hover:text-white transition-colors">Terms of Service</a></li>
                            <li><a href="#" className="hover:text-white transition-colors">Privacy Policy</a></li>
                            <li><a href="#" className="hover:text-white transition-colors">FAQ</a></li>
                            <li><a href="#" className="hover:text-white transition-colors">Support</a></li>
                        </ul>
                    </div>
                </div>
                
                {/* Copyright */}
                <div className="border-t border-gray-800 pt-8 text-center text-gray-400">
                    <p>&copy; 2024 ChatHi. All rights reserved.</p>
                </div>
            </div>
        </footer>
    );
}
