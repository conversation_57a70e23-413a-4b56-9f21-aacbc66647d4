<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, Link, useForm, usePage } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import TextInput from '@/Components/TextInput.vue';
import TextAreaInput from '@/Components/TextAreaInput.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import DefaultCard from '@/Components/Forms/DefaultCard.vue';
import SelectInput from '@/Components/SelectInput.vue';
import { Inertia } from '@inertiajs/inertia';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import BreadcrumbDefault from '@/Components/Breadcrumbs/BreadcrumbDefault.vue';

const pageTitle = ref('Edit Complaint')


const { Complaints } = usePage().props;
var baseurl = window.location.origin;

const form = useForm({
  complain_title: Complaints.complainTitle,
  complain_description: Complaints.complainDetail,
  priority: Complaints.priority,
  complainStatus: Complaints.complainStatus,
  selectedComplainID: Complaints.id,
  response: ''
});

const updateForm = (response) => {

  form.dealViewImage = response.props.dealDetail.deal_file;
  form.complain_title = response.props.complainTitle;
  form.complain_description = response.props.complainDetail;
  form.priority = response.props.priority;
  form.complainStatus = response.props.complainStatus;

};

const submitForm = () => {
  form.post(route('complaints.update', [Complaints.id]), {
    onSuccess: (response) => {
      console.log(response);
      updateForm(response);

    },
    onError: (errors) => {
    }
  });
};



</script>

<template>

  <Head :title="pageTitle" />

  <AuthenticatedLayout>


    <form @submit.prevent="submitForm">

      <TextInput id="selectedComplainID" name="selectedComplainID" class="hidden" type="hidden"
        v-model="form.selectedComplainID" />

      <div class="add-form-main-bcls">


        <div class="flex flex-col gap-4 col-span-12 xl:col-span-12 pr-5 xl:pb-1">
          <!-- Contact Form Start -->
          <DefaultCard cardTitle="Edit Complaints">

            <div class="p-6">


              <div class="mt-2">
                <InputLabel for="complain_title" value="Title" />
                <TextInput id="complain_title" name="complain_title" type="text" placeholder="Title"
                  class="block mt-1 w-full" v-model="form.complain_title" />
                <InputError :message="form.errors.complain_title" class="mt-2" />
              </div>

              <div class="mt-2">
                <InputLabel for="complain_description" value="Description" />
                <TextAreaInput id="complain_description" name="complain_description" placeholder="Description"
                  type="text" class="block mt-1 w-full h-60" v-model="form.complain_description" />
                <InputError :message="form.errors.complain_description" class="mt-2" />
              </div>

              <div class="mt-2 mb-3">
                <InputLabel for="priority" value="Priority" />
                <SelectInput id="priority" class="block mt-1 w-full" v-model="form.priority" required
                  autocomplete="priority" name="priority">
                  <option value="" disabled selected>Select your Priority</option>
                  <option value="0">Low</option>
                  <option value="1">Medium</option>
                  <option value="2">High</option>
                </SelectInput>
                <InputError class="mt-2" :message="form.errors.priority" />
              </div>

              <div class="mt-2 mb-3">
                <InputLabel for="complainStatus" value="Complain Status" />
                <SelectInput id="complainStatus" class="block mt-1 w-full" v-model="form.complainStatus" required
                  autocomplete="complainStatus" name="complainStatus">
                  <option value="" disabled selected>Select your complainStatus</option>
                  <option value="open">Open</option>
                  <option value="close">Close</option>
                </SelectInput>
                <InputError class="mt-2" :message="form.errors.complainStatus" />
              </div>

              <button class="dk-update-btn"> Update </button>


              <ResponsiveNavLink :href="route('complaints')" class="dk-cancle-btn">
                Cancel
              </ResponsiveNavLink>

              <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
                leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
                <p class="bg-green-100 mb-4 p-2 rounded-md text-green-700" v-if="$page.props.flash?.message"
                  :class="$page.props.flash.class">{{ $page.props.flash.message }}
                </p>
              </Transition>

            </div>

          </DefaultCard>

          <!-- Contact Form End -->
        </div>

      </div>
    </form>

  </AuthenticatedLayout>
</template>
