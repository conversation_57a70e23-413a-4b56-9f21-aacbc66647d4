<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import ApplicationLogo from '@/Components/ApplicationLogo.vue';
import { Head } from '@inertiajs/vue3';
import axios from 'axios';
import { ref, watch, nextTick, onMounted } from 'vue'; // Import watch and nextTick
import { usePage } from '@inertiajs/vue3';
import BreadcrumbDefault from '@/Components/Breadcrumbs/BreadcrumbDefault.vue';
import { Inertia } from '@inertiajs/inertia';
import PageHeading from '@/Components/Global/PageHeading.vue';
import TextInput from '@/Components/TextInput.vue';
// Import default profile image
import defaultProfileImage from '@/assets/images/user/user-profile.jpeg';

const pageTitle = ref('Chat Support')

const { chatData, baseUrl, allAgents, agentType, authDetail, selectedAgent } = usePage().props;

const showInput = ref(true);

const selectedSlug = ref('');
const selectedSource = ref('Chatbot');
const selectedCompanyProfile = ref('');
const selectedCompanyUID = ref('');
const selectedCompanyName = ref('');
const inputText = ref(''); // Define a ref for input text
const messages = ref([]); // Define a ref for messages array
const status_fallback = ref(0);
const statusLoading = ref(false);

const searchQuery = ref('');
const searchResults = ref([]);

selectedSlug.value = agentType;

const messagesContainer = ref(null);
const scrollToBottom = () => {
    if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
};

// Helper function to get profile image URL
const getProfileImageUrl = (profilePicture) => {
    if (!profilePicture) return defaultProfileImage;
    return profilePicture.startsWith('http')
        ? profilePicture
        : `${baseUrl}/storage/${profilePicture}`;
};

onMounted(() => {
    // Check if the URL contains ?source=
    if (!window.location.search.includes('?source=')) {
        // If not, hide the input element
        showInput.value = false;
    } else {
        showInput.value = true;
    }

    if (Object.keys(chatData).length !== 0) {
        Object.values(chatData).forEach(item => {
            if (item.message.trim() !== '' && item.id == authDetail.id) {
                messages.value.push({ text: item.message, timestamp: item.timestamp, sender: 'user' });
            }
            if (item.message.trim() !== '' && item.id != authDetail.id) {
                messages.value.push({ text: item.message, timestamp: item.timestamp, sender: 'server' });
            }
            status_fallback.value = item.status_fallback;
        });
    }

    if (selectedAgent) {
        selectedSource.value = selectedAgent.chatbot_name;
        selectedCompanyProfile.value = selectedAgent.profilePicture;
        selectedCompanyUID.value = selectedAgent.userid;
        selectedCompanyName.value = selectedAgent.companyName;
    }


    nextTick(() => {
        scrollToBottom();
    });




});

const searchUser = () => {

    fetch(`/dtadmin/search-company-user?query=${searchQuery.value}`)
        .then(response => response.json())
        .then(data => {
            searchResults.value = data.users;
        })
        .catch(error => {
            console.error(error);
        });
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const formattedDate = `${date.getFullYear()}-${('0' + (date.getMonth() + 1)).slice(-2)}-${('0' + date.getDate()).slice(-2)} ${('0' + date.getHours()).slice(-2)}:${('0' + date.getMinutes()).slice(-2)}`;
    return formattedDate;
}

const timestamptoFormat = (timestamp) => {
    const date = new Date(timestamp * 1000); // Convert seconds to milliseconds
    const today = new Date();

    // Check if the date is today
    if (
        date.getDate() === today.getDate() &&
        date.getMonth() === today.getMonth() &&
        date.getFullYear() === today.getFullYear()
    ) {
        return 'Today';
    }

    // Check if the date is yesterday
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    if (
        date.getDate() === yesterday.getDate() &&
        date.getMonth() === yesterday.getMonth() &&
        date.getFullYear() === yesterday.getFullYear()
    ) {
        return 'Yesterday';
    }

    // Format as 'Y-m-d H:i'
    const formattedDate = date.toISOString().slice(0, 16).replace('T', ' ');
    return formattedDate;
};


const handleClick = (slug, user_id, company_userid) => {

    statusLoading.value = true;
    // Update the URL with the parameter without refreshing the page
    window.history.pushState(null, null, `?source=${slug}` + `&umid=${user_id}` + `&cmpid=${company_userid}`);

    // Set the selectedSlug to the clicked agent's slug
    selectedSlug.value = slug;

    // Find the selected agent by its slug
    const selectedAgent = allAgents.find(agent => agent.userid === company_userid);

    // Get the name of the selected agent
    const selectedAgentName = selectedAgent ? selectedAgent.name : '';

    selectedSource.value = selectedAgentName;
    selectedCompanyName.value = selectedAgent.company_name ? selectedAgent.company_name : '';
    selectedCompanyProfile.value = selectedAgent.profilePicture;
    selectedCompanyUID.value = selectedAgent.userid;

    showInput.value = true;

    // Make a Get request to your Laravel backend with the parameter in the URL
    axios.get(`/chatbot-json?source=${slug}` + `&umid=${user_id}` + `&cmpid=${company_userid}`)
        .then(response => {
            let chatDataNew = response.data;
            messages.value = [];
            if (Object.keys(chatDataNew).length !== 0) {
                Object.values(chatDataNew).forEach(item => {
                    if (item.message.trim() !== '' && item.id == authDetail.id) {
                        messages.value.push({ text: item.message, timestamp: item.timestamp, sender: 'user' });
                    }
                    if (item.message.trim() !== '' && item.id != authDetail.id) {
                        messages.value.push({ text: item.message, timestamp: item.timestamp, sender: 'server' });
                    }

                    status_fallback.value = item.status_fallback;
                    if (status_fallback.value == 1) {
                        showInput.value = false;
                    }
                });
            }

            statusLoading.value = false;

            // Directly show the last message in the messages container
            nextTick(() => {
                const messageContainer = document.getElementById('messages');
                const lastMessage = messageContainer.querySelector('#messages > div:nth-last-child(2)');
                if (lastMessage) {
                    lastMessage.scrollIntoView({ behavior: 'auto', block: 'end' });
                }
            });

            // setTimeout(function () {
            //     let lastMessage = document.querySelector('#messages > div:nth-last-child(2)');
            //     if (lastMessage) {
            //         lastMessage.scrollIntoView({ behavior: 'smooth' });

            //     }
            // }, 500);

            let sourceMessage = document.querySelector('#inputsource');
            if (sourceMessage) {
                sourceMessage.focus();
            }



        })
        .catch(error => {
            // Handle errors if the request fails
            console.error('Error:', error);
        });

    searchResults.value = [];
}


const sendMessage = async () => {
    try {
        showInput.value = false;

        if (inputText.value.trim() === '') {
            return false;
        }

        let botTyping = document.querySelector('.botTyping');
        botTyping.classList.remove('hidden');
        botTyping.scrollIntoView();

        setTimeout(() => {
            botTyping.scrollIntoView();
        }, 1000);

        // Push the user message
        if (inputText.value.trim() !== '') {
            let timecurrent = () => Math.floor(Date.now() / 1000);
            messages.value.push({ text: inputText.value, timestamp: timecurrent(), sender: 'user' });

            // Update the last message and time for the current agent when user sends a message
            const currentAgent = allAgents.find(agent => agent.slug === selectedSlug.value);
            if (currentAgent) {
                currentAgent.last_message = inputText.value;
                currentAgent.last_msg_time = timestamptoFormat(timecurrent());
            }
        }

        let inputMesage = inputText.value;
        inputText.value = '';

        let token = document.head.querySelector('meta[name="csrf-token"]').content;

        let requestBody = {
            status_fallback: status_fallback.value,
            message: inputMesage,
            slug: selectedSlug.value, // Pass the selectedSlug
            selected_company_userid: document.querySelector('input[name="selected_company_userid"]').value,
            type: document.querySelector('input[name="type"]').value // Pass the type field
        };



        const response = await fetch('/dtadmin/send-message', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': token,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
        });

        const responseData = await response.json();



        // Push the server response
        // if(responseData.message.trim() !== ''){
        //     messages.value.push({ text: responseData.message, sender: 'server' });
        // }


        setTimeout(function () {
            let lastMessage = document.querySelector('#messages > div:nth-last-child(2)');
            if (lastMessage) {
                lastMessage.scrollIntoView({ behavior: 'smooth' });
            }

            botTyping.classList.add('hidden');

            if (responseData.status_fallback == 1) {
                showInput.value = false;
                status_fallback.value = 1;
            } else {
                showInput.value = true;

            }

            if (responseData.message.trim() !== '') {
                let timecurrent = () => Math.floor(Date.now() / 1000);
                let timesampes = (responseData.timestamp) ? responseData.timestamp : timecurrent();
                messages.value.push({ text: responseData.message, timestamp: timesampes, sender: 'server' });

                // Update the last message and time for the current agent
                const currentAgent = allAgents.find(agent => agent.slug === selectedSlug.value);
                if (currentAgent) {
                    currentAgent.last_message = responseData.message;
                    currentAgent.last_msg_time = timestamptoFormat(timesampes);
                }
            }


            nextTick(() => {
                let sourceMessage = document.querySelector('#inputsource');
                if (sourceMessage) {
                    sourceMessage.focus();
                }
            });

        }, 500);




    } catch (error) {
        console.error('Error sending message:', error);
    }
};





let authUserid = authDetail.id;

Echo.private('agent-channel').listen('ChatAgentMessage', (e) => {

    let socketMessage = e.message;

    let sourceMessage2 = document.querySelector('#inputsource');
    if (sourceMessage2) {
        sourceMessage2.focus();
    }

    Object.values(socketMessage).forEach(item => {

        //console.log(item);

        if (item.user_id === authUserid) {
        } else {
            if (item.text.trim() !== '') {
                let uid = encodeToBase64(authUserid);
                if ((item.chat_source.trim() === selectedSlug.value.trim()) && (item.chat_uid.trim() == uid)) {

                    if (item.status_fallback == 0) {
                        showInput.value = true;
                        status_fallback.value = 0;
                    }
                    let timecurrent = () => Math.floor(Date.now() / 1000);
                    let timesampes = (item.timestamp) ? item.timestamp : timecurrent();

                    messages.value.push({
                        text: item.text,
                        timestamp: timesampes,
                        sender: 'server'
                    });

                    // Update the last message and time for the agent
                    const currentAgent = allAgents.find(agent => agent.slug === item.chat_source.trim());
                    if (currentAgent) {
                        currentAgent.last_message = item.text;
                        currentAgent.last_msg_time = timestamptoFormat(item.timestamp || Math.floor(Date.now() / 1000));
                    }
                }

                let lastMessage = document.querySelector('#messages > div:nth-last-child(2)');
                if (lastMessage) {
                    lastMessage.scrollIntoView({ behavior: 'smooth' });

                }


            }
        }

    });
})

const encodeToBase64 = (projectId) => {
    return btoa(projectId); // Using btoa() to encode to base64
}

const truncateMessage = (message) => {
    if (!message) return '';
    // Strip HTML tags for length calculation
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = message;
    const textContent = tempDiv.textContent || tempDiv.innerText;

    if (textContent.length > 10) {
        // For HTML content, try to preserve tags while truncating
        const truncated = message.substring(0, 10);
        // Check if we're cutting in the middle of an HTML tag
        const lastLtIndex = truncated.lastIndexOf('<');
        const lastGtIndex = truncated.lastIndexOf('>');

        if (lastLtIndex > lastGtIndex) {
            // We're in the middle of a tag, so truncate at the last complete tag
            return message.substring(0, lastLtIndex) + '...';
        }
        return truncated + '...';
    }
    return message;
}




</script>

<style>
/* Updated scrollbar styling */
#messages::-webkit-scrollbar {
    width: 4px;
    border-radius: 8px;
}

#messages::-webkit-scrollbar-thumb {
    background-color: rgba(203, 213, 225, 0.8);
    border-radius: 8px;
}

#messages::-webkit-scrollbar-track {
    background-color: rgba(241, 245, 249, 0.5);
    border-radius: 8px;
}

/* Add smooth transitions */
.message-transition {
    transition: all 0.3s ease;
}

/* Message bubble styling */
.message-bubble {
    position: relative;
    transition: transform 0.2s ease;
}

.message-bubble:hover {
    transform: translateY(-1px);
}


/* Typing indicator animation */
@keyframes typingAnimation {
    0% { opacity: 0.4; }
    50% { opacity: 1; }
    100% { opacity: 0.4; }
}
</style>



<template>

    <Head :title="pageTitle" />

    <AuthenticatedLayout>

        <!-- <PageHeading :title="pageTitle"></PageHeading> -->

        <div class="block md:flex h-[calc(100vh-60px)]">
            <!-- Sidebar - Companies List -->
            <div class="top-5 z-10 md:sticky bg-white shadow-sm p-6 border w-full md:w-1/4 h-[calc(100vh-60px)] overflow-y-auto scrollbar-thin scrollbar-track-gray-100">
                <h3 class="mb-5 font-semibold text-lg text-slate-800 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-cfp-500" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                    </svg>
                    Companies
                </h3>

                <!-- Search field with icon -->
                <div class="mb-5">
                    <div class="relative mt-2">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <TextInput
                            id="searchname"
                            name="searchname"
                            type="text"
                            placeholder="Search Companies..."
                            v-model="searchQuery"
                            @keyup="searchUser"
                            class="pl-10 pr-4 py-2 border-gray-300 focus:border-cfp-500 focus:ring-cfp-500 rounded-lg w-full"
                        />

                        <!-- Search Results Dropdown -->
                        <div class="z-10 absolute bg-white shadow-lg mt-2 rounded-lg w-full max-h-60 overflow-y-auto"
                            v-if="searchResults.length > 0" :class="{ 'border border-gray-200': searchResults.length > 0 }">
                            <div v-if="searchResults.length > 0">
                                <ul class="py-1">
                                    <li v-for="user in searchResults" :key="user.id"
                                        @click="handleClick(user.slug, encodeToBase64($page.props.auth.user.id), user.userid)"
                                        class="relative flex justify-between items-center hover:bg-gray-50 px-4 py-3 border-b border-gray-100 last:border-b-0 cursor-pointer transition-colors duration-150">
                                        <!-- Left Section -->
                                        <div class="flex items-center gap-3">
                                            <div class="flex-shrink-0 relative">
                                                <img :src="getProfileImageUrl(user.profilePicture)"
                                                    alt="User"
                                                    class="rounded-full w-10 h-10 object-cover border border-gray-200"
                                                />
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-slate-800 text-sm">{{ user.company_name }}</h4>
                                                <p class="text-slate-500 text-xs truncate max-w-[150px]" v-if="user.companyAdd">
                                                    {{ user.companyAdd }}
                                                </p>
                                            </div>
                                        </div>

                                        <!-- Right Section -->
                                        <div class="text-right flex flex-col items-end">
                                            <div class="text-gray-400 text-xs mb-1">{{ user.city }}</div>
                                            <div class="flex gap-2">
                                                <span v-if="user.chat_support == 'livechat' || user.chat_support == 'both'"
                                                    class="inline-flex items-center justify-center w-6 h-6 rounded-full bg-blue-50 text-blue-500">
                                                    <i class="fa fa-user-secret text-xs" aria-hidden="true"></i>
                                                </span>
                                                <span v-if="user.chat_support == 'chatbot' || user.chat_support == 'both'"
                                                    class="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-50 text-green-500">
                                                    <i class="fa fa-user-plus text-xs" aria-hidden="true"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Companies List -->
                <div class="space-y-1" id="agents">
                    <li v-for="(agent, index) in allAgents" :key="index"
                        @click="handleClick(agent.slug, encodeToBase64($page.props.auth.user.id), agent.userid)"
                        class="list-none">
                        <div v-if="agent && agent.last_message"
                            class="relative flex justify-between items-center hover:bg-gray-50 p-3 rounded-lg cursor-pointer transition-colors duration-150"
                            :class="{'bg-cfp-50': selectedSlug.value === agent.slug}">
                            <!-- Left Section -->
                            <div class="flex items-center gap-3">
                                <div class="relative flex-shrink-0">
                                    <img :src="getProfileImageUrl(agent.profilePicture)"
                                        alt="User"
                                        class="rounded-full w-10 h-10 object-cover border border-gray-200"
                                    />
<!--                                    <span class="absolute bottom-0 right-0 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></span>-->
                                </div>
                                <div class="min-w-0">
                                    <h4 class="font-medium text-slate-800 text-sm truncate">{{ agent.company_name }}</h4>
                                    <p class="text-slate-500 text-xs truncate max-w-[120px]">
                                        <span v-html="truncateMessage(agent.last_message)"></span>
                                    </p>
                                </div>
                            </div>

                            <!-- Right Section -->
                            <div class="text-right flex flex-col items-end">
                                <div class="text-gray-400 text-xs mb-1">{{ agent.last_msg_time }}</div>
                                <div class="flex gap-1">
                                    <span v-if="agent.chat_support == 'livechat' || agent.chat_support == 'both'"
                                        class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-blue-50 text-blue-500">
                                        <i class="fa fa-user-secret text-xs" aria-hidden="true"></i>
                                    </span>
                                    <span v-if="agent.chat_support == 'chatbot' || agent.chat_support == 'both'"
                                        class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-green-50 text-green-500">
                                        <i class="fa fa-user-plus text-xs" aria-hidden="true"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </li>
                </div>
            </div>

            <!-- Main Chat Area -->
            <div class="flex-1 mt-4 md:mt-0">
                <div v-if="selectedSource != 'Chatbot'"
                    class="flex flex-col h-[calc(100vh-60px)] bg-white border overflow-hidden shadow-sm">
                    <!-- Chat Header -->
                    <div class="flex items-center justify-between p-4 border-b bg-white">
                        <div class="flex items-center">
                            <div class="relative mr-3">
                                <img :src="getProfileImageUrl(selectedCompanyProfile)"
                                    alt="User"
                                    class="w-10 h-10 rounded-full object-cover border border-gray-200"
                                />
<!--                                <span class="absolute bottom-0 right-0 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></span>-->
                            </div>
                            <div>
                                <h5 class="font-medium text-slate-900">
                                    <span class="source_name">{{ selectedCompanyName }}</span>
                                </h5>
<!--                                <p class="text-slate-500 text-xs">Online</p>-->
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
<!--                            <button class="p-2 rounded-full hover:bg-gray-100 transition-colors duration-150">-->
<!--                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">-->
<!--                                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />-->
<!--                                </svg>-->
<!--                            </button>-->
<!--                            <button class="p-2 rounded-full hover:bg-gray-100 transition-colors duration-150">-->
<!--                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">-->
<!--                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />-->
<!--                                </svg>-->
<!--                            </button>-->
                        </div>
                    </div>

                    <!-- Chat Messages Area -->
                    <div class="flex-1 flex flex-col overflow-hidden bg-gray-50">
                        <!-- Messages Container -->
                        <div id="messages"
                            ref="messagesContainer"
                            class="flex-1 overflow-y-auto p-4 space-y-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">

                            <!-- Date Separator -->
<!--                            <div class="flex justify-center">-->
<!--                                <div class="bg-gray-200 text-gray-600 text-xs px-3 py-1 rounded-full">Today</div>-->
<!--                            </div>-->

                            <!-- Message Bubbles -->
                            <div v-for="(message, index) in messages"
                                :key="index"
                                :class="message.sender + '_message'"
                                class="w-full message-transition">
                                <div class="flex"
                                    :class="message.sender === 'user' ? 'justify-end' : 'justify-start'">

                                    <!-- Bot Avatar (only for server messages) -->
                                    <div v-if="message.sender === 'server'" class="flex-shrink-0 mr-2">
                                        <img :src="getProfileImageUrl(selectedCompanyProfile)"
                                            alt="Bot"
                                            class="w-8 h-8 rounded-full object-cover border border-gray-200"
                                        />
                                    </div>

                                    <!-- Message Content -->
                                    <div :class="[
                                        'message-bubble max-w-[85%] sm:max-w-[75%] lg:max-w-[65%] rounded-2xl px-4 py-2 shadow-sm',
                                        message.sender === 'user'
                                            ? 'bg-cfp-500 text-white rounded-tr-none'
                                            : 'bg-white text-gray-800 rounded-tl-none'
                                    ]">
                                        <div class="text-sm sm:text-base break-words">
                                            <span v-html="message.text"></span>
                                        </div>
                                        <div class="text-[10px] mt-1 opacity-75 text-right">
                                            {{ timestamptoFormat(message.timestamp) }}
                                        </div>
                                    </div>

                                    <!-- User Avatar (only for user messages) -->
                                    <div v-if="message.sender === 'user'" class="flex-shrink-0 ml-2">
                                        <img :src="authDetail.profilePicture
                                            ? baseUrl + '/storage/' + authDetail.profilePicture
                                            : defaultProfileImage"
                                            alt="User"
                                            class="w-8 h-8 rounded-full object-cover border border-gray-200"
                                        />
                                    </div>
                                </div>
                            </div>

                            <!-- Typing Indicator -->
                            <div class="hidden botTyping">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 mr-2">
                                        <img :src="getProfileImageUrl(selectedCompanyProfile)"
                                            alt="Bot"
                                            class="w-8 h-8 rounded-full object-cover border border-gray-200"
                                        />
                                    </div>
                                    <div class="flex space-x-1 items-center bg-white rounded-xl px-4 py-2 shadow-sm">
                                        <span class="w-2 h-2 rounded-full bg-gray-400 animate-pulse" style="animation-delay: 0ms; animation-duration: 1.2s;"></span>
                                        <span class="w-2 h-2 rounded-full bg-gray-400 animate-pulse" style="animation-delay: 200ms; animation-duration: 1.2s;"></span>
                                        <span class="w-2 h-2 rounded-full bg-gray-400 animate-pulse" style="animation-delay: 400ms; animation-duration: 1.2s;"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Input Area -->
                        <div class="border-t bg-white p-4 w-full">
                            <div class="relative w-full mx-auto">
                                <input type="hidden" name="selected_company_userid" :value="selectedCompanyUID">
                                <input type="hidden" v-model="status_fallback">
                                <input type="hidden" name="type" :value="selectedSlug">

                                <div class="flex items-center gap-2 w-full">
                                    <div class="flex-1 relative w-full">
                                        <input ref="textInput"
                                            v-model="inputText"
                                            @keyup.enter="sendMessage"
                                            type="text"
                                            placeholder="Type your message..."
                                            autocomplete="off"
                                            autofocus="true"
                                            id="inputsource"
                                            :disabled="!showInput"
                                            class="w-full px-4 py-3 rounded-full border border-gray-300 focus:border-cfp-500 focus:ring-1 focus:ring-cfp-500 text-gray-600 text-sm sm:text-base placeholder-gray-400"
                                        />
                                        <button @click="sendMessage"
                                            type="button"
                                            :disabled="!showInput || inputText.trim() === ''"
                                            class="absolute right-2 top-1/2 transform -translate-y-1/2 inline-flex items-center justify-center w-10 h-10 rounded-full bg-cfp-500 hover:bg-cfp-600 transition-colors duration-150 text-white disabled:opacity-50 disabled:cursor-not-allowed">
                                            <span class="sr-only">Send message</span>
                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                class="w-5 h-5">
                                                <path stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M6 12L3.269 3.125A59.769 59.769 0 0121.485 12 59.768 59.768 0 013.27 20.875L5.999 12zm0 0h7.5"
                                                />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <!-- Message when chat is disabled remains the same -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Empty State -->
                <div v-else class="h-[calc(100vh-60px)] flex flex-col items-center justify-center bg-white p-6 border rounded-lg shadow-sm">
                    <div class="text-center max-w-md">
                        <div class="mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-medium text-gray-800 mb-2">Start a conversation</h3>
                        <p class="text-gray-600 mb-6">Select a company from the list to begin chatting</p>

                    </div>
                </div>
            </div>
        </div>







    </AuthenticatedLayout>
</template>
