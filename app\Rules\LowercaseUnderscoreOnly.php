<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class LowercaseUnderscoreOnly implements Rule
{
    public function passes($attribute, $value)
    {
        // Check if the string contains only lowercase letters, numbers and underscores
        return preg_match('/^[a-z0-9_]+$/', $value) === 1;
    }

    public function message()
    {
        return 'The :attribute must contain only lowercase letters, numbers, and underscores.';
    }
}
