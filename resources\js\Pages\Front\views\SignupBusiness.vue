<script setup>
import feather from 'feather-icons';
import RegisterFormBusiness from '../components/login/RegisterFormBusiness.vue';
import App from '../App.vue';
import { ref } from 'vue';
import { Head, usePage } from '@inertiajs/vue3';
const pageTitle = ref('Business Register');
feather.replace();

const {Countries, Categories} = usePage().props;

</script>

<template>

	<Head :title="pageTitle" />
	<App>

		<div class="container mx-auto flex flex-col-reverse md:flex-row py-5 md:py-10 md:mt-10">
			<!-- Register form -->
			<RegisterFormBusiness :Countries="Countries" :Categories="Categories"/>

			<!-- Contact details -->
		</div>

	</App>

</template>