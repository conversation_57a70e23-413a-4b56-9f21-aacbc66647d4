<?php

namespace App\Http\Controllers\dtadmin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Company;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class CategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {

        $categoryList = Category::orderBy('catId', 'desc')->paginate(env('PAGE_LIMIT'));

        return Inertia::render('Category/List', ['category' => $categoryList]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Category/Create', []);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $customMessages = [
            'required' => 'The :attribute field is required.',
            'mimes'    => 'The :attribute must be a file of type: jpg, png, jpeg, or gif.',
        ];

        $commonRules = [
            'catName'    => 'required',
            'catPicture' => 'required|mimes:jpg,png,jpeg,gif',
        ];

        $validatedData = $request->validate($commonRules, $customMessages);

        $itemQry = Category::where('catName', $request->catName)->first();

        if ($itemQry) {

            return Redirect::route('category.create')->with([
                'status'  => false,
                'message' => 'Category already exist',
                'class'   => 'text-sm text-red-700 ',
            ]);

        } else {

            $catName    = $request->input('catName');
            $catPicture = $request->file('catPicture');

            // Handle file upload
            if ($catPicture) {
                $path = $catPicture->store('category_pictures', 'public'); // Save file to 'public/category_pictures'
            } else {
                $path = ''; // Handle cases where no file is uploaded
            }

            // Create and save category
            $category             = new Category;
            $category->catName    = $catName;
            $category->catPicture = $path;
            $category->save();

            return Redirect::route('category.create')->with([
                'status'  => true,
                'message' => 'The category has been saved successfully.',
                'class'   => 'text-sm text-green-400 ',
            ]);

        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $itemDetail = Category::find($id);
        if (! $itemDetail) {
            return Redirect::route('category.list')->with([
                'status'  => false,
                'message' => 'Invalid request',
                'class'   => 'text-sm text-red-700 ',
            ]);
        }

        return Inertia::render('Category/Edit', ['itemDetail' => $itemDetail]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {

        $customMessages = [
            'required' => 'The :attribute field is required.',
            'unique'   => 'The :attribute has already been taken.',
        ];

        $commonRules = [
            'catName' => 'required|unique:category,catName,'.$request->catId.',catId',
        ];

        $validatedData = $request->validate($commonRules, $customMessages);

        $saveData = Category::find($request->catId);

        if ($saveData) {
        } else {

            return Redirect::back()->with([
                'status'  => false,
                'message' => 'Invalid request to add deal',
                'class'   => 'text-sm text-red-700 ',
            ]);
        }

        $catpicPath = $saveData->catPicture;
        if ($request->hasFile('catPicture')) {

            $customMessages = [
                'required' => 'The :attribute field is required.',
                'mimes'    => 'The :attribute must be a file of type: jpg, png, jpeg, or gif.',
            ];

            $commonRules = [
                'catPicture' => 'required|mimes:jpg,png,jpeg,gif',
            ];

            $validatedData = $request->validate($commonRules, $customMessages);

            try {

                $catPicture = $request->file('catPicture');

                if ($catPicture) {
                    $catpicPath = $catPicture->store('category_pictures', 'public'); // Save file to 'public/category_pictures'
                }

            } catch (Exception $ex) {

                return Redirect::back()->with([
                    'status'  => false,
                    'message' => $ex->getMessage(),
                    'class'   => 'text-sm text-red-700 ',
                ]);
            }

            if ($saveData->deal_file != '') {
                Storage::disk('public')->delete($saveData->deal_file);
            }

        }

        if ($saveData->catName != 'Other') {
            $saveData->catName = $request->catName;
        }
        $saveData->catPicture = $catpicPath;
        $saveData->save();

        return Redirect::back()->with([
            'status'   => true,
            'message'  => 'The category has been saved successfully.',
            'class'    => 'text-sm text-green-400 ',
            'imageurl' => $catpicPath,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $itemDeta = Category::where('catId', $id)->first();
        if ($itemDeta) {

            if (Company::where('catId', $itemDeta->catId)->first()) {

                return response()->json(['status' => false, 'message' => 'Can not delete this Category it\'s used on some company profile']);

            } else {

                if ($itemDeta->catPicture != '') {
                    Storage::disk('public')->delete($itemDeta->catPicture);
                }

                if ($itemDeta->catName == 'Other') {
                    return response()->json(['status' => false, 'message' => 'You can not delete Other category']);
                } else {
                    $itemDeta->delete();
                }

                return response()->json(['status' => true, 'message' => 'Category deleted successfully']);

            }

        } else {
            return response()->json(['status' => false, 'message' => 'Category not found']);
        }

    }
}
