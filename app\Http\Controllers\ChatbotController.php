<?php

namespace App\Http\Controllers;

use App\Events\ChatAgentMessage;
use App\Models\BotConversation;
use App\Models\Bots;
use App\Models\Company;
use App\Models\FirebaseFunction;
use App\Models\GuestQuery;
use App\Models\Notifications;
use App\Models\OAuthAuthorizationCode;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Google\Auth\Credentials\ServiceAccountCredentials;
use Google\Cloud\Dialogflow\V2\QueryInput;
use Google\Cloud\Dialogflow\V2\SessionsClient;
use Google\Cloud\Dialogflow\V2\TextInput;
use Google\Service\Firebaseappcheck\Resource\OauthClients;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use Kreait\Firebase\Contract\Database;

class ChatbotController extends Controller
{
    protected $database;

    protected $tableName;

    public function __construct(Database $database)
    {
        $this->database  = $database;
        $this->tableName = 'chats';
    }


    public function getjavachatresponse(Request $request){
        
        $currentUser = $request->userId;
        $companyuid = $request->companyId;
        $userMessage = $request->text;
        
        $fireBaeFun     = new FirebaseFunction($this->database);
        $result         = $fireBaeFun->javaChatAgent($userMessage, $currentUser, $companyuid, 0);
        $statusResponse = $result->getData();
        return $statusResponse;
        
    }

    // public function index()
    // {

    //     $authDetail = Auth::user();
    //     $ownerID = $authDetail->parent_id;
    //     $botConverList = BotConversation::getBotConversaction($ownerID);

    //     $chatData = [];
    //     $userData = [];
    //     $agentType = '';
    //     $sourceID = '';
    //     $activeTab = 'active';
    //     $umid = '';

    //     if(isset($_REQUEST['source']) && isset($_REQUEST['umid'])){

    //         $agentType = base64_decode($_REQUEST['source']);
    //         $userId = base64_decode($_REQUEST['umid']);
    //         $botResult = new FirebaseFunction($this->database);
    //         $chatData = $botResult->getBotConversation($userId,$agentType);
    //         $sourceID = $_REQUEST['source'];
    //         $umid = $_REQUEST['umid'];
    //         $userData = User::find($userId);
    //         $activeTab = (isset($_REQUEST['tab']) && trim($_REQUEST['tab']) != '') ? $_REQUEST['tab'] : $activeTab;
    //     }
    //     Notifications::updateStatus('chats');
    //     return Inertia::render('Chats', ['chatData'=>$chatData,'userData'=>$userData,
    //     'sourceID'=>$sourceID,'umid'=>$umid,'botConverList'=>$botConverList,
    //     'activeTab'=>$activeTab,
    //     'authDetail'=>$authDetail]);
    // }

    public function index()
    {
        $authDetail = Auth::user();
        $ownerID    = $authDetail->parent_id;
        if ($ownerID) {
        } else {
            $ownerID = Auth::id();
        }
        $botConverList = BotConversation::getBotConversaction($ownerID);

        $botConverListArray = [];
        // Enhance botConverList with last messages
        foreach ($botConverList as $conversation) {

            if (isset($conversation['company_id']) && isset($conversation['user_id']) && isset($conversation['company_user_id'])) {

                $agentType           = $conversation['company_user_id'].'_'.$conversation['company_id'];
                $lastMessageResponse = $this->getLastMessage($conversation['user_id'], $agentType);

                if ($lastMessageResponse->status() === 200) {
                    $lastMessageData                = $lastMessageResponse->getData(true);
                    $conversation['last_message']   = $lastMessageData['message']   ?? '';
                    $conversation['last_timestamp'] = $lastMessageData['timestamp'] ?? '';
                } else {
                    $conversation['last_message']   = '';
                    $conversation['last_timestamp'] = '';
                }
            }
        }

        $chatData  = [];
        $userData  = [];
        $agentType = '';
        $sourceID  = '';
        $activeTab = 'active';
        $umid      = '';

        if (isset($_REQUEST['source']) && isset($_REQUEST['umid'])) {
            $agentType = $_REQUEST['source'];
            $userId    = base64_decode($_REQUEST['umid']);
            $botResult = new FirebaseFunction($this->database);
            $chatData  = $botResult->getBotConversation($userId, $agentType);
            $sourceID  = $_REQUEST['source'];
            $umid      = $_REQUEST['umid'];
            $userData  = User::find($userId);
            $activeTab = (isset($_REQUEST['tab']) && trim($_REQUEST['tab']) != '') ? $_REQUEST['tab'] : $activeTab;

            $route = 'dtadmin/chats?source='.$_REQUEST['source'].'&umid='.$_REQUEST['umid'];
            Notifications::removeNotification($route,'chats');


        }

        
        $title = 'Chats';

        return Inertia::render('Chats', [
            'chatData'      => $chatData,
            'userData'      => $userData,
            'sourceID'      => $sourceID,
            'umid'          => $umid,
            'botConverList' => $botConverList,
            'activeTab'     => $activeTab,
            'authDetail'    => $authDetail,
            'title'         => $title,
        ]);
    }

    public function getUserDetail(Request $request)
    {
        $userId    = $request->input('userid');
        $user      = User::find($userId);
        $activeTab = $request->input('activeTab');

        return response()->json(['userData' => $user, 'activeTab' => $activeTab]);
    }

    public function searchUser(Request $request)
    {
        try {

            $firebaseFunction = new FirebaseFunction($this->database);

            $typesFromDB = Bots::where('user_id', Auth::id())->pluck('chatbot_project_id')->toArray();
            $userIds     = $firebaseFunction->getUserIdsByTypes($typesFromDB);
            $otehrUsers  = BotConversation::where('company_user_id', Auth::id())->pluck('user_id')->toArray();

            $uniqueUserIds = array_unique(array_merge($userIds, $otehrUsers));

            $filterData = $request->filterData;
            // Fetch all users with the required profile type and verification status
            $users = User::where('profileType', 'user')
                ->whereIn('id', $uniqueUserIds)
                ->where(function ($query) use ($filterData) {
                    $query->where('name', 'like', '%'.$filterData.'%')
                        ->orWhere('email', 'like', '%'.$filterData.'%')
                        ->orWhere('phone', 'like', '%'.$filterData.'%');
                })->orderBy('id', 'desc')->get();

            if ($users->isEmpty()) {
                return response()->json(['status' => false, 'message' => 'No verified users found', 'data' => []], 404);
            }

            // Prepare the response data for each user
            $userData = $users->map(function ($user) {
                return User::getUserDetail($user);
            });

            // Return the response with user data
            return response()->json([
                'status'  => true,
                'message' => 'Users retrieved successfully',
                'data'    => $userData,
            ], 200);

        } catch (Exception $e) {
            // Return a generic error message
            return response()->json(['status' => false, 'message' => 'Failed to load users', 'error' => $e->getMessage()], 500);
        }
    }

    public function getLastMessage($userId, $agentType)
    {

        $chatData     = [];
        $path         = $this->tableName.'/user_id_'.$userId.'/agent_'.$agentType;
        $userChatsRef = $this->database->getReference($path);
        $chats        = $userChatsRef->getValue();

        if ($chats) {
            $chats = array_filter($chats, function ($chat) {
                return is_array($chat) && isset($chat['timestamp']);
            });

            // Get the last message
            $lastMessage = end($chats);
            if ($lastMessage) {
                return response()->json([
                    'id'        => $lastMessage['id'],
                    'message'   => $lastMessage['message'],
                    'timestamp' => $lastMessage['timestamp'],
                ]);
            }
        }

        return response()->json(['message' => 'No chats found'], 404);
    }

    public function botConversation()
    {
        $authDetail = Auth::user();
        $ownerID    = $authDetail->parent_id;
        if ($ownerID) {
        } else {
            $ownerID = Auth::id();
        }
        $botConverList = BotConversation::getBotConversaction($ownerID);

        return json_encode(['botConverList' => $botConverList]);
    }

    public function chatbotJson()
    {
        $chatData = [];

        if (isset($_REQUEST['source']) && isset($_REQUEST['umid']) && trim($_REQUEST['source']) != '') {

            $agentType = $_REQUEST['source'];
            $userId    = base64_decode($_REQUEST['umid']);

            $botResult = new FirebaseFunction($this->database);
            $chatData  = $botResult->getBotConversation($userId, $agentType);

        }

        if (isset($_REQUEST['cmpid']) && isset($_REQUEST['umid']) && trim($_REQUEST['cmpid']) != '') {

            $agentType = $_REQUEST['source'];
            $userId    = base64_decode($_REQUEST['umid']);
            $botResult = new FirebaseFunction($this->database);
            $chatData  = $botResult->getBotConversation($userId, $agentType);

        }

        return $chatData;
    }

    public function chatbot()
    {

        $chatData  = [];
        $agentType = '';

        if (isset($_REQUEST['source']) && isset($_REQUEST['umid']) && $_REQUEST['source'] != '') {
            $agentType = $_REQUEST['source'];
            $userId    = base64_decode($_REQUEST['umid']);
            $botResult = new FirebaseFunction($this->database);
            $chatData  = $botResult->getBotConversation($userId, $agentType);

        }
        $baseUrl = url('/');

        $dataList = Company::join('users', 'users.id', '=', 'company.userId')
            ->select(
                'company.id as company_id',
                'company.userId',
                'company.companyName',
                'company.chatSupport',
                'users.profilePicture'
            )
            ->get();

        $allAgents      = [];
        $selecteduserid = Auth::id(); // Moved outside loop since it's constant

        foreach ($dataList as $detail) {
            $selecedagentType = $detail->userId.'_'.$detail->company_id;

            $resultData = $this->getLastMessage($selecteduserid, $selecedagentType);

            $lastMessage   = '';
            $timestampReal = '';

            if ($resultData->status() === 200) {
                $data        = $resultData->getData(true);
                $lastMessage = $data['message'] ?? '';

                if (! empty($data['timestamp'])) {
                    $timestamp     = Carbon::createFromTimestamp($data['timestamp']);
                    $timestampReal = match (true) {
                        $timestamp->isToday()     => 'Today',
                        $timestamp->isYesterday() => 'Yesterday',
                        default                   => $timestamp->format('Y-m-d H:i')
                    };
                }
            }

            $allAgents[] = [
                'userid'         => $detail->userId,
                'name'           => $detail->companyName,
                'company_name'   => $detail->companyName,
                'chat_support'   => $detail->chatSupport,
                'profilePicture' => $detail->profilePicture,
                'last_message'   => $lastMessage,
                'last_msg_time'  => $timestampReal,
                'slug'           => $selecedagentType,
            ];
        }

        $authDetail = Auth::user();

        $selectedAgent = '';

        if (isset($_REQUEST['cmpid']) && trim($_REQUEST['cmpid']) != '') {
            $companyUid    = $_REQUEST['cmpid'];
            $selectedAgent = Company::join('users', 'users.id', 'company.userId')
                ->select('company.*', 'users.profilePicture')
                ->where('users.id', $companyUid)
                ->first();

                

           $route = 'dtadmin/chatbot?source='.$_REQUEST['source'].'&umid='.$_REQUEST['umid'].'&cmpid='.$companyUid;
           Notifications::removeNotification($route,'chatbot');

        }
        $title = 'Chat Support';

        return Inertia::render('Chatbot', [
            'selectedAgent' => $selectedAgent,
            'chatData'      => $chatData,
            'baseUrl'       => $baseUrl,
            'agentType'     => $agentType,
            'allAgents'     => $allAgents,
            'authDetail'    => $authDetail,
            'title'         => $title,
        ]);
    }

    //    public function chatbot()
    //    {
    //
    //        $chatData  = [];
    //        $agentType = '';
    //
    //        if (isset($_REQUEST['source']) && isset($_REQUEST['umid']) && $_REQUEST['source'] != '') {
    //            $agentType = base64_decode($_REQUEST['source']);
    //            $userId    = base64_decode($_REQUEST['umid']);
    //            $botResult = new FirebaseFunction($this->database);
    //            $chatData  = $botResult->getBotConversation($userId, $agentType);
    //        }
    //        $baseUrl = url('/');
    //
    //        // $dataList = Bots::join('company', 'company.id', '=', 'bots.company_id')
    //        //     ->join('users', 'users.id', 'bots.user_id')
    //        //     ->select('bots.chatbot_name', 'bots.chatbot_project_id', 'company.companyName', 'company.chatSupport', 'users.profilePicture')->get();
    //
    //        $dataList = Company::leftJoin('bots', 'company.id', '=', 'bots.company_id')
    //            ->join('users', 'users.id', '=', 'company.userId')
    //            ->select('bots.chatbot_name', 'bots.chatbot_project_id', 'company.companyName', 'company.chatSupport', 'users.profilePicture', 'company.userId', 'company.id as company_id')->get();
    //
    //        $allAgents = [];
    //        foreach ($dataList as $detadetail) {
    //            if ($detadetail->chatbot_project_id) {
    //                $slug             = base64_encode($detadetail->chatbot_project_id);
    //                $selecedagentType = base64_decode($slug);
    //            } else {
    //
    //                $companyDetail = Company::where('id', $detadetail->company_id)->first();
    //                $slug          = $selecedagentType = $companyDetail->userId.'_'.$companyDetail->id;
    //
    //            }
    //
    //            $selecteduserid = Auth::id();
    //
    //            $resultData = $this->getLastMessage($selecteduserid, $selecedagentType);
    //
    //            $lastMessage   = '';
    //            $timestampReal = '';
    //            if ($resultData->status() === 200) {
    //                $data = $resultData->getData(true);
    //
    //                $lastMessage   = $data['message']   ?? '';
    //                $timestapmData = $data['timestamp'] ?? '';
    //
    //                if ($timestapmData != '') {
    //
    //                    $timestamp     = Carbon::createFromTimestamp($timestapmData);
    //                    $timestampReal = $timestamp->isToday()
    //                        ? 'Today'
    //                        : ($timestamp->isYesterday() ? 'Yesterday' : $timestamp->format('Y-m-d H:i'));
    //
    //                }
    //
    //            }
    //
    //            $allAgents[] = [
    //                'userid'         => $detadetail->userId,
    //                'name'           => $detadetail->chatbot_name,
    //                'company_name'   => $detadetail->companyName,
    //                'chat_support'   => $detadetail->chatSupport,
    //                'profilePicture' => $detadetail->profilePicture,
    //                'last_message'   => $lastMessage,
    //                'last_msg_time'  => $timestampReal,
    //                'slug'           => $slug,
    //            ];
    //
    //        }
    //
    //        $authDetail = Auth::user();
    //        $agentType  = base64_encode($agentType);
    //
    //        $selectedAgent = '';
    //        if ($agentType != '') {
    //            $selectedAgent = base64_decode($agentType);
    //            $selectedAgent = Bots::join('users', 'users.id', 'bots.user_id')
    //                ->join('company', 'company.id', '=', 'bots.company_id')
    //                ->select('bots.*', 'users.profilePicture', 'company.companyName')
    //                ->where('bots.chatbot_project_id', $selectedAgent)
    //                ->first();
    //        }
    //
    //        if (isset($_REQUEST['source']) && isset($_REQUEST['umid']) && isset($_REQUEST['cmpid'])) {
    //
    //            if (trim($_REQUEST['source']) == '' && trim($_REQUEST['cmpid']) != '') {
    //
    //                $userId     = base64_decode($_REQUEST['umid']);
    //                $companyUid = $_REQUEST['cmpid'];
    //
    //                $selectedAgent = Company::join('users', 'users.id', 'company.userId')
    //                    ->select('company.*', 'users.profilePicture')
    //                    ->where('users.id', $companyUid)
    //                    ->first();
    //            }
    //
    //        }
    //
    //        $title = 'Chat Support';
    //
    //        return Inertia::render('Chatbot', ['selectedAgent' => $selectedAgent, 'chatData' => $chatData, 'baseUrl' => $baseUrl,
    //            'agentType'                                    => $agentType, 'allAgents' => $allAgents, 'authDetail' => $authDetail,
    //            'title'                                        => $title,
    //        ]);
    //    }

    public function searchcompanyUser(Request $request)
    {
        $search = $request->input('query');

        $user = Auth::user();

        if (trim($search) != '') {

            $dataList = Company::join('users', 'users.id', '=', 'company.userId')
                ->select('company.companyName', 'company.userId',
                    'company.chatSupport', 'users.profilePicture', 'users.name', 'company.companyAdd', 'company.city', 'company.state', 'company.zipCode',
                    'company.id as company_id'
                )
                ->where('users.id', '<>', Auth::id())
                ->where(function ($query) use ($search) {
                    $query->where('users.name', 'like', "%$search%")
                        ->orWhere('company.companyName', 'like', "%$search%")
                        ->orWhere('company.companyAdd', 'like', "%$search%")
                        ->orWhere('company.city', 'like', "%$search%")
                        ->orWhere('company.state', 'like', "%$search%")
                        ->orWhere('company.zipCode', 'like', "%$search%")
                        ->orWhere('company.websiteUrl', 'like', "%$search%")
                        ->orWhere('users.email', 'like', "%$search%")
                        ->orWhere('users.phone', 'like', "%$search%");
                })->get();

            $allAgents = [];
            foreach ($dataList as $detadetail) {
                $selecteduserid   = Auth::id();
                $selecedagentType = $detadetail->userId.'_'.$detadetail->company_id;

                $allAgents[] = [
                    'userid'         => $detadetail->userId,
                    'company_id'     => $detadetail->company_id,
                    'name'           => $detadetail->chatbot_name,
                    'company_name'   => ($detadetail->companyName && $detadetail->companyName != '') ? $detadetail->companyName : $detadetail->name,
                    'chat_support'   => $detadetail->chatSupport,
                    'profilePicture' => $detadetail->profilePicture,
                    'companyAdd'     => $detadetail->companyAdd,
                    'last_message'   => '',
                    'last_msg_time'  => '',
                    'slug'           => $selecedagentType,
                ];

            }

            if (count($allAgents) > 0) {
                return response()->json(['users' => $allAgents]);
            } else {
                return response()->json(['users' => []]);
            }

        } else {
            return response()->json(['users' => []]);
        }
    }

    public function sendJavaGuestMessage(Request $request)
    {
        $checkCompany = Company::find($request->company_id);
        if ($checkCompany) {
        } else {
            return response()->json(['message' => "You need to <a href='".url('login')."' style='text-decoration:underline; font-weight:bold'>Login</a> first to chat with Live agent.", 'status_fallback' => 1]);
        }

        $companyID       = $request->company_id;
        $userMessage     = $request->message;
        $status_fallback = $request->status_fallback;

        $companyuid = Company::where('id', $companyID)->value('userId');

        $baseURl        = env('BOTBASE_URL');
        $queryparameter = '?userId=0&companyId='.$companyuid.'&text='.urlencode($userMessage); // Encode the user message
        $finalUrl       = $baseURl.$queryparameter;

        try {
            $client   = new Client;
            $response = $client->get($finalUrl);

            if ($response->getStatusCode() == 200) {
                $result          = $response->getBody()->getContents();
                $data            = json_decode($result, true);
                $receiverMessage = $data['message'] ?? 'No message received.';

                return response()->json(['message' => $receiverMessage, 'status_fallback' => $status_fallback, 'status_ticket' => 0]);

            } else {
                // Handle non-200 status codes (e.g., log an error, return a default message)
                Log::error('javaChatAgent: HTTP status code '.$response->getStatusCode());

                return response()->json(['message' => 'Error retrieving data from the chatbot'], 500);
            }
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            // Handle network errors or other exceptions
            Log::error('javaChatAgent: Request exception: '.$e->getMessage());

            return response()->json(['message' => 'Failed to connect to the chatbot'], 500);
        } catch (Exception $e) {
            Log::error('javaChatAgent: General exception: '.$e->getMessage());

            return response()->json(['message' => 'An unexpected error occurred.'], 500);
        }

        // $javaApiURL = 'https://bot.chatfil.com/api/ChatBot?companyId='.$checkCompany->userId.'&text='.$request->message;

        // try {
        //     $response = Http::get($javaApiURL);

        //     if ($response->successful()) {
        //         $responsedata = $response->body(); // Get raw response data
        //     } else {
        //         $responsedata = 'Error fetching response from chat server.';
        //     }
        // } catch (\Exception $e) {
        //     $responsedata = 'Error: '.$e->getMessage();
        // }

        // return response()->json(['message' => $responsedata, 'status_fallback' => 0]);

    }

    public function sendGuestMessage(Request $request)
    {

        $types = base64_decode($request->type);

        $checkCompany = Company::find($request->company_id);
        if ($checkCompany && $checkCompany->chatSupport == 'livechat') {

            return response()->json(['message' => "You need to <a href='".url('login')."' style='text-decoration:underline; font-weight:bold'>Login</a> first to chat with Live agent.", 'status_fallback' => 1]);

        }

        $agentDetail = Bots::where('chatbot_project_id', $types)->first();
        if ($agentDetail) {

            if ($request->status_fallback == 2) {
                // Step 1: Validate full name
                $validator = Validator::make($request->all(), [
                    'message' => 'required|string|max:255',
                ], [
                    'message.required' => 'Full name is required.',
                    'message.string'   => 'Full name must be a valid string.',
                    'message.max'      => 'Full name should not exceed 255 characters.',
                ]);

                if ($validator->fails()) {
                    return response()->json(['message' => $validator->errors()->first(), 'status_fallback' => 2], 400);
                }

                // Store full name in session
                Session::put('query.full_name', $request->message);

                $queryForm = 'What is your email id?';

                return response()->json(['message' => $queryForm, 'status_fallback' => 3]);
            }

            if ($request->status_fallback == 3) {
                // Step 2: Validate email
                $validator = Validator::make($request->all(), [
                    'message' => 'required|email',
                ], [
                    'message.required' => 'Email is required.',
                    'message.email'    => 'Please provide a valid email address.',
                ]);

                if ($validator->fails()) {
                    return response()->json(['message' => $validator->errors()->first(), 'status_fallback' => 3], 400);
                }

                // Store email in session
                Session::put('query.email', $request->message);

                $queryForm = 'Please provide your query';

                return response()->json(['message' => $queryForm, 'status_fallback' => 4]);
            }

            if ($request->status_fallback == 4) {
                // Step 3: Validate query
                $validator = Validator::make($request->all(), [
                    'message' => 'required|string|max:1000',
                ], [
                    'message.required' => 'Query is required.',
                    'message.string'   => 'Query must be a valid string.',
                    'message.max'      => 'Query should not exceed 1000 characters.',
                ]);

                if ($validator->fails()) {
                    return response()->json(['message' => $validator->errors()->first(), 'status_fallback' => 4], 400);
                }

                // Store query in session
                Session::put('query.full_query', $request->message);

                // Retrieve all data from session
                $fullName  = Session::get('query.full_name');
                $email     = Session::get('query.email');
                $fullQuery = Session::get('query.full_query');

                // Store all data in the database
                $query             = new GuestQuery;
                $query->company_id = $agentDetail->company_id;
                $query->full_name  = $fullName;
                $query->user_email = $email;
                $query->user_query = $fullQuery;
                $query->save();

                // Clear session after storing
                Session::forget('query');

                // Return the final response
                $queryForm = 'Thank you, our service agent will contact you soon';

                return response()->json(['message' => $queryForm, 'status_fallback' => 1]);
            }

            if ($request->status_fallback == 0) {

                $status_fallback = 0;
                $receiverMessage = '';

                $privateKeyString    = str_replace('\n', "\n", $agentDetail->chatbot_private_key);
                $formattedPrivateKey = "-----BEGIN PRIVATE KEY-----\n".$privateKeyString."\n-----END PRIVATE KEY-----";

                $jsonData = [
                    'type'                        => 'service_account',
                    'project_id'                  => $agentDetail->chatbot_project_id,
                    'private_key_id'              => $agentDetail->chatbot_private_key_id,
                    'private_key'                 => $formattedPrivateKey,
                    'client_email'                => $agentDetail->chatbot_client_email,
                    'client_id'                   => $agentDetail->chatbot_client_id,
                    'auth_uri'                    => 'https://accounts.google.com/o/oauth2/auth',
                    'token_uri'                   => 'https://oauth2.googleapis.com/token',
                    'auth_provider_x509_cert_url' => 'https://www.googleapis.com/oauth2/v1/certs',
                    'client_x509_cert_url'        => 'https://www..com/robot/v1/metadata/x509/'.urlencode($agentDetail->chatbot_client_email),
                    'universe_domain'             => 'googleapis.com',
                ];

                $credentials = new ServiceAccountCredentials(
                    'https://www.googleapis.com/auth/cloud-platform',
                    $jsonData
                );

                $sessionsClient = new SessionsClient([
                    'credentials' => $credentials,
                ]);

                $userInput = $request->message;

                try {
                    $session = $sessionsClient->sessionName($agentDetail->chatbot_project_id, $agentDetail->chatbot_client_id);

                    $textInput = new TextInput;
                    $textInput->setText($userInput);
                    $textInput->setLanguageCode('en-US');

                    $queryInput = new QueryInput;
                    $queryInput->setText($textInput);

                    $response    = $sessionsClient->detectIntent($session, $queryInput);
                    $queryResult = $response->getQueryResult();

                    $intent          = $queryResult->getIntent();
                    $status_fallback = 0;

                    if ($intent->getDisplayName() === 'Default Fallback Intent') {
                        $status_fallback = 1;

                        $companyId = $agentDetail->company_id;

                        $queryForm = 'What is your full name?';

                        return response()->json(['message' => $queryForm, 'status_fallback' => 2]);

                        // return response()->json(['message' => "You need to <a href='".url('login')."' style='text-decoration:underline; font-weight:bold'>Login</a> first to chat with Live agent.",'status_fallback'=>1]);

                    }

                    $receiverMessage = $queryResult->getFulfillmentText().PHP_EOL;

                    return response()->json(['message' => $receiverMessage, 'status_fallback' => $status_fallback]);

                } catch (Exception $ex) {
                    return response()->json(['message' => $ex->getMessage()], 500);
                }

            } elseif ($request->status_fallback == 1) {

                return response()->json(['message' => "You need to <a href='".url('login')."' style='text-decoration:underline; font-weight:bold'>Login</a> first to chat with Live agent.", 'status_fallback' => 1]);

            } else {
                return response()->json(['message' => "You need to <a href='".url('login')."' style='text-decoration:underline; font-weight:bold'>Login</a> first to chat with Live agent.", 'status_fallback' => 1]);

            }

        } else {
            return '';
        }

    }

    public function send(Request $request)
    {

        try {

            if ($request->selected_company_userid && trim($request->selected_company_userid) != '') {
                $companyUserID = $request->selected_company_userid;
            } else {
                $arrayData     = explode('_', $request->type);
                $companyUserID = $arrayData[0];
            }

            $checkExistingConversation = BotConversation::where('user_id', Auth::id())
                ->where('company_user_id', $companyUserID)->where('status', '<>', 2)->first();
            if ($checkExistingConversation) {

                $fireBaeFun = new FirebaseFunction($this->database);
                $result     = $fireBaeFun->liveagentChat($request->message, Auth::id(), $companyUserID, $request->status_fallback);

                return $result;

            } else {

                $checkCompany = Company::where('userId', $companyUserID)->first();
                if ($checkCompany) {

                    if ($checkCompany->chatSupport == 'both' || $checkCompany->chatSupport == 'chatbot') {


                        $checkOauth = OAuthAuthorizationCode::where('user_id', Auth::id())->where('user_id_for_company', $companyUserID)->first();
                        if($checkOauth){

                            $fireBaeFun     = new FirebaseFunction($this->database);
                            $result         = $fireBaeFun->javaChatAgent($request->message, Auth::id(), $companyUserID, $request->status_fallback);
                            $statusResponse = $result->getData();
                            
                        }else{
                            
                            if ($checkCompany->chatSupport == 'both'){
                                $fireBaeFun = new FirebaseFunction($this->database);
                                $result     = $fireBaeFun->liveagentChat($request->message, Auth::id(), $companyUserID, $request->status_fallback);

                            }else{
                                    return response()->json(['message' => 'Invalid authentication to chat with bot'], 500);
                            }
                            

                        }


                        
                        // if ($statusResponse->status_fallback == 1 || $statusResponse->status_fallback == true) {
                        //     $fireBaeFun2 = new FirebaseFunction($this->database);
                        //     $fireBaeFun2->liveagentChat($request->message, Auth::id(), $companyUserID, 0);
                        // }

                        return $result;

                    } elseif ($checkCompany->chatSupport == 'livechat') {

                        $fireBaeFun = new FirebaseFunction($this->database);
                        $result     = $fireBaeFun->liveagentChat($request->message, Auth::id(), $companyUserID, $request->status_fallback);

                        return $result;

                    }

                } else {
                    return '';
                }

            }

        } catch (Exception $ex) {
            Log::error('Error in send function: '.$ex->getMessage());

            return '';
        }

        exit;

        $types = base64_decode($request->type);

        $agentDetail = Bots::where('chatbot_project_id', $types)->first();
        if ($agentDetail) {

            if ($request->status_fallback == 0) {

                $fireBaeFun = new FirebaseFunction($this->database);
                $result     = $fireBaeFun->chatbotWork($request, $agentDetail, $types);

                return $result;

            } else {
                if ($request->status_fallback == 1) {

                    BotConversation::checkNotification($agentDetail->user_id);

                    return response()->json(['message' => 'Please wait for a customer service agent to assist you.', 'status_fallback' => 1]);

                } else {
                    return response()->json(['message' => 'Please wait for a customer service agent to assist you', 'status_fallback' => 1]);

                }

            }

        } else {
            return '';
        }

    }

    public function sendbyAgent(Request $request)
    {

        $twoTypes        = explode('_', $request->type);
        $company_user_id = (isset($twoTypes[0])) ? $twoTypes[0] : '';
        if ($company_user_id != '') {

            $checkBotconver = BotConversation::where('company_user_id', $company_user_id)->where('user_id', $request->userid)->first();
            if ($checkBotconver) {
                $currenetUser = Auth::id();
                $fireBaeFun   = new FirebaseFunction($this->database);
                $result       = $fireBaeFun->agentChatbotWorkLive($request->message, $request->userid, $checkBotconver, $request->type, $currenetUser);

                return $result;
            }

        }

        return '';

    }

    // public function chatbotWork($request,$agentDetail,$types){

    //     $checkBot = BotConversation::where('user_id',Auth::id())->where('bot_id',$agentDetail->id)->where('status',1)->first();
    //     if($checkBot){

    //         $userInput = $request->message;

    //         $saveBotcon = BotConversation::find($checkBot->id);
    //         $saveBotcon->lastmessage = $userInput;
    //         $saveBotcon->last_msg_type = 'user';
    //         $saveBotcon->save();
    //         $userId = $checkBot->user_id;
    //         $postData1 = [
    //             'id'=> Auth::id(),
    //             'message'=>$userInput,
    //             'timestamp'=>time()
    //         ];

    //         /* remove after testing */

    //         $chat_source = base64_encode($types);
    //         $chat_uid = base64_encode($userId);

    //         $response = [];
    //         $response[] = [
    //             'chat_source' => $chat_source,
    //             'chat_uid' => $chat_uid,
    //             'bot_user_id' => $agentDetail->user_id,
    //             'user_id'=> Auth::id(),
    //             'text' => $request->message,
    //             'sender' => 'server',
    //             'agent_type' => $types,
    //             'status_fallback' => 0
    //         ];
    //         broadcast(new ChatAgentMessage($response))->toOthers();

    //         /* remove after testing */

    //         $path = $this->tableName . '/user_id_' . $userId . '/agent_' . $types;
    //         $reference = $this->database->getReference($path);

    //         // Update status_fallback without altering existing data
    //         $reference->update(['status_fallback' => 0]);
    //         $status_fallback = 0;

    //         // Push new messages
    //         $reference->push($postData1);
    //         $receiverMessage = '';

    //         return response()->json(['message' => $receiverMessage,'status_fallback'=>$status_fallback]);

    //     }else{

    //         $privateKeyString = str_replace('\n', "\n", $agentDetail->chatbot_private_key);
    //         $formattedPrivateKey = "-----BEGIN PRIVATE KEY-----\n".$privateKeyString."\n-----END PRIVATE KEY-----";

    //         $jsonData = [
    //             "type" => "service_account",
    //             "project_id" => $agentDetail->chatbot_project_id,
    //             "private_key_id" => $agentDetail->chatbot_private_key_id,
    //             "private_key" => $formattedPrivateKey,
    //             "client_email" => $agentDetail->chatbot_client_email,
    //             "client_id" => $agentDetail->chatbot_client_id,
    //             "auth_uri" => "https://accounts.google.com/o/oauth2/auth",
    //             "token_uri" => "https://oauth2.googleapis.com/token",
    //             "auth_provider_x509_cert_url" => "https://www.googleapis.com/oauth2/v1/certs",
    //             "client_x509_cert_url" => "https://www..com/robot/v1/metadata/x509/" . urlencode($agentDetail->chatbot_client_email),
    //             "universe_domain" => "googleapis.com"
    //         ];

    //         $credentials = new ServiceAccountCredentials(
    //             'https://www.googleapis.com/auth/cloud-platform',
    //             $jsonData
    //         );

    //         $sessionsClient = new SessionsClient([
    //             'credentials' => $credentials,
    //         ]);

    //         $userInput = $request->message;

    //         try {
    //             $session = $sessionsClient->sessionName($agentDetail->chatbot_project_id, $agentDetail->chatbot_client_id);

    //             $textInput = new TextInput();
    //             $textInput->setText($userInput);
    //             $textInput->setLanguageCode('en-US');

    //             $queryInput = new QueryInput();
    //             $queryInput->setText($textInput);

    //             $response = $sessionsClient->detectIntent($session, $queryInput);
    //             $queryResult = $response->getQueryResult();

    //             $intent = $queryResult->getIntent();
    //             $status_fallback = 0;

    //             if ($intent->getDisplayName() === 'Default Fallback Intent') {
    //                 $status_fallback = 1;
    //             }

    //             $receiverMessage =  $queryResult->getFulfillmentText() . PHP_EOL;

    //             if($status_fallback == 1){

    //                 $checkBot = BotConversation::where('user_id',Auth::id())->where('bot_id',$agentDetail->id)->first();
    //                 if($checkBot){ $saveBotcon = BotConversation::find($checkBot->id); }else{ $saveBotcon = new BotConversation(); }

    //                 $saveBotcon->user_id = Auth::id();
    //                 $saveBotcon->bot_id = $agentDetail->id;
    //                 $saveBotcon->company_user_id = $agentDetail->user_id;
    //                 $saveBotcon->lastmessage = $userInput;
    //                 $saveBotcon->status = 0;
    //                 $saveBotcon->last_msg_type = 'user';
    //                 $saveBotcon->save();

    //                 BotConversation::checkNotification($agentDetail->user_id);

    //                 $triggerBroadCast = 1; //(trigger brod carst to 1 otherwise 0;)
    //                 $this->AgentWorkSend($request,$agentDetail,$types,$receiverMessage,$triggerBroadCast);
    //                 return response()->json(['message' => $receiverMessage,'status_fallback'=>$status_fallback]);

    //             }else{

    //                 $userId = Auth::id();
    //                 $postData1 = [
    //                     'id'=> Auth::id(),
    //                     'message'=>$request->message,
    //                     'timestamp'=>time()
    //                 ];

    //                 $postData2 = [
    //                     'id'=> $types,
    //                     'message'=>$receiverMessage,
    //                     'timestamp'=>time()
    //                 ];

    //                 /* remove after testing */

    //                 $chat_source = base64_encode($types);
    //                 $chat_uid = base64_encode($userId);

    //                 $response = [];
    //                 $response[] = [
    //                     'chat_source' => $chat_source,
    //                     'chat_uid' => $chat_uid,
    //                     'bot_user_id' => $agentDetail->user_id,
    //                     'user_id'=> Auth::id(),
    //                     'text' => $request->message,
    //                     'sender' => 'user',
    //                     'agent_type' => $types,

    //                 ];
    //                 $response[] = [
    //                     'chat_source' => $chat_source,
    //                     'chat_uid' => $chat_uid,
    //                     'bot_user_id' => $agentDetail->user_id,
    //                     'user_id'=> Auth::id(),
    //                     'text' => $receiverMessage,
    //                     'sender' => 'server',
    //                     'agent_type' => $types,
    //                 ];
    //                 broadcast(new ChatAgentMessage($response))->toOthers();

    //                 /* remove after testing */

    //                 $path = $this->tableName . '/user_id_' . $userId . '/agent_' . $types;
    //                 $reference = $this->database->getReference($path);

    //                 // Update status_fallback without altering existing data
    //                 $reference->update(['status_fallback' => 0]);

    //                 // Push new messages
    //                 $reference->push($postData1);
    //                 $reference->push($postData2);

    //                 return response()->json(['message' => $receiverMessage,'status_fallback'=>$status_fallback]);

    //             }
    //         } catch (Exception $ex) {
    //             return response()->json(['message' => $ex->getMessage()], 500);
    //         }

    //     }

    // }

    // public function agentChatbotWork($request,$agentDetail,$types){

    //     $userInput = $request->message;
    //     $user_reqid = $request->userid;

    //     try {

    //             $checkBot = BotConversation::where('user_id',$user_reqid)->where('bot_id',$agentDetail->id)->first();
    //             if($checkBot){

    //                 if($checkBot->status == 1){
    //                     if($checkBot->agent_id == Auth::id()){}else{
    //                         $agentDetail = User::find($checkBot->agent_id);
    //                         return response()->json(['message' => 'The user is already associated with the agent ('.$agentDetail->name.')'], 500);
    //                     }
    //                 }

    //                 $saveBotcon = BotConversation::find($checkBot->id);
    //                 $saveBotcon->agent_id = Auth::id();
    //                 $saveBotcon->lastmessage = $userInput;
    //                 $saveBotcon->status = 1;
    //                 $saveBotcon->last_msg_type = 'agent';
    //                 $saveBotcon->save();
    //                 $userId = $checkBot->user_id;
    //                 $postData1 = [
    //                     'id'=> Auth::id(),
    //                     'message'=>$userInput,
    //                     'timestamp'=>time()
    //                 ];

    //                 /* remove after testing */

    //                 $chat_source = base64_encode($types);
    //                 $chat_uid = base64_encode($userId);

    //                 $response = [];
    //                 $response[] = [
    //                     'chat_source' => $chat_source,
    //                     'chat_uid' => $chat_uid,
    //                     'bot_user_id' => $agentDetail->user_id,
    //                     'user_id'=> Auth::id(),
    //                     'text' => $request->message,
    //                     'sender' => 'user',
    //                     'agent_type' => $types,
    //                     'status_fallback' => 0
    //                 ];
    //                 broadcast(new ChatAgentMessage($response))->toOthers();

    //                 /* remove after testing */

    //                 $path = $this->tableName . '/user_id_' . $userId . '/agent_' . $types;
    //                 $reference = $this->database->getReference($path);

    //                 // Update status_fallback without altering existing data
    //                 $reference->update(['status_fallback' => 0]);
    //                 $status_fallback = 0;

    //                 // Push new messages
    //                 $reference->push($postData1);
    //                 $receiverMessage = '';

    //                 return response()->json(['message' => $receiverMessage,'status_fallback'=>$status_fallback]);

    //             }else{
    //                 return response()->json(['message' => 'invalid request'], 500);
    //             }

    //     } catch (Exception $ex) {
    //         return response()->json(['message' => $ex->getMessage()], 500);
    //     }

    // }

    public function cancelbotConversation(Request $request)
    {

        $userid  = base64_decode($request->umid);
        $bottype = base64_decode($request->sourceID);
        $botQry  = Bots::where('chatbot_project_id', $bottype)->first();
        if ($botQry) {
            BotConversation::where('user_id', $userid)->where('bot_id', $botQry->id)->update(['status' => 2, 'agent_id' => 0]);
        }

        return json_encode(['status' => true]);

    }

    public function userDetail($userId)
    {

        $userData = User::find($userId);

        return Inertia::render('User/UserDetail', ['userData' => $userData]);

    }

    public function fetchNotifications()
    {
        if (auth()->check()) {
            return Notifications::where('receiver_id', auth()->id())
                ->orderBy('created_at', 'desc')
                ->take(10)
                ->get();
        }
        return response()->json([]);
    }
}
