import React from 'react';
import clsx from 'clsx';

const variants = {
    primary: 'text-cf-primary hover:text-cf-primary-700 hover:bg-cf-primary-50',
    secondary: 'text-gray-600 hover:text-gray-900 hover:bg-gray-100',
    success: 'text-green-600 hover:text-green-700 hover:bg-green-50',
    warning: 'text-yellow-600 hover:text-yellow-700 hover:bg-yellow-50',
    danger: 'text-red-600 hover:text-red-700 hover:bg-red-50',
};

export const ActionButton = ({
    children,
    variant = 'secondary',
    size = 'sm',
    className = '',
    icon: Icon,
    ...props
}) => {
    return (
        <button
            className={clsx(
                'cursor-pointer inline-flex items-center justify-center rounded-lg font-medium transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-offset-1 border border-transparent',
                size === 'xs' && 'p-1.5 min-w-[28px] min-h-[28px]',
                size === 'sm' && 'p-2 min-w-[32px] min-h-[32px]',
                size === 'md' && 'px-3 py-2 min-w-[36px] min-h-[36px]',
                variants[variant],
                variant === 'primary' && 'focus:ring-cf-primary-300 hover:border-cf-primary-200',
                variant === 'danger' && 'focus:ring-red-300 hover:border-red-200',
                variant === 'success' && 'focus:ring-green-300 hover:border-green-200',
                variant === 'warning' && 'focus:ring-yellow-300 hover:border-yellow-200',
                variant === 'secondary' && 'focus:ring-gray-300 hover:border-gray-200',
                className
            )}
            {...props}
        >
            {Icon && <Icon className={clsx('w-4 h-4', children && 'mr-1')} />}
            {children}
        </button>
    );
};

// Icon components for common actions
export const EditIcon = ({ className = '', ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M15.232 5.232l3.536 3.536M9 13h3l8-8a2.828 2.828 0 00-4-4l-8 8v3zm0 0v3h3" />
    </svg>
);

export const DeleteIcon = ({ className = '', ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
    </svg>
);

export const ViewIcon = ({ className = '', ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        <path strokeLinecap="round" strokeLinejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
    </svg>
);

export const PlusIcon = ({ className = '', ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
    </svg>
);

export const AssignIcon = ({ className = '', ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
    </svg>
);
