<script setup>
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';

// Define props with defineProps
const props = defineProps({
    title: {
        type: String,
        required: true,
    },
    buttonLabel: {
        type: String,
        required: true,
    },
    href: {
        type: String,
        required: true,
    },
});
</script>

<template>
    <div
        class="flex justify-between items-center gap-5 col-span-12 xl:col-span-4 bg-gray-50 mb-5 p-6 border rounded-md w-full h-17">
        <h5 class="font-medium text-2xl text-slate-700">{{ title }}</h5>
        <ResponsiveNavLink :href="href" class="!p-0">
            <PrimaryButton v-if="buttonLabel">+ {{ buttonLabel }}</PrimaryButton>
        </ResponsiveNavLink>
    </div>
</template>