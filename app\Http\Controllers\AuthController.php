<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

use Inertia\Inertia;

class AuthController extends Controller
{
    public function login(Request $request)
    {
        if($request->isMethod('get')) {
            return Inertia::render('Auth/Login');
        }

        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);

        if (Auth::attempt($credentials, $request->boolean('remember'))) {
            $request->session()->regenerate();
            return redirect()->intended('/dashboard');
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->onlyInput('email');
    }

    public function register(Request $request)
    {
        if($request->isMethod('get')) {
            return Inertia::render('Auth/Register');
        }

        $validationRules = [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'profileType' => ['required', 'in:user,company'],
        ];

        // Add conditional validation for company fields
        if ($request->profileType === 'company') {
            $validationRules['companyName'] = ['required', 'string', 'max:255'];
            $validationRules['companyPhone'] = ['required', 'string', 'max:20'];
            $validationRules['websiteUrl'] = ['required', 'url', 'max:255'];
            $validationRules['companyAddress'] = ['required', 'string', 'max:255'];
            $validationRules['city'] = ['required', 'string', 'max:255'];
            $validationRules['state'] = ['required', 'string', 'max:255'];
            $validationRules['country'] = ['required', 'string', 'max:255'];
            $validationRules['zipcode'] = ['required', 'string', 'max:8'];
        }

        $request->validate($validationRules);

        $userData = [
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'profileType' => $request->profileType,
            'verificationStatus' => true,
            'status' => true,
        ];

        // Add company specific fields
        if ($request->profileType === 'company') {
            $userData['phone'] = $request->companyPhone;
        }

        $user = User::create($userData);

        // Create company record if user is registering as company
        if ($request->profileType === 'company') {
            \App\Models\Company::create([
                'userId' => $user->id,
                'companyName' => $request->companyName,
                'companyPhone' => $request->companyPhone,
                'companyEmail' => $request->email,
                'websiteUrl' => $request->websiteUrl,
                'companyAddress' => $request->companyAddress,
                'city' => $request->city,
                'state' => $request->state,
                'country' => $request->country,
                'zipcode' => $request->zipcode,
                'status' => true,
            ]);
        }

        Auth::login($user);
        return redirect('/dashboard');
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect('/login');
    }
} 