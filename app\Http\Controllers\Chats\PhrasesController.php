<?php

namespace App\Http\Controllers\Chats;

use App\Http\Controllers\Controller;
use App\Models\CbIntent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PhrasesController extends Controller
{
    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Validate the request
        $request->validate([
            'training_phrases' => 'nullable|string',
            'responses'        => 'nullable|string',
        ]);

        // Find the intent
        $intent = CbIntent::where('id', $id)
            ->where('user_id', Auth::id())
            ->firstOrFail();

        // Update training phrases
        if ($request->has('training_phrases')) {
            $phrases                  = json_decode($request->training_phrases, true);
            $intent->training_phrases = json_encode($phrases);
        }

        // Update responses
        if ($request->has('responses')) {
            $responses         = json_decode($request->responses, true);
            $intent->responses = json_encode($responses);
        }

        $intent->save();

        return response()->json([
            'success' => true,
            'message' => 'Updated successfully',
        ]);
    }
}
