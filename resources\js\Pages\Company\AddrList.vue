<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, usePage } from '@inertiajs/vue3';
import { ref } from 'vue';
import CompaniesTable from '@/Components/Tables/CompaniesTable.vue';
import BreadcrumbDefault from '@/Components/Breadcrumbs/BreadcrumbDefault.vue'
const pageTitle = ref('Address');

const { allCompanies } = usePage().props;
</script>

<template>

  <Head :title="pageTitle" />

  <AuthenticatedLayout>

    <CompaniesTable :CompaniesList="allCompanies" />

  </AuthenticatedLayout>
</template>
