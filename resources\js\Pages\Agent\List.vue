<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Link } from '@inertiajs/vue3';
import ApplicationLogo from '@/Components/ApplicationLogo.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import BreadcrumbDefault from '@/Components/Breadcrumbs/BreadcrumbDefault.vue';
import axios from 'axios';
import { ref, watch, nextTick, onMounted } from 'vue'; // Import watch and nextTick
import { usePage, Head } from '@inertiajs/vue3';
import PageHeading from '@/Components/Global/PageHeading.vue';
import DtIconGlobal from '@/Components/DtIcon/DtIconGlobal.vue';

const pageTitle = ref('Agent')

const { allAgents, success } = usePage().props;

let successMessage = ref(success ? success : null);


const deleteAgent = async (agent) => {
    try {
        // Show a SweetAlert confirmation popup
        const confirmation = await Swal.fire({
            title: 'Are you sure?',
            text: 'You are about to delete this agent. This action cannot be undone. Are you sure you want to proceed?',
            icon: 'warning',
            showCancelButton: true,

            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel',
            customClass: {
                confirmButton: 'dk-update-btn',
                cancelButton: 'dk-cancle-btn',
            },
            buttonsStyling: false,
        });

        // If user confirms deletion
        if (confirmation.isConfirmed) {
            const response = await axios.delete(route('manage-agent.delete', { id: agent.id }));
            if (response.data.success) {
                Swal.fire({
                    title: 'Deleted!',
                    text: 'The agent has been deleted.',
                    icon: 'success',
                    confirmButtonText: 'OK'
                }).then((result) => {
                    if (result.isConfirmed) {
                        location.reload(); // Reload the page
                    }
                });

            } else {
                throw new Error('Failed to delete agent');
            }
        }
    } catch (error) {
        console.error(error);
        // Show error message if deletion fails
        Swal.fire('Error', 'An error occurred while deleting the agent.', 'error');
    }
};



onMounted(() => {
    setTimeout(() => {
        successMessage = null;
    }, 1000);
});


</script>


<template>

    <Head :title="pageTitle" />

    <AuthenticatedLayout>

        <!-- <PageHeading :title="pageTitle" :href="route('manage-agent.add')" buttonLabel="Add Agent"></PageHeading> -->

        <Transition v-if="successMessage" name="fade">
            <p class="mb-4 text-green-400 text-sm successMessages">{{ successMessage }}</p>
        </Transition>

        <div class="flex">


            <div class="flex-1">

                <div class="border-stroke bg-white p-6 max-md:p-4 border rounded-md">

                    <div class="max-w-full overflow-x-auto">
                        <table class="dt-deals-table">
                            <thead>
                                <tr>
                                    <th class="dt-deals-th">
                                        Name
                                    </th>
                                    <th class="dt-deals-th">
                                        Phone
                                    </th>
                                    <th class="dt-deals-th">
                                        Email
                                    </th>

                                    <th class="dt-deals-th">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="agent in allAgents.data" :key="agent.id" class="dt-deals-tr">
                                    <td class="dt-deals-td">
                                        <div>{{ agent.name }}</div>
                                    </td>
                                    <td class="dt-deals-td">
                                        <div>{{ agent.phone }}</div>
                                    </td>
                                    <td class="dt-deals-td">
                                        <div>{{ agent.email }}</div>
                                    </td>
                                    <td class="dt-deals-td">

                                        <div class="dt-deals-actions">
                                            <Link :href="route('manage-agent.edit', { id: agent.id })"
                                                class="dt-deals-action-btn">
                                            <DtIconGlobal :type="'edit'" />
                                            </Link>

                                            <button class="dt-deals-action-btn dt-deals-delete-btn"
                                                @click="deleteAgent(agent)">
                                                <DtIconGlobal :type="'delete'" />
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <tr v-if="allAgents.data.length == 0">
                                    <td colspan="4">
                                        <div class="my-4 text-danger">No record found</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <div v-if="allAgents.data.length > 0" class="dt-table-pagi">
                            <div class="content-center row-span-2 row-start-2">
                                Showing <b>{{ (allAgents.current_page - 1) * allAgents.per_page + 1 }}</b>-
                                <b>{{ Math.min(allAgents.current_page * allAgents.per_page, allAgents.total) }}</b>
                                from <b>{{ allAgents.total }}</b> data
                            </div>

                            <div class="row-start-2 row-end-4 text-end">
                                <div class="pagination-links">
                                    <ul class="flex justify-items-end place-content-end">
                                        <li v-for="page in allAgents.links" :key="page.url">
                                            <button @click="$inertia.visit(page.url)"
                                                :class="{ 'bg-cfp-500 text-white': page.active, 'hover:bg-cfp-500 hover:text-white': !page.active }"
                                                class="px-3 py-1 rounded-full focus:outline-none mx-1"
                                                v-html="page.label"></button>
                                        </li>
                                    </ul>
                                </div>

                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
