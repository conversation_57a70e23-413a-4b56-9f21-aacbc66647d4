<?php

namespace App\Http\Controllers\Admin;

use App\Actions\Subscription\CancelSubscriptionAction;
use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class SubscriptionController extends Controller
{
    /**
     * Display a listing of subscriptions.
     */
    public function index(Request $request): Response
    {
        $query = Subscription::with(['user', 'plan']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by plan
        if ($request->filled('plan_id')) {
            $query->where('subscription_plan_id', $request->plan_id);
        }

        // Search by user
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $subscriptions = $query->latest()->paginate(20);

        return Inertia::render('Admin/Subscriptions/Index', [
            'subscriptions' => $subscriptions->through(function ($subscription) {
                return [
                    'id' => $subscription->id,
                    'user' => [
                        'id' => $subscription->user->id,
                        'name' => $subscription->user->name,
                        'email' => $subscription->user->email,
                    ],
                    'plan' => [
                        'id' => $subscription->plan->id,
                        'name' => $subscription->plan->name,
                        'formatted_price' => $subscription->plan->formatted_price,
                        'billing_interval' => $subscription->plan->billing_interval,
                    ],
                    'status' => $subscription->status,
                    'payment_method' => $subscription->payment_method,
                    'trial_ends_at' => $subscription->trial_ends_at,
                    'current_period_start' => $subscription->current_period_start,
                    'current_period_end' => $subscription->current_period_end,
                    'ends_at' => $subscription->ends_at,
                    'canceled_at' => $subscription->canceled_at,
                    'created_at' => $subscription->created_at,
                    'is_active' => $subscription->isActive(),
                    'on_trial' => $subscription->onTrial(),
                    'is_canceled' => $subscription->isCanceled(),
                    'days_remaining_in_period' => $subscription->daysRemainingInPeriod(),
                ];
            }),
            'filters' => [
                'status' => $request->status,
                'plan_id' => $request->plan_id,
                'search' => $request->search,
            ],
            'plans' => SubscriptionPlan::active()->ordered()->get(['id', 'name']),
            'stats' => [
                'total' => Subscription::count(),
                'active' => Subscription::where('status', 'active')->count(),
                'trial' => Subscription::where('status', 'trial')->count(),
                'canceled' => Subscription::where('status', 'canceled')->count(),
                'past_due' => Subscription::where('status', 'past_due')->count(),
            ],
        ]);
    }

    /**
     * Display the specified subscription.
     */
    public function show(Subscription $subscription): Response
    {
        $subscription->load(['user', 'plan', 'invoices', 'transactions']);

        return Inertia::render('Admin/Subscriptions/Show', [
            'subscription' => [
                'id' => $subscription->id,
                'user' => [
                    'id' => $subscription->user->id,
                    'name' => $subscription->user->name,
                    'email' => $subscription->user->email,
                    'profileType' => $subscription->user->profileType,
                ],
                'plan' => [
                    'id' => $subscription->plan->id,
                    'name' => $subscription->plan->name,
                    'description' => $subscription->plan->description,
                    'formatted_price' => $subscription->plan->formatted_price,
                    'billing_interval' => $subscription->plan->billing_interval,
                    'features' => $subscription->plan->features,
                    'limits' => $subscription->plan->limits,
                ],
                'status' => $subscription->status,
                'payment_method' => $subscription->payment_method,
                'payment_method_details' => $subscription->payment_method_details,
                'stripe_subscription_id' => $subscription->stripe_subscription_id,
                'paypal_subscription_id' => $subscription->paypal_subscription_id,
                'trial_ends_at' => $subscription->trial_ends_at,
                'current_period_start' => $subscription->current_period_start,
                'current_period_end' => $subscription->current_period_end,
                'ends_at' => $subscription->ends_at,
                'canceled_at' => $subscription->canceled_at,
                'created_at' => $subscription->created_at,
                'updated_at' => $subscription->updated_at,
                'is_active' => $subscription->isActive(),
                'on_trial' => $subscription->onTrial(),
                'is_canceled' => $subscription->isCanceled(),
                'trial_days_remaining' => $subscription->trialDaysRemaining(),
                'days_remaining_in_period' => $subscription->daysRemainingInPeriod(),
            ],
            'invoices' => $subscription->invoices->map(function ($invoice) {
                return [
                    'id' => $invoice->id,
                    'invoice_number' => $invoice->invoice_number,
                    'status' => $invoice->status,
                    'formatted_amount_due' => $invoice->formatted_amount_due,
                    'formatted_amount_paid' => $invoice->formatted_amount_paid,
                    'billing_reason' => $invoice->billing_reason,
                    'due_date' => $invoice->due_date,
                    'paid_at' => $invoice->paid_at,
                    'created_at' => $invoice->created_at,
                    'is_paid' => $invoice->isPaid(),
                    'is_overdue' => $invoice->isOverdue(),
                ];
            }),
            'transactions' => $subscription->transactions->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'transaction_id' => $transaction->transaction_id,
                    'gateway' => $transaction->gateway,
                    'type' => $transaction->type,
                    'status' => $transaction->status,
                    'formatted_amount' => $transaction->formatted_amount,
                    'processed_at' => $transaction->processed_at,
                    'created_at' => $transaction->created_at,
                ];
            }),
        ]);
    }

    /**
     * Cancel a subscription.
     */
    public function cancel(Request $request, Subscription $subscription, CancelSubscriptionAction $action)
    {
        $validated = $request->validate([
            'immediately' => 'boolean',
        ]);

        try {
            $action->handle($subscription, $validated['immediately'] ?? false);

            $message = $validated['immediately'] ?? false
                ? 'Subscription canceled immediately.'
                : 'Subscription will be canceled at the end of the current billing period.';

            return redirect()->route('admin.subscriptions.show', $subscription)
                            ->with('success', $message);
        } catch (\Exception $e) {
            return back()->withErrors(['subscription' => $e->getMessage()]);
        }
    }

    /**
     * Resume a canceled subscription.
     */
    public function resume(Subscription $subscription)
    {
        if (!$subscription->isCanceled()) {
            return back()->withErrors(['subscription' => 'Subscription is not canceled.']);
        }

        try {
            $subscription->resume();

            return redirect()->route('admin.subscriptions.show', $subscription)
                            ->with('success', 'Subscription resumed successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['subscription' => $e->getMessage()]);
        }
    }
}
