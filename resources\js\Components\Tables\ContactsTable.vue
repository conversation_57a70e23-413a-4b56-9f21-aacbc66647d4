<script setup>
import { ref } from 'vue';
const props = defineProps(['ContactsList']);
let ContactsData = props.ContactsList;
import { Link } from '@inertiajs/vue3';
var baseurl = window.location.origin;
var adminBaseurl = window.location.origin + '/dtadmin';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import PageHeading from '../Global/PageHeading.vue';
import DtIconGlobal from '../DtIcon/DtIconGlobal.vue';


const deleteItem = async (item) => {
  try {
    // Show a SweetAlert confirmation popup
    const confirmation = await Swal.fire({
      title: 'Are you sure?',
      text: 'You are about to delete this Contact Inquiry. Are you sure you want to proceed?',
      icon: 'warning',
      showCancelButton: true,

      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'Cancel',
      customClass: {
        confirmButton: 'dk-update-btn',
        cancelButton: 'dk-cancle-btn',
      },
      buttonsStyling: false,
    });

    // If user confirms deletion
    if (confirmation.isConfirmed) {
      const response = await axios.delete(route('contacts.delete', { id: item.id }));
      if (response.data.success) {
        // Remove the deleted item from the allitems array
        const index = ContactsData.data.findIndex(a => a.id === item.id);
        if (index !== -1) {
          ContactsData.data.splice(index, 1);
        }
        // Show success message
        //Swal.fire('Deleted!', 'The item has been deleted.', 'success');
      } else {
        throw new Error('Failed to delete item');
      }
    }
  } catch (error) {
    console.error(error);
    // Show error message if deletion fails
    Swal.fire('Error', 'An error occurred while deleting the item.', 'error');
  }
};

</script>

<template>

  <!-- <PageHeading title="Inquiry"></PageHeading> -->

  <div class="border-stroke p-6 border rounded-md">

    <h4 v-if="$page.props.auth.user.profileType == 'admin'"
      class="hidden mb-6 pb-2 border-b font-semibold text-slate-800 text-xl">Contact Inquiry List</h4>

    <div class="max-w-full overflow-x-auto">

      <table class="dt-deals-table">
        <thead>
          <tr class="">
            <th class="hidden dt-deals-th">
              <div class="flex items-center gap-4">
                <label :for="'checkboxAll-data'" class="flex items-center font-medium cursor-pointer select-none">
                  <div class="relative">
                    <input type="checkbox" :id="'checkboxAll-data'" class="sr-only tableCheckbox">
                    <div
                      class="flex justify-center items-center border-[.5px] border-stroke bg-gray-2 rounded-[3px] w-5 h-5 text-white -2 box">
                      <span class="opacity-0">
                        <svg width="14" height="14" viewBox="0 0 10 10">
                          <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M8.62796 2.20602C8.79068 2.36874 8.79068 2.63256 8.62796 2.79528L4.04463 7.37861C3.88191 7.54133 3.61809 7.54133 3.45537 7.37861L1.37204 5.29528C1.20932 5.13256 1.20932 4.86874 1.37204 4.70602C1.53476 4.5433 1.79858 4.5433 1.96129 4.70602L3.75 6.49473L8.03871 2.20602C8.20142 2.0433 8.46524 2.0433 8.62796 2.20602Z"
                            fill="currentColor"></path>
                        </svg>
                      </span>
                    </div>
                  </div>
                </label>
              </div>
            </th>

            <th class="dt-deals-th">
              Name
            </th>
            <th class="dt-deals-th">
              Email
            </th>
            <th class="dt-deals-th">
              Subject
            </th>
            <th class="dt-deals-th">
              Message
            </th>
            <th class="dt-deals-th">
              Action
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(itemDetail, key) in ContactsData.data" :key="key" class="dt-deals-tr">

            <td class="dt-deals-td">
              <p class="text-slate-800">{{ itemDetail.fullname }}</p>
            </td>
            <td class="dt-deals-td">
              <p class="text-slate-800">{{ itemDetail.email }}</p>
            </td>
            <td class="dt-deals-td">
              <p class="text-slate-800">{{ itemDetail.subject }}</p>
            </td>
            <td class="dt-deals-td">
              <p class="text-slate-800">{{ itemDetail.message }}</p>
            </td>
            <td class="dt-deals-td">
              <div class="dt-deals-actions">

                <button class="dt-deals-action-btn dt-deals-delete-btn" @click="deleteItem(itemDetail)">
                  <DtIconGlobal :type="'delete'" />
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>


      <div class="dt-table-pagi">
        <div class="content-center row-span-2 row-start-2">
          Showing <b>{{ (ContactsData.current_page - 1) * ContactsData.per_page + 1 }}</b>-
          <b>{{ Math.min(ContactsData.current_page * ContactsData.per_page, ContactsData.total) }}</b>
          from <b>{{ ContactsData.total }}</b> data
        </div>
        <div class="row-start-2 row-end-4 text-end">
          <div class="pagination-links">
            <ul class="flex justify-items-end place-content-end">
              <li v-for="page in ContactsData.links" :key="page.url">
                <button @click="$inertia.visit(page.url)"
                  :class="{ 'bg-cfp-500 text-white': page.active, 'hover:bg-cfp-500 hover:text-white': !page.active }"
                  class="px-3 py-1 rounded-full focus:outline-none mx-1" v-html="page.label"></button>
              </li>
            </ul>
          </div>

        </div>
      </div>

    </div>
  </div>
</template>
