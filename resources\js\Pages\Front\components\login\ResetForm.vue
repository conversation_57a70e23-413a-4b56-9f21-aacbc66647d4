<script setup>
import { ref } from 'vue';
import Button from '../reusable/Button.vue';
import { useForm } from '@inertiajs/vue3';
import FormInput from '../reusable/FormInput.vue';
import InputError from '@/Components/InputError.vue';
import { Inertia } from '@inertiajs/inertia';

const registrationType = ref('user');

const props = defineProps(['fromData']);
let fromData = props.fromData;


const userFields = [
  { label: 'New Password', inputIdentifier: 'password', inputType: 'password', model: 'password' },
  { label: 'Confirm Password', inputIdentifier: 'password_confirmation', inputType: 'password', model: 'password_confirmation' }
];


const form = useForm({
  token: fromData.identity,
  email: fromData.email,
  password: '',
  password_confirmation: '',
});

const submit = () => {
  form.post(route('reset-password-agent'), {
    onFinish: () => {
      form.reset('password', 'password_confirmation');
      if (form.hasErrors) { } else {
        Swal.fire({
          icon: 'success',
          title: 'Success',
          text: 'Password has been reset successfully.',
        }).then(() => {
          var loginUrl = window.location.origin + '/login';
          Inertia.replace(loginUrl);
        });
      }
    }
  });
};

</script>

<template>
  <div class="mx-auto w-full md:w-1/2">
    <div class="login-body-cls">
      <p class="mb-4 font-general-medium text-2xl text-center text-cfp-500-dark">
        Set New Password
      </p>


      <!-- Registration form -->
      <form @submit.prevent="submit" class="space-y-7 pt-4 border-t font-general-regular">

        <template v-for="(field, index) in userFields">
          <FormInput v-model="form[field.model]" :label="field.label" :inputIdentifier="field.inputIdentifier"
            :inputType="field.inputType" :name="field.inputIdentifier" />
          <InputError class="" :message="form.errors[field.inputIdentifier]" />
        </template>

        <div class="text-center">
          <Button title="Submit"
            class="w-60 max-md:w-auto dk-update-btn"
            type="submit" aria-label="Submit" />
        </div>
      </form>
    </div>
  </div>
</template>
