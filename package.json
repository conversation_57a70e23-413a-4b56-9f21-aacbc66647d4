{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"@inertiajs/vue3": "^1.0.0", "@tailwindcss/forms": "^0.5.3", "@vitejs/plugin-vue": "^5.0.0", "autoprefixer": "^10.4.12", "axios": "^1.6.4", "laravel-echo": "^1.19.0", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.31", "pusher-js": "^8.4.0", "tailwindcss": "^3.2.1", "vite": "^5.0", "vue": "^3.4.0"}, "dependencies": {"@heroicons/vue": "^2.1.5", "@inertiajs/inertia": "^0.11.1", "date-fns": "^3.6.0", "element-plus": "^2.7.6", "feather-icons": "^4.29.2", "flatpickr": "^4.6.13", "lucide-vue-next": "^0.474.0", "tailwind-scrollbar": "^3.1.0", "vue-backtotop": "^1.6.1", "vue-flatpickr-component": "^11.0.5", "vue-router": "^4.0.13"}}