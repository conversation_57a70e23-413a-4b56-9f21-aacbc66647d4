{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build && vite build --ssr", "dev": "vite", "build:ssr": "vite build --ssr"}, "devDependencies": {"@tailwindcss/vite": "^4.0.0", "@vitejs/plugin-react": "^4.7.0", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "tailwindcss": "^4.0.0", "vite": "^6.2.4"}, "dependencies": {"@inertiajs/react": "^2.0.15", "chart.js": "^4.5.0", "clsx": "^2.1.1", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "ziggy-js": "^2.5.3"}}