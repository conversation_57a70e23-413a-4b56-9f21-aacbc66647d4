import React, { useState } from "react";
import DashboardLayout from "@/Layouts/DashboardLayout";
import { Input } from "@/Components/FormElements";
import Button from "@/Components/Button";
import { Card, CardContent, PageHeader } from "@/Components/UI";
import { useForm } from "@inertiajs/react";

export default function Create({ companies = [], users = [] }) {
    const [showNewUserForm, setShowNewUserForm] = useState(false);
    const [searchUser, setSearchUser] = useState("");

    const { data, setData, post, processing, errors, reset } = useForm({
        title: "",
        description: "",
        priority: "0", // 0=Low, 1=Medium, 2=High
        company_id: "",
        assigned_user_id: "",
    });

    const {
        data: newUserData,
        setData: setNewUserData,
        post: postNewUser,
        processing: processingNewUser,
        errors: newUserErrors,
        reset: resetNewUser,
    } = useForm({
        name: "",
        email: "",
        phone: "",
        password: "",
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route("complains.store"), {
            onSuccess: () => {
                reset();
            },
        });
    };

    const generateRandomString = (length) => {
        const chars =
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        let result = "";
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    };

    const generateEmail = (name) => {
        const cleanName = name.toLowerCase().replace(/\s+/g, ".");
        return `${cleanName}@example.com`;
    };

    const generatePhone = () => {
        return `+1${Math.floor(Math.random() * 9000000000) + 1000000000}`;
    };

    const handleAutoGenerate = () => {
        if (newUserData.name) {
            setNewUserData({
                ...newUserData,
                email: generateEmail(newUserData.name),
                phone: generatePhone(),
                password: generateRandomString(8),
            });
        }
    };

    const handleCreateNewUser = (e) => {
        e.preventDefault();
        postNewUser(route("users.store"), {
            onSuccess: (response) => {
                // Auto-assign the new user to the complaint
                setData("assigned_user_id", response.user.id);
                setShowNewUserForm(false);
                resetNewUser();
            },
        });
    };

    const filteredUsers = users.filter(
        (user) =>
            user.name.toLowerCase().includes(searchUser.toLowerCase()) ||
            user.email.toLowerCase().includes(searchUser.toLowerCase())
    );

    const getPriorityLabel = (value) => {
        switch (value) {
            case "0":
                return "Low";
            case "1":
                return "Medium";
            case "2":
                return "High";
            default:
                return "Low";
        }
    };

    return (
        <DashboardLayout title="Submit Your Complaints">
            <title>Submit Your Complaints</title>

            <div className="space-y-6">
                <PageHeader
                    title="Create New Complaint"
                    subtitle="Submit a complaint and assign it to a user for resolution"
                />

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Left Column - Complaint Form */}
                    <div className="lg:col-span-2">
                        <Card>
                            <CardContent>
                                <form
                                    onSubmit={handleSubmit}
                                    className="space-y-8"
                                >
                                    {/* Complaint Details Section */}
                                    <div className="space-y-6">
                                        <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                                            Complaint Details
                                        </h3>

                                        <div className="space-y-6">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Complaint Title *
                                                </label>
                                                <Input
                                                    name="title"
                                                    value={data.title}
                                                    onChange={(e) =>
                                                        setData(
                                                            "title",
                                                            e.target.value
                                                        )
                                                    }
                                                    placeholder="Enter complaint title"
                                                    className="w-full"
                                                    error={errors.title}
                                                />
                                                {errors.title && (
                                                    <p className="text-red-500 text-sm mt-1">
                                                        {errors.title}
                                                    </p>
                                                )}
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Company *
                                                </label>
                                                <select
                                                    value={data.company_id}
                                                    onChange={(e) =>
                                                        setData(
                                                            "company_id",
                                                            e.target.value
                                                        )
                                                    }
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-cf-primary focus:border-transparent"
                                                >
                                                    <option value="">
                                                        Select a company
                                                    </option>
                                                    {companies.map(
                                                        (company) => (
                                                            <option
                                                                key={company.id}
                                                                value={
                                                                    company.id
                                                                }
                                                            >
                                                                {
                                                                    company.companyName
                                                                }
                                                            </option>
                                                        )
                                                    )}
                                                </select>
                                                {errors.company_id && (
                                                    <p className="text-red-500 text-sm mt-1">
                                                        {errors.company_id}
                                                    </p>
                                                )}
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Priority Level *
                                                </label>
                                                <div className="grid grid-cols-3 gap-2">
                                                    {[
                                                        {
                                                            value: "0",
                                                            label: "Low",
                                                        },
                                                        {
                                                            value: "1",
                                                            label: "Medium",
                                                        },
                                                        {
                                                            value: "2",
                                                            label: "High",
                                                        },
                                                    ].map((level) => (
                                                        <button
                                                            type="button"
                                                            key={level.value}
                                                            className={`px-4 py-3 rounded-lg border text-sm font-medium transition-all ${
                                                                data.priority ===
                                                                level.value
                                                                    ? "bg-cf-primary text-white border-cf-primary shadow-sm"
                                                                    : "bg-white border-gray-300 text-gray-700 hover:border-cf-primary-300 hover:bg-cf-primary-50"
                                                            }`}
                                                            onClick={() =>
                                                                setData(
                                                                    "priority",
                                                                    level.value
                                                                )
                                                            }
                                                        >
                                                            {level.label}
                                                        </button>
                                                    ))}
                                                </div>
                                                {errors.priority && (
                                                    <p className="text-red-500 text-sm mt-1">
                                                        {errors.priority}
                                                    </p>
                                                )}
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Description *
                                                </label>
                                                <Input
                                                    name="description"
                                                    value={data.description}
                                                    onChange={(e) =>
                                                        setData(
                                                            "description",
                                                            e.target.value
                                                        )
                                                    }
                                                    as="textarea"
                                                    rows={6}
                                                    placeholder="Describe the complaint in detail..."
                                                    className="w-full"
                                                    error={errors.description}
                                                />
                                                {errors.description && (
                                                    <p className="text-red-500 text-sm mt-1">
                                                        {errors.description}
                                                    </p>
                                                )}
                                            </div>
                                        </div>
                                    </div>

                                    {/* Action Buttons */}
                                    <div className="flex items-center justify-end gap-4 pt-6 border-t border-gray-200">
                                        <Button
                                            type="button"
                                            variant="dark"
                                            onClick={() =>
                                                window.history.back()
                                            }
                                        >
                                            Cancel
                                        </Button>
                                        <Button
                                            type="submit"
                                            variant="default"
                                            className="bg-cf-primary hover:bg-cf-primary-600 text-white px-6 py-2 flex items-center gap-2"
                                            disabled={
                                                processing ||
                                                !data.assigned_user_id
                                            }
                                        >
                                            {processing
                                                ? "Creating..."
                                                : "Create Complaint"}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Right Column - Assign to User Section */}
                    <div className="lg:col-span-1">
                        <Card>
                            <CardContent>
                                <div className="space-y-6">
                                    <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                                        Assign to User
                                    </h3>

                                    {/* Search Users */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Search Users
                                        </label>
                                        <Input
                                            value={searchUser}
                                            onChange={(e) =>
                                                setSearchUser(e.target.value)
                                            }
                                            placeholder="Search by name or email..."
                                            className="w-full"
                                        />
                                    </div>

                                    {/* User Selection */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Select User *
                                        </label>
                                        <div className="max-h-48 overflow-y-auto border border-gray-300 rounded-md">
                                            {filteredUsers.length > 0 ? (
                                                filteredUsers.map((user) => (
                                                    <div
                                                        key={user.id}
                                                        className={`p-3 border-b border-gray-200 cursor-pointer hover:bg-gray-50 ${
                                                            data.assigned_user_id ==
                                                            user.id
                                                                ? "bg-cf-primary-50 border-cf-primary"
                                                                : ""
                                                        }`}
                                                        onClick={() =>
                                                            setData(
                                                                "assigned_user_id",
                                                                user.id
                                                            )
                                                        }
                                                    >
                                                        <div className="font-medium text-gray-900">
                                                            {user.name}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            {user.email}
                                                        </div>
                                                    </div>
                                                ))
                                            ) : (
                                                <div className="p-3 text-gray-500 text-center">
                                                    No users found
                                                </div>
                                            )}
                                        </div>
                                        {errors.assigned_user_id && (
                                            <p className="text-red-500 text-sm mt-1">
                                                {errors.assigned_user_id}
                                            </p>
                                        )}
                                    </div>

                                    {/* Create New User Option */}
                                    <div className="pt-2 text-right">
                                        <Button
                                            type="button"
                                            variant={ showNewUserForm ? "dark" : "default" }
                                            className="text-md px-5 py-2"
                                            onClick={() =>
                                                setShowNewUserForm(
                                                    !showNewUserForm
                                                )
                                            }
                                        >
                                            {showNewUserForm
                                                ? "Cancel"
                                                : "Create New User"}
                                        </Button>
                                    </div>

                                    {/* New User Form */}
                                    {showNewUserForm && (
                                        <div className="space-y-4 border border-gray-200 rounded-lg p-4 bg-gray-50">
                                            <h4 className="font-medium text-gray-900">
                                                Create New User
                                            </h4>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Name *
                                                </label>
                                                <Input
                                                    value={newUserData.name}
                                                    onChange={(e) =>
                                                        setNewUserData(
                                                            "name",
                                                            e.target.value
                                                        )
                                                    }
                                                    placeholder="Enter user name"
                                                    className="w-full"
                                                    error={newUserErrors.name}
                                                />
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Email
                                                </label>
                                                <Input
                                                    value={newUserData.email}
                                                    onChange={(e) =>
                                                        setNewUserData(
                                                            "email",
                                                            e.target.value
                                                        )
                                                    }
                                                    placeholder="User email"
                                                    className="w-full"
                                                    error={newUserErrors.email}
                                                />
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Phone
                                                </label>
                                                <Input
                                                    value={newUserData.phone}
                                                    onChange={(e) =>
                                                        setNewUserData(
                                                            "phone",
                                                            e.target.value
                                                        )
                                                    }
                                                    placeholder="User phone"
                                                    className="w-full"
                                                    error={newUserErrors.phone}
                                                />
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Password
                                                </label>
                                                <Input
                                                    value={newUserData.password}
                                                    onChange={(e) =>
                                                        setNewUserData(
                                                            "password",
                                                            e.target.value
                                                        )
                                                    }
                                                    placeholder="Password"
                                                    className="w-full"
                                                    error={
                                                        newUserErrors.password
                                                    }
                                                />
                                            </div>

                                            <div className="flex gap-2 justify-end">
                                                <Button
                                                    type="button"
                                                    className="text-md px-5 py-2 hidden"
                                                    onClick={handleAutoGenerate}
                                                    disabled={!newUserData.name}
                                                >
                                                    Auto Generate
                                                </Button>
                                                <Button
                                                    type="button"
                                                    className="text-md px-5 py-2"
                                                    onClick={
                                                        handleCreateNewUser
                                                    }
                                                    disabled={
                                                        processingNewUser ||
                                                        !newUserData.name
                                                    }
                                                >
                                                    {processingNewUser
                                                        ? "Creating..."
                                                        : "Create User"}
                                                </Button>
                                            </div>
                                        </div>
                                    )}

                                    {/* Selected User Display */}
                                    {data.assigned_user_id && (
                                        <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                                            <div className="text-sm font-medium text-green-800">
                                                Selected User:
                                            </div>
                                            <div className="text-sm text-green-700">
                                                {users.find(
                                                    (u) =>
                                                        u.id ==
                                                        data.assigned_user_id
                                                )?.name || "New User"}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    );
}
