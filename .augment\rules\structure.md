---
type: "always_apply"
---

`newdigitalk` workspace is working project that we will update. Do not update anything on `web` workspace project. Take reference from `newdigitalk` workspace project.

Migrations are no aligned with models code in `newdigitalk` workspace. So, create migration based on models and it's relationships.

Whenever needed install composer or npm packages in `web` workspace project, if needed take reference from `newdigitalk` workspace project. Make sure we are using latest version of packages.

If you feel `newdigitalk` workspace project is not using proper method anywhere and we can improve `web` workspace project please update and craete a .md file with explaination.

Please keep new updated Ract.js design and keep using react js in `web` workspace project. We are not carryforward vue js in `web` workspace project which we are using in `newdigitalk` workspace project.