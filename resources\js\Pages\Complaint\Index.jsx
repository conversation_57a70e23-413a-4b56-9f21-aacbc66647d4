import React, {useState, useEffect} from 'react';
import DashboardLayout from '../../Layouts/DashboardLayout';
import Button from '../../Components/Button';
import {Link} from '@inertiajs/react';
import {
    DataTable,
    DataTableHeader,
    DataTableBody,
    DataTableRow,
    DataTableHeaderCell,
    DataTableCell,
    DataTableActions,
    PageHeader,
    StatusBadge,
    ActionButton
} from '@/Components/UI';
import {FiEye, FiEdit2, FiTrash2, FiPlus} from 'react-icons/fi';
import {TableSkeleton} from '@/Components/SkeletonLoader';
import HeaderStats from '@/Components/HeaderStats';
import Pagination from '@/Components/Pagination';
import ComplaintFilters from '@/Components/ComplaintFilters';
import axios from 'axios';
import toast from 'react-hot-toast';

// Get route function from global scope
const route = window.route;

export default function Index() {
    const [complaints, setComplaints] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        current_page: 1,
        last_page: 1,
        per_page: 10,
        total: 0,
        from: 0,
        to: 0,
        has_more_pages: false,
    });
    const [filters, setFilters] = useState({
        priority: '',
        status: '',
        company_id: '',
        assigned_user_id: '',
    });
    const [filterOptions, setFilterOptions] = useState({
        priorities: [],
        statuses: [],
        companies: [],
        users: [],
    });
    const [filtersLoading, setFiltersLoading] = useState(true);
    const [searchValue, setSearchValue] = useState('');
    const [stats, setStats] = useState({
        total: 0,
        open: 0,
        new: 0,
        in_progress: 0,
        resolved: 0,
    });

    useEffect(() => {
        fetchComplaints();
        fetchFilterOptions();
    }, []);

    useEffect(() => {
        fetchComplaints(1, pagination.per_page);
    }, [filters, searchValue]);

    const fetchComplaints = async (page = 1, perPage = pagination.per_page) => {
        try {
            setLoading(true);
            const params = {
                page: page,
                per_page: perPage,
                search: searchValue,
                ...filters
            };

            const response = await axios.get('/api/complains/unresolved', {params});
            setComplaints(response.data.complaints);
            setPagination(response.data.pagination);
            setStats(response.data.stats || {
                total: 0,
                open: 0,
                new: 0,
                in_progress: 0,
                resolved: 0,
            });
            setError(null);
        } catch (err) {
            setError('Failed to load complaints. Please try again.');
            console.error('Error fetching complaints:', err);
        } finally {
            setLoading(false);
        }
    };

    const fetchFilterOptions = async () => {
        try {
            setFiltersLoading(true);
            const response = await axios.get('/api/complains-filters');
            setFilterOptions(response.data);
        } catch (err) {
            console.error('Error fetching filter options:', err);
        } finally {
            setFiltersLoading(false);
        }
    };

    const handlePageChange = (page) => {
        fetchComplaints(page, pagination.per_page);
    };

    const handlePerPageChange = (perPage) => {
        fetchComplaints(1, perPage);
    };

    const handleFilterChange = (newFilters) => {
        setFilters(newFilters);
    };

    const handleClearFilters = () => {
        setFilters({
            priority: '',
            status: '',
            company_id: '',
            assigned_user_id: '',
        });
    };

    const handleSearchChange = (value) => {
        setSearchValue(value);
    };

    const handleSearchClear = () => {
        setSearchValue('');
    };

    const getStatusVariant = (status) => {
        switch (status.toLowerCase()) {
            case 'resolved':
                return 'success';
            case 'in progress':
                return 'warning';
            case 'new':
                return 'secondary';
            case 'need user input':
                return 'warning';
            default:
                return 'secondary';
        }
    };

    const getPriorityVariant = (priority) => {
        switch (priority.toLowerCase()) {
            case 'high':
                return 'danger';
            case 'medium':
                return 'warning';
            case 'low':
                return 'secondary';
            default:
                return 'secondary';
        }
    };

    const handleDelete = async (complaintId) => {
        // Show confirmation toast
        toast((t) => (
            <div className="flex items-center gap-3">
                <div className="flex-1">
                    <p className="font-medium text-gray-900">Delete Complaint</p>
                    <p className="text-sm text-gray-600">Are you sure you want to delete this complaint? This action
                        cannot be undone.</p>
                </div>
                <div className="flex gap-2">
                    <Button
                        variant="danger"
                        onClick={() => {
                            toast.dismiss(t.id);
                            confirmDelete(complaintId);
                        }}
                    >
                        Delete
                    </Button>
                    <Button
                        variant="secondary"
                        className="text-gray-900 hover:text-gray-950"
                        onClick={() => toast.dismiss(t.id)}
                    >
                        Cancel
                    </Button>
                </div>
            </div>
        ), {
            duration: 10000,
            style: {
                background: '#fff',
                color: '#000',
                maxWidth: '500px',
            },
        });
    };

    const confirmDelete = async (complaintId) => {
        try {
            const response = await axios.delete(route('complains.destroy', complaintId), {
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                }
            });

            if (response.data.success) {
                toast.success('Complaint deleted successfully');
                // Refresh the complaints list
                fetchComplaints(pagination.current_page, pagination.per_page);
            } else {
                toast.error('Failed to delete complaint');
            }
        } catch (error) {
            console.error('Delete error:', error);
            toast.error('Failed to delete complaint');
        }
    };

    return (
        <DashboardLayout title="Complaints">
            <title>Complaints</title>

            <div className="space-y-6">
                {/* Header Stats - Show skeleton only for dynamic numbers */}
                <HeaderStats
                    title="Complaint Management"
                    subtitle="Track and resolve customer complaints efficiently"
                    buttonText="New Complaint"
                    buttonLink={route('complains.create')}
                    stats={[
                        {label: 'Total Complaints', value: stats.total, loading: loading},
                        {label: 'New', value: stats.new, loading: loading},
                        {label: 'In Progress', value: stats.in_progress, loading: loading},
                        {label: 'Resolved', value: stats.resolved, loading: loading}
                    ]}
                    loading={loading}
                />

                {/* Page Header - Always visible (static content) */}
                <PageHeader
                    title="Open Complaints"
                    subtitle="View and manage open customer complaints"
                >
                </PageHeader>

                {/* Filters */}
                <ComplaintFilters
                    filters={filters}
                    onFilterChange={handleFilterChange}
                    onClearFilters={handleClearFilters}
                    filterOptions={filterOptions}
                    loading={filtersLoading}
                    searchValue={searchValue}
                    onSearchChange={handleSearchChange}
                    onSearchClear={handleSearchClear}
                />

                {loading ? (
                    <div className="bg-white rounded-lg shadow overflow-hidden">
                        <div className="overflow-x-auto">
                            <TableSkeleton
                                rows={10}
                                columns={8}
                                headers={['ID', 'Title', 'Company', 'Assigned User', 'Priority', 'Status', 'Created', 'Actions']}
                            />
                        </div>
                    </div>
                ) : error ? (
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="text-center py-8">
                            <div className="text-red-500 mb-4">
                                <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor"
                                     strokeWidth="1" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round"
                                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                </svg>
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Complaints</h3>
                            <p className="text-gray-500 mb-4">{error}</p>
                            <Button
                                onClick={fetchComplaints}
                                className="bg-cf-primary hover:bg-cf-primary-600 text-white"
                            >
                                Try Again
                            </Button>
                        </div>
                    </div>
                ) : (
                    <div className="bg-white rounded-lg shadow overflow-hidden">
                        <div className="overflow-x-auto">
                            <DataTable>
                                <DataTableHeader>
                                    <DataTableRow>
                                        <DataTableHeaderCell className="min-w-[100px]">ID</DataTableHeaderCell>
                                        <DataTableHeaderCell className="min-w-[200px]">Title</DataTableHeaderCell>
                                        <DataTableHeaderCell className="min-w-[150px]">Company</DataTableHeaderCell>
                                        <DataTableHeaderCell className="min-w-[150px]">Assigned User</DataTableHeaderCell>
                                        <DataTableHeaderCell className="min-w-[100px]">Priority</DataTableHeaderCell>
                                        <DataTableHeaderCell className="min-w-[100px]">Status</DataTableHeaderCell>
                                        <DataTableHeaderCell className="min-w-[100px]">Created</DataTableHeaderCell>
                                        <DataTableHeaderCell className="min-w-[120px]">Actions</DataTableHeaderCell>
                                    </DataTableRow>
                                </DataTableHeader>
                                <DataTableBody>
                                    {complaints.map((complaint) => (
                                        <DataTableRow key={complaint.id}>
                                            <DataTableCell className="font-medium">
                                                {complaint.cmp_id || `#${complaint.id}`}
                                            </DataTableCell>
                                            <DataTableCell className="font-medium text-gray-900">
                                                <div className="max-w-xs">
                                                    <p className="truncate" title={complaint.title}>
                                                        {complaint.title}
                                                    </p>
                                                </div>
                                            </DataTableCell>
                                            <DataTableCell>
                                                <div className="max-w-xs">
                                                    <p className="truncate" title={complaint.company}>
                                                        {complaint.company || 'N/A'}
                                                    </p>
                                                </div>
                                            </DataTableCell>
                                            <DataTableCell>
                                                <div className="max-w-xs">
                                                    <p className="truncate" title={complaint.assignedUser}>
                                                        {complaint.assignedUser || 'N/A'}
                                                    </p>
                                                </div>
                                            </DataTableCell>
                                            <DataTableCell>
                                                <StatusBadge variant={getPriorityVariant(complaint.priority)}>
                                                    {complaint.priority}
                                                </StatusBadge>
                                            </DataTableCell>
                                            <DataTableCell>
                                                <StatusBadge variant={getStatusVariant(complaint.status)}>
                                                    {complaint.status}
                                                </StatusBadge>
                                            </DataTableCell>
                                            <DataTableCell className="text-gray-500">
                                                {new Date(complaint.created_at).toLocaleDateString()}
                                            </DataTableCell>
                                            <DataTableActions>
                                                <Link href={route('complains.show', complaint.cmp_id || complaint.id)}>
                                                    <ActionButton variant="primary" size="sm" title="View Details">
                                                        <FiEye className="w-4 h-4"/>
                                                    </ActionButton>
                                                </Link>
                                                {complaint.status !== 'Resolved' && (
                                                    <Link href={route('complains.edit', complaint.cmp_id || complaint.id)}>
                                                        <ActionButton variant="secondary" size="sm" title="Edit">
                                                            <FiEdit2 className="w-4 h-4"/>
                                                        </ActionButton>
                                                    </Link>
                                                )}
                                                
                                                <ActionButton
                                                    variant="danger"
                                                    size="sm"
                                                    title="Delete"
                                                    onClick={() => handleDelete(complaint.cmp_id || complaint.id)}
                                                >
                                                    <FiTrash2 className="w-4 h-4"/>
                                                </ActionButton>
                                            </DataTableActions>
                                        </DataTableRow>
                                    ))}
                                </DataTableBody>
                            </DataTable>

                            {/* Pagination */}
                            {complaints.length > 0 && (
                                <Pagination
                                    currentPage={pagination.current_page}
                                    lastPage={pagination.last_page}
                                    total={pagination.total}
                                    perPage={pagination.per_page}
                                    from={pagination.from}
                                    to={pagination.to}
                                    onPageChange={handlePageChange}
                                    onPerPageChange={handlePerPageChange}
                                />
                            )}
                        </div>
                    </div>
                )}

                {/* Empty State */}
                {!loading && !error && complaints.length === 0 && (
                    <div className="bg-white rounded-lg shadow">
                        <div className="text-center py-12">
                            <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor"
                                 strokeWidth="1" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round"
                                      d="M9 17v-2a4 4 0 018 0v2m-4-4V7a4 4 0 10-8 0v6m4 4v2a4 4 0 01-8 0v-2m4 4v2a4 4 0 008 0v-2"/>
                            </svg>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">No complaints found</h3>
                            <p className="text-gray-500 mb-4">Get started by creating your first complaint.</p>
                        </div>
                    </div>
                )}
            </div>
        </DashboardLayout>
    );
}
