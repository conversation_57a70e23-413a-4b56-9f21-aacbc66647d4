<?php

namespace App\Http\Controllers;

use App\Models\Company;
use App\Models\Deals;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class DealsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {

        if (Auth::user()->profileType == 'admin') {

            $companyIds = Company::pluck('id')->toArray();
            $deals      = Deals::whereIn('companyId', $companyIds)->join('company', 'company.id', '=', 'deals.companyId')->select('deals.*', 'company.companyName')->orderBy('deals.id', 'desc')->paginate(env('PAGE_LIMIT'));

        } else {
            $companyIds = Company::where('userId', Auth::id())->pluck('id')->toArray();
            $deals      = Deals::whereIn('companyId', $companyIds)->join('company', 'company.id', '=', 'deals.companyId')->select('deals.*', 'company.companyName')->orderBy('deals.id', 'desc')->paginate(env('PAGE_LIMIT'));
        }

        $companyList = Company::all();

        $title     = 'Deals';
        $actionBtn = ['title' => 'Add Deal', 'route' => route('deal.add')];

        return Inertia::render('Deals/Deals', ['deals' => $deals, 'companyList' => $companyList, 'title' => $title, 'actionBtn' => $actionBtn]);
    }

    public function view($id)
    {

        $dealDetail = Deals::join('company', 'company.id', 'deals.companyId')
            ->join('users', 'users.id', '=', 'company.userId')
            ->where('deals.id', $id)
            ->select('deals.*', 'company.companyName', 'users.email', 'users.name', 'users.phone', 'users.profilePicture')
            ->first();
        if ($dealDetail) {
            $title = 'Deal Details';

            return Inertia::render('Deals/DealsDetails', ['dealDetail' => $dealDetail, 'title' => $title]);
        } else {
            return redirect()->back();
        }

    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $companyList = Company::all();
        $title       = 'Add Deal';

        return Inertia::render('Deals/Create', ['companyList' => $companyList, 'title' => $title]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $customMessages = [
            'required'       => 'The :attribute field is required.',
            'after_or_equal' => 'The :attribute must be a date after or equal to the :date.',
            'url'            => 'The :attribute must be a valid URL.',
        ];

        $commonRules = [
            'deal_title'       => 'required',
            'deal_link'        => 'required|url',
            'deal_description' => 'required',
            'start_date'       => 'required|date|after_or_equal:today',
            'end_date'         => 'required|date|after_or_equal:start_date',
        ];

        if (Auth::user()->profileType == 'admin') {
            $commonRules['company'] = 'required';
        }

        $validatedData = $request->validate($commonRules, $customMessages);

        $DealFile = '';
        if ($request->hasFile('dealImage')) {

            $customMessages = [
                'required' => 'The :attribute field is required.',
                'mimes'    => 'The :attribute must be a file of type: jpg, png, jpeg, or gif.',
            ];

            $commonRules = [
                'dealImage' => 'required|mimes:jpg,png,jpeg,gif',
            ];

            $validatedData = $request->validate($commonRules, $customMessages);

            try {
                $image       = $request->file('dealImage');
                $folder_name = 'images/deals';
                $DealFile    = $image->store($folder_name, 'public');

            } catch (Exception $ex) {

                return Redirect::back()->with([
                    'status'  => false,
                    'message' => $ex->getMessage(),
                    'class'   => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
                ]);

            }
        }

        if (Auth::user()->profileType == 'admin') {

            $companyQry = Company::where('id', $request->company)->first();

        } else {

            $companyUseriD = Auth::id();
            $companyQry    = Company::where('userId', $companyUseriD)->first();
        }

        if ($companyQry) {
            $saveData                = new Deals;
            $saveData->companyId     = $companyQry->id;
            $saveData->deal_file     = $DealFile;
            $saveData->dealTitle     = $request->deal_title;
            $saveData->deal_link     = $request->deal_link;
            $saveData->dealContent   = $request->deal_description;
            $saveData->dealStartOn   = $request->start_date;
            $saveData->dealExpiresOn = $request->end_date;
            $saveData->save();

            Artisan::call('deals:update-status');

            return Redirect::back()->with([
                'status'  => true,
                'message' => 'The deal has been saved successfully.',
                'class'   => 'flex items-center p-2 mb-4 text-green-700 rounded-md bg-green-100',
            ]);

        } else {

            return Redirect::back()->with([
                'status'  => false,
                'message' => 'Invalid request to add deal',
                'class'   => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
            ]);

        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $companyList = Company::all();
        $dealDetail  = Deals::find($id);
        if (! $dealDetail) {
            return redirect()->back();
        }
        $title = 'Edit Deal';

        return Inertia::render('Deals/Edit', ['companyList' => $companyList, 'dealDetail' => $dealDetail, 'title' => $title]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {

        $customMessages = [
            'required'       => 'The :attribute field is required.',
            'after_or_equal' => 'The :attribute must be a date after or equal to the :date.',
            'in'             => 'The :attribute field must be pending, start, or end.',
            'url'            => 'The :attribute must be a valid URL.',

        ];

        $commonRules = [
            'deal_title'       => 'required',
            'deal_description' => 'required',
            'deal_link'        => 'required|url',
            'start_date'       => 'required|date|after_or_equal:today',
            'end_date'         => 'required|date|after_or_equal:start_date',
        ];

        if (Auth::user()->profileType == 'admin') {
            $commonRules['company'] = 'required';
        }

        $validatedData = $request->validate($commonRules, $customMessages);

        $saveData = Deals::find($request->deal_id);

        if ($saveData) {
        } else {

            return Redirect::back()->with([
                'status'  => false,
                'message' => 'Invalid request to add deal',
                'class'   => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
            ]);

        }

        $DealFile = $saveData->deal_file;
        if ($request->hasFile('dealImage')) {

            $customMessages = [
                'required' => 'The :attribute field is required.',
                'mimes'    => 'The :attribute must be a file of type: jpg, png, jpeg, or gif.',
            ];

            $commonRules = [
                'dealImage' => 'required|mimes:jpg,png,jpeg,gif',
            ];

            $validatedData = $request->validate($commonRules, $customMessages);

            try {

                $image       = $request->file('dealImage');
                $folder_name = 'images/deals';
                $DealFile    = $image->store($folder_name, 'public');

            } catch (Exception $ex) {

                return Redirect::back()->with([
                    'status'  => false,
                    'message' => $ex->getMessage(),
                    'class'   => 'text-sm text-red-700 ',
                ]);
            }

            if ($saveData->deal_file != '') {
                Storage::disk('public')->delete($saveData->deal_file);
            }

        }

        if (Auth::user()->profileType == 'admin') {

            $companyQry = Company::where('id', $request->company)->first();

        } else {

            $companyUseriD = Auth::id();
            $companyQry    = Company::where('userId', $companyUseriD)->first();
        }

        if ($companyQry) {

            $saveData->companyId     = $companyQry->id;
            $saveData->deal_file     = $DealFile;
            $saveData->dealTitle     = $request->deal_title;
            $saveData->deal_link     = $request->deal_link;
            $saveData->dealContent   = $request->deal_description;
            $saveData->dealStartOn   = $request->start_date;
            $saveData->dealExpiresOn = $request->end_date;
            $saveData->save();

            Artisan::call('deals:update-status');

            return Redirect::back()->with([
                'status'  => true,
                'message' => 'The deal has been saved successfully.',
                'class'   => 'flex items-center p-2 mb-4 text-green-700 rounded-md bg-green-100',
            ]);

        } else {

            return Redirect::back()->with([
                'status'  => false,
                'message' => 'Invalid request to add deal',
                'class'   => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
            ]);

        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $itemDeta = Deals::where('id', $id)->first();
        if ($itemDeta) {

            if ($itemDeta->deal_file != '') {
                Storage::disk('public')->delete($itemDeta->deal_file);
            }

            $itemDeta->delete();

            return response()->json(['success' => true]);
        }

        return response()->json(['success' => true]);
    }
}
