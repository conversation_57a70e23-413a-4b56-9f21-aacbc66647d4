<script setup>
import ResponsiveNavLink from '../ResponsiveNavLink.vue';

const props = defineProps(['showModal', 'isOpen']);

const isActive = (path) => {
	// Get the current URL path
	const currentPath = window.location.pathname;
	// Check if the current path matches the specified path
	return currentPath === path;
};

</script>

<style lang="scss" scoped></style>

<template>
	<!-- Header links -->
	<div :class="isOpen ? 'block' : 'hidden'"
		class="sm:flex justify-center items-center sm:shadow-none m-0 sm:mt-2 sm:ml-4 p-5 sm:p-0">
		<ResponsiveNavLink :href="route('home')"
			:class="['font-general-medium block text-left text-base font-medium', isActive('/') ? 'text-cfp-500' : 'text-cfp-500-dark ', 'hover:text-cfp-500  sm:mx-4 mb-2 sm:py-2']"
			aria-label="Projects">Home</ResponsiveNavLink>

		<ResponsiveNavLink :href="route('front-companies')"
			:class="['font-general-medium block text-left text-base font-medium', isActive('/companies') ? 'text-cfp-500' : 'text-cfp-500-dark ', 'hover:text-cfp-500  sm:mx-4 mb-2 sm:py-2']"
			aria-label="Companies">Companies</ResponsiveNavLink>
		<ResponsiveNavLink :href="route('front-deals')"
			:class="['font-general-medium block text-left text-base font-medium', isActive('/deals') ? 'text-cfp-500' : 'text-cfp-500-dark ', 'hover:text-cfp-500  sm:mx-4 mb-2 sm:py-2']"
			aria-label="Deals">Deals</ResponsiveNavLink>
		<ResponsiveNavLink :href="route('contact')"
			:class="['font-general-medium block text-left text-base font-medium', isActive('/contact') ? 'text-cfp-500' : 'text-cfp-500-dark ', 'hover:text-cfp-500  sm:mx-4 mb-2 sm:py-2']"
			aria-label="Contact">Contact</ResponsiveNavLink>
		<ResponsiveNavLink :href="route('user-login')"
			:class="['font-general-medium hidden text-left text-base font-medium  max-md:block', isActive('/login') ? 'text-cfp-500' : 'text-cfp-500-dark ', 'hover:text-cfp-500  sm:mx-4 mb-2 sm:py-2']"
			aria-label="Contact">Login</ResponsiveNavLink>

	</div>
</template>