<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, Link, useForm, usePage } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import InputLabel from '@/Components/InputLabel.vue';
import SelectInput from '@/Components/SelectInput.vue';
import FileInput from '@/Components/FileInput.vue';
import { Inertia } from '@inertiajs/inertia';
import PageHeading from '@/Components/Global/PageHeading.vue';

const pageTitle = ref('Edit User');

const { userDetail } = usePage().props;
var baseurl = window.location.origin;

const form = useForm({
    user_id: userDetail.id,
    name: userDetail.name,
    company_name: userDetail.company_name,
    phone: userDetail.phone,
    verificationStatus: userDetail.verificationStatus,
    status: userDetail.status,
    profilePicture: null, // Change to null to handle file uploads correctly
});

const onFileChange = (event) => {
    const file = event.target.files[0];
    form.profilePicture = file;
};

const submitForm = () => {
    form.post(route('manage-users.update', { id: userDetail.id }), {
        onSuccess: (response) => {
            // Update the userDetail.profilePicture with the new path from the response
            if (form.profilePicture) {
                userDetail.profilePicture = response.props.userDetail.profilePicture;
            }
            // Clear the file input
            const fileInput = document.getElementById('profilePicture');
            if (fileInput) {
                fileInput.value = '';
            }
            form.profilePicture = null;
        },
        onError: (errors) => {
            // Handle errors
        }
    });
};
</script>

<template>
    <Head :title="pageTitle" />

    <AuthenticatedLayout>
        <form @submit.prevent="submitForm" enctype="multipart/form-data" class="form-main-body">

            
            <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
                leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
                <p v-if="form.recentlySuccessful" enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
                    leave-active-class="transition ease-in-out" leave-to-class="opacity-0" :class="$page.props.flash?.class">
                    {{ $page.props.flash.message }}
                </p>
            </Transition>

            <div class="add-form-main-bcls">
                <div class="flex flex-col gap-4 col-span-12 pr-5 xl:pb-1">
                    <div class="p-6">
                        <div class="mt-2 mb-4">
                            <InputLabel for="profilePicture" value="Upload Image" />
                            <FileInput id="profilePicture" type="file" class="" @input="onFileChange"
                                autocomplete="profilePicture" />
                            <InputError class="mt-2" :message="form.errors.profilePicture" />

                            <div class="my-2" v-if="userDetail.profilePicture">
                                <img 
                                    :key="userDetail.profilePicture" 
                                    :src="baseurl + '/storage/' + userDetail.profilePicture" 
                                    alt="Profile" 
                                    class="rounded-full w-15 h-15" 
                                />
                            </div>
                        </div>

                        <div class="mt-2 mb-4">
                            <InputLabel for="name" value="Name" />
                            <TextInput id="name" name="name" type="text" placeholder="Name" class="block mt-1 w-full"
                                v-model="form.name" />
                            <InputError :message="form.errors.name" class="mt-2" />
                        </div>

                        <div v-if="userDetail.profileType == 'company'" class="mt-2 mb-4">
                            <InputLabel for="company_name" value="Company Name" />
                            <TextInput id="company_name" name="company_name" type="text" placeholder="Company Name"
                                class="block mt-1 w-full" v-model="form.company_name" />
                            <InputError :message="form.errors.company_name" class="mt-2" />
                        </div>

                        <div class="mt-2 mb-4">
                            <InputLabel for="phone" value="Phone" />
                            <TextInput id="phone" name="phone" type="text" placeholder="Phone" class="block mt-1 w-full"
                                v-model="form.phone" />
                            <InputError :message="form.errors.phone" class="mt-2" />
                        </div>

                        <div class="mt-2 mb-4">
                            <InputLabel for="verificationStatus" value="Verification Status" />
                            <SelectInput id="verificationStatus" class="block mt-1 w-full" v-model="form.verificationStatus"
                                autocomplete="verificationStatus" name="verificationStatus">
                                <option value="not_verified" :selected="form.verificationStatus === 'not_verified'">Not Verified</option>
                                <option value="verified" :selected="form.verificationStatus === 'verified'">Verified</option>
                            </SelectInput>
                            <InputError class="mt-2" :message="form.errors.verificationStatus" />
                        </div>

                        <div class="mt-2 mb-4">
                            <InputLabel for="status" value="Status" />
                            <SelectInput id="status" class="block mt-1 w-full" v-model="form.status" autocomplete="status"
                                name="status">
                                <option value="inactive" :selected="form.status === 'inactive'">Inactive</option>
                                <option value="active" :selected="form.status === 'active'">Active</option>
                            </SelectInput>
                            <InputError class="mt-2" :message="form.errors.status" />
                        </div>

                        <button class="dk-update-btn">
                            Update
                        </button>

                        <ResponsiveNavLink :href="route('manage-users')" class="dk-cancle-btn">
                            Cancel
                        </ResponsiveNavLink>
                    </div>
                </div>
            </div>
        </form>
    </AuthenticatedLayout>
</template>