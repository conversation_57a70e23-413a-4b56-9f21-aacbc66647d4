<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}" />

    <title inertia>{{ config('app.name', 'Laravel') }}</title>

    <!-- Favicon -->
    <link rel="icon" href="{{ asset('favicon.ico') }}" type="image/x-icon">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="{{ asset('js/sweetalerts2/sweetalerts2.css') }}">
    <script src="{{ asset('js/sweetalerts2/sweetalerts2.min.js') }}"></script>


    <!-- Scripts -->
    @routes
    @vite(['resources/js/app.js', "resources/js/Pages/{$page['component']}.vue"])
    @inertiaHead
</head>

<body class="font-sans antialiased bg-gray-10">
    @inertia

    {{-- @include('chat') --}}

    {{-- <script src="https://chatfil.com/dialogflow-chat.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const chat = new DialogflowChat({
                baseUrl: 'https://chatfil.com',
                companyName: 'Your Company',
                primaryColor: '#337E81',
                botAvatar: '/images/chat-icon.png',
                endpoint: 'https://your-api-endpoint.com/chat'
            });
        });
    </script> --}}

</body>

</html>
