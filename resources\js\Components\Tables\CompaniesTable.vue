<script setup>
import { ref } from 'vue';
const props = defineProps(['CompaniesList']);
let Company = props.CompaniesList;
import { Link } from '@inertiajs/vue3';
var baseurl = window.location.origin;
var adminBaseurl = window.location.origin + '/dtadmin';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import PageHeading from '../Global/PageHeading.vue';
import DtIconGlobal from '../DtIcon/DtIconGlobal.vue';

const pageTitle = ref('All Companies')

const fixUrl = (url) => {
  // Check if the url starts with http:// or https://
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    // If not, add the default prefix (e.g., http://)
    return 'http://' + url;
  }
  return url;
}

const deleteItem = async (item) => {
  try {
    // Show a SweetAlert confirmation popup
    const confirmation = await Swal.fire({
      title: 'Are you sure?',
      text: 'You are about to delete this company address. Are you sure you want to proceed?',
      icon: 'warning',
      showCancelButton: true,

      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'Cancel',
      customClass: {
        confirmButton: 'dk-update-btn',
        cancelButton: 'dk-cancle-btn',
      },
      buttonsStyling: false,
    });

    // If user confirms deletion
    if (confirmation.isConfirmed) {
      const response = await axios.delete(route('companies.delete', { id: item.id }));
      if (response.data.success) {
        // Remove the deleted item from the allitems array
        const index = Company.data.findIndex(a => a.id === item.id);
        if (index !== -1) {
          Company.data.splice(index, 1);
        }
        // Show success message
        //Swal.fire('Deleted!', 'The item has been deleted.', 'success');
      } else {
        throw new Error('Failed to delete item');
      }
    }
  } catch (error) {
    console.error(error);
    // Show error message if deletion fails
    Swal.fire('Error', 'An error occurred while deleting the item.', 'error');
  }
};

</script>

<template>
  <!-- <PageHeading :title="pageTitle" :href="route('companies.add')" buttonLabel="Add Company"></PageHeading> -->

  <div class="border-stroke p-6 border rounded-md">


    <div class="max-w-full overflow-x-auto">
      <!-- Table Header Section -->
      <!-- <div class="dt-deals-header" v-if="$page.props.auth.user.profileType == 'company'">
        <h3 class="dt-deals-title">Address List </h3>
      </div> -->

      <table class="dt-deals-table">
        <thead>
          <tr>
            <th class="hidden dt-deals-th">
              <div class="flex items-center gap-4">
                <label :for="'checkboxAll-data'" class="flex items-center font-medium cursor-pointer select-none">
                  <div class="relative">
                    <input type="checkbox" :id="'checkboxAll-data'" class="sr-only tableCheckbox">
                    <div
                      class="flex justify-center items-center border-[.5px] border-stroke bg-gray-2 rounded-[3px] w-5 h-5 text-white box">
                      <span class="opacity-0">
                        <svg width="14" height="14" viewBox="0 0 10 10">
                          <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M8.62796 2.20602C8.79068 2.36874 8.79068 2.63256 8.62796 2.79528L4.04463 7.37861C3.88191 7.54133 3.61809 7.54133 3.45537 7.37861L1.37204 5.29528C1.20932 5.13256 1.20932 4.86874 1.37204 4.70602C1.53476 4.5433 1.79858 4.5433 1.96129 4.70602L3.75 6.49473L8.03871 2.20602C8.20142 2.0433 8.46524 2.0433 8.62796 2.20602Z"
                            fill="currentColor"></path>
                        </svg>
                      </span>
                    </div>
                  </div>
                </label>
              </div>
            </th>
            <th class="dt-deals-th">
              Logo
            </th>
            <th class="dt-deals-th">
              Name
            </th>
            <th class="dt-deals-th">
              Category
            </th>
            <th class="dt-deals-th">
              Website
            </th>
            <th class="dt-deals-th">
              Address
            </th>
            <th class="dt-deals-th">
              Action
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(brand, key) in Company.data" :key="key" class="dt-deals-tr">

            <td class="dt-deals-td">
              <img v-if="brand.profilePicture && brand.profilePicture.startsWith('http')" :src="brand.profilePicture"
                alt="User" class="rounded-full w-15" />
              <img v-else-if="brand.profilePicture" :src="baseurl + '/storage/' + brand.profilePicture" alt="User"
                class="rounded-full w-15" />
              <img v-else src="@/assets/images/user/user-profile.jpeg" alt="User" class="rounded-full w-15" />
            </td>
            <td class="dt-deals-td">
              <p>{{ brand.companyName }}</p>
            </td>
            <td class="dt-deals-td">
              <p>{{ brand.catName }}</p>
            </td>
            <td class="dt-deals-td">
              <a :href="fixUrl(brand.websiteUrl)" target="_blank" class="dt-deals-title-link">Visit</a>
            </td>
            <td class="dt-deals-td">
              <p>{{ brand.companyAdd }}, {{ brand.city }}, {{ brand.state }}, {{ brand.zipCode }}
              </p>
            </td>
            <td class="dt-deals-td">
              <div class="dt-deals-actions">
                <Link :href="adminBaseurl + `/companies/edit/${brand.id}`" class="dt-deals-action-btn">
                <DtIconGlobal :type="'edit'" />
                </Link>
                <button class="dt-deals-action-btn dt-deals-delete-btn" @click="deleteItem(brand)">
                  <DtIconGlobal :type="'delete'" />
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <div class="dt-table-pagi">
        <div class="content-center row-span-2 row-start-2">
          Showing <b>{{ (Company.current_page - 1) * Company.per_page + 1 }}</b>-
          <b>{{ Math.min(Company.current_page * Company.per_page, Company.total) }}</b>
          from <b>{{ Company.total }}</b> data
        </div>
        <div class="row-start-2 row-end-4 text-end">
          <div class="pagination-links">
            <ul class="flex justify-items-end place-content-end">
              <li v-for="page in Company.links" :key="page.url">
                <button @click="$inertia.visit(page.url)"
                  :class="{ 'bg-cfp-500 text-white': page.active, 'hover:bg-cfp-500 hover:text-white': !page.active }"
                  class="px-3 py-1 rounded-full focus:outline-none mx-1" v-html="page.label"></button>
              </li>
            </ul>
          </div>

        </div>
      </div>

    </div>
  </div>
</template>
