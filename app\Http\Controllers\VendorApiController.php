<?php

namespace App\Http\Controllers;

use App\Models\VendorApi;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class VendorApiController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $user = Auth::user();
        $query = VendorApi::with(['company', 'user', 'creator']);

        // Apply role-based filtering
        if ($user->profileType === 'admin') {
            // Admin can see all APIs
        } elseif ($user->profileType === 'company') {
            // Company users can only see their APIs
            $query->where('user_id', $user->id);
        } else {
            // Other users can only see their own APIs
            $query->where('user_id', $user->id);
        }

        // Apply search filter
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Apply status filter
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Apply method filter
        if ($request->filled('method')) {
            $query->byMethod($request->method);
        }

        // Apply company filter (for admin)
        if ($request->filled('company_id') && $user->profileType === 'admin') {
            $query->where('company_id', $request->company_id);
        }

        $apis = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get companies for filter dropdown (admin only)
        $companies = $user->profileType === 'admin' 
            ? Company::select('id', 'companyName')->get() 
            : collect();

        // Create default API if none exist
        if ($apis->isEmpty() && $user->profileType !== 'admin') {
            $userCompany = $user->profileType === 'company' 
                ? Company::where('userId', $user->id)->first() 
                : null;
            
            VendorApi::createDefaultApi($user->id, $userCompany?->id);
            
            // Reload APIs after creating default
            $apis = $query->orderBy('created_at', 'desc')->paginate(15);
        }

        return Inertia::render('Apis/Index', [
            'apis' => $apis,
            'companies' => $companies,
            'httpMethods' => VendorApi::getHttpMethods(),
            'filters' => $request->only(['search', 'status', 'method', 'company_id']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        $user = Auth::user();
        
        // Get user's company (if applicable)
        $company = $user->profileType === 'company' 
            ? Company::where('userId', $user->id)->first() 
            : null;

        return Inertia::render('Apis/Create', [
            'company' => $company,
            'httpMethods' => VendorApi::getHttpMethods(),
            'sampleRequestParams' => $this->getSampleRequestParams(),
            'sampleResponseParams' => $this->getSampleResponseParams(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'name' => [
                'required', 
                'string', 
                'max:255',
                'regex:/^[a-zA-Z0-9_@-]+$/',
                'unique:vendor_apis,name,NULL,id,user_id,' . $user->id,
            ],
            'url' => 'nullable|url|max:500',
            'method' => 'required|in:' . implode(',', VendorApi::getHttpMethods()),
            'description' => 'nullable|string',
            'request_parameters' => 'nullable|array',
            'request_parameters.*.label' => 'required|string|max:255',
            'request_parameters.*.parameter' => 'required|string|max:255',
            'request_parameters.*.type' => 'required|string|max:100',
            'response_parameters' => 'nullable|array',
            'response_parameters.*.label' => 'required|string|max:255',
            'response_parameters.*.parameter' => 'required|string|max:255',
            'response_parameters.*.type' => 'required|string|max:100',
            'headers' => 'nullable|array',
            'headers.*.key' => 'required|string|max:255',
            'headers.*.value' => 'required|string|max:500',
            'is_active' => 'boolean',
        ]);

        // Get user's company
        $company = $user->profileType === 'company' 
            ? Company::where('userId', $user->id)->first() 
            : null;

        $apiData = $request->only([
            'name', 'url', 'method', 'description', 'request_parameters', 
            'response_parameters', 'headers', 'is_active'
        ]);
        $apiData['user_id'] = $user->id;
        $apiData['company_id'] = $company?->id;
        $apiData['created_by'] = $user->id;

        VendorApi::create($apiData);

        return redirect()->route('apis.index')->with('success', 'API created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(VendorApi $api): Response
    {
        $user = Auth::user();

        // Check if user can view this API
        if ($user->profileType !== 'admin' && $api->user_id !== $user->id) {
            abort(403, 'Unauthorized access to API.');
        }

        $api->load(['company', 'user', 'creator']);

        return Inertia::render('Apis/Show', [
            'api' => $api,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(VendorApi $api): Response
    {
        $user = Auth::user();

        // Check if user can edit this API
        if ($user->profileType !== 'admin' && $api->user_id !== $user->id) {
            abort(403, 'Unauthorized access to API.');
        }

        return Inertia::render('Apis/Edit', [
            'api' => $api,
            'httpMethods' => VendorApi::getHttpMethods(),
            'sampleRequestParams' => $this->getSampleRequestParams(),
            'sampleResponseParams' => $this->getSampleResponseParams(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, VendorApi $api)
    {
        $user = Auth::user();

        // Check if user can update this API
        if ($user->profileType !== 'admin' && $api->user_id !== $user->id) {
            abort(403, 'Unauthorized access to API.');
        }

        $request->validate([
            'name' => [
                'required', 
                'string', 
                'max:255',
                'regex:/^[a-zA-Z0-9_@-]+$/',
                'unique:vendor_apis,name,' . $api->id . ',id,user_id,' . $api->user_id,
            ],
            'url' => 'nullable|url|max:500',
            'method' => 'required|in:' . implode(',', VendorApi::getHttpMethods()),
            'description' => 'nullable|string',
            'request_parameters' => 'nullable|array',
            'request_parameters.*.label' => 'required|string|max:255',
            'request_parameters.*.parameter' => 'required|string|max:255',
            'request_parameters.*.type' => 'required|string|max:100',
            'response_parameters' => 'nullable|array',
            'response_parameters.*.label' => 'required|string|max:255',
            'response_parameters.*.parameter' => 'required|string|max:255',
            'response_parameters.*.type' => 'required|string|max:100',
            'headers' => 'nullable|array',
            'headers.*.key' => 'required|string|max:255',
            'headers.*.value' => 'required|string|max:500',
            'is_active' => 'boolean',
        ]);

        $apiData = $request->only([
            'name', 'url', 'method', 'description', 'request_parameters', 
            'response_parameters', 'headers', 'is_active'
        ]);

        $api->update($apiData);

        return redirect()->route('apis.index')->with('success', 'API updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(VendorApi $api)
    {
        $user = Auth::user();

        // Check if user can delete this API
        if ($user->profileType !== 'admin' && $api->user_id !== $user->id) {
            abort(403, 'Unauthorized access to API.');
        }

        // Prevent deletion of default API
        if ($api->name === '@apiauth-get_api_token') {
            return response()->json([
                'success' => false, 
                'message' => 'Default API cannot be deleted.'
            ], 422);
        }

        $api->delete();

        return response()->json(['success' => true, 'message' => 'API deleted successfully.']);
    }

    /**
     * Toggle API status.
     */
    public function toggleStatus(VendorApi $api)
    {
        $user = Auth::user();

        // Check if user can toggle this API status
        if ($user->profileType !== 'admin' && $api->user_id !== $user->id) {
            abort(403, 'Unauthorized access to API.');
        }

        $api->update(['is_active' => !$api->is_active]);

        return response()->json([
            'success' => true, 
            'message' => 'API status updated successfully.',
            'status' => $api->is_active
        ]);
    }

    /**
     * Test API endpoint.
     */
    public function testApi(VendorApi $api)
    {
        $user = Auth::user();

        // Check if user can test this API
        if ($user->profileType !== 'admin' && $api->user_id !== $user->id) {
            abort(403, 'Unauthorized access to API.');
        }

        $result = $api->testEndpoint();

        return response()->json($result);
    }

    /**
     * Get sample request parameters.
     */
    private function getSampleRequestParams(): array
    {
        return [
            ['label' => 'Username', 'parameter' => 'username', 'type' => 'String'],
            ['label' => 'Password', 'parameter' => 'password', 'type' => 'String'],
        ];
    }

    /**
     * Get sample response parameters.
     */
    private function getSampleResponseParams(): array
    {
        return [
            ['label' => 'Token', 'parameter' => 'token', 'type' => 'String'],
            ['label' => 'Expiry', 'parameter' => 'expires_at', 'type' => 'String'],
        ];
    }
}
