import React, { Children, forwardRef } from 'react'
import clsx from 'clsx';

export const Form = ({ children, onSubmit }) => {
    return (
        <form className="space-y-6" onSubmit={onSubmit}>
            {children}
        </form>
    )
}

export const FormRow = ({ children, items = 1 }) => {
  return (
    <div className={`grid grid-cols-${items} gap-4`}>
        {children}
    </div>
  )
}

export const Select = forwardRef(({ label, name, value, onChange, error, className = '', placeholder, children, ...props }, ref) => {
    return (
        <div className="flex flex-col gap-1 font-medium text-gray-700 leading-11 min-h-11">
            {label &&
                <label htmlFor={name} className="text-sm">
                    {label}
                </label>
            }
            <select
                ref={ref}
                id={name}
                name={name}
                value={value}
                onChange={onChange}
                className={clsx(
                    'w-full px-4 py-1 border rounded-lg focus:outline-none focus:ring-2 focus:ring-cf-primary-300 transition h-[54px]',
                    error ? 'border-red-500' : 'border-gray-300',
                    className
                )}
                {...props}
            >
                {placeholder && (
                    <option value="" disabled hidden>
                        {placeholder}
                    </option>
                )}
                {children}
            </select>
            {error && (
                <div id={`${name}-error`} className="text-red-500 text-xs">
                    {error}
                </div>
            )}
        </div>
    );
});

export const Input = forwardRef(({ label, type = 'text', name, value, onChange, error, wrapperClass= '', className = '', as = 'input', rows, ...props }, ref) => {
    const Component = as;

    return (
        <div className={clsx(
            'flex flex-col gap-1 font-medium text-gray-700 leading-11 min-h-11',
            wrapperClass
        )}>
            {label &&
                <label htmlFor={name} className="text-sm">
                    {label}
                </label>
            }
            <Component
                ref={ref}
                id={name}
                name={name}
                type={as === 'input' ? type : undefined}
                value={value}
                onChange={onChange}
                rows={as === 'textarea' ? rows : undefined}
                className={clsx(
                    'w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-cf-primary-300 transition bg-white',
                    error ? 'border-red-500' : 'border-gray-300',
                    as === 'textarea' ? 'resize-vertical min-h-[100px]' : 'h-12',
                    className
                )}
                {...props}
            />

            {error && <div className="text-red-500 text-xs">{error}</div>}
        </div>
    );
});

export const Checkbox = forwardRef(({ label, name, value, onChange, error, wrapperClass= '', className = '', ...props }, ref) => {
    return (
        <div className={clsx(
            'flex items-center gap-2 font-medium text-gray-700',
            wrapperClass
        )}>
            <input
                ref={ref}
                id={name}
                name={name}
                type="checkbox"
                value={value}
                onChange={onChange}
                className={clsx(
                    'size-4 border rounded-lg focus:outline-none focus:ring-cf-primary-300 transition',
                    error ? 'border-red-500' : 'border-gray-300',
                    className
                )}
                {...props}
            />
            {label &&
                <label htmlFor={name}>
                    {label}
                </label>
            }
            {error && <div className="text-red-500 text-xs">{error}</div>}
        </div>
    );
});
