# Subscription Plan Module - IMPLEMENTATION IN PROGRESS

## Requirements Overview
1. I want to integrate subsciption module with following plans and include 7 days trial in all plans:
    - Monthly 10 USD Subscription
    - Quartly with 10% discount
    - Half Quartly plan with 20% discount
    - Yearly plan with 30% discount

2. All plans and trial can be managed from Admin panel and admin can manage pricing and it's features and number of days for trial from admin area.

## Enhanced Database Schema Design

### Core Tables

1. `subscription_plans` Table:
    - `id` (Primary Key)
    - `name` (Plan name)
    - `slug` (URL-friendly identifier)
    - `description` (Plan description)
    - `price` (Base price in cents)
    - `billing_interval` (monthly, quarterly, semi_annual, annual)
    - `billing_interval_count` (Number of intervals, default 1)
    - `trial_days` (Number of trial days, default 7)
    - `features` (JSON - plan features for display)
    - `limits` (JSON - usage limits like max_agents, max_tickets, etc.)
    - `is_active` (Boolean)
    - `is_popular` (Boolean - for highlighting)
    - `sort_order` (Integer for ordering)
    - `stripe_price_id` (Stripe price ID)
    - `paypal_plan_id` (PayPal plan ID)
    - `created_at`, `updated_at`

2. `subscriptions` Table:
    - `id` (Primary Key)
    - `user_id` (Foreign Key to users)
    - `subscription_plan_id` (Foreign Key to subscription_plans)
    - `stripe_subscription_id` (Stripe subscription ID)
    - `paypal_subscription_id` (PayPal subscription ID)
    - `status` (trial, active, past_due, canceled, incomplete, incomplete_expired)
    - `trial_ends_at` (Trial end date)
    - `current_period_start` (Current billing period start)
    - `current_period_end` (Current billing period end)
    - `ends_at` (Subscription end date for canceled subscriptions)
    - `canceled_at` (Cancellation date)
    - `payment_method` (stripe, paypal)
    - `payment_method_details` (JSON - payment method info)
    - `created_at`, `updated_at`

### Supporting Tables

3. `payment_gateways` Table:
    - `id` (Primary Key)
    - `name` (stripe, paypal)
    - `display_name` (Display name)
    - `is_active` (Boolean)
    - `test_mode` (Boolean)
    - `test_config` (JSON - test keys/settings)
    - `live_config` (JSON - live keys/settings)
    - `webhook_secret` (Webhook secret)
    - `created_at`, `updated_at`

4. `invoices` Table:
    - `id` (Primary Key)
    - `subscription_id` (Foreign Key to subscriptions)
    - `user_id` (Foreign Key to users)
    - `invoice_number` (Unique invoice number)
    - `stripe_invoice_id` (Stripe invoice ID)
    - `paypal_invoice_id` (PayPal invoice ID)
    - `status` (draft, open, paid, void, uncollectible)
    - `amount_due` (Amount due in cents)
    - `amount_paid` (Amount paid in cents)
    - `tax_amount` (Tax amount in cents)
    - `currency` (Currency code)
    - `billing_reason` (subscription_create, subscription_cycle, etc.)
    - `invoice_pdf` (PDF file path)
    - `due_date` (Due date)
    - `paid_at` (Payment date)
    - `created_at`, `updated_at`

5. `payment_transactions` Table:
    - `id` (Primary Key)
    - `invoice_id` (Foreign Key to invoices)
    - `subscription_id` (Foreign Key to subscriptions)
    - `user_id` (Foreign Key to users)
    - `transaction_id` (Gateway transaction ID)
    - `gateway` (stripe, paypal)
    - `type` (payment, refund, chargeback)
    - `status` (pending, succeeded, failed, canceled, refunded)
    - `amount` (Amount in cents)
    - `currency` (Currency code)
    - `gateway_response` (JSON - full gateway response)
    - `processed_at` (Processing date)
    - `created_at`, `updated_at`

4. Update required relationships for each tables. Where need please do the needful changes.

5. For now, I want to use Stripe and Paypal as payment methods for my subscriptions management, I may add other payment gateway as needed. Please create payment Admin settings page to intigrate Stripe and Paypal gateways i.e. Add Test and Live keys, webhook urls if there is any. What I am thinking to inculde here:
    - Create admin settings with all required inputs.
    - Add/edit keys for both test and live mode
    - Switch between test/production mode
    - Restrict access to admin only

6. Create pricing frontend page for Company role, once signup process is completed they need to signup for a plan to access the dashboard and start using the app.

7. Create payments and invoice related pages i.e. Payment History, Invoice listing with print and download invoice option, and subscription informations. Create other frontend and admin necessary pages to manage all SaaS features.

8. Create reports page for admin users. Add all reports with graphs where needed.

9. Make frontend changes to align all these features.

10. Platform is free for end-users.
