<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class CategoryApiController extends Controller
{
    /**
     * Add a new category.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function addCategory(Request $request)
    {

        $result = User::checkRolePermission(Auth::id(), 'admin');
        if ($result !== true) {
            return $result;
        }

        $validator = Validator::make($request->all(), [
            'name'       => 'required|string|max:255|unique:category,catName',
            'catPicture' => 'required|file|mimes:jpeg,png,jpg,gif|max:5120', // Validate file type and size
        ], [
            'catPicture.required' => 'The category image is required.',
            'catPicture.file'     => 'The category image must be a file.',
            'catPicture.mimes'    => 'The category image must be a file of type: jpeg, png, jpg, gif.',
            'catPicture.max'      => 'The category image must not be greater than 5MB.',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $catName    = $request->input('name');
            $catPicture = $request->file('catPicture');

            // Handle file upload
            if ($catPicture) {
                $path = $catPicture->store('category_pictures', 'public'); // Save file to 'public/category_pictures'
            } else {
                $path = ''; // Handle cases where no file is uploaded
            }

            // Create and save category
            $category             = new Category;
            $category->catName    = $catName;
            $category->catPicture = $path; // Save file path to database

            if ($category->save()) {
                return response()->json(['status' => true, 'message' => 'Category added successfully.']);
            } else {
                return response()->json(['status' => false, 'message' => 'Failed to add category.']);
            }
        } catch (Exception $e) {
            return response()->json([
                'status'  => false,
                'message' => 'An error occurred: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Load all categories.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function loadCategory(Request $request)
    {
        try {
            $categories = Category::all();

            if ($categories->isNotEmpty()) {
                // Add URL prefix to category pictures if needed
                $categories->map(function ($category) {
                    $category->catPicture = $category->catPicture ? url('/storage/'.$category->catPicture) : '';

                    return $category;
                });

                return response()->json([
                    'status' => true,
                    'data'   => $categories,
                ]);
            } else {
                return response()->json([
                    'status'  => false,
                    'message' => 'No categories found.',
                ]);
            }
        } catch (Exception $e) {
            return response()->json([
                'status'  => false,
                'message' => 'An error occurred: '.$e->getMessage(),
            ], 500);
        }
    }
}
