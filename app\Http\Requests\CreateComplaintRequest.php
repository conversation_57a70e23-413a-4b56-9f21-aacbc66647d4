<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateComplaintRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'priority' => 'required|in:0,1,2',
            'company_id' => 'required|exists:companies,id',
            'assigned_user_id' => 'required|exists:users,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'title.required' => 'The complaint title is required.',
            'title.max' => 'The complaint title may not be greater than 255 characters.',
            'description.required' => 'The complaint description is required.',
            'priority.required' => 'The priority level is required.',
            'priority.in' => 'The priority level must be Low (0), Medium (1), or High (2).',
            'company_id.required' => 'Please select a company.',
            'company_id.exists' => 'The selected company does not exist.',
            'assigned_user_id.required' => 'Please assign the complaint to a user.',
            'assigned_user_id.exists' => 'The selected user does not exist.',
        ];
    }
}
