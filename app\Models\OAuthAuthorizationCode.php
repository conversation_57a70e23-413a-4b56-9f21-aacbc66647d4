<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OAuthAuthorizationCode extends Model
{
    protected $table = 'oauth_authorization_codes';

    use HasFactory;

    protected $fillable = ['user_id', 'user_id_for_company', 'client_id', 'code', 'vendor_uid', 'expires_at'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function client()
    {
        return $this->belongsTo(OAuthClient::class);
    }
}
