import React, { useState } from 'react';
import { <PERSON> } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import {
    DataTable,
    DataTableHeader,
    DataTableBody,
    DataTableRow,
    DataTableHeaderCell,
    DataTableCell,
    DataTableActions,
    PageHeader,
    StatusBadge,
    ActionButton,
    EditIcon,
    DeleteIcon,
    ViewIcon,
    Card,
    CardContent,
    PlusIcon
} from '@/Components/UI';
import HeaderStats from '@/Components/HeaderStats';

// Mock APIs data
const mockApis = [
    {
        id: 1,
        name: 'User Authentication API',
        method: 'POST',
        url: 'https://api.example.com/v1/auth',
        status: 'active',
        description: 'Authenticates users and returns JWT token',
        createdAt: '2024-01-15',
        updatedAt: '2024-01-20',
        version: '1.0',
        owner: 'Auth Team',
        requestParams: [
            { label: 'Username', parameter: 'username', type: 'String' },
            { label: 'Password', parameter: 'password', type: 'String' }
        ],
        responseParams: [
            { label: 'Token', parameter: 'token', type: 'String' },
            { label: 'Expiry', parameter: 'expires_at', type: 'String' }
        ]
    },
    {
        id: 2,
        name: 'User Profile API',
        method: 'GET',
        url: 'https://api.example.com/v1/users/profile',
        status: 'active',
        description: 'Retrieves user profile information',
        createdAt: '2024-01-16',
        updatedAt: '2024-01-21',
        version: '1.0',
        owner: 'User Team',
        requestParams: [
            { label: 'User ID', parameter: 'user_id', type: 'Number' }
        ],
        responseParams: [
            { label: 'User Data', parameter: 'user', type: 'Object' },
            { label: 'Preferences', parameter: 'preferences', type: 'Object' }
        ]
    },
    {
        id: 3,
        name: 'Payment Processing API',
        method: 'POST',
        url: 'https://api.example.com/v1/payments/process',
        status: 'active',
        description: 'Processes payment transactions',
        createdAt: '2024-01-17',
        updatedAt: '2024-01-22',
        version: '1.0',
        owner: 'Payments Team',
        requestParams: [
            { label: 'Amount', parameter: 'amount', type: 'Number' },
            { label: 'Currency', parameter: 'currency', type: 'String' },
            { label: 'Payment Method', parameter: 'payment_method', type: 'Object' }
        ],
        responseParams: [
            { label: 'Transaction ID', parameter: 'transaction_id', type: 'String' },
            { label: 'Status', parameter: 'status', type: 'String' },
            { label: 'Receipt URL', parameter: 'receipt_url', type: 'String' }
        ]
    },
    {
        id: 4,
        name: 'Product Catalog API',
        method: 'GET',
        url: 'https://api.example.com/v1/products',
        status: 'active',
        description: 'Retrieves product catalog with filtering options',
        createdAt: '2024-01-18',
        updatedAt: '2024-01-23',
        version: '1.0',
        owner: 'Product Team',
        requestParams: [
            { label: 'Category', parameter: 'category', type: 'String' },
            { label: 'Price Range', parameter: 'price_range', type: 'Object' },
            { label: 'Sort By', parameter: 'sort_by', type: 'String' }
        ],
        responseParams: [
            { label: 'Products', parameter: 'products', type: 'Array' },
            { label: 'Total Count', parameter: 'total_count', type: 'Number' },
            { label: 'Page Info', parameter: 'pagination', type: 'Object' }
        ]
    },
    {
        id: 5,
        name: 'Order Management API',
        method: 'POST',
        url: 'https://api.example.com/v1/orders',
        status: 'active',
        description: 'Creates and manages customer orders',
        createdAt: '2024-01-19',
        updatedAt: '2024-01-24',
        version: '1.0',
        owner: 'Order Team',
        requestParams: [
            { label: 'Customer ID', parameter: 'customer_id', type: 'Number' },
            { label: 'Products', parameter: 'products', type: 'Array' },
            { label: 'Shipping Address', parameter: 'shipping_address', type: 'Object' }
        ],
        responseParams: [
            { label: 'Order ID', parameter: 'order_id', type: 'String' },
            { label: 'Status', parameter: 'status', type: 'String' },
            { label: 'Total', parameter: 'total', type: 'Number' },
            { label: 'Estimated Delivery', parameter: 'estimated_delivery', type: 'String' }
        ]
    },
    {
        id: 6,
        name: 'Legacy Notification API',
        method: 'POST',
        url: 'https://api.example.com/v1/notifications/send',
        status: 'deprecated',
        description: 'Sends notifications to users (deprecated, use v2)',
        createdAt: '2023-06-10',
        updatedAt: '2024-01-05',
        version: '1.0',
        owner: 'Notifications Team',
        requestParams: [
            { label: 'User ID', parameter: 'user_id', type: 'Number' },
            { label: 'Message', parameter: 'message', type: 'String' },
            { label: 'Type', parameter: 'type', type: 'String' }
        ],
        responseParams: [
            { label: 'Success', parameter: 'success', type: 'Boolean' },
            { label: 'Notification ID', parameter: 'notification_id', type: 'String' }
        ]
    }
];

export default function Apis() {
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [methodFilter, setMethodFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);

    // Filter APIs based on search term and filters
    const filteredApis = mockApis.filter(api => {
        const matchesSearch = 
            api.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            api.url.toLowerCase().includes(searchTerm.toLowerCase()) ||
            api.description.toLowerCase().includes(searchTerm.toLowerCase());
        
        const matchesStatus = statusFilter === 'all' || api.status === statusFilter;
        const matchesMethod = methodFilter === 'all' || api.method === methodFilter;
        
        return matchesSearch && matchesStatus && matchesMethod;
    });

    // Pagination logic
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentApis = filteredApis.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredApis.length / itemsPerPage);

    // Calculate API statistics
    const totalApis = mockApis.length;
    const activeApis = mockApis.filter(api => api.status === 'active').length;
    const deprecatedApis = mockApis.filter(api => api.status === 'deprecated').length;
    const draftApis = mockApis.filter(api => api.status === 'draft').length;

    return (
        <DashboardLayout title="API Management">
            <title>API Management</title>

            <div className="space-y-6">
                <HeaderStats
                    title="API Management"
                    subtitle="Create and manage APIs for your applications"
                    buttonText="Create New API"
                    buttonLink="/apis/create"
                    stats={[
                        { value: totalApis.toString(), label: "Total APIs" },
                        { value: activeApis.toString(), label: "Active" },
                        { value: deprecatedApis.toString(), label: "Deprecated" },
                        { value: draftApis.toString(), label: "Draft" },
                    ]}
                    gradientFrom="cf-primary-600"
                    gradientTo="blue-600"
                />

                <Card>
                    <CardContent className="p-6">
                        <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
                            <div className="w-full md:w-1/3">
                                <input
                                    type="text"
                                    placeholder="Search APIs..."
                                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cf-primary focus:border-cf-primary"
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                            </div>
                            <div className="flex gap-4">
                                <select
                                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cf-primary focus:border-cf-primary"
                                    value={statusFilter}
                                    onChange={(e) => setStatusFilter(e.target.value)}
                                >
                                    <option value="all">All Statuses</option>
                                    <option value="active">Active</option>
                                    <option value="deprecated">Deprecated</option>
                                    <option value="draft">Draft</option>
                                </select>
                                <select
                                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cf-primary focus:border-cf-primary"
                                    value={methodFilter}
                                    onChange={(e) => setMethodFilter(e.target.value)}
                                >
                                    <option value="all">All Methods</option>
                                    <option value="GET">GET</option>
                                    <option value="POST">POST</option>
                                    <option value="PUT">PUT</option>
                                    <option value="DELETE">DELETE</option>
                                    <option value="PATCH">PATCH</option>
                                </select>
                                <Link href="/apis/create">
                                    <button className="flex items-center gap-2 px-4 py-2 bg-cf-primary text-white rounded-lg hover:bg-cf-primary-dark transition-colors">
                                        <PlusIcon className="w-5 h-5" />
                                        <span>New API</span>
                                    </button>
                                </Link>
                            </div>
                        </div>

                        <DataTable>
                            <DataTableHeader>
                                <DataTableRow>
                                    <DataTableHeaderCell>Name</DataTableHeaderCell>
                                    <DataTableHeaderCell>Method</DataTableHeaderCell>
                                    <DataTableHeaderCell>URL</DataTableHeaderCell>
                                    <DataTableHeaderCell>Status</DataTableHeaderCell>
                                    <DataTableHeaderCell>Version</DataTableHeaderCell>
                                    <DataTableHeaderCell>Last Updated</DataTableHeaderCell>
                                    <DataTableHeaderCell>Actions</DataTableHeaderCell>
                                </DataTableRow>
                            </DataTableHeader>
                            <DataTableBody>
                                {currentApis.length > 0 ? (
                                    currentApis.map((api) => (
                                        <DataTableRow key={api.id}>
                                            <DataTableCell>
                                                <div className="font-medium text-gray-900">{api.name}</div>
                                                <div className="text-sm text-gray-500 truncate max-w-xs">{api.description}</div>
                                            </DataTableCell>
                                            <DataTableCell>
                                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                    api.method === 'GET' ? 'bg-green-100 text-green-800' :
                                                    api.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                                                    api.method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                                                    api.method === 'DELETE' ? 'bg-red-100 text-red-800' :
                                                    'bg-purple-100 text-purple-800'
                                                }`}>
                                                    {api.method}
                                                </span>
                                            </DataTableCell>
                                            <DataTableCell>
                                                <div className="text-sm text-gray-500 truncate max-w-xs">{api.url}</div>
                                            </DataTableCell>
                                            <DataTableCell>
                                                <StatusBadge variant={
                                                    api.status === 'active' ? 'success' :
                                                    api.status === 'deprecated' ? 'error' : 'warning'
                                                }>
                                                    {api.status}
                                                </StatusBadge>
                                            </DataTableCell>
                                            <DataTableCell>{api.version}</DataTableCell>
                                            <DataTableCell>{api.updatedAt}</DataTableCell>
                                            <DataTableCell>
                                                <DataTableActions>
                                                    <ActionButton
                                                        icon={ViewIcon}
                                                        label="View"
                                                        onClick={() => {}}
                                                    />
                                                    <Link href={`/apis/${api.id}/edit`}>
                                                        <ActionButton
                                                            icon={EditIcon}
                                                            label="Edit"
                                                        />
                                                    </Link>
                                                    <ActionButton
                                                        icon={DeleteIcon}
                                                        label="Delete"
                                                        onClick={() => {}}
                                                        variant="danger"
                                                    />
                                                </DataTableActions>
                                            </DataTableCell>
                                        </DataTableRow>
                                    ))
                                ) : (
                                    <DataTableRow>
                                        <DataTableCell colSpan={7} className="text-center py-8">
                                            <div className="flex flex-col items-center justify-center space-y-3">
                                                <div className="text-gray-400 bg-gray-100 p-3 rounded-full">
                                                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                </div>
                                                <div className="text-gray-500 text-lg font-medium">No APIs Found</div>
                                                <p className="text-gray-400 text-sm max-w-md text-center">
                                                    We couldn't find any APIs matching your search criteria. Try adjusting your filters or create a new API.
                                                </p>
                                                <Link href="/apis/create">
                                                    <button className="mt-2 flex items-center gap-2 px-4 py-2 bg-cf-primary text-white rounded-lg hover:bg-cf-primary-dark transition-colors">
                                                        <PlusIcon className="w-5 h-5" />
                                                        <span>Create New API</span>
                                                    </button>
                                                </Link>
                                            </div>
                                        </DataTableCell>
                                    </DataTableRow>
                                )}
                            </DataTableBody>
                        </DataTable>

                        {/* Pagination */}
                        {filteredApis.length > itemsPerPage && (
                            <div className="flex justify-between items-center mt-6">
                                <div className="text-sm text-gray-500">
                                    Showing {indexOfFirstItem + 1} to {Math.min(indexOfLastItem, filteredApis.length)} of {filteredApis.length} APIs
                                </div>
                                <div className="flex gap-2">
                                    <button
                                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                                        disabled={currentPage === 1}
                                        className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50"
                                    >
                                        Previous
                                    </button>
                                    <button
                                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                                        disabled={currentPage === totalPages}
                                        className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50"
                                    >
                                        Next
                                    </button>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </DashboardLayout>
    );
}
