<script setup lang="ts">
import { ref } from 'vue'

const checkboxToggle = ref<boolean>(false)
</script>

<template>
  <div>
    <label for="checkboxLabelFive" class="flex items-center cursor-pointer select-none">
      <div class="relative">
        <input type="checkbox" id="checkboxLabelFive" class="sr-only" @change="checkboxToggle = !checkboxToggle" />
        <div :class="checkboxToggle && '!border-4'"
          class="flex justify-center items-center mr-4 border border-cfp-500 rounded-full w-5 h-5 box">
          <span class="bg-white rounded-full w-2.5 h-2.5"> </span>
        </div>
      </div>
      Checkbox Text
    </label>
  </div>
</template>
