import React from 'react';
import { Head, Link, router } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import Button from '@/Components/Button';

export default function Index({ plans }) {
    const handleToggleStatus = (plan) => {
        router.post(route('admin.subscription-plans.toggle-status', plan.id), {}, {
            preserveScroll: true,
            onSuccess: () => {
                // Handle success
            }
        });
    };

    const handleDelete = (plan) => {
        if (confirm('Are you sure you want to delete this subscription plan?')) {
            router.delete(route('admin.subscription-plans.destroy', plan.id));
        }
    };

    const getStatusBadge = (plan) => {
        if (plan.is_active) {
            return <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Active</span>;
        }
        return <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">Inactive</span>;
    };

    const getBillingIntervalLabel = (interval) => {
        const labels = {
            monthly: 'Monthly',
            quarterly: 'Quarterly',
            semi_annual: 'Semi-Annual',
            annual: 'Annual'
        };
        return labels[interval] || interval;
    };

    return (
        <DashboardLayout>
            <Head title="Subscription Plans" />
            
            <div className="py-6">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="md:flex md:items-center md:justify-between">
                        <div className="flex-1 min-w-0">
                            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                                Subscription Plans
                            </h2>
                            <p className="mt-1 text-sm text-gray-500">
                                Manage your subscription plans and pricing.
                            </p>
                        </div>
                        <div className="mt-4 flex md:mt-0 md:ml-4">
                            <Link
                                href={route('admin.subscription-plans.create')}
                                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                Create Plan
                            </Link>
                        </div>
                    </div>

                    <div className="mt-8">
                        <div className="bg-white shadow overflow-hidden sm:rounded-md">
                            <ul className="divide-y divide-gray-200">
                                {plans.map((plan) => (
                                    <li key={plan.id}>
                                        <div className="px-4 py-4 sm:px-6">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center">
                                                    <div className="flex-shrink-0">
                                                        <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                                                            <span className="text-sm font-medium text-indigo-700">
                                                                {plan.name.charAt(0)}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div className="ml-4">
                                                        <div className="flex items-center">
                                                            <p className="text-sm font-medium text-gray-900">
                                                                {plan.name}
                                                            </p>
                                                            <div className="ml-2 flex space-x-2">
                                                                {getStatusBadge(plan)}
                                                                {plan.is_popular && (
                                                                    <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                                                                        Popular
                                                                    </span>
                                                                )}
                                                            </div>
                                                        </div>
                                                        <div className="mt-1 flex items-center text-sm text-gray-500">
                                                            <span>{plan.formatted_price} / {getBillingIntervalLabel(plan.billing_interval)}</span>
                                                            <span className="mx-2">•</span>
                                                            <span>{plan.trial_days} days trial</span>
                                                            <span className="mx-2">•</span>
                                                            <span>{plan.active_subscriptions_count} active subscriptions</span>
                                                        </div>
                                                        <p className="mt-1 text-sm text-gray-500">
                                                            {plan.description}
                                                        </p>
                                                    </div>
                                                </div>
                                                <div className="flex items-center space-x-2">
                                                    <Button
                                                        variant={plan.is_active ? "danger" : "primary"}
                                                        size="sm"
                                                        onClick={() => handleToggleStatus(plan)}
                                                    >
                                                        {plan.is_active ? 'Deactivate' : 'Activate'}
                                                    </Button>
                                                    
                                                    <Link
                                                        href={route('admin.subscription-plans.show', plan.id)}
                                                        className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                                    >
                                                        View
                                                    </Link>
                                                    
                                                    <Link
                                                        href={route('admin.subscription-plans.edit', plan.id)}
                                                        className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                                    >
                                                        Edit
                                                    </Link>
                                                    
                                                    {plan.active_subscriptions_count === 0 && (
                                                        <Button
                                                            variant="danger"
                                                            size="sm"
                                                            onClick={() => handleDelete(plan)}
                                                        >
                                                            Delete
                                                        </Button>
                                                    )}
                                                </div>
                                            </div>
                                            
                                            {plan.features && plan.features.length > 0 && (
                                                <div className="mt-4">
                                                    <h4 className="text-sm font-medium text-gray-900 mb-2">Features:</h4>
                                                    <div className="flex flex-wrap gap-2">
                                                        {plan.features.map((feature, index) => (
                                                            <span
                                                                key={index}
                                                                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                                            >
                                                                {feature}
                                                            </span>
                                                        ))}
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>

                    {plans.length === 0 && (
                        <div className="text-center py-12">
                            <svg
                                className="mx-auto h-12 w-12 text-gray-400"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                                aria-hidden="true"
                            >
                                <path
                                    vectorEffect="non-scaling-stroke"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
                                />
                            </svg>
                            <h3 className="mt-2 text-sm font-medium text-gray-900">No subscription plans</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                Get started by creating a new subscription plan.
                            </p>
                            <div className="mt-6">
                                <Link
                                    href={route('admin.subscription-plans.create')}
                                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                >
                                    Create Plan
                                </Link>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </DashboardLayout>
    );
}
