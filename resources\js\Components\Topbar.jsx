import React, { useState, useRef, useEffect } from "react";
import { usePage, Link } from "@inertiajs/react";
import Button from "./Button";
import { FiSidebar, FiBell, FiDisc } from "react-icons/fi";


export default function Topbar({ title, setMobileMenuOpen }) {
    const { props } = usePage();
    const user = props.auth?.user;
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const dropdownRef = useRef(null);

    useEffect(() => {
        function handleClickOutside(event) {
            if (
                dropdownRef.current &&
                !dropdownRef.current.contains(event.target)
            ) {
                setDropdownOpen(false);
            }
        }
        if (dropdownOpen) {
            document.addEventListener("mousedown", handleClickOutside);
        } else {
            document.removeEventListener("mousedown", handleClickOutside);
        }
        return () =>
            document.removeEventListener("mousedown", handleClickOutside);
    }, [dropdownOpen]);

    return (
        <header className="h-16 bg-white flex items-center justify-between px-5 md:px-10 border-b border-gray-200 relative z-20">
            <div className="flex gap-4 items-center">
                <Button
                    className="md:hidden"
                    variant="secondary"
                    onClick={() => setMobileMenuOpen(true)}
                    aria-label="Open sidebar"
                >
                    <FiSidebar />
                </Button>
                <div className="text-lg font-semibold flex gap-2 items-center">
                    <FiDisc /> {title || "Dashboard"}
                </div>
            </div>
            <div className="flex gap-4 items-center">
                <Button
                    variant="link"
                    className="hidden md:block"
                    onClick={() => setMobileMenuOpen(true)}
                    aria-label="Open sidebar"
                >
                    <FiBell className="text-xl" />
                    <span className="absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-500"></span>
                </Button>
                <div className="relative" ref={dropdownRef}>
                    <Button
                        className="size-10 rounded-full font-bold border-2 text-cf-primary border-cf-primary hover:bg-cf-primary-500 hover:text-white"
                        onClick={() => setDropdownOpen((v) => !v)}
                        aria-label="User menu"
                    >
                        {user.name[0]}
                    </Button>
                    {dropdownOpen && (
                        <div className="absolute right-0 mt-2 w-60 bg-white rounded-xl shadow-2xl py-2 z-50 border border-gray-100 animate-fade-in">
                            <div className="px-4 py-3 border-b border-gray-100">
                                <div className="font-semibold text-gray-800 text-base">
                                    {user.name}
                                </div>
                                {user.email && (
                                    <div className="text-xs text-gray-500">
                                        {user.email}
                                    </div>
                                )}
                            </div>
                            <Link href="/profile" className="block px-4 py-2">
                                Profile
                            </Link>
                            <Link
                                href="/logout"
                                method="post"
                                as="button"
                                className="w-full text-left px-4 py-2 text-red-600 hover:bg-red-50"
                            >
                                Sign out
                            </Link>
                        </div>
                    )}
                </div>
            </div>
        </header>
    );
}
