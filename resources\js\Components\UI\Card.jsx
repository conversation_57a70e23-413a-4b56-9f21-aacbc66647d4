import React from 'react';
import clsx from 'clsx';

export const Card = ({ children, className = '', padding = 'p-6', ...props }) => {
    return (
        <div 
            className={clsx(
                'bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200',
                padding,
                className
            )}
            {...props}
        >
            {children}
        </div>
    );
};

export const CardHeader = ({ children, className = '', ...props }) => {
    return (
        <div 
            className={clsx(
                'border-b border-gray-100 pb-4 mb-6',
                className
            )}
            {...props}
        >
            {children}
        </div>
    );
};

export const CardTitle = ({ children, className = '', ...props }) => {
    return (
        <h3 
            className={clsx(
                'text-lg font-semibold text-gray-900',
                className
            )}
            {...props}
        >
            {children}
        </h3>
    );
};

export const CardContent = ({ children, className = '', ...props }) => {
    return (
        <div 
            className={clsx(
                'text-gray-600',
                className
            )}
            {...props}
        >
            {children}
        </div>
    );
};
