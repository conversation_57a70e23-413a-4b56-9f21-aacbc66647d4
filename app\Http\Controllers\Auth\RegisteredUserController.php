<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Mail\SendOTP;
use App\Models\Category;
use App\Models\Company;
use App\Models\Person;
use App\Models\User;
use Exception;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     */
    public function create(): Response
    {
        return Inertia::render('Auth/Register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name'     => 'required|string|max:255',
            'email'    => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        $user = User::create([
            'name'     => $request->name,
            'email'    => $request->email,
            'password' => Hash::make($request->password),
        ]);

        event(new Registered($user));

        Auth::login($user);

        return redirect(route('dashboard', absolute: false));
    }

    public function registerBusiness(Request $request): RedirectResponse
    {

        $otpReal = rand(111111, 999999);

        // $request->validate([
        //     'companyName' => 'required|string|max:255',
        //     'email' => 'required|email|unique:users,email',
        //     'password' => 'required|min:6',
        //     'selectedCategory' => 'required|exists:category,catId',
        //     'country' => 'required|exists:country,id',
        //     'companyPhone' => 'required|string|max:20',
        //     'companyAddress' => 'required|string|max:255',
        //     'city' => 'required|string|max:255',
        //     'state' => 'required|string|max:255',
        //     'zipcode' => 'required|string|max:20',
        //     'websiteUrl' => 'nullable|url',
        //     'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
        // ]);

        $request->validate([
            'companyName'    => 'required|string|max:255',
            'email'          => 'required|email|unique:users,email',
            'password'       => 'required|min:6',
            'country'        => 'required|exists:country,id',
            'companyPhone'   => 'required|string|max:20',
            'companyAddress' => 'required|string|max:255',
            'city'           => 'required|string|max:255',
            'state'          => 'required|string|max:255',
            'zipcode'        => 'required|string|max:20',
            'websiteUrl'     => 'nullable|url',
            'logo'           => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
        ]);

        $user               = new User;
        $user->name         = $request->companyName;
        $user->company_name = $request->companyName;
        $user->email        = $request->email;
        $user->phone        = $request->companyPhone ?? '';
        $user->password     = Hash::make($request->password);
        $user->profileType  = 'company';

        if ($request->hasFile('logo')) {
            $profilePicture       = $request->file('logo');
            $folder_name          = 'images/profile';
            $profileUrl           = $profilePicture->store($folder_name, 'public');
            $user->profilePicture = $profileUrl;
        }

        $user->verificationStatus = 'not_verified';
        $user->status             = 'active';
        $user->otp                = $otpReal;
        $user->save();

        $checkCategory = Category::where('catName', 'Other')->first();
        if ($checkCategory) {
        } else {
            $checkCategory = Category::orderBy('catId', 'desc')->first();
        }

        if ($checkCategory) {

            $company              = Company::firstOrNew(['userId' => $user->id]);
            $company->catId       = $checkCategory->catId;
            $company->companyName = $request->companyName;
            $company->companyAdd  = $request->companyAddress;
            $company->city        = $request->city;
            $company->state       = $request->state;
            $company->countryId   = $request->country;
            $company->zipCode     = $request->zipcode;
            $company->websiteUrl  = $request->websiteUrl ?? '';
            $company->chatSupport = 'chatbot';
            $company->save();

            $setmailids = $user->email;
            $mailData   = [
                'otp'     => $otpReal,
                'subject' => 'Please verify your email',
            ];

            try {
                Mail::to($setmailids)->send(new SendOTP($mailData));
            } catch (Exception $ex) {

            }

            return redirect()->route('verify-otp', ['email' => $request->email])
                ->with('message', 'Please check your email for the verification code.');

            // event(new Registered($user));

            // Auth::login($user);

            // return redirect(route('dashboard'));

        } else {
            return redirect()->back()->with('error', 'Invalid cateory');
        }

    }

    public function store_user(Request $request): RedirectResponse
    {
        $otpReal = rand(111111, 999999);

        if (trim($request->companyname) != '') {

            $request->validate([
                'companyname' => 'required|string|max:255',
                'email'       => 'required|string|lowercase|email|max:255|unique:'.User::class,
                'password'    => ['required', 'confirmed', Rules\Password::defaults()],
            ]);

            $name = $request->companyname;

            $user                     = new User;
            $user->name               = $request->companyname;
            $user->company_name       = $request->companyname;
            $user->email              = $request->email;
            $user->phone              = $request->phone ?? ''; // Optional phone field
            $user->password           = Hash::make($request->password);
            $user->profileType        = 'company';
            $user->verificationStatus = 'not_verified';
            $user->status             = 'active';
            $user->profilePicture     = '';
            $user->otp                = $otpReal;
            $user->save();

            // $company = Company::firstOrNew(['userId' => $user->id]);

            // $company->catId = $request->catId;
            // $company->companyName = $request->name;
            // $company->companyAdd = $request->address;
            // $company->city = $request->city;
            // $company->state = $request->state;
            // $company->countryId = $request->country;
            // $company->zipCode = $request->zip;
            // $company->websiteUrl = ($request->url != '') ? $request->url : '';
            // $company->chatSupport = 'chatbot';

            // $company->save();

        } else {

            $request->validate([
                'name'     => 'required|string|max:255',
                'email'    => 'required|string|lowercase|email|max:255|unique:'.User::class,
                'password' => ['required', 'confirmed', Rules\Password::defaults()],
            ]);

            $user                     = new User;
            $user->name               = $request->name;
            $user->company_name       = '';
            $user->email              = $request->email;
            $user->phone              = $request->phone ?? ''; // Optional phone field
            $user->password           = Hash::make($request->password);
            $user->profileType        = 'user';
            $user->verificationStatus = 'not_verified';
            $user->status             = 'active';
            $user->profilePicture     = ''; // No profile picture initially
            $user->otp                = $otpReal; // No OTP initially
            $user->save();

            $person            = new Person;
            $person->userId    = $user->id;
            $person->firstName = $request->name;
            $person->lastName  = '';
            $person->save();

        }

        $setmailids = $user->email;
        $mailData   = [
            'otp'     => $otpReal,
            'subject' => 'Please verify your email',
        ];

        try {
            Mail::to($setmailids)->send(new SendOTP($mailData));
        } catch (Exception $ex) {

        }

        return redirect()->route('verify-otp', ['email' => $request->email])
            ->with('message', 'Please check your email for the verification code.');
        // event(new Registered($user));

        // Auth::login($user);

        // return redirect(route('dashboard', absolute: false));
    }

    public function showVerifyOtp(Request $request)
    {
        $email = $request->query('email');

        if (! $email) {
            return redirect(route('user-login'));
        }

        // Verify if this email exists and needs verification
        $user = User::where('email', $email)
            ->where('verificationStatus', 'not_verified')
            ->first();

        if (! $user) {
            return redirect(route('user-login'));
        }

        return Inertia::render('Auth/VerifyOtp', [
            'email'   => $email,
            'message' => 'Please check your email for the verification code.',
        ]);
    }

    public function verifyOtp(Request $request): RedirectResponse
    {
        $request->validate([
            'email' => 'required|email',
            'otp'   => 'required|numeric|digits:6',
        ]);

        $user = User::where('email', $request->email)
            ->where('otp', $request->otp)
            ->first();

        if (! $user) {
            return back()->withErrors([
                'otp' => 'The verification code is invalid.',
            ]);
        }

        $user->verificationStatus = 'verified';
        $user->otp                = null; // Clear the OTP after successful verification
        $user->save();

        return redirect(route('user-login'))
            ->with('message', 'Email verified successfully. Please login to continue.');
    }

    public function resendOtp(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        $user = User::where('email', $request->email)
            ->where('verificationStatus', 'not_verified')
            ->first();

        if (! $user) {
            return response()->json([
                'status'  => false,
                'message' => 'Invalid request',
            ], 400);
        }

        $otpReal   = rand(111111, 999999);
        $user->otp = $otpReal;
        $user->save();

        $mailData = [
            'otp'     => $otpReal,
            'subject' => 'Please verify your email',
        ];

        try {
            Mail::to($user->email)->send(new SendOTP($mailData));

            return response()->json([
                'status'  => true,
                'message' => 'Verification code sent successfully',
            ]);
        } catch (Exception $ex) {

            Log::info($ex->getMessage());

            return response()->json([
                'status'  => true,
                'message' => 'Verification code sent successfully....',
            ]);

            return response()->json([
                'status'  => false,
                'message' => 'Failed to send verification code',
            ], 500);
        }
    }
}
