<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head } from '@inertiajs/vue3';
import { ref, watch, nextTick } from 'vue';
import InputError from '@/Components/InputError.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SelectInput from '@/Components/SelectInput.vue';
import TextInput from '@/Components/TextInput.vue';
import { usePage, useForm } from '@inertiajs/vue3';
import PageHeading from '@/Components/Global/PageHeading.vue';
import TextAreaInput from '@/Components/TextAreaInput.vue';
import DtIconGlobal from '@/Components/DtIcon/DtIconGlobal.vue';

const { title } = usePage().props;
const pageTitle = ref(title);

const requestParams = ref([]);
const responseParams = ref([]);

const newRequestParam = ref({
    label: '',
    param: '',
    datatype: 'string'
});

const newResponseParam = ref({
    label: '',
    param: '',
    datatype: 'string'
});

const datatypes = ['string', 'number', 'date'];

const form = useForm({
    name: '',
    url: '',
    method: 'GET',
    request_parameters: '',
    response_parameters: '',
    description: '',
    username: '',
    password: '',
});

// Watch for changes in the name field
watch(() => form.name, (newValue) => {
    if (newValue) {
        // Convert to lowercase and remove spaces, replacing with underscores
        const formattedName = newValue.toLowerCase().replace(/\s+/g, '_');
        // Add @api- prefix if it doesn't exist
        const prefixedName = formattedName.startsWith('@api-') ? formattedName : '@api-' + formattedName;
        form.name = prefixedName;
    }
});

const methods = ['GET', 'POST'];


function addRow(event) {
    let table = document.getElementById("paramsTable");
    let lastRow = table.rows[table.rows.length - 1]; // Get the last row

    // Find the input field in the first column (Label)
    let labelInput = lastRow?.cells[0]?.querySelector('input');

    // Check if the label field is not empty
    if (labelInput && labelInput.value.trim() !== "") {
        // Add a new row at the end of the table
        let newRow = table.insertRow();
        newRow.classList.add("new-row-cls");

        newRow.innerHTML = `
            <td class="p-1"><input type="text" class="w-full p-2 border border-gray-300 rounded-md"></td>
            <td class="p-1"><input type="text" class="w-full p-2 border border-gray-300 rounded-md"></td>
            <td class="p-1">
                <select class="w-full p-2 border border-gray-300 rounded-md">
                    <option value="string">String</option>
                    <option value="number">Number</option>
                    <option value="date">Date</option>
                </select>
            </td>
            <td class="p-1 text-center">
                <button class="px-2 py-1 hover:text-red-500 rounded-md delete-btn"><svg class="dt-deals-icon" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg></button>
            </td>
        `;

        // Use nextTick to ensure Vue updates the DOM and bind event listeners
        nextTick(() => {
            const newLabelInput = newRow.cells[0].querySelector('input');
            newLabelInput.addEventListener('input', addRow);

            // Bind delete event to the new delete button
            const deleteButton = newRow.querySelector(".delete-btn");
            deleteButton.addEventListener("click", function () {
                deleteRow(this);
            });
        });
    }
}

function addResponseRow(event) {
    let table = document.getElementById("responseParamsTable"); // Target the correct table
    let lastRow = table.rows[table.rows.length - 1]; 
    let labelInput = lastRow?.cells[0]?.querySelector('input');

    if (labelInput && labelInput.value.trim() !== "") {
        let newRow = table.insertRow();
        newRow.classList.add("new-row-cls");

        newRow.innerHTML = `
            <td class="p-1"><input type="text" class="w-full p-2 border border-gray-300 rounded-md"></td>
            <td class="p-1"><input type="text" class="w-full p-2 border border-gray-300 rounded-md"></td>
            <td class="p-1">
                <select class="w-full p-2 border border-gray-300 rounded-md">
                    <option value="string">String</option>
                    <option value="number">Number</option>
                    <option value="date">Date</option>
                </select>
            </td>
            <td class="p-1 text-center">
                <button class="px-2 py-1 hover:text-red-500 rounded-md delete-btn"><svg class="dt-deals-icon" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg></button>
            </td>
        `;

        nextTick(() => {
            const newLabelInput = newRow.cells[0].querySelector('input');
            newLabelInput.addEventListener('input', addResponseRow); // Call addResponseRow recursively

            const deleteButton = newRow.querySelector(".delete-btn");
            deleteButton.addEventListener("click", function () {
                deleteRow(this);
            });
        });
    }
}


// Function to delete a row
function deleteRow(button) {
    let row = button.closest("tr");  // Find the row containing the button
    row.remove();  // Remove the row
}

// Ensure all existing delete buttons also work
document.addEventListener("DOMContentLoaded", function () {
    document.querySelectorAll(".delete-btn").forEach(button => {
        button.addEventListener("click", function () {
            deleteRow(this);
        });
    });
});

// Function to collect and format parameters from the table
function collectTableParameters(tableId) {
    const table = document.getElementById(tableId);
    const params = [];
    
    // Skip header row by starting from index 1
    for (let i = 1; i < table.rows.length; i++) {
        const row = table.rows[i];
        const label = row.cells[0]?.querySelector('input')?.value;
        const param = row.cells[1]?.querySelector('input')?.value;
        const datatype = row.cells[2]?.querySelector('select')?.value;
        
        if (label && param) {
            params.push({ label, param, datatype: datatype || 'string' });
        }
    }
    
    return JSON.stringify(params);
}


const resetForm = () => {
  form.reset();
  requestParams.value = []; // Reset request parameters
  responseParams.value = []; // Reset response parameters

  // Clear all rows in the request parameters table except the header and the default row
  const requestTable = document.getElementById('paramsTable');
  while (requestTable.rows.length > 2) { // Keep the header and the first default row
      requestTable.deleteRow(1); // Remove the first added row
  }

  // Clear all rows in the response parameters table except the header and the default row
  const responseTable = document.getElementById('responseParamsTable');
  while (responseTable.rows.length > 2) { // Keep the header and the first default row
      responseTable.deleteRow(1); // Remove the first added row
  }
};

// Function to handle form submission
const handleSubmit = () => {
    // Collect request parameters
    form.request_parameters = collectTableParameters('paramsTable');
    
    // Collect response parameters
    form.response_parameters = collectTableParameters('responseParamsTable');
    
    // Submit the form
    form.post(route('vendor-apis.store'), {
        preserveScroll: true,
        onSuccess: (response) => {
            // Force a full page reload to ensure data is fresh
            // window.location.href = route('vendor-apis.edit', response.props.api.id);
        },
    });

};

</script>

<template>
    <Head :title="pageTitle" />

    <AuthenticatedLayout>
        <div class="flex">
            <div class="flex-1">
                <div class="main-body border p-4 rounded-md mb-4 dynamicthisform">
                    <form @submit.prevent="handleSubmit">

                          <!-- Success Message -->
                          <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
                            leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
                            <p v-if="form.recentlySuccessful" enter-active-class="transition ease-in-out"
                                enter-from-class="opacity-0" leave-active-class="transition ease-in-out"
                                leave-to-class="opacity-0" :class="$page.props.flash?.class">
                                {{ $page.props.flash.message }}</p>
                        </Transition>



                        <div class="mb-4">
                            <InputLabel for="name" value="API Name" />
                            <TextInput id="name" type="text" class="mt-1 block w-full" v-model="form.name" required />
                            <InputError :message="form.errors.name" class="mt-2" />
                        </div>

                        <div class="flex space-x-2 mb-4">
                            <div>
                                <select v-model="form.method" class="p-2 border border-gray-300 rounded-md w-25">
                                    <option value="GET">GET</option>
                                    <option value="POST">POST</option>
                                </select>
                                <InputError :message="form.errors.method" class="mt-2" />
                            </div>
                            <div class="flex-grow">
                                <input 
                                    type="text" 
                                    v-model="form.url"
                                    placeholder="Enter API URL"
                                    class="w-full p-2 border border-gray-300 rounded-md" 
                                    required
                                />
                                <InputError :message="form.errors.url" class="mt-2" />
                            </div>
                        </div>

                        <!-- Request Parameters Table -->
                        <div v-if="form.name !== '@apiauth-get_api_token'" class="mt-4 mb-4 border-b pb-2">
                            <h3 class="font-medium">Request Parameters</h3>
                            <table class="w-full border-collapse" id="paramsTable">
                                <tr>
                                    <th class="p-1 text-left font-medium">Label</th>
                                    <th class="p-1 text-left font-medium">Parameter</th>
                                    <th class="p-1 text-left font-medium">Type</th>
                                </tr>
                                <tr>
                                    <td class="p-1">
                                        <TextInput type="text" class="w-full p-2 border border-gray-300 rounded-md"
                                            @input="addRow" />
                                    </td>
                                    <td class="p-1">
                                        <TextInput type="text" class="w-full p-2 border border-gray-300 rounded-md" />
                                    </td>
                                    <td class="p-1">
                                        <select class="w-full p-2 border border-gray-300 rounded-md">
                                            <option value="string">String</option>
                                            <option value="number">Number</option>
                                            <option value="date">Date</option>
                                        </select>
                                    </td>
                                </tr>
                            </table>
                            <InputError :message="form.errors.request_parameters" class="mt-2" />
                        </div>

                        <!-- Authentication Details (shown only for @apiauth-get_api_token) -->
                        <div v-if="form.name === '@apiauth-get_api_token'" class="space-y-4">
                            <h3 class="font-semibold">Authentication Detail</h3>
                            <div class="flex gap-2">
                                <div class="flex-1">Username</div>
                                <div class="flex-1">
                                    <TextInput type="text" placeholder="Username" v-model="form.username" />
                                    <InputError :message="form.errors.username" class="mt-2" />
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <div class="flex-1">Password</div>
                                <div class="flex-1">
                                    <TextInput type="text" placeholder="Password" v-model="form.password" />
                                    <InputError :message="form.errors.password" class="mt-2" />
                                </div>
                            </div>
                        </div>

                        <!-- Response Parameters Table -->
                        <div class="mt-4 mb-4 border-b pb-2">
                            <h3 class="font-medium mb-2">Response Parameters</h3> 
                            <table class="w-full border-collapse" id="responseParamsTable">
                                <thead>
                                    <tr>
                                        <th class="p-1 text-left font-medium">Label</th>
                                        <th class="p-1 text-left font-medium">Parameter</th>
                                        <th class="p-1 text-left font-medium">Type</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="p-1">
                                            <TextInput type="text" class="w-full p-2 border border-gray-300 rounded-md"
                                                @input="addResponseRow" />
                                        </td>
                                        <td class="p-1">
                                            <TextInput type="text" class="w-full p-2 border border-gray-300 rounded-md" />
                                        </td>
                                        <td class="p-1">
                                            <select class="w-full p-2 border border-gray-300 rounded-md">
                                                <option value="string">String</option>
                                                <option value="number">Number</option>
                                                <option value="date">Date</option>
                                            </select>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <InputError :message="form.errors.response_parameters" class="mt-2" />
                        </div>

                        <div class="mb-4">
                            <InputLabel for="description" value="Description" />
                            <TextInput id="description" type="text" class="mt-1 block w-full" v-model="form.description" />
                            <InputError :message="form.errors.description" class="mt-2" />
                        </div>

                        <div class="flex items-center gap-4 mt-4">
                            <PrimaryButton :disabled="form.processing">
                                <span v-if="form.processing">Loading...</span>
                                <span v-else>Save</span>
                            </PrimaryButton>

                            <ResponsiveNavLink :href="route('vendor-apis.index')" class="dk-cancle-btn">
                                Back
                            </ResponsiveNavLink>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
