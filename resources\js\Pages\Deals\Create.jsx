import React, { useRef } from 'react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { Input } from '@/Components/FormElements';
import Button from '@/Components/Button';

export default function DealManage() {
    const fileInput = useRef();
    return (
        <DashboardLayout title="Deal">
            <title>Deal</title>
            <div className="w-full mx-auto p-6">
                <div className="bg-white rounded-2xl shadow p-8 border border-gray-200 max-w-3xl">
                    <form className="space-y-6">
                        <div>
                            <label className="block font-medium text-gray-700 mb-1">Upload Image</label>
                            <input type="file" ref={fileInput} className="block" />
                        </div>
                        <div className="flex items-center gap-2 mb-2">
                            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" /><path d="M8 12l2 2 4-4" /></svg>
                            <span className="text-gray-700 font-semibold">Deal</span>
                        </div>
                        <div>
                            <label className="block font-medium text-gray-700 mb-1">Title</label>
                            <Input name="title" value="Macy's Last Act Sales" onChange={() => {}} />
                        </div>
                        <div>
                            <label className="block font-medium text-gray-700 mb-1">Link</label>
                            <Input name="link" value="https://akademi.eruditetechnology.com/deal/1" onChange={() => {}} />
                        </div>
                        <div>
                            <label className="block font-medium text-gray-700 mb-1">Description</label>
                            <Input name="description" value="75% off 1,000s of items buy now, the deal is for a short period of time." onChange={() => {}} as="textarea" rows={3} />
                        </div>
                        <div>
                            <label className="block font-medium text-gray-700 mb-1">Start Date</label>
                            <Input name="start_date" value="2024-12-18 05:00" onChange={() => {}} type="datetime-local" />
                        </div>
                        <div>
                            <label className="block font-medium text-gray-700 mb-1">End Date</label>
                            <Input name="end_date" value="2025-12-01 05:00" onChange={() => {}} type="datetime-local" />
                        </div>
                        <div className="flex gap-2 mt-4">
                            <Button type="submit">Update</Button>
                            <Button type="button" className="bg-red-400 text-gray-700 hover:bg-red-500">Cancel</Button>
                        </div>
                    </form>
                </div>
            </div>
        </DashboardLayout>
    );
} 