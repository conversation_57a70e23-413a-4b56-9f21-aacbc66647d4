import React from 'react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import Button from '@/Components/Button';
import { Link } from '@inertiajs/react';
import {
    Card,
    CardContent,
    PageHeader,
    StatusBadge,
    ActionButton,
    EditIcon,
    DeleteIcon,
    ViewIcon,
    PlusIcon
} from '@/Components/UI';

export default function Deals() {
    // Placeholder data
    const deals = [
        {
            id: 1,
            image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop',
            title: "Macy's Last Act Sales",
            link: 'https://akademi.eruditetechnology.com/deal/1',
            description: '75% off 1,000s of items buy now, the deal is for a short period of time.',
            discount: '75%',
            category: 'Fashion',
            status: 'active',
            start_date: '2024-12-18 05:00',
            end_date: '2025-12-01 05:00',
        },
        {
            id: 2,
            image: 'https://images.unsplash.com/photo-1523474253046-8cd2748b5fd2?w=400&h=300&fit=crop',
            title: "Amazon Prime Day",
            link: 'https://amazon.com/prime-day',
            description: 'Exclusive deals for Prime members.',
            discount: '50%',
            category: 'Electronics',
            status: 'expired',
            start_date: '2024-07-15 00:00',
            end_date: '2024-07-16 23:59',
        },
        {
            id: 3,
            image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop',
            title: "Black Friday Mega Sale",
            link: 'https://example.com/black-friday',
            description: 'Biggest sale of the year with up to 80% off on selected items.',
            discount: '80%',
            category: 'General',
            status: 'active',
            start_date: '2024-11-29 00:00',
            end_date: '2024-11-30 23:59',
        },
    ];

    const getStatusVariant = (status) => {
        switch (status.toLowerCase()) {
            case 'active': return 'success';
            case 'expired': return 'secondary';
            case 'upcoming': return 'warning';
            default: return 'secondary';
        }
    };

    const isExpired = (endDate) => {
        return new Date(endDate) < new Date();
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <DashboardLayout title="Deals">
            <title>Deals</title>

            <div className="space-y-6">
                <PageHeader
                    title="Deal Management"
                    subtitle="Create and manage promotional deals and offers"
                >
                    <Link href="/deals/create">
                        <Button type="button" className="flex items-center gap-2">
                            <PlusIcon className="w-4 h-4" />
                            Create Deal
                        </Button>
                    </Link>
                </PageHeader>

                {/* Deals Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {deals.map((deal) => (
                        <Card key={deal.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-200">
                            <div className="relative">
                                <img
                                    src={deal.image}
                                    alt={deal.title}
                                    className="w-full h-48 object-cover"
                                    onError={(e) => {
                                        e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNzUgMTI1SDE3NS4wMUgxNzVaTTE3NSAxNzVIMTc1LjAxSDE3NVpNMjI1IDEyNUgyMjUuMDFIMjI1Wk0yMjUgMTc1SDIyNS4wMUgyMjVaIiBzdHJva2U9IiM5Q0E0QUYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=';
                                    }}
                                />
                                <div className="absolute top-3 left-3">
                                    <StatusBadge variant={getStatusVariant(deal.status)}>
                                        {deal.status}
                                    </StatusBadge>
                                </div>
                                <div className="absolute top-3 right-3">
                                    <div className="bg-red-500 text-white px-2 py-1 rounded-lg text-sm font-bold">
                                        {deal.discount} OFF
                                    </div>
                                </div>
                            </div>

                            <CardContent className="p-6">
                                <div className="space-y-4">
                                    <div>
                                        <div className="flex items-center justify-between mb-2">
                                            <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
                                                {deal.title}
                                            </h3>
                                            <StatusBadge variant="info" size="sm">
                                                {deal.category}
                                            </StatusBadge>
                                        </div>
                                        <p className="text-gray-600 text-sm line-clamp-2">
                                            {deal.description}
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <div className="flex items-center justify-between text-sm">
                                            <span className="text-gray-500">Start Date:</span>
                                            <span className="text-gray-900">{formatDate(deal.start_date)}</span>
                                        </div>
                                        <div className="flex items-center justify-between text-sm">
                                            <span className="text-gray-500">End Date:</span>
                                            <span className={`${isExpired(deal.end_date) ? 'text-red-600' : 'text-gray-900'}`}>
                                                {formatDate(deal.end_date)}
                                            </span>
                                        </div>
                                    </div>

                                    <div className="pt-4 border-t border-gray-200">
                                        <div className="flex items-center justify-between">
                                            <a
                                                href={deal.link}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-cf-primary hover:text-cf-primary-600 text-sm font-medium truncate max-w-[150px]"
                                                title={deal.link}
                                            >
                                                View Deal →
                                            </a>
                                            <div className="flex items-center gap-1">
                                                <ActionButton
                                                    variant="primary"
                                                    size="sm"
                                                    icon={ViewIcon}
                                                    title="View Details"
                                                />
                                                <Link href={`/deals/edit/${deal.id}`}>
                                                    <ActionButton
                                                        variant="secondary"
                                                        size="sm"
                                                        icon={EditIcon}
                                                        title="Edit Deal"
                                                    />
                                                </Link>
                                                <ActionButton
                                                    variant="danger"
                                                    size="sm"
                                                    icon={DeleteIcon}
                                                    title="Delete Deal"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* Empty State */}
                {deals.length === 0 && (
                    <div className="text-center py-12">
                        <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" strokeWidth="1" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No deals found</h3>
                        <p className="text-gray-500 mb-4">Get started by creating your first deal.</p>
                        <Link href="/deals/create">
                            <Button type="button">Create Deal</Button>
                        </Link>
                    </div>
                )}
            </div>
        </DashboardLayout>
    );
} 