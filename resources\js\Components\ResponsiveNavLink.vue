<script setup>
import { computed } from 'vue';
import { Link } from '@inertiajs/vue3';

const props = defineProps({
    href: {
        type: String,
        required: true,
    },
    active: {
        type: Boolean,
    },
});

const classes = computed(() =>
    props.active
        ? 'block  ps-3 pe-4 py-2   text-start text-base font-medium text-cfp-500  bg-gray-50   transition duration-150 ease-in-out'
        : 'block  ps-3 pe-4 py-2   text-start text-base font-medium text-gray-600  hover:text-slate-800 transition duration-150 ease-in-out'
);
</script>

<template>
    <Link :href="href" :class="classes">
    <slot />
    </Link>
</template>
