import React from 'react';

export function TableSkeleton({ rows = 5, columns = 8, headers = [] }) {
    const defaultHeaders = ['ID', 'Title', 'Company', 'Assigned User', 'Priority', 'Status', 'Created', 'Actions'];
    const tableHeaders = headers.length > 0 ? headers : defaultHeaders.slice(0, columns);

    return (
        <div>
            {/* Static Header - No skeleton, show actual headers */}
            <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
                <div className="grid gap-4 items-center" style={{ gridTemplateColumns: `repeat(${columns}, minmax(0, 1fr))` }}>
                    {tableHeaders.map((header, index) => (
                        <div key={index} className="text-sm font-medium text-gray-700">
                            {header}
                        </div>
                    ))}
                </div>
            </div>

            {/* Rows skeleton - Only data cells have skeleton */}
            <div className="animate-pulse">
                {Array.from({ length: rows }).map((_, rowIndex) => (
                    <div key={rowIndex} className="bg-white px-6 py-4 border-b border-gray-200">
                        <div className="grid gap-4 items-center" style={{ gridTemplateColumns: `repeat(${columns}, minmax(0, 1fr))` }}>
                            {Array.from({ length: columns }).map((_, colIndex) => (
                                <div key={colIndex}>
                                    {colIndex === 4 || colIndex === 5 ? (
                                        // Status and priority badges
                                        <div className="h-6 bg-gray-300 rounded-full w-20"></div>
                                    ) : colIndex === columns - 1 ? (
                                        // Actions column
                                        <div className="flex space-x-2">
                                            <div className="h-8 w-8 bg-gray-300 rounded"></div>
                                            <div className="h-8 w-8 bg-gray-300 rounded"></div>
                                            <div className="h-8 w-8 bg-gray-300 rounded"></div>
                                        </div>
                                    ) : (
                                        // Regular text columns
                                        <div className="h-4 bg-gray-300 rounded w-full"></div>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}

export function CardSkeleton() {
    return (
        <div className="animate-pulse">
            <div className="bg-white rounded-lg shadow p-6">
                <div className="h-4 bg-gray-300 rounded w-3/4 mb-4"></div>
                <div className="h-4 bg-gray-300 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-gray-300 rounded w-5/6"></div>
            </div>
        </div>
    );
}

export function HeaderStatsSkeleton({
    title = "Complaint Management",
    subtitle = "Track and resolve customer complaints efficiently",
    stats = null,
    loading = true
}) {
    const defaultStats = [
        { label: 'Total Complaints', value: 0, loading: true },
        { label: 'New', value: 0, loading: true },
        { label: 'In Progress', value: 0, loading: true },
        { label: 'Resolved', value: 0, loading: true }
    ];

    const displayStats = stats || defaultStats;

    return (
        <div className="bg-gradient-to-r from-cf-primary-600 to-blue-600 rounded-lg p-8 text-white relative overflow-hidden">
            <div className="relative z-10">
                {/* Static title and subtitle - No skeleton */}
                <h1 className="text-3xl font-bold mb-2">{title}</h1>
                <p className="text-cf-primary-100 mb-6">{subtitle}</p>

                {/* Dynamic stats - Show skeleton only for numbers when loading */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    {displayStats.map((stat, index) => (
                        <div key={index} className="text-center">
                            {loading || stat.loading ? (
                                <div className="animate-pulse">
                                    <div className="h-8 bg-white/20 rounded w-16 mx-auto mb-2"></div>
                                </div>
                            ) : (
                                <div className="text-2xl font-bold mb-2">{stat.value}</div>
                            )}
                            <div className="text-cf-primary-100 text-sm">{stat.label}</div>
                        </div>
                    ))}
                </div>

                {/* Static button - No skeleton */}
                <a href="/complains/create" className="flex items-center gap-2 text-cf-primary-100 hover:text-white transition-colors">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
                    </svg>
                    <span>Create New Complaint</span>
                </a>
            </div>

            {/* Background decoration */}
            <div className="absolute top-0 right-0 -mt-4 -mr-4 opacity-10">
                <svg className="w-32 h-32" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
            </div>
        </div>
    );
}

export function FiltersSkeleton() {
    return (
        <div className="bg-white p-4 rounded-lg shadow mb-6">
            {/* Static header - No skeleton */}
            <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Search & Filters</h3>
            </div>

            {/* Static search bar structure - No skeleton */}
            <div className="mb-4">
                <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                    <input
                        type="text"
                        disabled
                        placeholder="Search by title, complaint ID, or description..."
                        className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                    />
                </div>
            </div>

            {/* Static filter labels with skeleton for options */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {[
                    { label: 'Priority', placeholder: 'All Priorities' },
                    { label: 'Status', placeholder: 'All Statuses' },
                    { label: 'Company', placeholder: 'All Companies' },
                    { label: 'Assigned User', placeholder: 'All Users' }
                ].map((filter, index) => (
                    <div key={index}>
                        {/* Static label - No skeleton */}
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            {filter.label}
                        </label>
                        {/* Skeleton for select options */}
                        <div className="animate-pulse">
                            <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100">
                                <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}

export default function SkeletonLoader({ type = 'table', ...props }) {
    switch (type) {
        case 'table':
            return <TableSkeleton {...props} />;
        case 'card':
            return <CardSkeleton {...props} />;
        case 'header-stats':
            return <HeaderStatsSkeleton {...props} />;
        case 'filters':
            return <FiltersSkeleton {...props} />;
        default:
            return <TableSkeleton {...props} />;
    }
}
