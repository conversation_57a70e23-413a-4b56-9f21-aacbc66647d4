<script setup>
import { ref, computed, nextTick, onMounted } from 'vue';
import { Head, Link, usePage } from '@inertiajs/vue3';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Inertia } from '@inertiajs/inertia';
import TextAreaInput from '@/Components/TextAreaInput.vue';
import axios from 'axios';

const pageTitle = ref('Complaints')
const props = usePage().props;
const ComplaintsSingle = ref(props.ComplaintsSingle || null);
const ComplainMessages = ref(props.complaintConversation || []);
const senderID = ref(props.senderID || '');
const senderComplainID = ref(props.senderComplainID || '');

var baseurl = window.location.origin;
var adminBaseurl = window.location.origin + '/dtadmin';

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const formattedDate = `${date.getFullYear()}-${('0' + (date.getMonth() + 1)).slice(-2)}-${('0' + date.getDate()).slice(-2)} ${('0' + date.getHours()).slice(-2)}:${('0' + date.getMinutes()).slice(-2)}`;
    return formattedDate;
}

const formatLasttime = (dateString) => {
    const date = new Date(dateString);
    const formattedTime = `${('0' + date.getHours()).slice(-2)}:${('0' + date.getMinutes()).slice(-2)}`;
    return formattedTime;
};

const senderUid = ref(props.senderID || '');
const senderCmpid = ref(props.senderComplainID || '');
const inputText = ref('');
const selectedFile = ref(null);
const filePreview = ref('');
const isImage = ref(false);

const sendMessage = async () => {
    try {
        const inputMessage = inputText.value;
        inputText.value = '';
        const token = document.head.querySelector('meta[name="csrf-token"]').content;

        const requestBody = {
            message: inputMessage,
            senderUid: senderUid,
            complain_id: senderCmpid,
        };

        const formData = new FormData();
        formData.append('message', inputMessage);
        formData.append('senderUid', senderUid.value);
        formData.append('complain_id', senderCmpid.value);

        let fileattachment = 0;
        const attachmentInput = document.querySelector('#attachFileInput');
        if (attachmentInput && attachmentInput.files[0]) {
            formData.append('attachment', attachmentInput.files[0]);
            fileattachment = 1;
        }

        if (fileattachment == 0 && inputMessage.trim() == '') {
            return;
        }

        const response = await fetch('/dtadmin/complaints/send-complain-message', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': token,
            },
            body: formData,
        });

        const responseData = await response.json();

        if (responseData.status) {

            if (attachmentInput) {
                attachmentInput.value = '';
            }

            ComplainMessages.value = responseData.data;

            selectedFile.value = null;
            filePreview.value = '';
            isImage.value = false;

            nextTick(() => {
                const lastMessage = document.querySelector('#messages > div:nth-last-child(2)');
                if (lastMessage) {
                    lastMessage.scrollIntoView({ behavior: 'smooth' });
                }
                const sourceMessage = document.querySelector('#inputsource');
                if (sourceMessage) {
                    sourceMessage.focus();
                }
            });


        }
    } catch (error) {
        console.error('Error sending message:', error);
    }
};

const progressStatus = ref(ComplaintsSingle.value?.progressStatus || '');
const previousStatus = ref(ComplaintsSingle.value?.progressStatus || '');

const updateProcessStatus = async () => {
    try {
        const confirmation = await Swal.fire({
            title: 'Are you sure?',
            text: 'You want to update this complain progress status',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes',
            cancelButtonText: 'Cancel',
            customClass: {
                confirmButton: 'dk-update-btn',
                cancelButton: 'dk-cancle-btn',
            },
            buttonsStyling: false,
        });

        if (confirmation.isConfirmed) {
            const requestData = {
                complaint_id: ComplaintsSingle.value.id,
                progressStatus: progressStatus.value,
            };

            const response = await axios.post(route('complaints.update-progress-status'), requestData);

            if (response.data.success) {

                ComplaintsSingle.complainStatus = response.data.complainStatus;

                var complainUrl = adminBaseurl + '/complaints/show/' + ComplaintsSingle.value.id;
                // Swal.fire({
                //     title: 'Success',
                //     text: response.data.message,
                //     icon: 'success',
                //     confirmButtonText: 'OK'
                // }).then((result) => {
                //     if (result.isConfirmed) {
                //         Inertia.visit(complainUrl);
                //     }
                // });
                Inertia.visit(complainUrl);
            } else {
                throw new Error('Failed to update status');
            }
        } else {

            progressStatus.value = previousStatus.value;

        }
    } catch (error) {
        console.error(error);
        Swal.fire('Error', 'An error occurred while updating the status.', 'error');
    }


};

const allowedFileTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
];

const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
        if (!allowedFileTypes.includes(file.type)) {
            Swal.fire({
                title: 'Invalid File Type',
                text: 'Please select a valid file type (image, Word, Excel, text, or PDF).',
                icon: 'error',
            });
            selectedFile.value = null;
            filePreview.value = '';
            isImage.value = false;
            return;
        }

        selectedFile.value = file;
        const fileType = file.type.split('/')[0];
        isImage.value = fileType === 'image';
        filePreview.value = isImage.value
            ? URL.createObjectURL(file)
            : 'Preview not available for this file type';
    }
};

const isImageFile = (fileUrl) => {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp'];
    const fileExtension = fileUrl.split('.').pop().toLowerCase();
    return imageExtensions.includes(fileExtension);
};

const getFileName = (fileUrl) => {
    return fileUrl.split('/').pop();
};

// Mapping for `priority` values
const priorityMap = {
    0: 'Low',
    1: 'Medium',
    2: 'High',
};
// Function to get the display label for `priority`
const getDisplayPriority = (priority) => priorityMap[priority] || 'Undefined';
</script>

<template>
    <Head :title="pageTitle" />

    <AuthenticatedLayout>
        <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
            leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
            <p class="bg-green-100 mb-4 p-2 rounded-md text-green-700" v-if="$page.props.flash?.message"
                :class="$page.props.flash.class">{{
                    $page.props.flash.message }}</p>
        </Transition>
        <div class="flex items-center gap-2 mb-6">
            <Link :href="route('complaints')" class="text-sm text-primary hover:underline flex items-center">
                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7"/></svg>
                Back to Complaints
            </Link>
            <h1 class="text-2xl font-bold">Complaint #{{ ComplaintsSingle.cmp_id }}</h1>
            <span
                class="px-2 py-1 rounded-full ml-2"
                :class="{
                    'text-red-600': ComplaintsSingle.priority === '2' || ComplaintsSingle.priority === 2,
                    'text-yellow-600': ComplaintsSingle.priority === '1' || ComplaintsSingle.priority === 1,
                    'text-green-600': ComplaintsSingle.priority === '0' || ComplaintsSingle.priority === 0
                }"
            >{{ getDisplayPriority(ComplaintsSingle.priority) }}</span>
        </div>

        <div class="grid gap-6 md:grid-cols-3">
            <div class="md:col-span-2 space-y-6">
                <div class="bg-white shadow rounded-lg p-6">
                    <h2 class="text-2xl font-semibold mb-2">{{ ComplaintsSingle.complainTitle }}</h2>
                    <div class="text-sm text-gray-500 mb-4 flex justify-between">
                    <span><b>Created:</b> {{ formatDate(ComplaintsSingle.created_at) }}</span>
                    <span><b>Last Updated:</b> {{ formatDate(ComplaintsSingle.updated_at) }}</span>
                    </div>
                    <p class="text-gray-800">{{ ComplaintsSingle.complainDetail }}</p>
                </div>

                <div class="bg-white shadow rounded-lg">
                    <div class="p-6 border-b">
                        <h2 class="text-lg font-semibold">Conversation History</h2>
                    </div>
                    <div id="messagesArea" class="p-6 h-[400px] overflow-y-auto space-y-4">
                        <div id="messages" ref="messagesContainer" class="space-y-3.5 px-0 pb-4 max-h-full overflow-auto">
                            <div v-for="(message, index) in ComplainMessages" :key="index" class="flex items-start space-x-4"
                                :class="[
                                    (message.senderId == $page.props.auth.user.id || message.senderId == $page.props.auth.user.parent_id) ? 'justify-end' : 'justify-start',
                                    `${message.senderId}_message`
                                ]">
                                <div v-if="message.senderId == $page.props.auth.user.id || message.senderId == $page.props.auth.user.parent_id"
                                    class="flex items-center space-x-4 px-4 rounded-md">
                                    <div class="ml-auto max-w-125 sendermessage">
                                        <div class="chat-receiver-cls">
                                            <p>{{ message.content }}</p>
                                            <div v-if="message.content_file" class="mt-2">
                                                <template v-if="isImageFile(baseurl + '/storage/' + message.content_file)">
                                                    <img :src="baseurl + '/storage/' + message.content_file"
                                                        alt="Attachment" class="rounded w-24" />
                                                </template>
                                                <template v-else>
                                                    <a :href="baseurl + '/storage/' + message.content_file"
                                                        target="_blank" class="text-cfp-300 underline">
                                                        {{ getFileName(baseurl + '/storage/' + message.content_file) }}
                                                    </a>
                                                </template>
                                            </div>
                                        </div>
                                        <p class="text-right font-medium text-[11px] text-gray-400">
                                            {{ formatDate(message.created_at) }}
                                        </p>
                                    </div>
                                </div>
                                <div v-else class="flex items-center space-x-4 rounded-md">
                                    <div class="max-w-125 receiverMessage">
                                        <div class="chat-sender-cls">
                                            <p>{{ message.content }}</p>
                                            <div v-if="message.content_file" class="mt-2">
                                                <template v-if="isImageFile(baseurl + '/storage/' + message.content_file)">
                                                    <img :src="baseurl + '/storage/' + message.content_file"
                                                        alt="Attachment" class="rounded w-24 h-24" />
                                                </template>
                                                <template v-else>
                                                    <a :href="baseurl + '/storage/' + message.content_file"
                                                        target="_blank" class="text-cfp-300 underline">
                                                        {{ getFileName(baseurl + '/storage/' + message.content_file) }}
                                                    </a>
                                                </template>
                                            </div>
                                        </div>
                                        <p class="font-medium text-[11px] text-gray-400">
                                            {{ formatDate(message.created_at) }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if="ComplaintsSingle.complainStatus != 'close' && $page.props.auth.user.profileType != 'admin'" class="p-6 border-t">
                        <h2 class="text-lg font-semibold mb-2">Add Feedback</h2>
                        <div class="relative w-full space-y-6">
                            <div class="relative">
                                <TextAreaInput ref="textInput" autocomplete="off" id="inputsource"
                                    name="complain_description" placeholder="Say something..." type="text"
                                    v-model="inputText"
                                    class="border-1 border-gray-200 bg-gray-100 px-5 py-3 pr-36 rounded-md w-full !h-30 min-h-[80px] text-gray-600 text-md focus:outline-none focus:placeholder-gray-400 placeholder-gray-600 resize-none" />
                                <div class="top-1/2 right-2 absolute flex items-center space-x-2 -translate-y-1/2">
                                    
                                </div>
                            </div>
                            <div>
                                <input
                                    type="file"
                                    @change="handleFileChange"
                                    accept="image/*,.doc,.docx,.xls,.xlsx,.txt,.pdf"
                                    id="attachFileInput"
                                    class="hidden"
                                />
                                <button
                                    type="button"
                                    onclick="document.getElementById('attachFileInput').click()"
                                    class="text-sm px-3 py-2 border rounded flex items-center"
                                >
                                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M21 15V8a2 2 0 00-2-2H7a2 2 0 00-2 2v7m4 4h6m0 0l-3-3m3 3l-3 3" /></svg>
                                    Attach File
                                </button>
                                <div v-if="selectedFile" class="mt-3">
                                    <p class="text-slate-500 text-sm">Selected File: {{ selectedFile.name }}</p>
                                    <img v-if="isImage" :src="filePreview" class="mt-2 rounded w-24 h-24" alt="Preview" />
                                </div>
                            </div>
                            <div>
                                <button
                                    @click="sendMessage"
                                    type="button"
                                    class="inline-flex gap-2 items-center bg-cfp-500 hover:bg-cfp-500/85 px-4 py-2 rounded-full text-white transition duration-200 ease-in-out focus:outline-none min-w-24">
                                    <span>Send Feedback</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                        stroke-width="1.5" stroke="currentColor" class="size-5">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M6 12 3.269 3.125A59.769 59.769 0 0 1 21.485 12 59.768 59.768 0 0 1 3.27 20.875L5.999 12Zm0 0h7.5" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="space-y-4">
                <div class="bg-white shadow rounded-lg p-6 space-y-4">
                    <h2 class="text-lg font-semibold mb-2">Customer Information</h2>
                    <div>
                    <h3 class="text-sm font-medium text-gray-500">Name</h3>
                    <p>{{ ComplaintsSingle.complainant_name }}</p>
                    </div>
                    <div>
                    <h3 class="text-sm font-medium text-gray-500">Email</h3>
                    <p>{{ ComplaintsSingle.complainant_email }}</p>
                    </div>
                    <div>
                    <h3 class="text-sm font-medium text-gray-500">Phone</h3>
                    <p>{{ ComplaintsSingle.complainant_phone }}</p>
                    </div>
                    <div>
                    <h3 class="text-sm font-medium text-gray-500">Company</h3>
                    <p>{{ ComplaintsSingle.companyName }}</p>
                    </div>
                </div>
                <div class="bg-white shadow rounded-lg p-6 space-y-4"
                    v-if="$page.props.auth.user.profileType == 'admin' || $page.props.auth.user.profileType == 'company' || $page.props.auth.user.profileType == 'agent'">
                    <label class="flex flex-col gap-2 font-medium text-gray-900 text-sm">
                        Progress Status:
                        <template v-if="ComplaintsSingle.complainStatus === 'close'">
                            <span class="text-red-600">Closed</span>
                        </template>
                        <template v-else>
                            <div class="flex items-center gap-4 mt-1">
                                <label class="flex items-center gap-2">
                                    <input type="radio" value="new" v-model="progressStatus"
                                        @change="updateProcessStatus"
                                        class="border-gray-300 rounded text-gray-900">
                                    New
                                </label>
                                <label class="flex items-center gap-2">
                                    <input type="radio" value="inprogress" v-model="progressStatus"
                                        @change="updateProcessStatus"
                                        class="border-gray-300 rounded text-gray-900">
                                    In progress
                                </label>
                                <label class="flex items-center gap-2">
                                    <input type="radio" value="need_user_input" v-model="progressStatus"
                                        @change="updateProcessStatus"
                                        class="border-gray-300 rounded text-gray-900">
                                    Need User Input
                                </label>
                                <label class="flex items-center gap-2">
                                    <input type="radio" value="resolved" v-model="progressStatus"
                                        @change="updateProcessStatus"
                                        class="border-gray-300 rounded text-gray-900">
                                    Resolved
                                </label>
                            </div>
                        </template>
                    </label>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>