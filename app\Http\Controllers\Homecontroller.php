<?php

namespace App\Http\Controllers;

use App\Models\Company;
use App\Models\Complaint;
use App\Models\Deals;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class Homecontroller extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index() {}

    public function dashboard()
    {

        $authDetail  = Auth::user();
        $profileType = $authDetail->profileType;

        if ($profileType == 'admin') {

            $companyIds        = Company::pluck('id')->toArray();
            $TotalDeals        = Deals::whereIn('companyId', $companyIds)->count();
            $totalUser         = User::count();
            $resolvedComplains = Complaint::where('complainStatus', 'close')->whereIn('companyId', $companyIds)->count();
            $totalComplains    = Complaint::whereIn('companyId', $companyIds)->count();

        } elseif ($profileType == 'user') {

            $companyIds        = Company::pluck('id')->toArray();
            $TotalDeals        = Deals::whereIn('companyId', $companyIds)->count();
            $totalUser         = User::count();
            $resolvedComplains = Complaint::where('complainStatus', 'close')->where('assignedToUserId', Auth::id())->count();
            $totalComplains    = Complaint::where('assignedToUserId', Auth::id())->count();

        } elseif ($profileType == 'agent') {

            $authDetail = Auth::user();
            $ownerID    = $authDetail->parent_id;

            $companyIds         = Company::where('userId', $ownerID)->pluck('id')->toArray();
            $TotalDeals         = Deals::whereIn('companyId', $companyIds)->count();
            $totalComplainUsers = Complaint::whereIn('companyId', $companyIds)->distinct()->pluck('assignedToUserId')->toArray();

            $totalUser         = User::whereIn('id', $totalComplainUsers)->count();
            $resolvedComplains = Complaint::where('complainStatus', 'close')->whereIn('companyId', $companyIds)->count();
            $totalComplains    = Complaint::whereIn('companyId', $companyIds)->count();

        } elseif ($profileType == 'company') {

            $companyIds         = Company::where('userId', Auth::id())->pluck('id')->toArray();
            $TotalDeals         = Deals::whereIn('companyId', $companyIds)->count();
            $totalComplainUsers = Complaint::whereIn('companyId', $companyIds)->distinct()->pluck('assignedToUserId')->toArray();

            $totalUser         = User::whereIn('id', $totalComplainUsers)->count();
            $resolvedComplains = Complaint::where('complainStatus', 'close')->whereIn('companyId', $companyIds)->count();
            $totalComplains    = Complaint::whereIn('companyId', $companyIds)->count();

        }

        // $allCompanies = Company::join('category','category.catId','=','company.catId')
        // ->join('country','country.id','=','company.countryId')
        // ->join('users','users.id','=','company.userId')
        // ->where('users.profileType','company')
        // ->select('company.*','country.name','category.catName','users.profilePicture')
        // ->paginate(env('PAGE_LIMIT'));
        $title     = 'Dashboard';
        $actionBtn = '';

        return Inertia::render('Dashboard', [
            'TotalDeals'        => $TotalDeals,
            'resolvedComplains' => $resolvedComplains,
            'totalComplains'    => $totalComplains,
            'totalUser'         => $totalUser,
            'title'             => $title,
            'actionBtn'         => $actionBtn,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
