<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentGateway extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'is_active',
        'test_mode',
        'test_config',
        'live_config',
        'webhook_secret',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'test_mode' => 'boolean',
        'test_config' => 'array',
        'live_config' => 'array',
    ];

    /**
     * Scope to get only active gateways.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the current configuration based on test mode.
     */
    public function getCurrentConfig(): array
    {
        return $this->test_mode ? ($this->test_config ?? []) : ($this->live_config ?? []);
    }

    /**
     * Get a specific configuration value.
     */
    public function getConfigValue(string $key): ?string
    {
        $config = $this->getCurrentConfig();
        return $config[$key] ?? null;
    }

    /**
     * Check if the gateway is properly configured.
     */
    public function isConfigured(): bool
    {
        $config = $this->getCurrentConfig();

        return match ($this->name) {
            'stripe' => !empty($config['secret_key']) && !empty($config['publishable_key']),
            'paypal' => !empty($config['client_id']) && !empty($config['client_secret']),
            default => false,
        };
    }

    /**
     * Get the required configuration fields for this gateway.
     */
    public function getRequiredFields(): array
    {
        return match ($this->name) {
            'stripe' => ['secret_key', 'publishable_key'],
            'paypal' => ['client_id', 'client_secret'],
            default => [],
        };
    }
}
