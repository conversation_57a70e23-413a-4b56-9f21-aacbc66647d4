import React from 'react';
import { Head, <PERSON> } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { Card } from '@/Components/UI/Card';
import { StatusBadge } from '@/Components/UI/StatusBadge';
import { FiDownload, FiArrowLeft, FiFileText, FiCalendar, FiDollarSign } from 'react-icons/fi';

export default function Invoices({ invoices, subscription }) {
    const getStatusColor = (status) => {
        switch (status) {
            case 'paid':
                return 'success';
            case 'pending':
                return 'warning';
            case 'failed':
                return 'danger';
            default:
                return 'secondary';
        }
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    const formatCurrency = (amount, currency = 'USD') => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    };

    return (
        <DashboardLayout>
            <Head title="Invoices" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link
                            href="/company/subscription/dashboard"
                            className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
                        >
                            <FiArrowLeft className="h-5 w-5 mr-2" />
                            Back to Dashboard
                        </Link>
                    </div>
                </div>

                <div className="flex items-center space-x-3">
                    <FiFileText className="h-8 w-8 text-cf-primary-600" />
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Invoices</h1>
                        <p className="text-gray-600">
                            View and download your subscription invoices
                        </p>
                    </div>
                </div>

                {/* Subscription Info */}
                <Card>
                    <div className="p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Current Subscription
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-500">Plan</label>
                                <p className="mt-1 text-sm text-gray-900">{subscription.plan.name}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-500">Status</label>
                                <div className="mt-1">
                                    <StatusBadge status={subscription.status}> {subscription.status} </StatusBadge>
                                </div>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-500">Total Invoices</label>
                                <p className="mt-1 text-sm text-gray-900">{invoices.total}</p>
                            </div>
                        </div>
                    </div>
                </Card>

                {/* Invoices List */}
                <Card>
                    <div className="p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-6">
                            Invoice History
                        </h3>

                        {invoices.data.length === 0 ? (
                            <div className="text-center py-12">
                                <FiFileText className="mx-auto h-12 w-12 text-gray-400" />
                                <h3 className="mt-2 text-sm font-medium text-gray-900">No invoices</h3>
                                <p className="mt-1 text-sm text-gray-500">
                                    You don't have any invoices yet.
                                </p>
                            </div>
                        ) : (
                            <div className="overflow-hidden">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Invoice
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Date
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Amount
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Status
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Actions
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {invoices.data.map((invoice) => (
                                            <tr key={invoice.id} className="hover:bg-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center">
                                                        <FiFileText className="h-5 w-5 text-gray-400 mr-3" />
                                                        <div>
                                                            <div className="text-sm font-medium text-gray-900">
                                                                {invoice.invoice_number || `INV-${String(invoice.id).padStart(6, '0')}`}
                                                            </div>
                                                            <div className="text-sm text-gray-500">
                                                                {invoice.billing_reason || 'Subscription'}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center text-sm text-gray-900">
                                                        <FiCalendar className="h-4 w-4 text-gray-400 mr-2" />
                                                        {formatDate(invoice.created_at)}
                                                    </div>
                                                    {invoice.due_date && (
                                                        <div className="text-sm text-gray-500">
                                                            Due: {formatDate(invoice.due_date)}
                                                        </div>
                                                    )}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center text-sm text-gray-900">
                                                        <FiDollarSign className="h-4 w-4 text-gray-400 mr-1" />
                                                        {invoice.formatted_amount_due || formatCurrency(invoice.amount_due)}
                                                    </div>
                                                    {invoice.is_paid && invoice.paid_at && (
                                                        <div className="text-sm text-gray-500">
                                                            Paid: {formatDate(invoice.paid_at)}
                                                        </div>
                                                    )}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <StatusBadge 
                                                        status={getStatusColor(invoice.status)}
                                                        text={invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                                                    >
                                                        {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                                                    </StatusBadge>
                                                    {invoice.is_overdue && (
                                                        <div className="mt-1">
                                                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                                Overdue
                                                            </span>
                                                        </div>
                                                    )}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                    <a
                                                        href={`/company/subscription/invoices/${invoice.id}/download`}
                                                        className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cf-primary-500"
                                                    >
                                                        <FiDownload className="h-4 w-4 mr-2" />
                                                        Download
                                                    </a>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>

                                {/* Pagination */}
                                {invoices.last_page > 1 && (
                                    <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                                        <div className="flex-1 flex justify-between sm:hidden">
                                            {invoices.prev_page_url && (
                                                <Link
                                                    href={invoices.prev_page_url}
                                                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                                >
                                                    Previous
                                                </Link>
                                            )}
                                            {invoices.next_page_url && (
                                                <Link
                                                    href={invoices.next_page_url}
                                                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                                >
                                                    Next
                                                </Link>
                                            )}
                                        </div>
                                        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                            <div>
                                                <p className="text-sm text-gray-700">
                                                    Showing{' '}
                                                    <span className="font-medium">{invoices.from}</span>
                                                    {' '}to{' '}
                                                    <span className="font-medium">{invoices.to}</span>
                                                    {' '}of{' '}
                                                    <span className="font-medium">{invoices.total}</span>
                                                    {' '}results
                                                </p>
                                            </div>
                                            <div>
                                                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                                    {invoices.links.map((link, index) => (
                                                        <Link
                                                            key={index}
                                                            href={link.url || '#'}
                                                            className={`relative inline-flex items-center px-2 py-2 border text-sm font-medium ${
                                                                link.active
                                                                    ? 'z-10 bg-cf-primary-50 border-cf-primary-500 text-cf-primary-600'
                                                                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                                            } ${
                                                                index === 0 ? 'rounded-l-md' : ''
                                                            } ${
                                                                index === invoices.links.length - 1 ? 'rounded-r-md' : ''
                                                            }`}
                                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                                        />
                                                    ))}
                                                </nav>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </Card>
            </div>
        </DashboardLayout>
    );
}
