import React, { useState } from 'react';
import { Link, useForm } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { Input } from '@/Components/FormElements';
import Button from '@/Components/Button';
import { Card, CardContent, PageHeader, StatusBadge } from '@/Components/UI';
import HeaderStats from '@/Components/HeaderStats';

export default function ApiCreate() {
    const [formData, setFormData] = useState({
        apiName: '',
        method: 'GET',
        apiUrl: '',
        description: '',
        status: 'draft'
    });

    const [requestParams, setRequestParams] = useState([
        { label: '', parameter: '', type: 'String' }
    ]);

    const [responseParams, setResponseParams] = useState([
        { label: '', parameter: '', type: 'String' }
    ]);

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const addRequestParam = () => {
        setRequestParams([...requestParams, { label: '', parameter: '', type: 'String' }]);
    };

    const removeRequestParam = (index) => {
        setRequestParams(requestParams.filter((_, i) => i !== index));
    };

    const updateRequestParam = (index, field, value) => {
        const updated = [...requestParams];
        updated[index][field] = value;
        setRequestParams(updated);
    };

    const addResponseParam = () => {
        setResponseParams([...responseParams, { label: '', parameter: '', type: 'String' }]);
    };

    const removeResponseParam = (index) => {
        setResponseParams(responseParams.filter((_, i) => i !== index));
    };

    const updateResponseParam = (index, field, value) => {
        const updated = [...responseParams];
        updated[index][field] = value;
        setResponseParams(updated);
    };

    const httpMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
    const dataTypes = ['String', 'Number', 'Boolean', 'Object', 'Array'];

    return (
        <DashboardLayout title="Create API">
            <title>Create API</title>
            
            <div className="space-y-6">
                <HeaderStats
                    title="API Management"
                    subtitle="Create and manage APIs for your applications"
                    buttonText="Back to APIs"
                    buttonLink="/apis"
                    stats={[
                        { value: "24", label: "Total APIs" },
                        { value: "18", label: "Active" },
                        { value: "6", label: "Deprecated" },
                        { value: "98.2%", label: "Uptime" },
                    ]}
                    gradientFrom="cf-primary-600"
                    gradientTo="blue-600"
                />

                <PageHeader 
                    title="Create API Configuration" 
                    subtitle="Define API endpoints and their parameters for integration"
                />

                <div className="max-w-4xl mx-auto">
                    <Card>
                        <CardContent className="p-8">
                            <form className="space-y-8">
                                {/* Basic API Information */}
                                <div className="space-y-6">
                                    <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                                        API Configuration
                                    </h3>
                                    
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="md:col-span-2">
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                API Name *
                                            </label>
                                            <Input
                                                name="apiName"
                                                value={formData.apiName}
                                                onChange={(e) => handleInputChange('apiName', e.target.value)}
                                                placeholder="e.g., User Authentication API"
                                                className="w-full"
                                            />
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                HTTP Method *
                                            </label>
                                            <select
                                                value={formData.method}
                                                onChange={(e) => handleInputChange('method', e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cf-primary focus:border-cf-primary"
                                            >
                                                {httpMethods.map(method => (
                                                    <option key={method} value={method}>{method}</option>
                                                ))}
                                            </select>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Status
                                            </label>
                                            <div className="flex gap-3">
                                                {['draft', 'active', 'deprecated'].map((status) => (
                                                    <label key={status} className="flex items-center">
                                                        <input
                                                            type="radio"
                                                            name="status"
                                                            value={status}
                                                            checked={formData.status === status}
                                                            onChange={(e) => handleInputChange('status', e.target.value)}
                                                            className="mr-2 text-cf-primary focus:ring-cf-primary"
                                                        />
                                                        <StatusBadge variant={
                                                            status === 'active' ? 'success' :
                                                            status === 'deprecated' ? 'error' : 'warning'
                                                        }>
                                                            {status}
                                                        </StatusBadge>
                                                    </label>
                                                ))}
                                            </div>
                                        </div>

                                        <div className="md:col-span-2">
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                API URL *
                                            </label>
                                            <Input
                                                name="apiUrl"
                                                value={formData.apiUrl}
                                                onChange={(e) => handleInputChange('apiUrl', e.target.value)}
                                                placeholder="https://api.example.com/v1/endpoint"
                                                className="w-full"
                                            />
                                        </div>

                                        <div className="md:col-span-2">
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Description
                                            </label>
                                            <Input
                                                name="description"
                                                value={formData.description}
                                                onChange={(e) => handleInputChange('description', e.target.value)}
                                                as="textarea"
                                                rows={3}
                                                placeholder="Describe what this API does..."
                                                className="w-full"
                                            />
                                        </div>
                                    </div>
                                </div>

                                {/* Request Parameters */}
                                <div className="space-y-6">
                                    <div className="flex items-center justify-between">
                                        <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2 flex-1">
                                            Request Parameters
                                        </h3>
                                        <Button
                                            type="button"
                                            onClick={addRequestParam}
                                            className="text-sm bg-gray-100 text-gray-700 hover:bg-gray-200"
                                        >
                                            Add Parameter
                                        </Button>
                                    </div>
                                    
                                    <div className="space-y-4">
                                        {requestParams.map((param, index) => (
                                            <div key={index} className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Label
                                                    </label>
                                                    <Input
                                                        value={param.label}
                                                        onChange={(e) => updateRequestParam(index, 'label', e.target.value)}
                                                        placeholder="e.g., User ID"
                                                        className="w-full"
                                                    />
                                                </div>
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Parameter
                                                    </label>
                                                    <Input
                                                        value={param.parameter}
                                                        onChange={(e) => updateRequestParam(index, 'parameter', e.target.value)}
                                                        placeholder="e.g., user_id"
                                                        className="w-full"
                                                    />
                                                </div>
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Type
                                                    </label>
                                                    <select
                                                        value={param.type}
                                                        onChange={(e) => updateRequestParam(index, 'type', e.target.value)}
                                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cf-primary focus:border-cf-primary"
                                                    >
                                                        {dataTypes.map(type => (
                                                            <option key={type} value={type}>{type}</option>
                                                        ))}
                                                    </select>
                                                </div>
                                                <div className="flex items-end">
                                                    <Button
                                                        type="button"
                                                        onClick={() => removeRequestParam(index)}
                                                        className="text-sm bg-red-100 text-red-700 hover:bg-red-200"
                                                    >
                                                        Remove
                                                    </Button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* Response Parameters */}
                                <div className="space-y-6">
                                    <div className="flex items-center justify-between">
                                        <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2 flex-1">
                                            Response Parameters
                                        </h3>
                                        <Button
                                            type="button"
                                            onClick={addResponseParam}
                                            className="text-sm bg-gray-100 text-gray-700 hover:bg-gray-200"
                                        >
                                            Add Parameter
                                        </Button>
                                    </div>
                                    
                                    <div className="space-y-4">
                                        {responseParams.map((param, index) => (
                                            <div key={index} className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Label
                                                    </label>
                                                    <Input
                                                        value={param.label}
                                                        onChange={(e) => updateResponseParam(index, 'label', e.target.value)}
                                                        placeholder="e.g., User Data"
                                                        className="w-full"
                                                    />
                                                </div>
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Parameter
                                                    </label>
                                                    <Input
                                                        value={param.parameter}
                                                        onChange={(e) => updateResponseParam(index, 'parameter', e.target.value)}
                                                        placeholder="e.g., user_data"
                                                        className="w-full"
                                                    />
                                                </div>
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Type
                                                    </label>
                                                    <select
                                                        value={param.type}
                                                        onChange={(e) => updateResponseParam(index, 'type', e.target.value)}
                                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cf-primary focus:border-cf-primary"
                                                    >
                                                        {dataTypes.map(type => (
                                                            <option key={type} value={type}>{type}</option>
                                                        ))}
                                                    </select>
                                                </div>
                                                <div className="flex items-end">
                                                    <Button
                                                        type="button"
                                                        onClick={() => removeResponseParam(index)}
                                                        className="text-sm bg-red-100 text-red-700 hover:bg-red-200"
                                                    >
                                                        Remove
                                                    </Button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                <div className="flex gap-3 pt-4">
                                    <Button type="submit">
                                        Save API
                                    </Button>
                                    <Link href="/apis">
                                        <Button type="button" className="bg-gray-100 text-gray-700 hover:bg-gray-200">
                                            Cancel
                                        </Button>
                                    </Link>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </DashboardLayout>
    );
}