<script setup>
import { ArrowPathIcon, CloudArrowUpIcon, FingerPrintIcon, LockClosedIcon } from '@heroicons/vue/24/outline'


// eslint-disable-next-line no-unused-vars
const features = [
	{
		name: 'Push to deploy',
		description:
			'Morbi viverra dui mi arcu sed. Tellus semper adipiscing suspendisse semper morbi. Odio urna massa nunc massa.',
		icon: CloudArrowUpIcon,
	},
	{
		name: 'SSL certificates',
		description:
			'Sit quis amet rutrum tellus ullamcorper ultricies libero dolor eget. Sem sodales gravida quam turpis enim lacus amet.',
		icon: LockClosedIcon,
	},
	{
		name: 'Simple queues',
		description:
			'Quisque est vel vulputate cursus. Risus proin diam nunc commodo. Lobortis auctor congue commodo diam neque.',
		icon: ArrowPathIcon,
	},
	{
		name: 'Advanced security',
		description:
			'Arcu egestas dolor vel iaculis in ipsum mauris. Tincidunt mattis aliquet hac quis. Id hac maecenas ac donec pharetra eget.',
		icon: FingerPrintIcon,
	},
]
</script>
<template>
	<div class="bg-white py-24 sm:py-32 max-md:py-6">
		<div class="mx-auto px-6 max-md:px-0 lg:px-8 max-w-7xl">
			<div class="mx-auto max-w-2xl lg:text-center">
				<h2 class="font-semibold text-base text-cfp-500 leading-7">Deploy faster</h2>
				<p class="mt-2 font-semibold text-3xl text-gray-900 sm:text-4xl tracking-tight">Everything you need to
					deploy your app</p>
				<p class="mt-6 text-gray-600 text-lg leading-8">Quis tellus eget adipiscing convallis sit sit eget
					aliquet quis. Suspendisse eget egestas a elementum pulvinar et feugiat blandit at. In mi viverra
					elit nunc.</p>
			</div>
			<div class="mx-auto mt-16 sm:mt-20 lg:mt-24 max-w-2xl lg:max-w-4xl">
				<dl class="gap-x-8 gap-y-10 lg:gap-y-16 grid grid-cols-1 lg:grid-cols-2 max-w-xl lg:max-w-none">
					<div v-for="feature in features" :key="feature.name" class="relative pl-16 text-left">
						<dt class="font-semibold text-base text-gray-900 leading-7">
							<div
								class="top-0 left-0 absolute flex justify-center items-center bg-cfp-500 rounded-md w-10 h-10">
								<component :is="feature.icon" class="w-6 h-6 text-white" aria-hidden="true" />
							</div>
							{{ feature.name }}
						</dt>
						<dd class="mt-2 text-base text-gray-600 leading-7">{{ feature.description }}</dd>
					</div>
				</dl>
			</div>
		</div>
	</div>
</template>