<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Complaint permissions
            'view complaints',
            'create complaints',
            'edit complaints',
            'delete complaints',
            'assign complaints',

            // Company permissions
            'view companies',
            'create companies',
            'edit companies',
            'delete companies',
            'manage company agents',

            // User permissions
            'view users',
            'create users',
            'edit users',
            'delete users',
            'manage user roles',

            // Agent permissions
            'view assigned complaints',
            'update complaint status',
            'add complaint messages',

            // Admin permissions
            'view all data',
            'manage system settings',
            'view reports',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Admin role - has all permissions
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminRole->syncPermissions(Permission::all());

        // Company role - can manage their own data
        $companyRole = Role::firstOrCreate(['name' => 'company']);
        $companyRole->syncPermissions([
            'view complaints',
            'create complaints',
            'edit complaints',
            'view companies',
            'edit companies',
            'manage company agents',
            'view users',
            'create users',
        ]);

        // Support Agent role - assigned by company
        $agentRole = Role::firstOrCreate(['name' => 'support_agent']);
        $agentRole->syncPermissions([
            'view assigned complaints',
            'update complaint status',
            'add complaint messages',
            'view complaints',
        ]);

        // End User role - basic permissions
        $endUserRole = Role::firstOrCreate(['name' => 'end_user']);
        $endUserRole->syncPermissions([
            'view complaints',
            'create complaints',
        ]);

        // Assign roles to existing users
        $adminUser = User::where('email', '<EMAIL>')->first();
        if ($adminUser) {
            $adminUser->syncRoles(['admin']);
        }

        // Get all users and assign roles
        $allUsers = User::all();
        $userCount = $allUsers->count();

        // Assign company role to some users (10% of users)
        $companyCount = max(1, intval($userCount * 0.1));
        $companyUsers = $allUsers->take($companyCount);
        foreach ($companyUsers as $user) {
            if ($user->email !== '<EMAIL>') {
                $user->syncRoles(['company']);
            }
        }

        // Assign support agent role to some users (20% of users)
        $agentCount = max(1, intval($userCount * 0.2));
        $agentUsers = $allUsers->slice($companyCount, $agentCount);
        foreach ($agentUsers as $user) {
            $user->syncRoles(['support_agent']);
        }

        // Assign end user role to remaining users
        $endUsers = $allUsers->slice($companyCount + $agentCount);
        foreach ($endUsers as $user) {
            if (!$user->hasAnyRole(['admin', 'company', 'support_agent'])) {
                $user->syncRoles(['end_user']);
            }
        }

        $this->command->info('Roles and permissions created successfully!');
        $this->command->info('Admin: ' . Role::findByName('admin')->users->count() . ' users');
        $this->command->info('Company: ' . Role::findByName('company')->users->count() . ' users');
        $this->command->info('Support Agent: ' . Role::findByName('support_agent')->users->count() . ' users');
        $this->command->info('End User: ' . Role::findByName('end_user')->users->count() . ' users');
    }
}
