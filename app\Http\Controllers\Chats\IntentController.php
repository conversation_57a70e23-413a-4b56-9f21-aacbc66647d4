<?php

namespace App\Http\Controllers\Chats;

use App\Http\Controllers\Controller;
use App\Models\CbIntent;
use App\Rules\LowercaseUnderscoreOnly;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;

class IntentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $title     = 'Intents';
        $actionBtn = ['title' => 'Add Intent', 'route' => route('intents.create')];
        CbIntent::checkOrCreateDetailfIntent(Auth::id());
        $allIntents = CbIntent::where('user_id', Auth::id())
            ->orderBy('id', 'desc')
            ->get();

        return Inertia::render('Chats/List', ['title' => $title, 'allIntents' => $allIntents, 'actionBtn' => $actionBtn, 'success' => session('success')]);
    }

    public function helpinfo()
    {
        return [
            'intent_name'             => '',
            'intent_context'          => 'Can be used to "remember" parameter value, so they can be passed between intents.',
            'intent_description'      => '',
            'intent_training_phrases' => 'Phrases you can expect from users, that will trigger the intent.',
            'intent_responses'        => 'Text,spoken and media rich response the agent will deliver to a user',
        ];
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $title    = 'Create Intent';
        $dataInfo = $this->helpinfo();

        return Inertia::render('Chats/Add', ['title' => $title, 'dataInfo' => $dataInfo]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Validate the request
        $request->validate([
            'name' => ['required', 'string', new LowercaseUnderscoreOnly, 'max:255', 'unique:cb_intents,name,NULL,id,user_id,'.Auth::id(), function ($attribute, $value, $fail) {
                $defaultNames = ['welcome', 'fallback'];
                if (in_array($value, $defaultNames)) {
                    $fail('The intent name "'.$value.'" is reserved and cannot be created.');
                }
            }],
            'context'          => ['nullable', 'string', new LowercaseUnderscoreOnly, 'max:255'],
            'description'      => 'nullable|string',
            'training_phrases' => 'nullable|array', // Validate as string
            'responses'        => 'nullable|array', // Validate as string
        ]);

        // Before saving, ensure the values are properly formatted
        $name    = strtolower(str_replace(' ', '_', $request->name));
        $context = $request->context ? strtolower(str_replace(' ', '_', $request->context)) : '';

        // Decode the training_phrases and responses if they exist
        $trainingPhrases = [];
        $responses       = [];

        if ($request->has('training_phrases')) {
            $trainingPhrases = $request->training_phrases; // Decode from JSON string
        }

        if ($request->has('responses')) {
            $responses = $request->responses; // Decode from JSON string
        }

        // Create a new intent
        $intent              = new CbIntent;
        $intent->user_id     = Auth::id();
        $intent->name        = $name;
        $intent->context     = $context;
        $intent->description = $request->description ?? '';

        // Save the intent
        $intent->save();

        // Store training phrases and responses if provided
        if (! empty($trainingPhrases)) {
            $intent->training_phrases = implode(', ', $trainingPhrases); // Store as a comma-separated string
        } else {
            $intent->training_phrases = '';
        }

        if (! empty($responses)) {
            $intent->responses = implode(', ', $responses); // Store as a comma-separated string
        } else {
            $intent->responses = '';
        }

        $intent->save(); // Save the intent with the training phrases and responses

        return Redirect::route('intents.edit', $intent->id)->with([
            'status'  => true,
            'message' => 'Intent saved successfully',
            'class'   => 'flex items-center p-2 mb-4 text-green-700 rounded-md bg-green-100',
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $title  = 'Intent Details';
        $intent = CbIntent::findOrFail($id);
        $title  = $intent->name;

        return Inertia::render('Chats/Show', [
            'title'  => $title,
            'intent' => $intent,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $title    = 'Update Intent';
        $intent   = CbIntent::find($id);
        $dataInfo = $this->helpinfo();

        return Inertia::render('Chats/Edit', ['title' => $title, 'intent' => $intent, 'dataInfo' => $dataInfo]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'name'             => ['required', 'string', new LowercaseUnderscoreOnly, 'max:255', 'unique:cb_intents,name,'.$id.',id,user_id,'.Auth::id()],
            'context'          => ['nullable', 'string', new LowercaseUnderscoreOnly, 'max:255'],
            'description'      => 'nullable|string',
            'training_phrases' => 'nullable|array', // Validate as string
            'responses'        => 'nullable|array', // Validate as string
        ]);

        // Decode the training_phrases and responses if they exist
        $trainingPhrases = [];
        $responses       = [];

        if ($request->has('training_phrases')) {
            $trainingPhrases = $request->training_phrases; // Decode from JSON string
        }

        if ($request->has('responses')) {
            $responses = $request->responses; // Decode from JSON string
        }

        $intent = CbIntent::find($id);

        if (in_array($request->name, ['welcome', 'fallback'])) {

        } else {
            $intent->name = $request->name;
        }

        $intent->context     = ($request->context) ? $request->context : '';
        $intent->description = ($request->description) ? $request->description : '';

        if (! empty($trainingPhrases)) {
            $intent->training_phrases = implode(', ', $trainingPhrases); // Store as a comma-separated string
        } else {
            $intent->training_phrases = '';
        }

        if (! empty($responses)) {
            $intent->responses = implode(', ', $responses); // Store as a comma-separated string
        } else {
            $intent->responses = '';
        }

        $intent->save();

        return Redirect::route('intents.edit', $id)->with([
            'status'  => true,
            'message' => 'Intent saved successfully',
            'class'   => 'flex items-center p-2 mb-4 text-green-700 rounded-md bg-green-100',
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $intent = CbIntent::find($id);

        // Prevent deletion if the intent name is 'welcome' or 'fallback'
        if (in_array($intent->name, ['welcome', 'fallback'])) {
            return response()->json(['success' => false, 'message' => 'The intent "'.$intent->name.'" cannot be deleted.'], 403);
        }

        $intent->forceDelete();

        return response()->json(['success' => true]);
    }
}
