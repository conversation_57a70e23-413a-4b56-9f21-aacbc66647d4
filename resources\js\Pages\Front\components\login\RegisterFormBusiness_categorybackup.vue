<script setup>
import { ref, computed } from 'vue';
import { useForm } from '@inertiajs/vue3';
import Button from '../reusable/Button.vue';


const props = defineProps(['Countries', 'Categories']);

let Countries = props.Countries;
let Categories = props.Categories;

const currentStep = ref(1);
const totalSteps = 3;
const touched = ref({});

const form = useForm({
  companyName: '',
  email: '',
  password: '',
  password_confirmation: '',
  logo: '',
  websiteUrl: '',
  companyPhone: '',
  companyAddress: '',
  city: '',
  state: '',
  country: '',
  zipcode: '',
  selectedCategory: ''
});

const validationRules = {
  companyName: (value) => {
    if (!value) return 'Company name is required';
    if (value.length < 2) return 'Company Name must be at least 2 characters';
    return null;
  },
  email: (value) => {
    if (!value) return 'Email is required';
    if (!/\S+@\S+\.\S+/.test(value)) return 'Email must be valid';
    return null;
  },
  password: (value) => {
    if (!value) return 'Password is required';
    if (value.length < 8) return 'Password must be at least 8 characters';
    return null;
  },
  password_confirmation: (value) => {
    if (!value) return 'Please confirm password';
    if (value !== form.password) return 'Passwords do not match';
    return null;
  },
  companyPhone: (value) => {
    if (!value) return 'Company phone is required';
    if (!/^\d+$/.test(value)) return 'Phone number must be numeric and cannot contain special characters';
    return null;
  },
  websiteUrl: (value) => {
    if (!value) return 'Website URL is required';
    if (!/^https?:\/\/.+/.test(value)) return 'Website URL must be valid';
    return null;
  },
  companyAddress: (value) => {
    if (!value) return 'Company address is required';
    return null;
  },
  city: (value) => {
    if (!value) return 'City is required';
    return null;
  },
  state: (value) => {
    if (!value) return 'State is required';
    return null;
  },
  country: (value) => {
    if (!value) return 'Country is required';
    return null;
  },
  zipcode: (value) => {
    if (!value) return 'Zip code is required';
    if (value.length > 8) return 'Zip code must not exceed 8 characters';
    return null;
  },
  selectedCategory: (value) => {
    if (!value) return 'Please select a company photo';
    return null;
  }
};

const errors = computed(() => {
  const validationErrors = {};
  for (const key in validationRules) {
    if (touched.value[key]) {
      const error = validationRules[key](form[key]);
      if (error) validationErrors[key] = error;
    }
  }
  return validationErrors;
});

const handleInput = (field) => {
  touched.value[field] = true;
};

const validateStep = () => {
  let stepValid = true;
  if (currentStep.value === 1) {
    ['companyName', 'email', 'password', 'password_confirmation'].forEach(field => {
      if (!form[field] || validationRules[field](form[field])) {
        touched.value[field] = true;
        stepValid = false;
      }
    });
  } else if (currentStep.value === 2) {
    ['companyPhone', 'websiteUrl', 'companyAddress', 'city', 'state', 'country', 'zipcode'].forEach(field => {
      if (!form[field] || validationRules[field](form[field])) {
        touched.value[field] = true;
        stepValid = false;
      }
    });
  } else if (currentStep.value === 3) {
    if (!form.selectedCategory || validationRules.selectedCategory(form.selectedCategory)) {
      touched.value.selectedCategory = true;
      stepValid = false;
    }
  }
  return stepValid;
};

const nextStep = () => {
  if (validateStep()) {
    currentStep.value++;
  }
};

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

const handleFileUpload = (event) => {
  const file = event.target.files[0];
  if (file) {
    form.logo = file; // Store file in the form data
  }
};

const submit = () => {
  if (validateStep()) {
    form.post(route('register-business-user'), {
      onSuccess: () => {
        // Swal.fire({
        //   title: 'Registration Successful',
        //   text: 'You have registered successfully!',
        //   icon: 'success',
        //   confirmButtonText: 'Okay',
        // });
      },
      onError: (errors) => {
        // Handle the validation errors returned by Inertia
        // Mark touched fields
        Object.keys(errors).forEach(key => {
          touched.value[key] = true;
        });

        // Gather error messages
        const errorMessages = Object.values(errors).flat().join('<br>');

        // Show SweetAlert popup with errors
        Swal.fire({
          title: 'Error!',
          html: errorMessages, // Display error messages in HTML format
          icon: 'error',
          confirmButtonText: 'OK',
        });
      },
    });
  }
};



</script>

<template>
  <div class="mx-auto p-6 w-full max-w-3xl">
    <div class="login-body-cls">
      <div class="mb-4">
        <h2 class="font-semibold text-3xl text-center text-gray-900">Signup for Business Account</h2>
        <p class="mt-2 text-center text-gray-600 text-sm">Please fill in all required information</p>
      </div>

      <!-- Step 1: Company Name, Email, Password, Confirm Password -->
      <div v-if="currentStep === 1" class="pt-4 border-t">
        <div class="space-y-4">
          <div class="space-y-1">
            <label for="companyName" class="block font-normal text-slate-800 text-sm">Company Name</label>
            <input id="companyName" v-model="form.companyName" @input="handleInput('companyName')" type="text"
              class="block border-gray-200 focus:border-cfp-500 px-4 py-2 rounded-md focus:ring-cfp-500 w-full"
              :class="{ 'border-red-500': errors.companyName }" />
            <p v-if="errors.companyName" class="mt-1 text-red-600 text-sm">{{ errors.companyName }}</p>
          </div>

          <div class="space-y-1">
            <label for="email" class="block font-normal text-slate-800 text-sm">Email Address</label>
            <input id="email" v-model="form.email" @input="handleInput('email')" type="email"
              class="block border-gray-200 focus:border-cfp-500 px-4 py-2 rounded-md focus:ring-cfp-500 w-full"
              :class="{ 'border-red-500': errors.email }" />
            <p v-if="errors.email" class="mt-1 text-red-600 text-sm">{{ errors.email }}</p>
          </div>

          <div class="space-y-1">
            <label for="password" class="block font-normal text-slate-800 text-sm">Password</label>
            <input id="password" v-model="form.password" @input="handleInput('password')" type="password"
              class="block border-gray-200 focus:border-cfp-500 px-4 py-2 rounded-md focus:ring-cfp-500 w-full"
              :class="{ 'border-red-500': errors.password }" />
            <p v-if="errors.password" class="mt-1 text-red-600 text-sm">{{ errors.password }}</p>
          </div>

          <div class="space-y-1">
            <label for="password_confirmation" class="block font-normal text-slate-800 text-sm">Confirm Password</label>
            <input id="password_confirmation" v-model="form.password_confirmation"
              @input="handleInput('password_confirmation')" type="password"
              class="block border-gray-200 focus:border-cfp-500 px-4 py-2 rounded-md focus:ring-cfp-500 w-full"
              :class="{ 'border-red-500': errors.password_confirmation }" />
            <p v-if="errors.password_confirmation" class="mt-1 text-red-600 text-sm">{{ errors.password_confirmation }}
            </p>
          </div>
        </div>
      </div>

      <!-- Step 2: Company Information -->
      <div v-if="currentStep === 2" class="pt-4 border-t">
        <div class="space-y-6">
          <div class="space-y-1">
            <label for="companyPhoto" class="block font-normal text-slate-800 text-sm">Company Photo</label>
            <input type="file" id="companyPhoto" @change="handleFileUpload"
              class="block border-gray-200 focus:border-cfp-500 px-4 py-2 border rounded-md w-full"
              :class="{ 'border-red-500': errors.logo }" />
            <p v-if="errors.logo" class="mt-1 text-red-600 text-sm">{{ errors.logo }}</p>
          </div>


          <div class="space-y-1">
            <label for="companyPhone" class="block font-normal text-slate-800 text-sm">Company Phone</label>
            <input id="companyPhone" v-model="form.companyPhone" @input="handleInput('companyPhone')" type="tel"
              class="block border-gray-200 focus:border-cfp-500 px-4 py-2 rounded-md focus:ring-cfp-500 w-full"
              :class="{ 'border-red-500': errors.companyPhone }"
              aria-invalid="errors.companyPhone ? 'true' : 'false'" />
            <p v-if="errors.companyPhone" class="mt-1 text-red-600 text-sm">{{ errors.companyPhone }}</p>
          </div>

          <div class="space-y-1">
            <label for="websiteUrl" class="block font-normal text-slate-800 text-sm">Website URL</label>
            <input id="websiteUrl" v-model="form.websiteUrl" @input="handleInput('websiteUrl')" type="url"
              class="block border-gray-200 focus:border-cfp-500 px-4 py-2 rounded-md focus:ring-cfp-500 w-full"
              :class="{ 'border-red-500': errors.websiteUrl }" />
            <p v-if="errors.websiteUrl" class="mt-1 text-red-600 text-sm">{{ errors.websiteUrl }}</p>
          </div>

          <div class="space-y-1">
            <label for="companyAddress" class="block font-normal text-slate-800 text-sm">Company Address</label>
            <input id="companyAddress" v-model="form.companyAddress" @input="handleInput('companyAddress')" type="text"
              class="block border-gray-200 focus:border-cfp-500 px-4 py-2 rounded-md focus:ring-cfp-500 w-full"
              :class="{ 'border-red-500': errors.companyAddress }" />
            <p v-if="errors.companyAddress" class="mt-1 text-red-600 text-sm">{{ errors.companyAddress }}</p>
          </div>

          <div class="space-y-1">
            <label for="city" class="block font-normal text-slate-800 text-sm">City</label>
            <input id="city" v-model="form.city" @input="handleInput('city')" type="text"
              class="block border-gray-200 focus:border-cfp-500 px-4 py-2 rounded-md focus:ring-cfp-500 w-full"
              :class="{ 'border-red-500': errors.city }" />
            <p v-if="errors.city" class="mt-1 text-red-600 text-sm">{{ errors.city }}</p>
          </div>

          <div class="space-y-1">
            <label for="state" class="block font-normal text-slate-800 text-sm">State</label>
            <input id="state" v-model="form.state" @input="handleInput('state')" type="text"
              class="block border-gray-200 focus:border-cfp-500 px-4 py-2 rounded-md focus:ring-cfp-500 w-full"
              :class="{ 'border-red-500': errors.state }" />
            <p v-if="errors.state" class="mt-1 text-red-600 text-sm">{{ errors.state }}</p>
          </div>

          <div class="space-y-1">
            <label for="country" class="block font-normal text-slate-800 text-sm">Country</label>
            <select id="country" v-model="form.country" @change="handleInput('country')"
              class="block border-gray-200 focus:border-cfp-500 px-4 py-2 rounded-md focus:ring-cfp-500 w-full"
              :class="{ 'border-red-500': errors.country }">
              <option v-for="(option, index) in Countries" :key="index" :value="option.id">
                {{ option.name }}
              </option>
            </select>
            <p v-if="errors.country" class="mt-1 text-red-600 text-sm">{{ errors.country }}</p>
          </div>

          <div class="space-y-1">
            <label for="zipcode" class="block font-normal text-slate-800 text-sm">Zip Code</label>
            <input id="zipcode" v-model="form.zipcode" @input="handleInput('zipcode')" type="text"
              class="block border-gray-200 focus:border-cfp-500 px-4 py-2 rounded-md focus:ring-cfp-500 w-full"
              :class="{ 'border-red-500': errors.zipcode }" aria-invalid="errors.zipcode ? 'true' : 'false'" />
            <p v-if="errors.zipcode" class="mt-1 text-red-600 text-sm">{{ errors.zipcode }}</p>
          </div>


        </div>
      </div>

      <!-- Step 3: Image Selection -->
      <div v-if="currentStep === 3" class="pt-4 border-t">
        <h3 class="font-semibold text-lg">Select Category</h3>
        <div class="gap-4 grid grid-cols-3 mt-4">
          <div v-for="(image, index) in Categories" :key="index" class="relative cursor-pointer"
            @click="form.selectedCategory = image.catId">
            <img :src="image.catPicture" alt="Image" class="rounded-md w-full h-32 object-cover" />
            <div class="absolute inset-0 flex justify-center items-center" v-if="form.selectedCategory === image.catId">
              <span class="bg-cfp-500 px-2 py-1 rounded text-sm text-white">Selected</span>
            </div>
            <span class="caption">{{ image.catName }}</span>
          </div>
        </div>
        <p v-if="errors.selectedCategory" class="mt-1 text-red-600 text-sm">{{ errors.selectedCategory }}</p>
      </div>

      <div class="flex flex-row justify-between mt-4 gap-4">
        <Button title="Previous" v-if="currentStep > 1" type="button" @click="previousStep"
          class="flex-1 m-0 max-md:w-auto !text-center !table dk-cancle-btn" />
        <Button title="Next" v-if="currentStep < totalSteps" type="button" @click="nextStep"
          class="flex-1 m-0 max-md:w-auto dk-update-btn" />
        <Button title="Submit" v-if="currentStep === totalSteps" type="button" @click="submit"
          class="flex-1 m-0 max-md:w-auto dk-update-btn" />
      </div>
    </div>
  </div>
</template>



<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>