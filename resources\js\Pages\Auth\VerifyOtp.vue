<script setup>
import { useForm } from '@inertiajs/vue3'
import InputError from '@/Components/InputError.vue'
import InputLabel from '@/Components/InputLabel.vue'
import PrimaryButton from '@/Components/PrimaryButton.vue'
import TextInput from '@/Components/TextInput.vue'
import { ref } from 'vue'
import { Head, usePage } from '@inertiajs/vue3'

const pageTitle = ref('Verify OTP')
const resending = ref(false)

const props = defineProps({
    email: String,
    message: String
})

const form = useForm({
    email: props.email,
    otp: ''
})

const submit = () => {
    form.post(route('verify-otp.verify'))
}

const resendOtp = async () => {
    if (resending.value) return
    
    resending.value = true
    try {
        let token = document.head.querySelector('meta[name="csrf-token"]').content;
        const response = await fetch(route('resend-otp'), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': token
            },
            body: JSON.stringify({ email: form.email })
        })
        
        const data = await response.json()
        if (data.status) {
            form.reset('otp')
        }
    } catch (error) {
        console.error('Failed to resend OTP:', error)
    } finally {
        resending.value = false
    }
}
</script>

<template>
<Head :title="pageTitle" />
	<App>
		

    <div class="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gray-100">
        <div class="w-full sm:max-w-md mt-6 px-6 py-4 bg-white shadow-md overflow-hidden sm:rounded-lg">
            <h2 class="text-2xl font-bold mb-4 text-center">Verify Email</h2>
            
            <div v-if="message" class="mb-4 text-sm text-green-600">
                {{ message }}
            </div>

            <form @submit.prevent="submit">
                <input type="hidden" v-model="form.email">

                <div class="mt-4">
                    <InputLabel for="otp" value="Verification Code" />
                    <TextInput
                        id="otp"
                        type="text"
                        class="mt-1 block w-full"
                        v-model="form.otp"
                        required
                        autofocus
                        maxlength="6"
                        placeholder="Enter 6-digit code"
                    />
                    <InputError class="mt-2" :message="form.errors.otp" />
                </div>

                <div class="flex items-center justify-between mt-4">
                    <button 
                        type="button" 
                        @click="resendOtp" 
                        class="text-sm text-gray-600 hover:text-gray-900"
                        :disabled="resending"
                    >
                        {{ resending ? 'Sending...' : 'Resend Code' }}
                    </button>

                    <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                        Verify Email
                    </PrimaryButton>
                </div>
            </form>
        </div>
    </div>
    </App>
</template> 