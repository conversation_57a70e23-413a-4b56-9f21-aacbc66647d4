<?php

namespace App\Http\Controllers;

use App\Actions\CreateComplaintAction;
use App\Actions\UpdateComplaintAction;
use App\Http\Requests\CreateComplaintRequest;
use App\Http\Requests\StoreMessageRequest;
use App\Http\Requests\UpdateComplaintRequest;
use App\Models\Complaint;
use App\Models\Company;
use App\Models\ComplaintMessage;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class ComplaintController extends Controller
{
    public function index(): Response
    {
        return Inertia::render('Complaint/Index');
    }

    public function create(): Response
    {
        $companies = Company::query()
            ->select('id', 'companyName')
            ->get();

        $users = User::query()
            ->select('id', 'name', 'email')
            ->get();

        return Inertia::render('Complaint/Create', [
            'companies' => $companies,
            'users' => $users,
        ]);
    }

    public function store(CreateComplaintRequest $request, CreateComplaintAction $action)
    {
        $action->handle($request->validated());

        return redirect()->route('complains.index')->with([
            'status' => true,
            'message' => 'Complaint created successfully.',
            'class' => 'text-sm text-green-400',
        ]);
    }

    public function show($complaint): Response
    {
        $complaint = Complaint::where('cmp_id', $complaint)->first();

        $complaint->load(['company', 'assignedUser', 'messages' => function ($query) {
            $query->orderBy('created_at', 'desc');
        }, 'messages.sender']);

        return Inertia::render('Complaint/View', [
            'complaint' => [
                'id' => $complaint->id,
                'cmp_id' => $complaint->cmp_id,
                'title' => $complaint->complainTitle,
                'description' => $complaint->complainDetail,
                'priority' => $complaint->priority_label,
                'priority_key' => $complaint->priority,
                'status' => $complaint->status_label,
                'company' => $complaint->company,
                'assignedUser' => $complaint->assignedUser,
                'created_at' => $complaint->created_at,
                'updated_at' => $complaint->updated_at,
                'messages' => $complaint->messages->map(function ($message) {
                    return [
                        'id' => $message->id,
                        'content' => $message->content,
                        'sender' => $message->sender,
                        'created_at' => $message->created_at,
                    ];
                }),
            ],
        ]);
    }

    public function edit($complaint): Response
    {
        $complaint = Complaint::where('cmp_id', $complaint)->where('progressStatus', '!=', 'resolved')->firstOrFail();

        $companies = Company::query()
            ->select('id', 'companyName')
            ->get();

        $users = User::query()
            ->select('id', 'name', 'email')
            ->get();

        return Inertia::render('Complaint/Edit', [
            'complaint' => [
                'id' => $complaint->id,
                'cmp_id' => $complaint->cmp_id,
                'title' => $complaint->complainTitle,
                'description' => $complaint->complainDetail,
                'priority' => $complaint->priority,
                'company_id' => $complaint->companyId,
                'assigned_user_id' => $complaint->assignedToUserId,
                'status' => $complaint->progressStatus,
            ],
            'companies' => $companies,
            'users' => $users,
        ]);
    }

    public function update(UpdateComplaintRequest $request, $complaint, UpdateComplaintAction $action)
    {
        $complaint = Complaint::where('cmp_id', $complaint)->where('progressStatus', '!=', 'resolved')->firstOrFail();

        $action->handle($complaint, $request->validated());

        return redirect()->back()->with([
            'status' => true,
            'message' => 'Complaint updated successfully.',
            'class' => 'text-sm text-green-400',
        ]);
    }

    public function destroy($complaint)
    {
        $complaint = Complaint::where('cmp_id', $complaint)->first();

        try {
            $complaint->delete();

            // Check if it's an AJAX request
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Complaint deleted successfully'
                ]);
            }

            return redirect()->route('complains.index')->with([
                'status' => true,
                'message' => 'Complaint deleted successfully.',
                'class' => 'text-sm text-green-400',
            ]);
        } catch (\Exception $e) {
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete complaint'
                ], 500);
            }

            return redirect()->route('complains.index')->with([
                'status' => false,
                'message' => 'Failed to delete complaint.',
                'class' => 'text-sm text-red-400',
            ]);
        }
    }

    public function apiIndex(Request $request, string $status = '')
    {
        $perPage = $request->get('per_page', 10);
        $page = $request->get('page', 1);

        $query = Complaint::query()
            ->with(['company', 'assignedUser'])
            ->orderBy('created_at', 'desc');

        if ($status === 'unresolved' && $request->status !== 'resolved') {
            $query->whereNotIn('progressStatus', ['resolved']);
        }

        // Apply filters
        if ($request->filled('priority')) {
            $query->where('priority', $request->get('priority'));
        }

        if ($request->filled('status')) {
            $query->where('progressStatus', $request->get('status'));
        }

        if ($request->filled('company_id')) {
            $query->where('companyId', $request->get('company_id'));
        }

        if ($request->filled('assigned_user_id')) {
            $query->where('assignedToUserId', $request->get('assigned_user_id'));
        }

        // Apply search
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('complainTitle', 'like', "%{$search}%")
                    ->orWhere('cmp_id', 'like', "%{$search}%")
                    ->orWhere('complainDetail', 'like', "%{$search}%");
            });
        }

        $complaints = $query->paginate($perPage, ['*'], 'page', $page);

        $data = $complaints->getCollection()->map(function ($complaint) {
            return [
                'id' => $complaint->id,
                'cmp_id' => $complaint->cmp_id,
                'title' => $complaint->complainTitle,
                'description' => $complaint->complainDetail,
                'priority' => $complaint->priority_label,
                'status' => $complaint->status_label,
                'company' => $complaint->company?->companyName,
                'assignedUser' => $complaint->assignedUser?->name,
                'created_at' => $complaint->created_at->format('Y-m-d'),
                'updated_at' => $complaint->updated_at->format('Y-m-d'),
            ];
        });

        // Calculate stats based on current filters
        $statsQuery = Complaint::query();

        // Apply same filters for stats
        if ($request->filled('priority')) {
            $statsQuery->where('priority', $request->get('priority'));
        }
        if ($request->filled('status')) {
            $statsQuery->where('progressStatus', $request->get('status'));
        }
        if ($request->filled('company_id')) {
            $statsQuery->where('companyId', $request->get('company_id'));
        }
        if ($request->filled('assigned_user_id')) {
            $statsQuery->where('assignedToUserId', $request->get('assigned_user_id'));
        }
        if ($request->filled('search')) {
            $search = $request->get('search');
            $statsQuery->where(function ($q) use ($search) {
                $q->where('complainTitle', 'like', "%{$search}%")
                    ->orWhere('cmp_id', 'like', "%{$search}%")
                    ->orWhere('complainDetail', 'like', "%{$search}%");
            });
        }

        $stats = [
            'total' => $statsQuery->count(),
            'open' => (clone $statsQuery)->where('complainStatus', 'open')->count(),
            'new' => (clone $statsQuery)->where('progressStatus', 'new')->count(),
            'in_progress' => (clone $statsQuery)->where('progressStatus', 'inprogress')->count(),
            'resolved' => (clone $statsQuery)->where('progressStatus', 'resolved')->count(),
        ];

        return response()->json([
            'complaints' => $data,
            'pagination' => [
                'current_page' => $complaints->currentPage(),
                'last_page' => $complaints->lastPage(),
                'per_page' => $complaints->perPage(),
                'total' => $complaints->total(),
                'from' => $complaints->firstItem(),
                'to' => $complaints->lastItem(),
                'has_more_pages' => $complaints->hasMorePages(),
            ],
            'stats' => $stats,
        ]);
    }

    public function getFilterOptions()
    {
        $companies = Company::select('id', 'companyName')
            ->orderBy('companyName')
            ->get();

        $users = User::select('id', 'name')
            ->orderBy('name')
            ->get();

        $priorities = [
            ['value' => '0', 'label' => 'Low'],
            ['value' => '1', 'label' => 'Medium'],
            ['value' => '2', 'label' => 'High'],
        ];

        $statuses = [
            ['value' => 'new', 'label' => 'New'],
            ['value' => 'inprogress', 'label' => 'In Progress'],
            ['value' => 'need_user_input', 'label' => 'Need User Input'],
            ['value' => 'resolved', 'label' => 'Resolved'],
        ];

        return response()->json([
            'companies' => $companies,
            'users' => $users,
            'priorities' => $priorities,
            'statuses' => $statuses,
        ]);
    }

    public function storeMessage(StoreMessageRequest $request, $complaint)
    {
        $complaint = Complaint::where('cmp_id', $complaint)->first();
        $sender = Auth::user();

        $message = new ComplaintMessage();
        $message->complainId = $complaint->id;
        $message->senderId = $sender->id;
        $message->content = $request->message;
        $message->save();

        return redirect()->back()->with([
            'status' => true,
            'message' => 'Message sent successfully.',
        ]);
    }
}
