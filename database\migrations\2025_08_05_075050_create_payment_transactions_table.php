<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('subscription_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('transaction_id')->unique(); // Gateway transaction ID
            $table->enum('gateway', ['stripe', 'paypal']);
            $table->enum('type', ['payment', 'refund', 'chargeback'])->default('payment');
            $table->enum('status', ['pending', 'succeeded', 'failed', 'canceled', 'refunded'])->default('pending');
            $table->unsignedBigInteger('amount'); // Amount in cents
            $table->string('currency', 3)->default('USD');
            $table->json('gateway_response')->nullable(); // Full gateway response
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index(['gateway', 'status']);
            $table->index(['type', 'status']);
            $table->index('transaction_id');
        });
    }
};
