<?php

namespace Database\Factories;

use App\Models\Company;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Complaint>
 */
class ComplaintFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $complaintTitles = [
            'Login Issues with User Account',
            'Payment Processing Delays',
            'Feature Request: Dark Mode',
            'Data Export Functionality Not Working',
            'Email Notifications Not Received',
            'Mobile App Crashes on Startup',
            'Slow Loading Times',
            'Unable to Upload Files',
            'Password Reset Not Working',
            'Dashboard Display Issues',
            'API Integration Problems',
            'Database Connection Errors',
            'User Interface Bugs',
            'Security Vulnerability Report',
            'Performance Optimization Request',
            'Third-party Integration Issues',
            'Billing and Invoice Problems',
            'Customer Support Response Time',
            'Feature Enhancement Request',
            'System Maintenance Issues',
        ];

        $complaintDescriptions = [
            'Users are experiencing difficulties logging into their accounts. The system shows an error message "Invalid credentials" even with correct login information.',
            'Payment transactions are taking longer than usual to process. Customers are reporting delays of up to 24 hours.',
            'Multiple users have requested a dark mode option for better user experience during night time usage.',
            'The data export feature is not generating CSV files correctly. Some columns are missing and data appears corrupted.',
            'Email notifications for important updates are not being sent to users. This affects user engagement and communication.',
            'The mobile application crashes immediately upon startup on certain Android devices. This affects user accessibility.',
            'Page loading times have increased significantly, affecting user experience and productivity.',
            'Users cannot upload files larger than 5MB. The upload process fails without proper error messages.',
            'The password reset functionality is not working. Users are not receiving reset emails.',
            'Dashboard widgets are not displaying correct data. Some charts show outdated information.',
            'Third-party API integrations are failing intermittently, causing data synchronization issues.',
            'Database connection timeouts are occurring during peak usage hours, affecting system performance.',
            'Several UI elements are misaligned on mobile devices, affecting the overall user experience.',
            'A potential security vulnerability has been identified in the user authentication system.',
            'System performance needs optimization to handle increased user load and data volume.',
            'Integration with external services is causing unexpected errors and data inconsistencies.',
            'Billing calculations are incorrect for certain subscription plans, affecting revenue accuracy.',
            'Customer support response times have increased beyond acceptable limits, affecting customer satisfaction.',
            'Users have requested additional features to improve workflow efficiency and productivity.',
            'Regular system maintenance is causing extended downtime, affecting business operations.',
        ];

        return [
            'companyId' => Company::factory(),
            'assignedToUserId' => User::factory(),
            'priority' => fake()->randomElement(['0', '1', '2']),
            'progressStatus' => fake()->randomElement(['new', 'inprogress', 'need_user_input', 'resolved']),
            'complainTitle' => fake()->randomElement($complaintTitles),
            'complainDetail' => fake()->randomElement($complaintDescriptions),
            'complainStatus' => fake()->randomElement(['open', 'close']),
            'cmp_id' => null, // Will be set after creation
        ];
    }
}
