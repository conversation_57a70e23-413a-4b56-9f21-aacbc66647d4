<?php

namespace App\Http\Controllers;

use App\Models\Bots;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class SettingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $allAgents = Bots::join('company', 'company.id', '=', 'bots.company_id')
            ->select('company.companyAdd', 'company.city', 'company.state', 'company.zipCode', 'bots.*')
            ->orderBy('bots.id', 'desc')->where('bots.user_id', Auth::id())
            ->get();

        return Inertia::render('Bots/BotList', ['allAgents' => $allAgents, 'success' => session('success')]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $allCompny = Company::where('userId', Auth::id())->get();

        return Inertia::render('Bots/AddChatbot', ['allCompny' => $allCompny]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $customMessages = [
            'required' => 'The :attribute field is required.',
            'email'    => 'The :attribute must be a valid email address.',
            'unique'   => 'The :attribute has already been taken.',
        ];

        $validatedData = $request->validate([
            'chatbot_name'           => 'required|unique:bots',
            'company_id'             => 'required|unique:bots',
            'chatbot_project_id'     => 'required|unique:bots',
            'chatbot_private_key_id' => 'required|unique:bots',
            'chatbot_client_email'   => 'required|email|unique:bots',
            'chatbot_client_id'      => 'required|unique:bots',
            'chatbot_private_key'    => 'required|unique:bots',
        ], $customMessages);

        $userId = Auth::id();

        $saveAgent                         = new Bots;
        $saveAgent->user_id                = $userId;
        $saveAgent->chatbot_name           = $request->chatbot_name;
        $saveAgent->company_id             = $request->company_id;
        $saveAgent->chatbot_project_id     = $request->chatbot_project_id;
        $saveAgent->chatbot_private_key_id = $request->chatbot_private_key_id;
        $saveAgent->chatbot_client_email   = $request->chatbot_client_email;
        $saveAgent->chatbot_client_id      = $request->chatbot_client_id;
        $saveAgent->chatbot_private_key    = $request->chatbot_private_key;
        $saveAgent->save();

        return Redirect::back()->with([
            'status'  => true,
            'message' => 'Data saved successfully',
            'class'   => 'flex items-center p-2 mb-4 text-green-700 rounded-md bg-green-100',
        ]);

    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {

        $agentDetail = Bots::where('user_id', Auth::id())->where('id', $id)->first();
        $allCompny   = Company::where('userId', Auth::id())->get();

        return Inertia::render('Bots/EditChatbot', ['agentDetail' => $agentDetail, 'allCompny' => $allCompny]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {

        $customMessages = [
            'required' => 'The :attribute field is required.',
            'email'    => 'The :attribute must be a valid email address.',
            'unique'   => 'The :attribute has already been taken.',
        ];

        $validatedData = $request->validate([

            'company_id' => [
                'required',
                Rule::unique('bots')->ignore($request->chatbot_id),
            ],
            'chatbot_name' => [
                'required',
                Rule::unique('bots')->ignore($request->chatbot_id),
            ],
            'chatbot_project_id' => [
                'required',
                Rule::unique('bots')->ignore($request->chatbot_id),
            ],
            'chatbot_private_key_id' => [
                'required',
                Rule::unique('bots')->ignore($request->chatbot_id),
            ],
            'chatbot_client_email' => [
                'required',
                'email',
                Rule::unique('bots')->ignore($request->chatbot_id),
            ],
            'chatbot_client_id' => [
                'required',
                Rule::unique('bots')->ignore($request->chatbot_id),
            ],
            'chatbot_private_key' => [
                'required',
                Rule::unique('bots')->ignore($request->chatbot_id),
            ],
        ], $customMessages);

        $saveAgent                         = Bots::where('user_id', Auth::id())->where('id', $request->chatbot_id)->first();
        $saveAgent->company_id             = $request->company_id;
        $saveAgent->chatbot_name           = $request->chatbot_name;
        $saveAgent->chatbot_project_id     = $request->chatbot_project_id;
        $saveAgent->chatbot_private_key_id = $request->chatbot_private_key_id;
        $saveAgent->chatbot_client_email   = $request->chatbot_client_email;
        $saveAgent->chatbot_client_id      = $request->chatbot_client_id;
        $saveAgent->chatbot_private_key    = $request->chatbot_private_key;
        $saveAgent->save();

        return Redirect::back()->with([
            'status'  => true,
            'message' => 'Data saved successfully',
            'class'   => 'flex items-center p-2 mb-4 text-green-700 rounded-md bg-green-100',
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {

        $agentData = Bots::where('user_id', Auth::id())->where('id', $id)->first();
        if ($agentData) {
            $agentData->delete();

            return response()->json(['success' => true]);
        }

        return response()->json(['success' => true]);
    }
}
