<?php

namespace App\Http\Controllers\dtadmin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class ManageUserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $roleUser = $request->utype ?? 'all';
        $query    = User::query();
        $roles    = ['all', 'user', 'agent', 'company'];

        // Exclude admin role
        $query->where('profileType', '!=', 'admin');

        if ($roleUser !== 'all') {
            $query->where('profileType', $roleUser);
        }
        $users = $query->paginate(env('PAGE_LIMIT'));
        $title = 'Users List';

        return Inertia::render('DtAdmin/ManageUser/List', [
            'usersData'   => $users,
            'title'       => $title,
            'currentRole' => $roleUser,
            'roles'       => $roles,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $userDetail = User::where('id', $id)->where('profileType', '!=', 'admin')->first();
        if (! $userDetail) {
            abort(404);
        }
        $title = 'Users List';

        return Inertia::render('DtAdmin/ManageUser/Edit', ['userDetail' => $userDetail, 'title' => $title]);
    }

    public function accessPortal($id)
    {
        $user = User::where('id', $id)->where('profileType', '!=', 'admin')->first();
        if (! $user) {
            return redirect()->back()->withErrors(['message' => 'User not found.']);
        }
        Session::put('impersonate', Auth::id());
        Auth::login($user);

        return redirect()->route('dashboard'); // Change '/portal' to your actual portal route.
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {

        $validator = Validator::make($request->all(), [
            'name'               => 'required|string|max:255',
            'company_name'       => 'nullable|string|max:255',
            'phone'              => 'nullable|string|max:20',
            'verificationStatus' => 'required|string|in:not_verified,verified',
            'status'             => 'required|string|in:inactive,active',
            'profilePicture'     => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);

        if ($validator->fails()) {
            return Redirect::back()->withErrors($validator)->with([ // Use withErrors()
                'status' => false,
                'class'  => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
            ]);
        }

        $user = User::find($id);

        if (! $user) {
            return Redirect::back()->with([
                'status'  => false,
                'message' => 'User not found',
                'class'   => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
            ]);
        }

        $user->name               = $request->name;
        $user->company_name       = $request->company_name;
        $user->phone              = $request->phone;
        $user->verificationStatus = $request->verificationStatus;
        $user->status             = $request->status;

        if ($request->hasFile('profilePicture')) {
            if ($user->profilePicture) {
                Storage::disk('public')->delete($user->profilePicture);
            }

            // Store new profile picture
            $path                 = $request->file('profilePicture')->store('images/profile', 'public');
            $user->profilePicture = $path;
        }

        $user->save();

        return Redirect::back()->with([
            'status'     => true,
            'message'    => 'User updated successfully',
            'class'      => 'flex items-center p-2 mb-4 rounded-md bg-green-100',
            'userDetail' => $user,
        ]);

    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $user = User::find($id);
            if ($user->profileType == 'admin') {

                return response()->json(['success' => false, 'message' => 'not allow to delete admin role']);

            } else {

                User::deleteUserRelationTable($id);
                $user->delete();

                return response()->json(['success' => true]); // Return JSON response

            }

        } catch (Exception $ex) {
            return response()->json(['success' => false, 'message' => $ex->getMessage()]); // Return JSON response
        }

    }
}
