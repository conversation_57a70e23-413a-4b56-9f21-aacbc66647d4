<script setup>
defineProps({
    type: {
        type: String,
        default: 'button',
    },
});
</script>

<template>
    <button :type="type"
        class="inline-flex items-center border-gray-300 bg-white hover:bg-gray-50 disabled:opacity-25 shadow-sm px-4 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 font-semibold text-slate-800 text-xs uppercase tracking-widest focus:outline-none transition duration-150 ease-in-out">
        <slot />
    </button>
</template>
