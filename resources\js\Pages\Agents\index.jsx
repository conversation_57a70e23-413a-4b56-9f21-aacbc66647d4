import React, { useState } from 'react';
import { <PERSON> } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import {
    DataTable,
    DataTableHeader,
    DataTableBody,
    DataTableRow,
    DataTableHeaderCell,
    DataTableCell,
    DataTableActions,
    PageHeader,
    StatusBadge,
    ActionButton,
    EditIcon,
    DeleteIcon,
    ViewIcon,
    AssignIcon,
    Card,
    CardContent,
    PlusIcon
} from '@/Components/UI';
import HeaderStats from '@/Components/HeaderStats';

// Mock agents data
const mockAgents = [
    {
        id: 1,
        name: '<PERSON>',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        role: 'Senior Support Agent',
        department: 'Customer Support',
        status: 'active',
        phone: '+****************',
        joinDate: '2024-01-15',
        lastActive: '2025-01-20 14:30',
        ticketsResolved: 245,
        avgResponseTime: '2.5 hours',
        rating: 4.8,
        skills: ['Technical Support', 'Customer Service', 'Product Knowledge']
    },
    {
        id: 2,
        name: 'Michael Chen',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        role: 'Support Agent',
        department: 'Technical Support',
        status: 'active',
        phone: '+****************',
        joinDate: '2024-03-20',
        lastActive: '2025-01-20 16:45',
        ticketsResolved: 189,
        avgResponseTime: '3.2 hours',
        rating: 4.6,
        skills: ['Technical Troubleshooting', 'Software Support', 'Hardware Issues']
    },
    {
        id: 3,
        name: 'Emily Rodriguez',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        role: 'Junior Support Agent',
        department: 'Customer Support',
        status: 'inactive',
        phone: '+****************',
        joinDate: '2024-06-10',
        lastActive: '2025-01-18 10:15',
        ticketsResolved: 98,
        avgResponseTime: '4.1 hours',
        rating: 4.3,
        skills: ['Customer Service', 'Order Management', 'Basic Technical Support']
    },
    {
        id: 4,
        name: 'David Wilson',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        role: 'Lead Support Agent',
        department: 'Customer Support',
        status: 'active',
        phone: '+****************',
        joinDate: '2023-09-05',
        lastActive: '2025-01-20 17:20',
        ticketsResolved: 412,
        avgResponseTime: '1.8 hours',
        rating: 4.9,
        skills: ['Team Leadership', 'Advanced Technical Support', 'Training', 'Customer Relations']
    },
    {
        id: 5,
        name: 'Lisa Thompson',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-**********-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
        role: 'Support Agent',
        department: 'Billing Support',
        status: 'active',
        phone: '+****************',
        joinDate: '2024-02-28',
        lastActive: '2025-01-20 15:10',
        ticketsResolved: 156,
        avgResponseTime: '2.9 hours',
        rating: 4.5,
        skills: ['Billing Support', 'Payment Processing', 'Account Management']
    }
];

export default function Agents() {
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [departmentFilter, setDepartmentFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [viewMode, setViewMode] = useState('table'); // 'table' or 'cards'
    const agentsPerPage = 10;

    // Filter agents based on search and filters
    const filteredAgents = mockAgents.filter(agent => {
        const matchesSearch = agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            agent.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            agent.role.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesStatus = statusFilter === 'all' || agent.status === statusFilter;
        const matchesDepartment = departmentFilter === 'all' || agent.department === departmentFilter;
        
        return matchesSearch && matchesStatus && matchesDepartment;
    });

    // Pagination
    const totalPages = Math.ceil(filteredAgents.length / agentsPerPage);
    const startIndex = (currentPage - 1) * agentsPerPage;
    const currentAgents = filteredAgents.slice(startIndex, startIndex + agentsPerPage);

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    const departments = [...new Set(mockAgents.map(agent => agent.department))];

    return (
        <DashboardLayout title="Agents">
            <title>Agents Management</title>
            
            <div className="space-y-6">
                {/* Header with Stats */}
                <HeaderStats 
                    title="Support Agents"
                    subtitle="Manage your support team and track their performance"
                    buttonText="Add New Agent"
                    buttonLink="/agents/create"
                    stats={[
                        { value: mockAgents.length, label: 'Total Agents' },
                        { value: mockAgents.filter(a => a.status === 'active').length, label: 'Active Agents' },
                        { value: '4.6', label: 'Avg Rating' },
                        { value: '2.8h', label: 'Avg Response' }
                    ]}
                />

                <PageHeader
                    title="Agent Management"
                    subtitle="View and manage all support agents in your organization"
                />

                {/* Filters and Search */}
                <Card>
                    <CardContent className="p-6">
                        <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
                            <div className="flex flex-col sm:flex-row gap-4 flex-1">
                                {/* Search */}
                                <div className="relative flex-1 max-w-md">
                                    <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                    <input
                                        type="text"
                                        placeholder="Search agents..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cf-primary-500 focus:border-cf-primary-500"
                                    />
                                </div>

                                {/* Status Filter */}
                                <select
                                    value={statusFilter}
                                    onChange={(e) => setStatusFilter(e.target.value)}
                                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cf-primary-500 focus:border-cf-primary-500"
                                >
                                    <option value="all">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>

                                {/* Department Filter */}
                                <select
                                    value={departmentFilter}
                                    onChange={(e) => setDepartmentFilter(e.target.value)}
                                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cf-primary-500 focus:border-cf-primary-500"
                                >
                                    <option value="all">All Departments</option>
                                    {departments.map(dept => (
                                        <option key={dept} value={dept}>{dept}</option>
                                    ))}
                                </select>
                            </div>

                            {/* View Mode Toggle */}
                            <div className="flex items-center gap-2 bg-gray-100 rounded-lg p-1">
                                <button
                                    onClick={() => setViewMode('table')}
                                    className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                                        viewMode === 'table' 
                                            ? 'bg-white text-cf-primary-600 shadow-sm' 
                                            : 'text-gray-600 hover:text-gray-900'
                                    }`}
                                >
                                    Table
                                </button>
                                <button
                                    onClick={() => setViewMode('cards')}
                                    className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                                        viewMode === 'cards' 
                                            ? 'bg-white text-cf-primary-600 shadow-sm' 
                                            : 'text-gray-600 hover:text-gray-900'
                                    }`}
                                >
                                    Cards
                                </button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Agents Display */}
                {viewMode === 'table' ? (
                    /* Table View */
                    <DataTable>
                        <DataTableHeader>
                            <DataTableRow>
                                <DataTableHeaderCell>Agent</DataTableHeaderCell>
                                <DataTableHeaderCell>Role & Department</DataTableHeaderCell>
                                <DataTableHeaderCell>Status</DataTableHeaderCell>
                                <DataTableHeaderCell>Performance</DataTableHeaderCell>
                                <DataTableHeaderCell>Last Active</DataTableHeaderCell>
                                <DataTableHeaderCell>Actions</DataTableHeaderCell>
                            </DataTableRow>
                        </DataTableHeader>
                        <DataTableBody>
                            {currentAgents.map((agent) => (
                                <DataTableRow key={agent.id}>
                                    <DataTableCell>
                                        <div className="flex items-center gap-3">
                                            <img
                                                src={agent.avatar}
                                                alt={agent.name}
                                                className="w-10 h-10 rounded-full object-cover"
                                            />
                                            <div>
                                                <div className="font-semibold text-gray-900">{agent.name}</div>
                                                <div className="text-sm text-gray-500">{agent.email}</div>
                                            </div>
                                        </div>
                                    </DataTableCell>
                                    <DataTableCell>
                                        <div>
                                            <div className="font-medium text-gray-900">{agent.role}</div>
                                            <div className="text-sm text-gray-500">{agent.department}</div>
                                        </div>
                                    </DataTableCell>
                                    <DataTableCell>
                                        <StatusBadge variant={agent.status === 'active' ? 'success' : 'secondary'}>
                                            {agent.status === 'active' ? 'Active' : 'Inactive'}
                                        </StatusBadge>
                                    </DataTableCell>
                                    <DataTableCell>
                                        <div className="space-y-1">
                                            <div className="flex items-center gap-2">
                                                <span className="text-sm text-gray-600">Rating:</span>
                                                <div className="flex items-center gap-1">
                                                    <span className="text-sm font-medium">{agent.rating}</span>
                                                    <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                    </svg>
                                                </div>
                                            </div>
                                            <div className="text-xs text-gray-500">
                                                {agent.ticketsResolved} tickets • {agent.avgResponseTime}
                                            </div>
                                        </div>
                                    </DataTableCell>
                                    <DataTableCell>
                                        <div className="text-sm text-gray-900">{agent.lastActive}</div>
                                    </DataTableCell>
                                    <DataTableCell>
                                        <DataTableActions>
                                            <ActionButton variant="primary" size="sm" icon={ViewIcon} title="View Details" />
                                            <ActionButton variant="secondary" size="sm" icon={EditIcon} title="Edit Agent" />
                                            <ActionButton variant="success" size="sm" icon={AssignIcon} title="Assign Tickets" />
                                            <ActionButton variant="danger" size="sm" icon={DeleteIcon} title="Delete Agent" />
                                        </DataTableActions>
                                    </DataTableCell>
                                </DataTableRow>
                            ))}
                        </DataTableBody>
                    </DataTable>
                ) : (
                    /* Cards View */
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {currentAgents.map((agent) => (
                            <Card key={agent.id} className="hover:shadow-lg transition-all duration-200">
                                <CardContent className="p-6">
                                    <div className="flex items-start justify-between mb-4">
                                        <div className="flex items-center gap-3">
                                            <img
                                                src={agent.avatar}
                                                alt={agent.name}
                                                className="w-12 h-12 rounded-full object-cover"
                                            />
                                            <div>
                                                <h3 className="font-semibold text-gray-900">{agent.name}</h3>
                                                <p className="text-sm text-gray-500">{agent.role}</p>
                                            </div>
                                        </div>
                                        <StatusBadge variant={agent.status === 'active' ? 'success' : 'secondary'} size="xs">
                                            {agent.status === 'active' ? 'Active' : 'Inactive'}
                                        </StatusBadge>
                                    </div>

                                    <div className="space-y-3">
                                        <div>
                                            <div className="text-sm text-gray-600 mb-1">Department</div>
                                            <div className="text-sm font-medium">{agent.department}</div>
                                        </div>

                                        <div>
                                            <div className="text-sm text-gray-600 mb-1">Contact</div>
                                            <div className="text-sm">{agent.email}</div>
                                            <div className="text-sm text-gray-500">{agent.phone}</div>
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <div className="text-sm text-gray-600">Rating</div>
                                                <div className="flex items-center gap-1">
                                                    <span className="text-sm font-medium">{agent.rating}</span>
                                                    <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                    </svg>
                                                </div>
                                            </div>
                                            <div>
                                                <div className="text-sm text-gray-600">Tickets</div>
                                                <div className="text-sm font-medium">{agent.ticketsResolved}</div>
                                            </div>
                                        </div>

                                        <div>
                                            <div className="text-sm text-gray-600 mb-2">Skills</div>
                                            <div className="flex flex-wrap gap-1">
                                                {agent.skills.slice(0, 2).map((skill, index) => (
                                                    <span key={index} className="px-2 py-1 bg-cf-primary-50 text-cf-primary-600 text-xs rounded-full">
                                                        {skill}
                                                    </span>
                                                ))}
                                                {agent.skills.length > 2 && (
                                                    <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                                                        +{agent.skills.length - 2} more
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                    </div>

                                    <div className="flex gap-2 mt-6 pt-4 border-t border-gray-100">
                                        <ActionButton variant="primary" size="sm" icon={ViewIcon} className="flex-1 justify-center" title="View Details">
                                            View
                                        </ActionButton>
                                        <ActionButton variant="secondary" size="sm" icon={EditIcon} className="flex-1 justify-center" title="Edit Agent">
                                            Edit
                                        </ActionButton>
                                        <ActionButton variant="success" size="sm" icon={AssignIcon} className="flex-1 justify-center" title="Assign Tickets">
                                            Assign
                                        </ActionButton>
                                        <ActionButton variant="danger" size="sm" icon={DeleteIcon} title="Delete Agent" />
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                )}

                {/* Pagination */}
                {totalPages > 1 && (
                    <div className="flex items-center justify-between">
                        <div className="text-sm text-gray-700">
                            Showing {startIndex + 1} to {Math.min(startIndex + agentsPerPage, filteredAgents.length)} of {filteredAgents.length} agents
                        </div>
                        <div className="flex gap-2">
                            <button
                                onClick={() => handlePageChange(currentPage - 1)}
                                disabled={currentPage === 1}
                                className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Previous
                            </button>
                            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                                <button
                                    key={page}
                                    onClick={() => handlePageChange(page)}
                                    className={`px-3 py-2 text-sm border rounded-lg ${
                                        currentPage === page
                                            ? 'bg-cf-primary-600 text-white border-cf-primary-600'
                                            : 'border-gray-300 hover:bg-gray-50'
                                    }`}
                                >
                                    {page}
                                </button>
                            ))}
                            <button
                                onClick={() => handlePageChange(currentPage + 1)}
                                disabled={currentPage === totalPages}
                                className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Next
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </DashboardLayout>
    );
}