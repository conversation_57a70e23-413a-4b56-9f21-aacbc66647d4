
<script setup>
import feather from 'feather-icons';
import App from '../App.vue';
import { ref } from 'vue';
import { Head, usePage } from '@inertiajs/vue3';
import ContactForm from '../components/contact/ContactForm.vue';
import ContactDetails from '../components/contact/ContactDetails.vue';
const pageTitle = ref('Contact');

const contacts = [
  {
	id: 1,
	name: 'Your Address, Your City, Your Country',
	icon: 'map-pin',
  },
  {
	id: 2,
	name: '<EMAIL>',
	icon: 'mail',
  },
  {
	id: 3,
	name: '555 8888 888',
	icon: 'phone',
  },
];

feather.replace();
</script>


<template>
	<Head :title="pageTitle" />
	<App>

		<div
	  class="container mx-auto flex flex-col-reverse md:flex-row py-5 md:py-10 md:mt-10"
	>
	  <!-- Contact form -->
	  <ContactForm />
  
	  <!-- Contact details -->
	  <ContactDetails :contacts="contacts" />
	</div>

	</App>
	
  </template>
  