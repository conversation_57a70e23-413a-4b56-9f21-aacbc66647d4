<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\Complaint;
use App\Models\User;
use Illuminate\Database\Seeder;

class ComplaintSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample users if they don't exist
        $user1 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'password' => bcrypt('password')]
        );

        $user2 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'password' => bcrypt('password')]
        );

        // Create sample companies
        $company1 = Company::firstOrCreate(
            ['companyName' => 'Tech Solutions Inc.'],
            [
                'userId' => $user1->id,
                'companyAdd' => '123 Tech Street',
                'city' => 'San Francisco',
                'state' => 'CA',
                'zipCode' => '94105',
                'websiteUrl' => 'https://techsolutions.com',
                'chatSupport' => true,
            ]
        );

        $company2 = Company::firstOrCreate(
            ['companyName' => 'Digital Marketing Pro'],
            [
                'userId' => $user2->id,
                'companyAdd' => '456 Marketing Ave',
                'city' => 'New York',
                'state' => 'NY',
                'zipCode' => '10001',
                'websiteUrl' => 'https://digitalmarketingpro.com',
                'chatSupport' => false,
            ]
        );

        // Create sample complaints
        $complaints = [
            [
                'companyId' => $company1->id,
                'assignedToUserId' => $user1->id,
                'priority' => '2',
                'progressStatus' => 'new',
                'complainTitle' => 'Login Issues with User Account',
                'complainDetail' => 'Users are experiencing difficulties logging into their accounts. The system shows an error message "Invalid credentials" even with correct login information.',
            ],
            [
                'companyId' => $company1->id,
                'assignedToUserId' => $user2->id,
                'priority' => '1',
                'progressStatus' => 'inprogress',
                'complainTitle' => 'Payment Processing Delays',
                'complainDetail' => 'Payment transactions are taking longer than usual to process. Customers are reporting delays of up to 24 hours.',
            ],
            [
                'companyId' => $company2->id,
                'assignedToUserId' => $user1->id,
                'priority' => '0',
                'progressStatus' => 'resolved',
                'complainTitle' => 'Feature Request: Dark Mode',
                'complainDetail' => 'Multiple users have requested a dark mode option for better user experience during night time usage.',
            ],
            [
                'companyId' => $company2->id,
                'assignedToUserId' => $user2->id,
                'priority' => '2',
                'progressStatus' => 'need_user_input',
                'complainTitle' => 'Data Export Functionality Not Working',
                'complainDetail' => 'The data export feature is not generating CSV files correctly. Some columns are missing and data appears corrupted.',
            ],
        ];

        foreach ($complaints as $complaintData) {
            $complaint = Complaint::create($complaintData);

            // Generate complaint ID
            $currentYear = date('Y');
            $complaint->cmp_id = 'CP' . $currentYear . $complaint->id;
            $complaint->save();
        }
    }
}
