<script setup>
import { Head, usePage } from '@inertiajs/vue3';
import App from '../App.vue';
import { ref } from 'vue';
import CompanyList from '../components/shared/CompanyList.vue';
const pageTitle = ref('Companies');

const { allCompanies , searchField  } = usePage().props;
</script>

<template>
	<Head :title="pageTitle" />
<App>
	<div class="container mx-auto">
	<CompanyList :allCompanies="allCompanies"  :searchField="searchField" />
	</div>
</App>
</template>



