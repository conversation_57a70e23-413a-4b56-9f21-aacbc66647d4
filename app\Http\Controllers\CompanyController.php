<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Company;
use App\Models\Country;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password as FacadesPassword;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;

class CompanyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {

        if (Auth::user()->profileType == 'admin') {
            $allCompanies = Company::join('category', 'category.catId', '=', 'company.catId')
                ->join('country', 'country.id', '=', 'company.countryId')
                ->join('users', 'users.id', '=', 'company.userId')
                ->where('users.profileType', 'company')
                ->select('company.*', 'country.name', 'category.catName', 'users.profilePicture')
                ->orderBy('company.id', 'desc')
                ->paginate(env('PAGE_LIMIT'));

            return Inertia::render('Company/List', ['allCompanies' => $allCompanies]);
        } else {

            $userID       = Auth::id();
            $allCompanies = Company::join('category', 'category.catId', '=', 'company.catId')
                ->join('country', 'country.id', '=', 'company.countryId')
                ->join('users', 'users.id', '=', 'company.userId')
                ->where('users.id', $userID)
                ->where('users.profileType', 'company')
                ->select('company.*', 'country.name', 'category.catName', 'users.profilePicture')
                ->orderBy('company.id', 'desc')
                ->paginate(env('PAGE_LIMIT'));

            return Inertia::render('Company/AddrList', ['allCompanies' => $allCompanies]);
        }

    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {

        $categoryList = Category::all();
        $countryList  = Country::all();
        if (Auth::user()->profileType == 'admin') {
            return Inertia::render('Company/Create', ['categoryList' => $categoryList, 'countryList' => $countryList]);
        } else {
            return Inertia::render('Company/CreateCompanyAddress', ['categoryList' => $categoryList, 'countryList' => $countryList]);
        }

    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $saveUser = User::where('email', $request->email)->first();
        if ($saveUser) {

            if ($saveUser->profileType == 'admin') {

                return Redirect::route('companies.add')->with([
                    'status'  => false,
                    'message' => 'Not allow to add company for '.$request->email.' email id',
                    'class'   => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
                ]);
            }

        } else {

            $randomPass = Str::random(10);

            $saveUser = User::create([
                'name'        => $request->name,
                'phone'       => '',
                'profileType' => 'company',
                'email'       => $request->email,
                'password'    => Hash::make($randomPass),
            ]);

        }

        if ($request->hasFile('profilePicture')) {
            try {
                $image       = $request->file('profilePicture');
                $folder_name = 'images/profile';
                $profileUrl  = $image->store($folder_name, 'public');
                if ($saveUser->profilePicture != '') {
                    Storage::disk('public')->delete($saveUser->profilePicture);
                }

            } catch (Exception $ex) {
                return response()->json(['message' => $ex->getMessage()]);
            }
        } else {
            $profileUrl = $saveUser->profilePicture;
        }
        $saveUser->profilePicture = $profileUrl;
        $saveUser->profileType    = 'company';
        $saveUser->name           = $request->name;
        $saveUser->phone          = $request->phone;
        $saveUser->save();

        // $companyDetail = Company::where('userId',$saveUser->id)->first();
        // if($companyDetail){
        //     $saveCompany = Company::find($companyDetail->id);
        // }else{
        //     $saveCompany = new Company();
        // }

        $saveCompany = new Company;

        $saveCompany->userId      = $saveUser->id;
        $saveCompany->catId       = $request->catId;
        $saveCompany->countryId   = $request->countryId;
        $saveCompany->companyName = $request->name;
        $saveCompany->companyAdd  = $request->companyAdd;
        $saveCompany->city        = $request->city;
        $saveCompany->state       = $request->state;
        $saveCompany->zipCode     = $request->zipCode;
        $saveCompany->websiteUrl  = $request->websiteUrl;
        $saveCompany->save();

        $token = FacadesPassword::broker()->createToken($saveUser);

        try {
            $sendMail = User::sendActiveAgentMail($token, $request->email);
        } catch (Exception $exception) {

        }

        return Redirect::route('companies.add')->with([
            'status'  => true,
            'message' => 'Company saved successfully.',
            'class'   => 'flex items-center p-2 mb-4 text-green-700 rounded-md bg-green-100',
        ]);

    }

    public function addressStore(Request $request)
    {

        // Custom validation messages
        $customMessages = [
            'catId.required'      => 'The category is required.',
            'websiteUrl.required' => 'The website URL is required.',
            'companyAdd.required' => 'The company address is required.',
            'city.required'       => 'The city is required.',
            'state.required'      => 'The state is required.',
            'countryId.required'  => 'The country is required.',
            'zipCode.required'    => 'The zip code is required.',
        ];

        // Validation rules
        $validatedData = $request->validate([
            'catId'      => 'required',
            'websiteUrl' => 'required|url',
            'companyAdd' => 'required',
            'city'       => 'required',
            'state'      => 'required',
            'countryId'  => 'required',
            'zipCode'    => 'required',  // Example regex for zip code
        ], $customMessages);

        $saveUser = User::where('email', $request->email)->first();
        if ($saveUser) {

            if ($saveUser->profileType == 'admin') {

                return Redirect::back()->with([
                    'status'  => false,
                    'message' => 'Not allow to add company for '.$request->email.' email id',
                    'class'   => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
                ]);
            }

        }

        $currentUser = User::find(Auth::id());

        if ($request->hasFile('profilePicture')) {
            try {
                $image       = $request->file('profilePicture');
                $folder_name = 'images/profile';
                $profileUrl  = $image->store($folder_name, 'public');
                if ($currentUser->profilePicture != '') {
                    Storage::disk('public')->delete($currentUser->profilePicture);
                }

            } catch (Exception $ex) {
                return response()->json(['message' => $ex->getMessage()]);
            }
        } else {
            $profileUrl = $currentUser->profilePicture;
        }
        $currentUser->profilePicture = $profileUrl;
        $currentUser->profileType    = 'company';
        $currentUser->save();

        $companyDetail = Company::where('companyAdd', $request->companyAdd)->where('userId', Auth::id())->first();
        if ($companyDetail) {

            return Redirect::back()->with([
                'status'  => false,
                'message' => 'Address already exist',
                'class'   => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
            ]);

        } else {
            $saveCompany = new Company;
        }

        $saveCompany->userId      = $currentUser->id;
        $saveCompany->catId       = $request->catId;
        $saveCompany->countryId   = $request->countryId;
        $saveCompany->companyName = ($currentUser->company_name != '') ? $currentUser->company_name : $currentUser->name;
        $saveCompany->companyAdd  = $request->companyAdd;
        $saveCompany->city        = $request->city;
        $saveCompany->state       = $request->state;
        $saveCompany->zipCode     = $request->zipCode;
        $saveCompany->websiteUrl  = $request->websiteUrl;
        $saveCompany->save();

        return Redirect::back()->with([
            'status'  => true,
            'message' => 'Company address saved successfully.',
            'class'   => 'flex items-center p-2 mb-4 text-green-700 rounded-md bg-green-100',
        ]);

    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $categoryList  = Category::all();
        $countryList   = Country::all();
        $companyDetail = Company::find($id);
        if (Auth::user()->profileType == 'company') {
            $companyDetail = Company::where('id', $id)->where('userId', Auth::id())->first();
        }

        if ($companyDetail) {
            $userDetail = User::find($companyDetail->userId);
        } else {
            return redirect()->back();
        }

        if (Auth::user()->profileType == 'company') {

            return Inertia::render('Company/EditCompanyAddress', ['userDetail' => $userDetail, 'companyDetail' => $companyDetail, 'categoryList' => $categoryList, 'countryList' => $countryList]);
        } else {

            return Inertia::render('Company/Edit', ['userDetail' => $userDetail, 'companyDetail' => $companyDetail, 'categoryList' => $categoryList, 'countryList' => $countryList]);
        }

    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {

        $saveUser = User::find(Auth::id());

        if ($request->hasFile('profilePicture')) {
            try {
                $image       = $request->file('profilePicture');
                $folder_name = 'images/profile';
                $profileUrl  = $image->store($folder_name, 'public');
                if ($saveUser->profilePicture != '') {
                    Storage::disk('public')->delete($saveUser->profilePicture);
                }

            } catch (Exception $ex) {
                return response()->json(['message' => $ex->getMessage()]);
            }
        } else {
            $profileUrl = $saveUser->profilePicture;
        }

        $saveUser->profilePicture = $profileUrl;

        $saveUser->phone = $request->phone;
        $saveUser->save();

        $companyDetail = Company::where('userId', Auth::id())->first();
        if ($companyDetail) {
            $saveCompany = Company::find($companyDetail->id);
        } else {
            $saveCompany = new Company;
        }

        $checkCategory = Category::where('catName', 'Other')->first();
        if ($checkCategory) {
        } else {
            $checkCategory = Category::orderBy('catId', 'desc')->first();
        }

        if ($checkCategory) {
        } else {

            return Redirect::back()->with([
                'status'  => false,
                'message' => 'Server error',
                'class'   => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
            ]);
        }

        $saveCompany->userId      = Auth::id();
        $saveCompany->catId       = $checkCategory->catId;
        $saveCompany->countryId   = $request->countryId;
        $saveCompany->companyName = Auth::user()->name;
        $saveCompany->companyAdd  = $request->companyAdd;
        $saveCompany->city        = $request->city;
        $saveCompany->state       = $request->state;
        $saveCompany->zipCode     = $request->zipCode;
        $saveCompany->chatSupport = $request->chatSupport;
        $saveCompany->websiteUrl  = $request->websiteUrl;
        $saveCompany->save();

        // return Inertia::location(route('profile.edit'));

        return Redirect::back()->with([
            'status'  => true,
            'message' => 'Business Account saved successfully',
            'class'   => 'flex items-center p-2 mb-4 text-green-700 rounded-md bg-green-100',
        ]);

        // return Redirect::route('profile.edit');

    }

    public function save(Request $request)
    {

        $chckCompany = Company::find($request->company_id);
        if ($chckCompany) {

            $checkEmail = User::where('id', '<>', $chckCompany->userId)->where('email', $request->email)->first();
            if ($checkEmail) {

                return Redirect::back()->with([
                    'status'  => false,
                    'message' => 'Email already exist',
                    'class'   => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
                ]);

            }
            $saveUser = User::find($chckCompany->userId);

        } else {

            return Redirect::back()->with([
                'status'  => false,
                'message' => 'Invalid request found',
                'class'   => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
            ]);

        }

        if ($saveUser) {
            if ($saveUser->profileType == 'admin') {

                return Redirect::back()->with([
                    'status'  => false,
                    'message' => 'Not allow to add company for '.$request->email.' email id',
                    'class'   => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
                ]);

            }

        } else {

            return Redirect::back()->with([
                'status'  => false,
                'message' => 'Invalid request found',
                'class'   => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
            ]);

        }

        if ($request->hasFile('profilePicture')) {
            try {
                $image       = $request->file('profilePicture');
                $folder_name = 'images/profile';
                $profileUrl  = $image->store($folder_name, 'public');
                if ($saveUser->profilePicture != '') {
                    Storage::disk('public')->delete($saveUser->profilePicture);
                }

            } catch (Exception $ex) {
                return response()->json(['message' => $ex->getMessage()]);
            }
        } else {
            $profileUrl = $saveUser->profilePicture;
        }
        $saveUser->profilePicture = $profileUrl;
        $saveUser->profileType    = 'company';
        $saveUser->name           = $request->name;
        $saveUser->email          = $request->email;
        $saveUser->phone          = $request->phone;
        $saveUser->save();

        // $companyDetail = Company::where('userId',$saveUser->id)->first();
        // if($companyDetail){
        //     $saveCompany = Company::find($companyDetail->id);
        // }else{
        //     $saveCompany = new Company();
        // }

        $saveCompany = Company::find($request->company_id);

        $saveCompany->userId      = $saveUser->id;
        $saveCompany->catId       = $request->catId;
        $saveCompany->countryId   = $request->countryId;
        $saveCompany->companyName = $request->name;
        $saveCompany->companyAdd  = $request->companyAdd;
        $saveCompany->city        = $request->city;
        $saveCompany->state       = $request->state;
        $saveCompany->zipCode     = $request->zipCode;
        $saveCompany->websiteUrl  = $request->websiteUrl;
        $saveCompany->save();

        return Redirect::back()->with([
            'status'  => true,
            'message' => 'Company saved successfully.',
            'class'   => 'flex items-center p-2 mb-4 text-green-700 rounded-md bg-green-100',
        ]);

    }

    public function addressSave(Request $request)
    {

        $validated = $request->validate([
            'catId'      => 'required|exists:category,catId',
            'companyAdd' => 'required|string|max:255',
            'city'       => 'required|string|max:100',
            'state'      => 'required|string|max:100',
            'countryId'  => 'required|exists:country,id',
            'zipCode'    => 'required|string|max:20',
            'websiteUrl' => 'required|url',
        ], [
            // Custom messages for 'catId'
            'catId.required' => 'The category field is required.',
            'catId.exists'   => 'The selected category does not exist.',

            // Custom messages for 'companyAdd'
            'companyAdd.required' => 'The company address field is required.',
            'companyAdd.string'   => 'The company address must be a valid string.',
            'companyAdd.max'      => 'The company address must not exceed 255 characters.',

            // Custom messages for 'city'
            'city.required' => 'The city field is required.',
            'city.string'   => 'The city must be a valid string.',
            'city.max'      => 'The city name must not exceed 100 characters.',

            // Custom messages for 'state'
            'state.required' => 'The state field is required.',
            'state.string'   => 'The state must be a valid string.',
            'state.max'      => 'The state name must not exceed 100 characters.',

            // Custom messages for 'countryId'
            'countryId.required' => 'The country field is required.',
            'countryId.exists'   => 'The selected country does not exist.',

            // Custom messages for 'zipCode'
            'zipCode.required' => 'The zip code field is required.',
            'zipCode.string'   => 'The zip code must be a valid string.',
            'zipCode.max'      => 'The zip code must not exceed 20 characters.',

            // Custom messages for 'websiteUrl'
            'websiteUrl.required' => 'The website URL field is required.',
            'websiteUrl.url'      => 'The website URL must be a valid URL.',
        ]);

        $chckCompany = Company::where('id', $request->company_id)->where('userId', Auth::id())->first();
        if ($chckCompany) {

            $saveUser = User::find($chckCompany->userId);

        } else {

            return Redirect::back()->with([
                'status'  => false,
                'message' => 'Invalid request found',
                'class'   => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
            ]);

        }

        if ($saveUser) {
            if ($saveUser->profileType == 'admin') {

                return Redirect::back()->with([
                    'status'  => false,
                    'message' => 'Not allow to add company for '.$request->email.' email id',
                    'class'   => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
                ]);

                // return Redirect::back()->with([
                //         'status' => false,
                //         'message' => 'Not allow to add company for '.$request->email.' email id',
                //         'class' => 'text-sm text-red-600'
                //     ]);

            }

        } else {

            return Redirect::back()->with([
                'status'  => false,
                'message' => 'Invalid request found',
                'class'   => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
            ]);
            // return Redirect::back()->with([
            //     'status' => false,
            //     'message' => 'Invalid request found',
            //     'class' => 'text-sm text-red-600'
            // ]);
        }

        if ($request->hasFile('profilePicture')) {
            try {
                $image       = $request->file('profilePicture');
                $folder_name = 'images/profile';
                $profileUrl  = $image->store($folder_name, 'public');
                if ($saveUser->profilePicture != '') {
                    Storage::disk('public')->delete($saveUser->profilePicture);
                }

            } catch (Exception $ex) {
                return response()->json(['message' => $ex->getMessage()]);
            }
        } else {
            $profileUrl = $saveUser->profilePicture;
        }
        $saveUser->profilePicture = $profileUrl;
        $saveUser->profileType    = 'company';
        $saveUser->save();

        $saveCompany = Company::where('id', $request->company_id)->where('userId', Auth::id())->first();

        $saveCompany->catId      = $request->catId;
        $saveCompany->countryId  = $request->countryId;
        $saveCompany->companyAdd = $request->companyAdd;
        $saveCompany->city       = $request->city;
        $saveCompany->state      = $request->state;
        $saveCompany->zipCode    = $request->zipCode;
        $saveCompany->websiteUrl = $request->websiteUrl;
        $saveCompany->save();

        return Redirect::back()->with([
            'status'  => true,
            'message' => 'Company address saved successfully.',
            'class'   => 'flex items-center p-2 mb-4 text-green-700 rounded-md bg-green-100',
        ]);

        // return Redirect::back()->with([
        //     'status' => true,
        //     'message' => 'Company saved successfully.',
        //     'class' => 'text-sm text-green-400'
        // ]);

    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $companY = Company::find($id);
        if ($companY) {
            // $userID = $companY->userId;
            User::deleteRelationtoCompany($id);

            // User::where('id',$userID)->delete();
            return response()->json(['success' => true]);

        } else {
            return response()->json(['success' => true]);
        }
    }
}
