<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('complain', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('companyId');
            $table->unsignedBigInteger('assignedToUserId');
            $table->enum('priority', ['0', '1', '2'])->default('0')->comment('0-low,1-medium,2-high');
            $table->enum('progressStatus', ['new', 'inprogress', 'need_user_input', 'resolved'])->default('new');
            $table->string('complainTitle', 255);
            $table->text('complainDetail');
            $table->enum('complainStatus', ['open', 'close'])->default('open');
            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
            $table->foreign('companyId')->references('id')->on('company');
            $table->foreign('assignedToUserId')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('complain');
    }
};
