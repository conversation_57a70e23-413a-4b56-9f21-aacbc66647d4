<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import DeleteUserForm from './Partials/DeleteUserForm.vue';
import UpdatePasswordForm from './Partials/UpdatePasswordForm.vue';
import UpdateProfileInformationForm from './Partials/UpdateProfileInformationForm.vue';
import UpdateCompanyInformationForm from './Partials/UpdateCompanyInformationForm.vue';
import updateOauth from './Partials/updateOauth.vue';
import chatbotScript from './Partials/chatbotScript.vue';
import { Head, useForm, usePage } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';
import PageHeading from '@/Components/Global/PageHeading.vue';

const pageTitle = ref('Profile')

defineProps({
    mustVerifyEmail: {
        type: Boolean,
    },
    status: {
        type: String,
    },
});

const user = usePage().props.auth.user;
const { companyDetail } = usePage().props;

const form = useForm({
    profileType: user.profileType,
});

</script>

<template>

    <Head :title="pageTitle" />

    <AuthenticatedLayout>
        <!-- <PageHeading :title="pageTitle"></PageHeading> -->

        <template #header>
            <h2 class="font-semibold text-slate-800 text-xl leading-tight">Profile</h2>
        </template>

        <div class="gap-6 grid grid-cols-1 md:grid-cols-2">
            <div>
                <div class="p-4 sm:p-6 border rounded-md">
                    <UpdateProfileInformationForm :must-verify-email="mustVerifyEmail" :status="status" />
                </div>
            </div>

            <div>
                <div class="p-4 sm:p-6 border rounded-md">
                    <UpdatePasswordForm />
                </div>
            </div>

            <div v-if="companyDetail && (form.profileType == 'company')" class="">
                <div class="p-4 sm:p-6 border rounded-md">
                    <UpdateCompanyInformationForm :must-verify-email="mustVerifyEmail" :status="status" />
                </div>
            </div>

            <div v-if="companyDetail && (form.profileType == 'company')" class="">
                <div class="p-4 sm:p-6 border rounded-md">
                    <updateOauth />
                </div>

                <div class="p-4 sm:p-6 mt-5 border rounded-md">
                    <chatbotScript />
                </div>

            </div>


            <div class="" v-if="form.profileType == 'user' || form.profileType == 'company'">
                <div class="p-4 sm:p-6 border rounded-md">
                    <DeleteUserForm />
                </div>
            </div>





        </div>

    </AuthenticatedLayout>
</template>
