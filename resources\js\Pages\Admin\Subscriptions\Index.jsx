import React, { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import Button from '@/Components/Button';
import Select from '@/Components/Select';

export default function Index({ subscriptions, filters, plans, stats }) {
    const [search, setSearch] = useState(filters.search || '');
    const [status, setStatus] = useState(filters.status || '');
    const [planId, setPlanId] = useState(filters.plan_id || '');

    const handleFilter = () => {
        router.get(route('admin.subscriptions.index'), {
            search: search || undefined,
            status: status || undefined,
            plan_id: planId || undefined,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const clearFilters = () => {
        setSearch('');
        setStatus('');
        setPlanId('');
        router.get(route('admin.subscriptions.index'));
    };

    const getStatusBadge = (subscription) => {
        const statusColors = {
            trial: 'bg-blue-100 text-blue-800',
            active: 'bg-green-100 text-green-800',
            past_due: 'bg-yellow-100 text-yellow-800',
            canceled: 'bg-red-100 text-red-800',
            incomplete: 'bg-gray-100 text-gray-800',
            incomplete_expired: 'bg-gray-100 text-gray-800',
        };

        const colorClass = statusColors[subscription.status] || 'bg-gray-100 text-gray-800';

        return (
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${colorClass}`}>
                {subscription.status.replace('_', ' ').toUpperCase()}
            </span>
        );
    };

    const formatDate = (date) => {
        if (!date) return 'N/A';
        return new Date(date).toLocaleDateString();
    };

    return (
        <DashboardLayout>
            <Head title="Subscriptions" />
            
            <div className="py-6">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="md:flex md:items-center md:justify-between">
                        <div className="flex-1 min-w-0">
                            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                                Subscriptions
                            </h2>
                            <p className="mt-1 text-sm text-gray-500">
                                Manage all customer subscriptions.
                            </p>
                        </div>
                    </div>

                    {/* Stats */}
                    <div className="mt-8">
                        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5">
                            <div className="bg-white overflow-hidden shadow rounded-lg">
                                <div className="p-5">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0">
                                            <div className="w-8 h-8 bg-gray-500 rounded-md flex items-center justify-center">
                                                <span className="text-white text-sm font-medium">T</span>
                                            </div>
                                        </div>
                                        <div className="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt className="text-sm font-medium text-gray-500 truncate">Total</dt>
                                                <dd className="text-lg font-medium text-gray-900">{stats.total}</dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-white overflow-hidden shadow rounded-lg">
                                <div className="p-5">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0">
                                            <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                                <span className="text-white text-sm font-medium">A</span>
                                            </div>
                                        </div>
                                        <div className="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt className="text-sm font-medium text-gray-500 truncate">Active</dt>
                                                <dd className="text-lg font-medium text-gray-900">{stats.active}</dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-white overflow-hidden shadow rounded-lg">
                                <div className="p-5">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0">
                                            <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                                <span className="text-white text-sm font-medium">T</span>
                                            </div>
                                        </div>
                                        <div className="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt className="text-sm font-medium text-gray-500 truncate">Trial</dt>
                                                <dd className="text-lg font-medium text-gray-900">{stats.trial}</dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-white overflow-hidden shadow rounded-lg">
                                <div className="p-5">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0">
                                            <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                                <span className="text-white text-sm font-medium">P</span>
                                            </div>
                                        </div>
                                        <div className="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt className="text-sm font-medium text-gray-500 truncate">Past Due</dt>
                                                <dd className="text-lg font-medium text-gray-900">{stats.past_due}</dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-white overflow-hidden shadow rounded-lg">
                                <div className="p-5">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0">
                                            <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                                                <span className="text-white text-sm font-medium">C</span>
                                            </div>
                                        </div>
                                        <div className="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt className="text-sm font-medium text-gray-500 truncate">Canceled</dt>
                                                <dd className="text-lg font-medium text-gray-900">{stats.canceled}</dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Filters */}
                    <div className="mt-8 bg-white shadow rounded-lg p-6">
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Search</label>
                                <input
                                    type="text"
                                    value={search}
                                    onChange={(e) => setSearch(e.target.value)}
                                    placeholder="Search by user name or email..."
                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                />
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Status</label>
                                <select
                                    value={status}
                                    onChange={(e) => setStatus(e.target.value)}
                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                >
                                    <option value="">All Statuses</option>
                                    <option value="trial">Trial</option>
                                    <option value="active">Active</option>
                                    <option value="past_due">Past Due</option>
                                    <option value="canceled">Canceled</option>
                                    <option value="incomplete">Incomplete</option>
                                </select>
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Plan</label>
                                <select
                                    value={planId}
                                    onChange={(e) => setPlanId(e.target.value)}
                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                >
                                    <option value="">All Plans</option>
                                    {plans.map((plan) => (
                                        <option key={plan.id} value={plan.id}>
                                            {plan.name}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            
                            <div className="flex items-end space-x-2">
                                <Button onClick={handleFilter}>
                                    Filter
                                </Button>
                                <Button variant="outline" onClick={clearFilters}>
                                    Clear
                                </Button>
                            </div>
                        </div>
                    </div>

                    {/* Subscriptions Table */}
                    <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-md">
                        <ul className="divide-y divide-gray-200">
                            {subscriptions.data.map((subscription) => (
                                <li key={subscription.id}>
                                    <div className="px-4 py-4 sm:px-6">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center">
                                                <div className="flex-shrink-0">
                                                    <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                        <span className="text-sm font-medium text-gray-700">
                                                            {subscription.user.name.charAt(0)}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className="ml-4">
                                                    <div className="flex items-center">
                                                        <p className="text-sm font-medium text-gray-900">
                                                            {subscription.user.name}
                                                        </p>
                                                        <div className="ml-2">
                                                            {getStatusBadge(subscription)}
                                                        </div>
                                                    </div>
                                                    <div className="mt-1 flex items-center text-sm text-gray-500">
                                                        <span>{subscription.user.email}</span>
                                                        <span className="mx-2">•</span>
                                                        <span>{subscription.plan.name}</span>
                                                        <span className="mx-2">•</span>
                                                        <span>{subscription.plan.formatted_price} / {subscription.plan.billing_interval}</span>
                                                    </div>
                                                    <div className="mt-1 text-sm text-gray-500">
                                                        {subscription.on_trial && (
                                                            <span>Trial ends: {formatDate(subscription.trial_ends_at)}</span>
                                                        )}
                                                        {subscription.is_active && (
                                                            <span>Next billing: {formatDate(subscription.current_period_end)}</span>
                                                        )}
                                                        {subscription.is_canceled && (
                                                            <span>Canceled: {formatDate(subscription.canceled_at)}</span>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="flex items-center space-x-2">
                                                <Link
                                                    href={route('admin.subscriptions.show', subscription.id)}
                                                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                                >
                                                    View Details
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Pagination */}
                    {subscriptions.links && (
                        <div className="mt-6">
                            {/* Add pagination component here */}
                        </div>
                    )}
                </div>
            </div>
        </DashboardLayout>
    );
}
