<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import axios from 'axios';
import { ref, onMounted, computed } from 'vue'; // Import watch and nextTick
import { usePage, Head } from '@inertiajs/vue3';
import PageHeading from '@/Components/Global/PageHeading.vue';
import DtIconGlobal from '@/Components/DtIcon/DtIconGlobal.vue';

const { title, allIntents, success } = usePage().props;
const pageTitle = ref(title);
let successMessage = ref(success ? success : null);

// Add this computed property to sort the intents
const sortedIntents = computed(() => {
    return [...allIntents].sort((a, b) => {
        // Put 'welcome' first
        if (a.name === 'welcome') return -1;
        if (b.name === 'welcome') return 1;
        // Put 'fallback' second
        if (a.name === 'fallback') return -1;
        if (b.name === 'fallback') return 1;
        // Sort remaining items alphabetically by name
        return a.name.localeCompare(b.name);
    });
});

const deleteIntent = async (intent) => {
    try {
        // Show a SweetAlert confirmation popup
        const confirmation = await Swal.fire({
            title: 'Are you sure?',
            text: 'You are about to delete this intent. This action cannot be undone. Are you sure you want to proceed?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel',
            customClass: {
                confirmButton: 'dk-update-btn',
                cancelButton: 'dk-cancle-btn',
            },
            buttonsStyling: false,
        });

        // If user confirms deletion
        if (confirmation.isConfirmed) {
            const response = await axios.delete(route('intents.destroy', { id: intent.id }));
            if (response.data.success) {
                // Remove the deleted agent from the allAgents array
                const index = allIntents.findIndex(a => a.id === intent.id);
                if (index !== -1) {
                    allIntents.splice(index, 1);
                }
                // Show success message
                //Swal.fire('Deleted!', 'The intent has been deleted.', 'success');
            } else {
                throw new Error('Failed to delete intent');
            }
        }
    } catch (error) {
        console.error(error);
        // Show error message if deletion fails
        Swal.fire('Error', 'An error occurred while deleting the intent.', 'error');
    }
};

onMounted(() => {
    setTimeout(() => {
        successMessage = null;
    }, 1000);
});

const navigateToIntent = (id) => {
    window.location.href = route('intents.show', { id });
};

// Function to toggle editing mode
const toggleEditMode = (intent) => {
    intent.isEditing = !intent.isEditing;
};

// Function to save the updated intent
const saveIntent = async (intent) => {
    try {
        const response = await axios.put(route('intents.update', { id: intent.id }), intent);
        if (response.data.success) {
            intent.isEditing = false;
            Swal.fire('Updated!', 'The intent has been updated.', 'success');
        } else {
            throw new Error('Failed to update intent');
        }
    } catch (error) {
        console.error(error);
        Swal.fire('Error', 'An error occurred while updating the intent.', 'error');
    }
};
</script>

<template>

    <Head :title="pageTitle" />
    <AuthenticatedLayout>
        <Transition v-if="successMessage" name="fade">
            <p class="mb-4 text-green-400 text-sm successMessages">{{ successMessage }}</p>
        </Transition>
        <div class="flex">
            <div class="flex-1">
                <div class="">
                    <div class="max-w-full overflow-x-auto">
                        <table class="w-full border border-gray-300 rounded-md ">
                            <thead>
                                <tr class="border-b text-left  border-gray-300 bg-gray-100">
                                    <th class="p-2 font-medium border-r ">Intent Name</th>
                                    <th class="p-2 border-r font-medium">Description</th>
                                    <th class="p-2 border-r font-medium">Context</th>
                                    <th class="p-2 font-medium text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="text-sm text-slate-600">
                                <tr v-for="intent in sortedIntents" :key="intent.id" class="border-b border-gray-300">
                                    <td class="p-2 border-r">
                                        <input v-if="intent.isEditing" v-model="intent.name" type="text"
                                            class="w-full p-2 border border-gray-300 rounded-md" />
                                        <span v-else>{{ intent.name }}</span>
                                    </td>
                                    <td class="p-2 border-r">
                                        <input v-if="intent.isEditing" v-model="intent.description" type="text"
                                            class="w-full p-2 border border-gray-300 rounded-md" />
                                        <span v-else>{{ intent.description }}</span>
                                    </td>
                                    <td class="p-2 border-r">
                                        <input v-if="intent.isEditing" v-model="intent.context" type="text"
                                            class="w-full p-2 border border-gray-300 rounded-md" />
                                        <span v-else>{{ intent.context }}</span>
                                    </td>
                                    <td class="p-2 flex space-x-2 justify-center">
                                        <a :href="route('intents.edit', { id: intent.id })" class="dt-deals-action-btn">
                                            <DtIconGlobal :type="'edit'" />
                                        </a>
                                        <!-- <button @click="saveIntent(intent)" v-if="intent.isEditing"
                                            class="dt-deals-action-btn dt-deals-save-btn">
                                            <DtIconGlobal :type="'save'" />
                                        </button> -->
                                        <button v-if="intent.name !== 'welcome' && intent.name !== 'fallback'" @click="deleteIntent(intent)"
                                            class="dt-deals-action-btn dt-deals-delete-btn">
                                            <DtIconGlobal :type="'delete'" />
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>

<style>
.dt-deals-tr {
    transition: background-color 0.2s;
}

.dt-deals-tr:hover {
    background-color: rgb(249, 250, 251);
}
</style>
