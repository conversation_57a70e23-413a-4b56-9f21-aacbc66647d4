<?php

namespace Database\Seeders;

use App\Models\SubscriptionPlan;
use Illuminate\Database\Seeder;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plans = [
            [
                'name' => 'Monthly Plan',
                'slug' => 'monthly',
                'description' => 'Perfect for small businesses getting started with customer support automation.',
                'price' => 1000, // $10.00 in cents
                'billing_interval' => 'monthly',
                'billing_interval_count' => 1,
                'trial_days' => 7,
                'features' => [
                    'Live Chat Support',
                    'Chatbot Integration',
                    'Ticket Management',
                    'Basic Analytics',
                    'Email Support',
                    '24/7 Customer Support'
                ],
                'limits' => [
                    'max_agents' => 5,
                    'max_tickets_per_month' => 100,
                    'max_chat_sessions_per_month' => 500,
                    'max_knowledge_base_articles' => 50
                ],
                'is_active' => true,
                'is_popular' => false,
                'sort_order' => 1,
            ],
            [
                'name' => 'Quarterly Plan',
                'slug' => 'quarterly',
                'description' => 'Save 10% with our quarterly plan. Great for growing businesses.',
                'price' => 2700, // $27.00 in cents (10% discount from $30)
                'billing_interval' => 'quarterly',
                'billing_interval_count' => 1,
                'trial_days' => 7,
                'features' => [
                    'Live Chat Support',
                    'Chatbot Integration',
                    'Ticket Management',
                    'Advanced Analytics',
                    'Email Support',
                    'Priority Support',
                    'Custom Branding'
                ],
                'limits' => [
                    'max_agents' => 10,
                    'max_tickets_per_month' => 300,
                    'max_chat_sessions_per_month' => 1500,
                    'max_knowledge_base_articles' => 150
                ],
                'is_active' => true,
                'is_popular' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Semi-Annual Plan',
                'slug' => 'semi-annual',
                'description' => 'Save 20% with our 6-month plan. Perfect for established businesses.',
                'price' => 4800, // $48.00 in cents (20% discount from $60)
                'billing_interval' => 'semi_annual',
                'billing_interval_count' => 1,
                'trial_days' => 7,
                'features' => [
                    'Live Chat Support',
                    'Advanced Chatbot',
                    'Ticket Management',
                    'Advanced Analytics',
                    'Email & Phone Support',
                    'Priority Support',
                    'Custom Branding',
                    'API Access',
                    'Integrations'
                ],
                'limits' => [
                    'max_agents' => 25,
                    'max_tickets_per_month' => 1000,
                    'max_chat_sessions_per_month' => 5000,
                    'max_knowledge_base_articles' => 500
                ],
                'is_active' => true,
                'is_popular' => false,
                'sort_order' => 3,
            ],
            [
                'name' => 'Annual Plan',
                'slug' => 'annual',
                'description' => 'Save 30% with our annual plan. Best value for enterprise customers.',
                'price' => 8400, // $84.00 in cents (30% discount from $120)
                'billing_interval' => 'annual',
                'billing_interval_count' => 1,
                'trial_days' => 7,
                'features' => [
                    'Live Chat Support',
                    'AI-Powered Chatbot',
                    'Advanced Ticket Management',
                    'Enterprise Analytics',
                    'Email, Phone & Chat Support',
                    'Dedicated Account Manager',
                    'White-label Solution',
                    'Full API Access',
                    'All Integrations',
                    'Custom Development'
                ],
                'limits' => [
                    'max_agents' => -1, // Unlimited
                    'max_tickets_per_month' => -1, // Unlimited
                    'max_chat_sessions_per_month' => -1, // Unlimited
                    'max_knowledge_base_articles' => -1 // Unlimited
                ],
                'is_active' => true,
                'is_popular' => false,
                'sort_order' => 4,
            ],
        ];

        foreach ($plans as $planData) {
            SubscriptionPlan::updateOrCreate(
                ['slug' => $planData['slug']],
                $planData
            );
        }
    }
}
