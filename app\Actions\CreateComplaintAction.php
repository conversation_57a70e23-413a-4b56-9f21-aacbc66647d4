<?php

namespace App\Actions;

use App\Models\Complaint;

class CreateComplaintAction
{
    public function handle(array $data): Complaint
    {
        $complaint = new Complaint();
        $complaint->companyId = $data['company_id'];
        $complaint->assignedToUserId = $data['assigned_user_id'];
        $complaint->priority = $data['priority'];
        $complaint->complainTitle = $data['title'];
        $complaint->complainDetail = $data['description'];
        $complaint->save();

        // Generate complaint ID
        $currentYear = date('Y');
        $complaint->cmp_id = 'CP' . $currentYear . $complaint->id;
        $complaint->save();

        return $complaint;
    }
}
