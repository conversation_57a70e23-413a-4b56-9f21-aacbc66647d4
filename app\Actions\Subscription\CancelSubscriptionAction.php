<?php

namespace App\Actions\Subscription;

use App\Models\PaymentGateway;
use App\Models\Subscription;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CancelSubscriptionAction
{
    /**
     * Cancel a subscription.
     */
    public function handle(Subscription $subscription, bool $immediately = false): Subscription
    {
        return DB::transaction(function () use ($subscription, $immediately) {
            if ($subscription->isCanceled()) {
                throw new \Exception('Subscription is already canceled.');
            }

            // Cancel in payment gateway first
            $this->cancelGatewaySubscription($subscription, $immediately);

            // Update subscription status
            $subscription->update([
                'status' => 'canceled',
                'canceled_at' => now(),
                'ends_at' => $immediately ? now() : $subscription->current_period_end,
            ]);

            return $subscription->fresh();
        });
    }

    /**
     * Cancel subscription in the payment gateway.
     */
    private function cancelGatewaySubscription(Subscription $subscription, bool $immediately): void
    {
        try {
            $gateway = PaymentGateway::where('name', $subscription->payment_method)
                ->where('is_active', true)
                ->first();

            if (!$gateway || !$gateway->isConfigured()) {
                Log::warning('Payment gateway not available for cancellation', [
                    'subscription_id' => $subscription->id,
                    'payment_method' => $subscription->payment_method,
                ]);
                return;
            }

            if ($gateway->name === 'stripe' && $subscription->stripe_subscription_id) {
                $this->cancelStripeSubscription($subscription, $gateway, $immediately);
            } elseif ($gateway->name === 'paypal' && $subscription->paypal_subscription_id) {
                $this->cancelPayPalSubscription($subscription, $gateway, $immediately);
            }
        } catch (\Exception $e) {
            Log::error('Failed to cancel gateway subscription', [
                'subscription_id' => $subscription->id,
                'gateway' => $subscription->payment_method,
                'error' => $e->getMessage(),
            ]);

            // Don't throw the exception - we still want to cancel locally
        }
    }

    /**
     * Cancel Stripe subscription.
     */
    private function cancelStripeSubscription(Subscription $subscription, PaymentGateway $gateway, bool $immediately): void
    {
        $config = $gateway->getCurrentConfig();
        \Stripe\Stripe::setApiKey($config['secret_key']);

        $stripeSubscription = \Stripe\Subscription::retrieve($subscription->stripe_subscription_id);

        if ($immediately) {
            $stripeSubscription->cancel();
        } else {
            $stripeSubscription->cancel_at_period_end = true;
            $stripeSubscription->save();
        }
    }

    /**
     * Cancel PayPal subscription.
     */
    private function cancelPayPalSubscription(Subscription $subscription, PaymentGateway $gateway, bool $immediately): void
    {
        // PayPal subscription cancellation logic would go here
        // This is a placeholder for the actual PayPal API integration

        Log::info('PayPal subscription cancellation requested', [
            'subscription_id' => $subscription->id,
            'paypal_subscription_id' => $subscription->paypal_subscription_id,
            'immediately' => $immediately,
        ]);
    }
}
