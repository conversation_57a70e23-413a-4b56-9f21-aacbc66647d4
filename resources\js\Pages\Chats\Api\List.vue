<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { usePage, Head } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';
import PageHeading from '@/Components/Global/PageHeading.vue';
import DtIconGlobal from '@/Components/DtIcon/DtIconGlobal.vue';
import { Link } from '@inertiajs/vue3';


const { title, apis, success } = usePage().props;
const pageTitle = ref(title);
let successMessage = ref(success ? success : null);

// Sort APIs to ensure @apiauth-get_api_token is always first
const sortedApis = computed(() => {
    return [...apis].sort((a, b) => {
        if (a.name === '@apiauth-get_api_token') return -1;
        if (b.name === '@apiauth-get_api_token') return 1;
        return 0;
    });
});

const deleteApi = async (api) => {
    try {
        // Show a SweetAlert confirmation popup
        const confirmation = await Swal.fire({
            title: 'Are you sure?',
            text: 'You are about to delete this API. This action cannot be undone. Are you sure you want to proceed?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel',
            customClass: {
                confirmButton: 'dk-update-btn',
                cancelButton: 'dk-cancle-btn',
            },
            buttonsStyling: false,
        });

        // If user confirms deletion
        if (confirmation.isConfirmed) {
            const response = await axios.delete(route('vendor-apis.destroy', { id: api.id }));

            if (response.data.success) {
                // Remove the deleted API from the apis array
                const index = apis.findIndex(a => a.id === api.id);
                if (index !== -1) {
                    apis.splice(index, 1);
                }
                // Show success message
                //Swal.fire('Deleted!', 'The API has been deleted.', 'success');
            } else {
                throw new Error('Failed to delete API');
            }
        }
    } catch (error) {
        console.error(error);
        // Show error message if deletion fails
        Swal.fire('Error', 'An error occurred while deleting the API.', 'error');
    }
};

onMounted(() => {
    setTimeout(() => {
        successMessage = null;
    }, 1000);
});

</script>

<template>

    <Head :title="pageTitle" />

    <AuthenticatedLayout>
        <!-- <PageHeading :title="pageTitle" :href="route('vendor-apis.create')" buttonLabel="Add API"></PageHeading> -->

        <Transition v-if="successMessage" name="fade">
            <p class="mb-4 text-green-400 text-sm successMessages">{{ successMessage }}</p>
        </Transition>

        <div class="flex">
            <div class="flex-1">
                <div class="">
                    <div class="max-w-full overflow-x-auto">
                        <table class="w-full border border-gray-300 ">
                            <thead>
                                <tr class="border-b text-left border-gray-300 bg-gray-100">
                                    <th class="p-2 font-medium border-r">Name</th>
                                    <th class="p-2 font-medium border-r">URL</th>
                                    <th class="p-2 font-medium border-r">Method</th>
                                    <th class="p-2 font-medium border-r">Description</th>
                                    <th class="p-2 font-medium text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="text-sm text-slate-600">
                                <tr v-for="api in sortedApis" :key="api.id" class="border-b border-gray-300">
                                    <td class="p-2 border-r">{{ api.name }}</td>
                                    <td class="p-2 border-r">{{ api.url }}</td>
                                    <td class="p-2 border-r">{{ api.method }}</td>
                                    <td class="p-2 border-r">{{ api.description }}</td>
                                    <td class="p-2 flex space-x-2 justify-center">
                                        <Link :href="route('vendor-apis.edit', { id: api.id })"
                                            class="dt-deals-action-btn">
                                        <DtIconGlobal :type="'edit'" />
                                        </Link>
                                        <button v-if="api.name != '@apiauth-get_api_token'"
                                            class="dt-deals-action-btn dt-deals-delete-btn" @click="deleteApi(api)">
                                            <DtIconGlobal :type="'delete'" />
                                        </button>

                                    </td>
                                </tr>
                            </tbody>
                        </table>


                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
