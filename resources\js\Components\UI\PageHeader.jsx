import React from 'react';
import clsx from 'clsx';

export const PageHeader = ({ 
    title, 
    subtitle, 
    children, 
    className = '',
    ...props 
}) => {
    return (
        <div 
            className={clsx(
                'flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8',
                className
            )}
            {...props}
        >
            <div>
                <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
                {subtitle && (
                    <p className="mt-1 text-sm text-gray-600">{subtitle}</p>
                )}
            </div>
            {children && (
                <div className="flex items-center gap-3">
                    {children}
                </div>
            )}
        </div>
    );
};
