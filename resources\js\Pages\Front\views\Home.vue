<script setup>
import AppBanner from '../components/shared/AppBanner.vue';
import App from '../App.vue';
import { ref } from 'vue';
import { Head, usePage } from '@inertiajs/vue3';
import FeatureSection from '../components/shared/FeatureSection.vue';
import AppSection from '../components/shared/AppSection.vue';
const pageTitle = ref('Home');
</script>

<style scoped>
/* Your scoped styles here */
</style>



<template>

	<Head :title="pageTitle" />
	<App>
		<div class="container mx-auto">
			<!-- Banner -->
			<AppBanner class="mb-5 " />

			<!-- About me -->
			<div class="container mx-auto ">
				<FeatureSection />
			</div>

			<!-- Projects -->
			<AppSection />
		</div>
	</App>

</template>