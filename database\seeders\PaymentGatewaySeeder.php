<?php

namespace Database\Seeders;

use App\Models\PaymentGateway;
use Illuminate\Database\Seeder;

class PaymentGatewaySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $gateways = [
            [
                'name' => 'stripe',
                'display_name' => 'Stripe',
                'is_active' => false,
                'test_mode' => true,
                'test_config' => [
                    'publishable_key' => '',
                    'secret_key' => '',
                    'webhook_endpoint_secret' => '',
                ],
                'live_config' => [
                    'publishable_key' => '',
                    'secret_key' => '',
                    'webhook_endpoint_secret' => '',
                ],
                'webhook_secret' => '',
            ],
            [
                'name' => 'paypal',
                'display_name' => 'PayPal',
                'is_active' => false,
                'test_mode' => true,
                'test_config' => [
                    'client_id' => '',
                    'client_secret' => '',
                    'webhook_id' => '',
                ],
                'live_config' => [
                    'client_id' => '',
                    'client_secret' => '',
                    'webhook_id' => '',
                ],
                'webhook_secret' => '',
            ],
        ];

        foreach ($gateways as $gatewayData) {
            PaymentGateway::updateOrCreate(
                ['name' => $gatewayData['name']],
                $gatewayData
            );
        }
    }
}
