import React, { useState } from 'react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { Input } from '@/Components/FormElements';
import Button from '@/Components/Button';
import { Card, CardContent, PageHeader, StatusBadge } from '@/Components/UI';

export default function IntentCreate() {
    const [formData, setFormData] = useState({
        intentName: '',
        context: '',
        description: '',
        category: 'General',
        status: 'draft'
    });

    const [trainingPhrases, setTrainingPhrases] = useState(['']);
    const [responsePhrases, setResponsePhrases] = useState(['']);

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const addTrainingPhrase = () => {
        setTrainingPhrases([...trainingPhrases, '']);
    };

    const removeTrainingPhrase = (index) => {
        setTrainingPhrases(trainingPhrases.filter((_, i) => i !== index));
    };

    const updateTrainingPhrase = (index, value) => {
        const updated = [...trainingPhrases];
        updated[index] = value;
        setTrainingPhrases(updated);
    };

    const addResponsePhrase = () => {
        setResponsePhrases([...responsePhrases, '']);
    };

    const removeResponsePhrase = (index) => {
        setResponsePhrases(responsePhrases.filter((_, i) => i !== index));
    };

    const updateResponsePhrase = (index, value) => {
        const updated = [...responsePhrases];
        updated[index] = value;
        setResponsePhrases(updated);
    };

    const categories = ['General', 'Greeting', 'Information', 'Banking', 'Orders', 'Account', 'System', 'Conversation'];

    return (
        <DashboardLayout title="Create Intent">
            <title>Create Intent</title>

            <div className="space-y-6">
                <PageHeader
                    title="Create New Intent"
                    subtitle="Define a new chatbot intent with training phrases and responses"
                />

                <div className="max-w-4xl mx-auto">
                    <Card>
                        <CardContent className="p-8">
                            <form className="space-y-8">
                                {/* Basic Information Section */}
                                <div className="space-y-6">
                                    <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                                        Basic Information
                                    </h3>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Intent Name *
                                                <span title="The unique name identifier for this intent" className="text-gray-400 cursor-help ml-1">
                                                    &#9432;
                                                </span>
                                            </label>
                                            <Input
                                                name="intentName"
                                                value={formData.intentName}
                                                onChange={(e) => handleInputChange('intentName', e.target.value)}
                                                placeholder="e.g., check_balance, greeting, order_status"
                                                className="w-full"
                                            />
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Context
                                                <span title="Context or scope where this intent applies" className="text-gray-400 cursor-help ml-1">
                                                    &#9432;
                                                </span>
                                            </label>
                                            <Input
                                                name="context"
                                                value={formData.context}
                                                onChange={(e) => handleInputChange('context', e.target.value)}
                                                placeholder="e.g., banking, support, general"
                                                className="w-full"
                                            />
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Category *
                                            </label>
                                            <select
                                                value={formData.category}
                                                onChange={(e) => handleInputChange('category', e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cf-primary focus:border-cf-primary"
                                            >
                                                {categories.map(category => (
                                                    <option key={category} value={category}>{category}</option>
                                                ))}
                                            </select>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Status
                                            </label>
                                            <div className="flex gap-3">
                                                {['draft', 'active'].map((status) => (
                                                    <label key={status} className="flex items-center">
                                                        <input
                                                            type="radio"
                                                            name="status"
                                                            value={status}
                                                            checked={formData.status === status}
                                                            onChange={(e) => handleInputChange('status', e.target.value)}
                                                            className="mr-2 text-cf-primary focus:ring-cf-primary"
                                                        />
                                                        <StatusBadge variant={status === 'active' ? 'success' : 'warning'}>
                                                            {status}
                                                        </StatusBadge>
                                                    </label>
                                                ))}
                                            </div>
                                        </div>

                                        <div className="md:col-span-2">
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Description
                                            </label>
                                            <Input
                                                name="description"
                                                value={formData.description}
                                                onChange={(e) => handleInputChange('description', e.target.value)}
                                                as="textarea"
                                                rows={3}
                                                placeholder="Describe what this intent handles..."
                                                className="w-full"
                                            />
                                        </div>
                                    </div>
                                </div>

                                {/* Training Phrases Section */}
                                <div className="space-y-6">
                                    <div className="flex items-center justify-between">
                                        <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2 flex-1">
                                            Training Phrases
                                            <span title="Examples of what users might say to trigger this intent" className="text-gray-400 cursor-help ml-1">
                                                &#9432;
                                            </span>
                                        </h3>
                                    </div>

                                    <div className="space-y-3">
                                        {trainingPhrases.map((phrase, index) => (
                                            <div key={index} className="flex gap-2">
                                                <Input
                                                    value={phrase}
                                                    onChange={(e) => updateTrainingPhrase(index, e.target.value)}
                                                    placeholder="e.g., What's my account balance?, Check my balance"
                                                    className="flex-1"
                                                />
                                                {trainingPhrases.length > 1 && (
                                                    <button
                                                        type="button"
                                                        onClick={() => removeTrainingPhrase(index)}
                                                        className="px-3 py-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                                                    >
                                                        ✕
                                                    </button>
                                                )}
                                            </div>
                                        ))}
                                        <button
                                            type="button"
                                            onClick={addTrainingPhrase}
                                            className="text-cf-primary hover:text-cf-primary-600 text-sm font-medium"
                                        >
                                            + Add Training Phrase
                                        </button>
                                    </div>
                                </div>

                                {/* Response Phrases Section */}
                                <div className="space-y-6">
                                    <div className="flex items-center justify-between">
                                        <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2 flex-1">
                                            Response Phrases
                                            <span title="How the chatbot should respond when this intent is triggered" className="text-gray-400 cursor-help ml-1">
                                                &#9432;
                                            </span>
                                        </h3>
                                    </div>

                                    <div className="space-y-3">
                                        {responsePhrases.map((phrase, index) => (
                                            <div key={index} className="flex gap-2">
                                                <Input
                                                    value={phrase}
                                                    onChange={(e) => updateResponsePhrase(index, e.target.value)}
                                                    placeholder="e.g., Your current balance is $1,234.56"
                                                    className="flex-1"
                                                />
                                                {responsePhrases.length > 1 && (
                                                    <button
                                                        type="button"
                                                        onClick={() => removeResponsePhrase(index)}
                                                        className="px-3 py-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                                                    >
                                                        ✕
                                                    </button>
                                                )}
                                            </div>
                                        ))}
                                        <button
                                            type="button"
                                            onClick={addResponsePhrase}
                                            className="text-cf-primary hover:text-cf-primary-600 text-sm font-medium"
                                        >
                                            + Add Response Phrase
                                        </button>
                                    </div>
                                </div>

                                {/* Action Buttons */}
                                <div className="flex items-center justify-end gap-4 pt-6 border-t border-gray-200">
                                    <Button
                                        type="button"
                                        className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2"
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        type="submit"
                                        className="bg-cf-primary hover:bg-cf-primary-600 text-white px-6 py-2"
                                    >
                                        Save Intent
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </DashboardLayout>
    );
}