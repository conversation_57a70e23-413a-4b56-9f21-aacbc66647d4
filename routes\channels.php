<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Broadcast;

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('chat-channel', function () {
    return Auth::check();
});

Broadcast::channel('agent-channel', function () {
    return Auth::check();
});

Broadcast::channel('broadcast-notification', function () {
    return Auth::check();
});

Broadcast::channel('online-users', function ($user) {
    if (Auth::check()) {
        return ['id' => $user->id, 'name' => $user->name];
    }

    return false;
});
