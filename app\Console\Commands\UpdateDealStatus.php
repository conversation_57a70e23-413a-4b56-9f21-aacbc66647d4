<?php

namespace App\Console\Commands;

use App\Events\ChatAgentMessage;
use App\Events\NotificationEvent;
use App\Models\BotConversation;
use App\Models\Company;
use App\Models\Deal;
use App\Models\Notifications;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateDealStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'deals:update-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update deal statuses based on start and expiry dates';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        //---------------------------------------DEAL----------------------------------------------------
        // Update deals that should start (status 0 -> 1)
        Deal::where('deal_status', 0)
            ->where('dealStartOn', '<=', Carbon::now())
            ->where('dealExpiresOn', '>', Carbon::now())
            ->update(['deal_status' => 1]);

        // Update expired deals (status 1 -> 2)
        Deal::where('deal_status', 1)
            ->where('dealExpiresOn', '<=', Carbon::now())
            ->update(['deal_status' => 2]);

        $this->info('Deal statuses updated successfully!');

        //-------------------------------- Notification add BotConvesaction ----------------------------

        // 1. Notify users if inactive for 1 minute
        $notificationMsg = 'Your chat is inactive. Please respond within 1 minute to keep the conversation active. Otherwise, it will be closed.';
        
        
        DB::table('botconversation')
            ->where('status', 1)
            ->whereNull('notification_msg')
            ->where('updated_at', '<=', Carbon::now()->subMinutes(1))
            ->update([
                'notification_msg' => $notificationMsg,
                'updated_at'       => DB::raw('updated_at'),   // ← keeps the old value
            ]);
    
        // Only update if notification_msg is still null (prevent re-updating every time)
        // BotConversation::where('status', 1)
        //     ->whereNull('notification_msg')
        //     ->where('updated_at', '<=', Carbon::now()->subMinutes(1))
        //     ->timestamps(false)
        //     ->update(['notification_msg' => $notificationMsg]);
        
        // 2. Process all chats with a pending notification
        $listOfConversations = BotConversation::where('status', 1)
            ->whereNotNull('notification_msg')
            ->get();
        
        foreach ($listOfConversations as $conversation) {
            $checkAgain = BotConversation::find($conversation->id);
        
            if ($checkAgain && $checkAgain->notification_msg !== '') {
                $companyDetail = Company::where('userId', $conversation->company_user_id)->first();
                if (!$companyDetail) continue;
        
                $types = $companyDetail->userId . '_' . $companyDetail->id;
        
                // User Notification
                $route = 'dtadmin/chats?source=' . $types . '&umid=' . base64_encode($conversation->user_id);
                Notifications::saveNotifications(
                    $conversation->user_id,
                    $conversation->company_user_id,
                    $notificationMsg,
                    $route,
                    'chats'
                );
        
                // Company Notification
                $route = 'dtadmin/chatbot?source=' . $types . '&umid=' . base64_encode($conversation->user_id) . '&cmpid=' . $companyDetail->id;
                Notifications::saveNotifications(
                    $conversation->company_user_id,
                    $conversation->user_id,
                    $notificationMsg,
                    $route,
                    'chatbot'
                );
        
                // Broadcast notification
                broadcast(new NotificationEvent(['status' => 1]))->toOthers();
        
                // Mark notification as sent
                // $checkAgain->timestamps = false;
                // $checkAgain->update(['notification_msg' => null]);
                
                DB::table('botconversation')
                ->where('id', $conversation->id)
                ->update([
                    'notification_msg' => null,
                    'updated_at'       => DB::raw('updated_at'),
                ]);
                
                
            }
        }
        
        // 3. Auto-close conversation after 2 minutes
        BotConversation::where('status', 1)
            ->where('updated_at', '<=', Carbon::now()->subMinutes(2))
            ->update(['status' => 2, 'notification_msg' => null]);



        //-------------------------------------- DELETE NOTIFICATION AFTER SEEND AND AFTER 15 MIN ----------------------

        Notifications::where('status', 'seen')
            ->where('updated_at', '<=', Carbon::now()->subMinutes(3))
            ->delete();
            
        $totalCount = Notifications::count();
        if ($totalCount == 0) {
            Notifications::truncate();
        }

        broadcast(new NotificationEvent(['status' => 1]))->toOthers();


    }
}
