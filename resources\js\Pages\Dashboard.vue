<!-- Dashboard.vue -->
<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, usePage } from '@inertiajs/vue3';
import CompaniesTable from '@/Components/Tables/CompaniesTable.vue';
import ChatCard from '@/Components/ChatCard.vue';
import DataStatsOne from '@/Components/DataStats/DataStatsOne.vue';

const { TotalDeals, resolvedComplains, totalComplains, totalUser } = usePage().props;

</script>

<template>

    <Head title="Dashboard" />

    <AuthenticatedLayout>
        <template #header>
            <h2 class="font-semibold text-slate-800 text-xl leading-tight">Dashboard</h2>
        </template>
        <div class="flex">


            <div class="flex-1">
                <div class="gap-4 md:gap-6 2xl:gap-6 grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4">
                    <DataStatsOne :total_deals="TotalDeals" :resolvedComplain="resolvedComplains"
                        :totalComplains="totalComplains" :totalUser="totalUser" />
                </div>
            </div>

        </div>

    </AuthenticatedLayout>
</template>
