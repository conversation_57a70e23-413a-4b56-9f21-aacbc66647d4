<script setup>
import { onMounted, ref } from 'vue';

const model = defineModel({
	type: String,
	required: true,
});

const input = ref(null);

onMounted(() => {
	if (input.value.hasAttribute('autofocus')) {
		input.value.focus();
	}
});

defineExpose({ focus: () => input.value.focus() });

// Define props and emits
const props = defineProps({
	label: String,
	inputIdentifier: String,
	val: String,
	inputType: {
		type: String,
		default: 'text',
	},
});


</script>

<template>
	<div>
		<label class="block mb-1 text-cfp-500-dark text-sm" :for="inputIdentifier">{{ label
			}}</label>
		<input
			class="border-stroke focus:border-cfp-500 bg-transparent px-4 py-2 border rounded-md focus:ring-cfp-500/50 w-full text-slate-800 outline-none"
			:id="inputIdentifier" :name="inputIdentifier" :placeholder="label" :type="inputType" v-model="model"
			ref="input" />
	</div>
</template>












<!-- <script>
export default {
	props: {
		label: {
			type: String,
			default: '',
		},
		inputIdentifier: {
			type: String,
			default: '',
		},
		val: {
			type: [String, Number],
			default: '',
		},
		inputType: {
			type: String,
			default: 'text',
		},
	},
};
</script>

<template>
	<div>
		<label
			class="block mb-2 text-cfp-500-dark text-lg"
			:for="label"
			>{{ label }}</label
		>
		<input
			class="border-gray-300 bg-ternary-light shadow-sm px-5 py-3 border border-opacity-50 rounded-md w-full text-cfp-500-dark text-md"
			:id="inputIdentifier"
			:name="inputIdentifier"
			:placeholder="label"
			:aria-label="inputIdentifier"
			:value="val"
			:type="inputType"
			v-bind="$attrs"
			@input="$emit('update:val', $event.target.value)"
			required
		/>
	</div>
</template>

<style lang="scss" scoped></style> -->
