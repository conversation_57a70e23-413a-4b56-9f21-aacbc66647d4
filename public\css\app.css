@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap");

*,
::before,
::after {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style: ;
}

::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style: ;
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
    box-sizing: border-box; /* 1 */
    border-width: 0; /* 2 */
    border-style: solid; /* 2 */
    border-color: #e5e7eb; /* 2 */
}

::before,
::after {
    --tw-content: "";
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
    line-height: 1.5; /* 1 */
    -webkit-text-size-adjust: 100%; /* 2 */
    -moz-tab-size: 4; /* 3 */
    -o-tab-size: 4;
    tab-size: 4; /* 3 */
    font-family: Poppins, ui-sans-serif, system-ui, sans-serif,
        "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
        "Noto Color Emoji"; /* 4 */
    font-feature-settings: normal; /* 5 */
    font-variation-settings: normal; /* 6 */
    -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
    margin: 0; /* 1 */
    line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
    height: 0; /* 1 */
    color: inherit; /* 2 */
    border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: inherit;
    font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
    color: inherit;
    text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
    font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
        "Liberation Mono", "Courier New", monospace; /* 1 */
    font-feature-settings: normal; /* 2 */
    font-variation-settings: normal; /* 3 */
    font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
    font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}

sub {
    bottom: -0.25em;
}

sup {
    top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
    text-indent: 0; /* 1 */
    border-color: inherit; /* 2 */
    border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
    font-family: inherit; /* 1 */
    font-feature-settings: inherit; /* 1 */
    font-variation-settings: inherit; /* 1 */
    font-size: 100%; /* 1 */
    font-weight: inherit; /* 1 */
    line-height: inherit; /* 1 */
    letter-spacing: inherit; /* 1 */
    color: inherit; /* 1 */
    margin: 0; /* 2 */
    padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
    text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type="button"]),
input:where([type="reset"]),
input:where([type="submit"]) {
    -webkit-appearance: button; /* 1 */
    background-color: transparent; /* 2 */
    background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
    outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
    box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
    vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
    height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type="search"] {
    -webkit-appearance: textfield; /* 1 */
    outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
    -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
    -webkit-appearance: button; /* 1 */
    font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
    display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
    margin: 0;
}

fieldset {
    margin: 0;
    padding: 0;
}

legend {
    padding: 0;
}

ol,
ul,
menu {
    list-style: none;
    margin: 0;
    padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
    padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
    resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder,
textarea::-moz-placeholder {
    opacity: 1; /* 1 */
    color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
    opacity: 1; /* 1 */
    color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
    cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
    cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
    display: block; /* 1 */
    vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
    max-width: 100%;
    height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
    display: none;
}

[type="text"],
input:where(:not([type])),
[type="email"],
[type="url"],
[type="password"],
[type="number"],
[type="date"],
[type="datetime-local"],
[type="month"],
[type="search"],
[type="tel"],
[type="time"],
[type="week"],
[multiple],
textarea,
select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    border-color: #6b7280;
    border-width: 1px;
    border-radius: 0px;
    padding-top: 0.5rem;
    padding-right: 0.75rem;
    padding-bottom: 0.5rem;
    padding-left: 0.75rem;
    font-size: 1rem;
    line-height: 1.5rem;
    --tw-shadow: 0 0 #0000;
}

[type="text"]:focus,
input:where(:not([type])):focus,
[type="email"]:focus,
[type="url"]:focus,
[type="password"]:focus,
[type="number"]:focus,
[type="date"]:focus,
[type="datetime-local"]:focus,
[type="month"]:focus,
[type="search"]:focus,
[type="tel"]:focus,
[type="time"]:focus,
[type="week"]:focus,
[multiple]:focus,
textarea:focus,
select:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    --tw-ring-inset: var(--tw-empty, /*!*/ /*!*/);
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: #337e81 !important;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow);
    border-color: #337e81;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
    color: #6b7280;
    opacity: 1;
}

input::placeholder,
textarea::placeholder {
    color: #6b7280;
    opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
}

::-webkit-date-and-time-value {
    min-height: 1.5em;
    text-align: inherit;
}

::-webkit-datetime-edit {
    display: inline-flex;
}

::-webkit-datetime-edit,
::-webkit-datetime-edit-year-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-minute-field,
::-webkit-datetime-edit-second-field,
::-webkit-datetime-edit-millisecond-field,
::-webkit-datetime-edit-meridiem-field {
    padding-top: 0;
    padding-bottom: 0;
}

select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
}

[multiple],
[size]:where(select:not([size="1"])) {
    background-image: initial;
    background-position: initial;
    background-repeat: unset;
    background-size: initial;
    padding-right: 0.75rem;
    -webkit-print-color-adjust: unset;
    print-color-adjust: unset;
}

[type="checkbox"],
[type="radio"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    padding: 0;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    display: inline-block;
    vertical-align: middle;
    background-origin: border-box;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    flex-shrink: 0;
    height: 1rem;
    width: 1rem;
    color: #337e81;
    background-color: #fff;
    border-color: #6b7280;
    border-width: 1px;
    --tw-shadow: 0 0 #0000;
}

[type="checkbox"] {
    border-radius: 0px;
}

[type="radio"] {
    border-radius: 100%;
}

[type="checkbox"]:focus,
[type="radio"]:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    --tw-ring-inset: var(--tw-empty, /*!*/ /*!*/);
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: #337e81;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow);
}

[type="checkbox"]:checked,
[type="radio"]:checked {
    border-color: transparent;
    background-color: currentColor;
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
}

[type="checkbox"]:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}

@media (forced-colors: active) {
    [type="checkbox"]:checked {
        -webkit-appearance: auto;
        -moz-appearance: auto;
        appearance: auto;
    }
}

[type="radio"]:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

@media (forced-colors: active) {
    [type="radio"]:checked {
        -webkit-appearance: auto;
        -moz-appearance: auto;
        appearance: auto;
    }
}

[type="checkbox"]:checked:hover,
[type="checkbox"]:checked:focus,
[type="radio"]:checked:hover,
[type="radio"]:checked:focus {
    border-color: transparent;
    background-color: currentColor;
}

[type="checkbox"]:indeterminate {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
    border-color: transparent;
    background-color: currentColor;
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
}

@media (forced-colors: active) {
    [type="checkbox"]:indeterminate {
        -webkit-appearance: auto;
        -moz-appearance: auto;
        appearance: auto;
    }
}

[type="checkbox"]:indeterminate:hover,
[type="checkbox"]:indeterminate:focus {
    border-color: transparent;
    background-color: currentColor;
}

[type="file"] {
    background: unset;
    border-color: inherit;
    border-width: 0;
    border-radius: 0;
    padding: 0;
    font-size: unset;
    line-height: inherit;
}

[type="file"]:focus {
    outline: 1px solid ButtonText;
    outline: 1px auto -webkit-focus-ring-color;
}

* {
    scrollbar-color: initial;
    scrollbar-width: initial;
}

body {
    position: relative;
    z-index: 1;
    --tw-bg-opacity: 1;
    background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
    font-size: 1rem;
    line-height: 1.5rem;
    font-weight: 400;
    --tw-text-opacity: 1;
    color: rgb(30 41 59 / var(--tw-text-opacity, 1));
}

input::-moz-placeholder {
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 400;
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

input::placeholder {
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 400;
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.container {
    width: 100%;
    padding-right: 1rem;
    padding-left: 1rem;
}
@media (min-width: 375px) {
    .container {
        max-width: 375px;
    }
}
@media (min-width: 425px) {
    .container {
        max-width: 425px;
    }
}
@media (min-width: 640px) {
    .container {
        max-width: 640px;
        padding-right: 2rem;
        padding-left: 2rem;
    }
}
@media (min-width: 768px) {
    .container {
        max-width: 768px;
    }
}
@media (min-width: 1024px) {
    .container {
        max-width: 1024px;
        padding-right: 5rem;
        padding-left: 5rem;
    }
}
@media (min-width: 1280px) {
    .container {
        max-width: 1280px;
        padding-right: 6rem;
        padding-left: 6rem;
    }
}
@media (min-width: 1536px) {
    .container {
        max-width: 1536px;
        padding-right: 8rem;
        padding-left: 8rem;
    }
}
@media (min-width: 2000px) {
    .container {
        max-width: 2000px;
    }
}
.main-logo-svg .logo-fillcolor {
    fill: #337e81;
}
.add-form-main-bcls {
    display: grid;
    grid-template-columns: repeat(12, minmax(0, 1fr));
    gap: 1rem;
    border-radius: 0.375rem;
    border-width: 1px;
    --tw-border-opacity: 1;
    border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.dk-update-btn {
    align-items: center;
    border-radius: 0.375rem;
    border-width: 1px;
    --tw-bg-opacity: 1;
    background-color: rgb(51 126 129 / var(--tw-bg-opacity, 1));
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
    text-align: center;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    line-height: 1.5;
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke, opacity, box-shadow, transform,
        filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke, opacity, box-shadow, transform,
        filter, backdrop-filter;
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke, opacity, box-shadow, transform,
        filter, backdrop-filter, -webkit-backdrop-filter;
    transition-duration: 150ms;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.dk-update-btn:hover {
    background-color: rgb(51 126 129 / 0.85);
}
@media not all and (min-width: 768px) {
    .dk-update-btn {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}
.dk-cancle-btn {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
    display: inline-flex !important;
    align-items: center;
    border-radius: 0.375rem;
    border-width: 1px;
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
    text-align: end;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    line-height: 1.5;
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke, opacity, box-shadow, transform,
        filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke, opacity, box-shadow, transform,
        filter, backdrop-filter;
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke, opacity, box-shadow, transform,
        filter, backdrop-filter, -webkit-backdrop-filter;
    transition-duration: 150ms;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.dk-cancle-btn:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
@media not all and (min-width: 768px) {
    .dk-cancle-btn {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}
a.dk-cancle-btn {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
    padding-top: 0.625rem !important;
    padding-bottom: 0.625rem !important;
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
    line-height: 1.5 !important;
}
@media not all and (min-width: 768px) {
    a.dk-cancle-btn {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }
}
.left-sidebar-menutext {
    padding-top: 0.25rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 400;
    --tw-text-opacity: 1;
    color: rgb(15 23 42 / var(--tw-text-opacity, 1));
}
.list-unstyled li:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.left-menuactive-effect {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.form-main-body {
    width: 100%;
}
@media (min-width: 640px) {
    .form-main-body {
        width: 50%;
    }
}
.chat-sender-cls {
    margin-bottom: 0px;
    border-radius: 0.375rem;
    border-top-left-radius: 0px;
    background-color: rgb(229 231 235 / 0.8);
    padding-left: 1.25rem;
    padding-right: 1.25rem;
    padding-top: 0.375rem;
    padding-bottom: 0.375rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 400;
    --tw-text-opacity: 1;
    color: rgb(36 48 63 / var(--tw-text-opacity, 1));
}
.chat-receiver-cls {
    margin-bottom: 0px;
    border-radius: 0.375rem;
    border-bottom-right-radius: 0px;
    background-color: rgb(51 126 129 / 0.1);
    padding-left: 1.25rem;
    padding-right: 1.25rem;
    padding-top: 0.375rem;
    padding-bottom: 0.375rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 400;
    --tw-text-opacity: 1;
    color: rgb(36 48 63 / var(--tw-text-opacity, 1));
}
.input-flt-datepicker {
    margin-top: 0.25rem;
    width: 100%;
    border-radius: 0.375rem;
    border-width: 1px;
    --tw-border-opacity: 1;
    border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
    background-color: transparent;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    --tw-text-opacity: 1;
    color: rgb(0 0 0 / var(--tw-text-opacity, 1));
    outline: 2px solid transparent;
    outline-offset: 2px;
}
.input-flt-datepicker:focus {
    --tw-border-opacity: 1;
    border-color: rgb(51 126 129 / var(--tw-border-opacity, 1));
}
.input-flt-datepicker:focus-visible {
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.form-ttl-gap {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.dash-box-main {
    border-radius: 0.375rem;
    border-width: 1px;
    --tw-border-opacity: 1;
    border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
    padding-left: 1.875rem;
    padding-right: 1.875rem;
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
}
.dash-box-main:hover {
    --tw-shadow: 0px 8px 13px -3px rgba(0, 0, 0, 0.07);
    --tw-shadow-colored: 0px 8px 13px -3px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.dt-table-pagi {
    display: grid;
    grid-auto-flow: column;
    grid-template-rows: repeat(3, minmax(0, 1fr));
    align-content: center;
    gap: 0.5rem;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
}
.login-body-cls {
    margin: 1rem;
    max-width: 36rem;
    border-radius: 0.375rem;
    border-width: 1px;
    padding: 1.75rem;
    text-align: left;
    line-height: 2;
}
@media not all and (min-width: 768px) {
    .login-body-cls {
        margin: 0.5rem;
        padding: 1.25rem;
    }
}
/* table css */
.dt-deals-header {
    border-bottom-width: 1px;
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
    padding: 1.5rem;
}
.dt-deals-title {
    font-size: 1.25rem;
    line-height: 1.75rem;
    font-weight: 600;
    --tw-text-opacity: 1;
    color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.dt-deals-wrapper {
    overflow-x: auto;
}
.dt-deals-table {
    width: 100%;
}
.dt-deals-table > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-y-reverse: 0;
    border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
    --tw-divide-opacity: 1;
    border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}
.dt-deals-table {
    border-bottom-width: 1px;
}
.dt-deals-th {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    text-align: left;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    --tw-text-opacity: 1;
    color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.dt-deals-table .dt-deals-th:last-child {
    text-align: right !important;
}
.dt-deals-tr:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.dt-deals-td {
    /* white-space: nowrap; */
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.dt-deals-th-checkbox,
.dt-deals-td-checkbox {
    white-space: nowrap;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
}
.dt-deals-checkbox {
    height: 1rem;
    width: 1rem;
    border-radius: 0.25rem;
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
    --tw-text-opacity: 1;
    color: rgb(67 56 202 / var(--tw-text-opacity, 1));
}
.dt-deals-checkbox:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(67 56 202 / var(--tw-ring-opacity, 1));
}
.dt-deals-title-wrap {
    display: flex;
    flex-direction: column;
}
.dt-deals-title-link {
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    --tw-text-opacity: 1;
    color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.dt-deals-title-link:hover {
    --tw-text-opacity: 1;
    color: rgb(51 126 129 / var(--tw-text-opacity, 1));
}
.dt-deals-status {
    display: inline-flex;
    border-radius: 9999px;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    font-size: 0.75rem;
    line-height: 1rem;
    font-weight: 600;
}
.dt-deals-status-pending {
    --tw-bg-opacity: 1;
    background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
    --tw-text-opacity: 1;
    color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.dt-deals-status-active {
    --tw-bg-opacity: 1;
    background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
    --tw-text-opacity: 1;
    color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.dt-deals-status-ended {
    --tw-bg-opacity: 1;
    background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
    --tw-text-opacity: 1;
    color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.dt-deals-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.dt-deals-actions > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.dt-deals-action-btn {
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity, 1));
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}
.dt-deals-action-btn:hover {
    --tw-text-opacity: 1;
    color: rgb(51 126 129 / var(--tw-text-opacity, 1));
}
.dt-deals-delete-btn:hover {
    --tw-text-opacity: 1;
    color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.dt-deals-icon {
    height: 1.25rem;
    width: 1.25rem;
    fill: none;
    stroke: currentColor;
}
.dt-deals-empty {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
    text-align: center;
    font-size: 0.875rem;
    line-height: 1.25rem;
    --tw-text-opacity: 1;
    color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
/* end table css  */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}
.pointer-events-none {
    pointer-events: none;
}
.static {
    position: static;
}
.fixed {
    position: fixed;
}
.absolute {
    position: absolute;
}
.relative {
    position: relative;
}
.sticky {
    position: sticky;
}
.inset-0 {
    inset: 0px;
}
.inset-x-0 {
    left: 0px;
    right: 0px;
}
.inset-y-1 {
    top: 0.25rem;
    bottom: 0.25rem;
}
.\!right-0 {
    right: 0px !important;
}
.\!right-1 {
    right: 0.25rem !important;
}
.-top-0\.5 {
    top: -0.125rem;
}
.-top-1 {
    top: -0.25rem;
}
.-top-40 {
    top: -10rem;
}
.bottom-0 {
    bottom: 0px;
}
.bottom-1 {
    bottom: 0.25rem;
}
.end-0 {
    inset-inline-end: 0px;
}
.left-0 {
    left: 0px;
}
.left-1 {
    left: 0.25rem;
}
.left-4 {
    left: 1rem;
}
.left-\[calc\(50\%\+3rem\)\] {
    left: calc(50% + 3rem);
}
.left-\[calc\(50\%-11rem\)\] {
    left: calc(50% - 11rem);
}
.left-auto {
    left: auto;
}
.right-0 {
    right: 0px;
}
.right-2 {
    right: 0.5rem;
}
.right-4 {
    right: 1rem;
}
.right-5 {
    right: 1.25rem;
}
.start-0 {
    inset-inline-start: 0px;
}
.top-0 {
    top: 0px;
}
.top-1 {
    top: 0.25rem;
}
.top-1\/2 {
    top: 50%;
}
.top-4 {
    top: 1rem;
}
.top-5 {
    top: 1.25rem;
}
.top-\[calc\(100\%-13rem\)\] {
    top: calc(100% - 13rem);
}
.top-full {
    top: 100%;
}
.isolate {
    isolation: isolate;
}
.-z-1 {
    z-index: -1;
}
.-z-10 {
    z-index: -10;
}
.z-0 {
    z-index: 0;
}
.z-1 {
    z-index: 1;
}
.z-10 {
    z-index: 10;
}
.z-20 {
    z-index: 20;
}
.z-30 {
    z-index: 30;
}
.z-40 {
    z-index: 40;
}
.z-50 {
    z-index: 50;
}
.order-1 {
    order: 1;
}
.order-2 {
    order: 2;
}
.col-span-1 {
    grid-column: span 1 / span 1;
}
.col-span-12 {
    grid-column: span 12 / span 12;
}
.col-span-2 {
    grid-column: span 2 / span 2;
}
.col-span-3 {
    grid-column: span 3 / span 3;
}
.row-span-2 {
    grid-row: span 2 / span 2;
}
.row-start-2 {
    grid-row-start: 2;
}
.row-end-4 {
    grid-row-end: 4;
}
.m-0 {
    margin: 0px;
}
.m-16 {
    margin: 4rem;
}
.mx-1 {
    margin-left: 0.25rem;
    margin-right: 0.25rem;
}
.mx-2 {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
}
.mx-5 {
    margin-left: 1.25rem;
    margin-right: 1.25rem;
}
.mx-6 {
    margin-left: 1.5rem;
    margin-right: 1.5rem;
}
.mx-auto {
    margin-left: auto;
    margin-right: auto;
}
.my-1 {
    margin-top: 0.25rem;
    margin-bottom: 0.25rem;
}
.my-1\.5 {
    margin-top: 0.375rem;
    margin-bottom: 0.375rem;
}
.my-2 {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}
.my-3 {
    margin-top: 0.75rem;
    margin-bottom: 0.75rem;
}
.my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.my-6 {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
}
.-ml-px {
    margin-left: -1px;
}
.-mt-2 {
    margin-top: -0.5rem;
}
.-mt-px {
    margin-top: -1px;
}
.mb-1 {
    margin-bottom: 0.25rem;
}
.mb-12 {
    margin-bottom: 3rem;
}
.mb-2 {
    margin-bottom: 0.5rem;
}
.mb-2\.5 {
    margin-bottom: 0.625rem;
}
.mb-3 {
    margin-bottom: 0.75rem;
}
.mb-4 {
    margin-bottom: 1rem;
}
.mb-4\.5 {
    margin-bottom: 1.125rem;
}
.mb-5 {
    margin-bottom: 1.25rem;
}
.mb-6 {
    margin-bottom: 1.5rem;
}
.mb-8 {
    margin-bottom: 2rem;
}
.ml-1 {
    margin-left: 0.25rem;
}
.ml-12 {
    margin-left: 3rem;
}
.ml-2 {
    margin-left: 0.5rem;
}
.ml-3 {
    margin-left: 0.75rem;
}
.ml-4 {
    margin-left: 1rem;
}
.ml-6 {
    margin-left: 1.5rem;
}
.ml-8 {
    margin-left: 2rem;
}
.ml-auto {
    margin-left: auto;
}
.mr-1 {
    margin-right: 0.25rem;
}
.mr-2 {
    margin-right: 0.5rem;
}
.mr-4 {
    margin-right: 1rem;
}
.mr-4\.5 {
    margin-right: 1.125rem;
}
.mr-6 {
    margin-right: 1.5rem;
}
.ms-2 {
    margin-inline-start: 0.5rem;
}
.ms-3 {
    margin-inline-start: 0.75rem;
}
.ms-4 {
    margin-inline-start: 1rem;
}
.mt-0 {
    margin-top: 0px;
}
.mt-1 {
    margin-top: 0.25rem;
}
.mt-10 {
    margin-top: 2.5rem;
}
.mt-12 {
    margin-top: 3rem;
}
.mt-16 {
    margin-top: 4rem;
}
.mt-2 {
    margin-top: 0.5rem;
}
.mt-20 {
    margin-top: 5rem;
}
.mt-3 {
    margin-top: 0.75rem;
}
.mt-4 {
    margin-top: 1rem;
}
.mt-5 {
    margin-top: 1.25rem;
}
.mt-6 {
    margin-top: 1.5rem;
}
.mt-7 {
    margin-top: 1.75rem;
}
.mt-8 {
    margin-top: 2rem;
}
.\!block {
    display: block !important;
}
.block {
    display: block;
}
.inline-block {
    display: inline-block;
}
.inline {
    display: inline;
}
.flex {
    display: flex;
}
.inline-flex {
    display: inline-flex;
}
.\!table {
    display: table !important;
}
.table {
    display: table;
}
.grid {
    display: grid;
}
.contents {
    display: contents;
}
.hidden {
    display: none;
}
.aspect-\[1155\/678\] {
    aspect-ratio: 1155/678;
}
.size-5 {
    width: 1.25rem;
    height: 1.25rem;
}
.size-6 {
    width: 1.5rem;
    height: 1.5rem;
}
.\!h-30 {
    height: 7.5rem !important;
}
.h-10 {
    height: 2.5rem;
}
.h-11\.5 {
    height: 2.875rem;
}
.h-115 {
    height: 28.75rem;
}
.h-12 {
    height: 3rem;
}
.h-12\.5 {
    height: 3.125rem;
}
.h-13 {
    height: 3.25rem;
}
.h-14 {
    height: 3.5rem;
}
.h-15 {
    height: 3.75rem;
}
.h-16 {
    height: 4rem;
}
.h-17 {
    height: 4.25rem;
}
.h-2 {
    height: 0.5rem;
}
.h-2\.5 {
    height: 0.625rem;
}
.h-20 {
    height: 5rem;
}
.h-24 {
    height: 6rem;
}
.h-3\.5 {
    height: 0.875rem;
}
.h-32 {
    height: 8rem;
}
.h-4 {
    height: 1rem;
}
.h-40 {
    height: 10rem;
}
.h-48 {
    height: 12rem;
}
.h-5 {
    height: 1.25rem;
}
.h-52 {
    height: 13rem;
}
.h-6 {
    height: 1.5rem;
}
.h-60 {
    height: 15rem;
}
.h-7 {
    height: 1.75rem;
}
.h-8 {
    height: 2rem;
}
.h-80 {
    height: 20rem;
}
.h-\[32\.5rem\] {
    height: 32.5rem;
}
.h-\[35\.5rem\] {
    height: 35.5rem;
}
.h-\[40vh\] {
    height: 40vh;
}
.h-\[60vh\] {
    height: 60vh;
}
.h-\[68vh\] {
    height: 68vh;
}
.h-\[calc\(100vh-200px\)\] {
    height: calc(100vh - 200px);
}
.h-\[calc\(100vh-256px\)\] {
    height: calc(100vh - 256px);
}
.h-\[calc\(100vh-8rem\)\] {
    height: calc(100vh - 8rem);
}
.h-\[calc\(80vh-5rem\)\] {
    height: calc(80vh - 5rem);
}
.h-auto {
    height: auto;
}
.h-full {
    height: 100%;
}
.h-screen {
    height: 100vh;
}
.max-h-32 {
    max-height: 8rem;
}
.max-h-40 {
    max-height: 10rem;
}
.max-h-full {
    max-height: 100%;
}
.min-h-\[80px\] {
    min-height: 80px;
}
.min-h-\[calc\(100vh-72px\)\] {
    min-height: calc(100vh - 72px);
}
.min-h-screen {
    min-height: 100vh;
}
.w-1\/2 {
    width: 50%;
}
.w-1\/4 {
    width: 25%;
}
.w-10 {
    width: 2.5rem;
}
.w-11\.5 {
    width: 2.875rem;
}
.w-12 {
    width: 3rem;
}
.w-14 {
    width: 3.5rem;
}
.w-15 {
    width: 3.75rem;
}
.w-16 {
    width: 4rem;
}
.w-2 {
    width: 0.5rem;
}
.w-2\.5 {
    width: 0.625rem;
}
.w-20 {
    width: 5rem;
}
.w-24 {
    width: 6rem;
}
.w-3\.5 {
    width: 0.875rem;
}
.w-3\/4 {
    width: 75%;
}
.w-30 {
    width: 7.5rem;
}
.w-4 {
    width: 1rem;
}
.w-40 {
    width: 10rem;
}
.w-48 {
    width: 12rem;
}
.w-5 {
    width: 1.25rem;
}
.w-50 {
    width: 12.5rem;
}
.w-52 {
    width: 13rem;
}
.w-56 {
    width: 14rem;
}
.w-6 {
    width: 1.5rem;
}
.w-60 {
    width: 15rem;
}
.w-7 {
    width: 1.75rem;
}
.w-8 {
    width: 2rem;
}
.w-\[36\.125rem\] {
    width: 36.125rem;
}
.w-\[8rem\] {
    width: 8rem;
}
.w-auto {
    width: auto;
}
.w-full {
    width: 100%;
}
.min-w-0 {
    min-width: 0px;
}
.min-w-\[120px\] {
    min-width: 120px;
}
.min-w-\[150px\] {
    min-width: 150px;
}
.min-w-\[220px\] {
    min-width: 220px;
}
.min-w-full {
    min-width: 100%;
}
.\!max-w-full {
    max-width: 100% !important;
}
.max-w-125 {
    max-width: 31.25rem;
}
.max-w-13 {
    max-width: 3.25rem;
}
.max-w-2xl {
    max-width: 42rem;
}
.max-w-3xl {
    max-width: 48rem;
}
.max-w-4xl {
    max-width: 56rem;
}
.max-w-6xl {
    max-width: 72rem;
}
.max-w-7xl {
    max-width: 80rem;
}
.max-w-full {
    max-width: 100%;
}
.max-w-lg {
    max-width: 32rem;
}
.max-w-md {
    max-width: 28rem;
}
.max-w-screen-lg {
    max-width: 1024px;
}
.max-w-xl {
    max-width: 36rem;
}
.flex-1 {
    flex: 1 1 0%;
}
.flex-auto {
    flex: 1 1 auto;
}
.flex-initial {
    flex: 0 1 auto;
}
.flex-none {
    flex: none;
}
.flex-shrink-0 {
    flex-shrink: 0;
}
.shrink-0 {
    flex-shrink: 0;
}
.flex-grow {
    flex-grow: 1;
}
.table-auto {
    table-layout: auto;
}
.border-collapse {
    border-collapse: collapse;
}
.origin-top {
    transform-origin: top;
}
.origin-top-right {
    transform-origin: top right;
}
.\!translate-x-full {
    --tw-translate-x: 100% !important;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;
}
.-translate-x-1\/2 {
    --tw-translate-x: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-full {
    --tw-translate-x: -100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
    --tw-translate-y: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-4 {
    --tw-translate-y: 1rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180 {
    --tw-rotate: 180deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-\[30deg\] {
    --tw-rotate: 30deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-95 {
    --tw-scale-x: 0.95;
    --tw-scale-y: 0.95;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform-gpu {
    transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0)
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes ping {
    75%,
    100% {
        transform: scale(2);
        opacity: 0;
    }
}
.animate-ping {
    animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
.cursor-default {
    cursor: default;
}
.cursor-not-allowed {
    cursor: not-allowed;
}
.cursor-pointer {
    cursor: pointer;
}
.select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.resize-none {
    resize: none;
}
.resize {
    resize: both;
}
.list-none {
    list-style-type: none;
}
.appearance-none {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}
.grid-flow-col {
    grid-auto-flow: column;
}
.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
}
.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
}
.grid-rows-3 {
    grid-template-rows: repeat(3, minmax(0, 1fr));
}
.flex-row {
    flex-direction: row;
}
.flex-row-reverse {
    flex-direction: row-reverse;
}
.flex-col {
    flex-direction: column;
}
.flex-col-reverse {
    flex-direction: column-reverse;
}
.flex-wrap {
    flex-wrap: wrap;
}
.place-content-end {
    place-content: end;
}
.content-center {
    align-content: center;
}
.items-start {
    align-items: flex-start;
}
.items-end {
    align-items: flex-end;
}
.items-center {
    align-items: center;
}
.items-baseline {
    align-items: baseline;
}
.justify-start {
    justify-content: flex-start;
}
.justify-end {
    justify-content: flex-end;
}
.justify-center {
    justify-content: center;
}
.justify-between {
    justify-content: space-between;
}
.justify-items-end {
    justify-items: end;
}
.justify-items-center {
    justify-items: center;
}
.gap-0 {
    gap: 0px;
}
.gap-1 {
    gap: 0.25rem;
}
.gap-2 {
    gap: 0.5rem;
}
.gap-3 {
    gap: 0.75rem;
}
.gap-4 {
    gap: 1rem;
}
.gap-5 {
    gap: 1.25rem;
}
.gap-6 {
    gap: 1.5rem;
}
.gap-8 {
    gap: 2rem;
}
.gap-x-6 {
    -moz-column-gap: 1.5rem;
    column-gap: 1.5rem;
}
.gap-x-8 {
    -moz-column-gap: 2rem;
    column-gap: 2rem;
}
.gap-y-10 {
    row-gap: 2.5rem;
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3\.5 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.875rem * var(--tw-space-x-reverse));
    margin-left: calc(0.875rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4\.5 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.125rem * var(--tw-space-x-reverse));
    margin-left: calc(1.125rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3\.5 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.875rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.875rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-5 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-7 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.75rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.75rem * var(--tw-space-y-reverse));
}
.divide-y > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-y-reverse: 0;
    border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-gray-100 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(243 244 246 / var(--tw-divide-opacity, 1));
}
.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}
.self-center {
    align-self: center;
}
.overflow-auto {
    overflow: auto;
}
.overflow-hidden {
    overflow: hidden;
}
.overflow-x-auto {
    overflow-x: auto;
}
.overflow-y-auto {
    overflow-y: auto;
}
.overflow-y-hidden {
    overflow-y: hidden;
}
.overflow-x-scroll {
    overflow-x: scroll;
}
.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.break-words {
    overflow-wrap: break-word;
}
.break-all {
    word-break: break-all;
}
.rounded {
    border-radius: 0.25rem;
}
.rounded-\[3px\] {
    border-radius: 3px;
}
.rounded-full {
    border-radius: 9999px;
}
.rounded-lg {
    border-radius: 0.5rem;
}
.rounded-md {
    border-radius: 0.375rem;
}
.rounded-sm {
    border-radius: 0.125rem;
}
.rounded-xl {
    border-radius: 0.75rem;
}
.rounded-l-lg {
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
}
.rounded-l-md {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}
.rounded-r-lg {
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
}
.rounded-r-md {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}
.rounded-t {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
}
.rounded-bl-md {
    border-bottom-left-radius: 0.375rem;
}
.rounded-br-none {
    border-bottom-right-radius: 0px;
}
.rounded-tl-md {
    border-top-left-radius: 0.375rem;
}
.rounded-tl-none {
    border-top-left-radius: 0px;
}
.rounded-tr-md {
    border-top-right-radius: 0.375rem;
}
.\!border-4 {
    border-width: 4px !important;
}
.border {
    border-width: 1px;
}
.border-0 {
    border-width: 0px;
}
.border-2 {
    border-width: 2px;
}
.border-\[\.5px\] {
    border-width: 0.5px;
}
.border-\[1\.5px\] {
    border-width: 1.5px;
}
.border-b {
    border-bottom-width: 1px;
}
.border-b-2 {
    border-bottom-width: 2px;
}
.border-l {
    border-left-width: 1px;
}
.border-l-2 {
    border-left-width: 2px;
}
.border-r {
    border-right-width: 1px;
}
.border-r-2 {
    border-right-width: 2px;
}
.border-t {
    border-top-width: 1px;
}
.border-t-2 {
    border-top-width: 2px;
}
.border-t-4 {
    border-top-width: 4px;
}
.border-solid {
    border-style: solid;
}
.border-black {
    --tw-border-opacity: 1;
    border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}
.border-cfp-500 {
    --tw-border-opacity: 1;
    border-color: rgb(51 126 129 / var(--tw-border-opacity, 1));
}
.border-gray-100 {
    --tw-border-opacity: 1;
    border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}
.border-gray-200 {
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-300 {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-gray-400 {
    --tw-border-opacity: 1;
    border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}
.border-gray-800 {
    --tw-border-opacity: 1;
    border-color: rgb(36 48 63 / var(--tw-border-opacity, 1));
}
.border-cfp-400 {
    --tw-border-opacity: 1;
    border-color: rgb(129 140 248 / var(--tw-border-opacity, 1));
}
.border-red-500 {
    --tw-border-opacity: 1;
    border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-stroke {
    --tw-border-opacity: 1;
    border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
}
.border-transparent {
    border-color: transparent;
}
.border-white {
    --tw-border-opacity: 1;
    border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-b-transparent {
    border-bottom-color: transparent;
}
.border-l-red-500 {
    --tw-border-opacity: 1;
    border-left-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-opacity-50 {
    --tw-border-opacity: 0.5;
}
.\!bg-cfp-500 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(51 126 129 / var(--tw-bg-opacity, 1)) !important;
}
.\!bg-gray-50 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1)) !important;
}
.bg-black {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-cfp-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-cfp-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(51 126 129 / var(--tw-bg-opacity, 1));
}
.bg-cyan-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(207 250 254 / var(--tw-bg-opacity, 1));
}
.bg-danger {
    --tw-bg-opacity: 1;
    background-color: rgb(211 64 83 / var(--tw-bg-opacity, 1));
}
.bg-gray-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-100\/80 {
    background-color: rgb(243 244 246 / 0.8);
}
.bg-gray-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-200\/80 {
    background-color: rgb(229 231 235 / 0.8);
}
.bg-gray-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-gray-700 {
    --tw-bg-opacity: 1;
    background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.bg-gray-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}
.bg-green-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-400 {
    --tw-bg-opacity: 1;
    background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));
}
.bg-green-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-green-700 {
    --tw-bg-opacity: 1;
    background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}
.bg-cfp-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(51 126 129 / var(--tw-bg-opacity, 1));
}
.bg-cfp-500\/10 {
    background-color: rgb(51 126 129 / 0.1);
}
.bg-meta-1 {
    --tw-bg-opacity: 1;
    background-color: rgb(220 53 69 / var(--tw-bg-opacity, 1));
}
.bg-meta-2 {
    --tw-bg-opacity: 1;
    background-color: rgb(239 242 247 / var(--tw-bg-opacity, 1));
}
.bg-meta-9 {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-red-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-red-500\/20 {
    background-color: rgb(239 68 68 / 0.2);
}
.bg-red-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.bg-secondary-light {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-success {
    --tw-bg-opacity: 1;
    background-color: rgb(33 150 83 / var(--tw-bg-opacity, 1));
}
.bg-ternary-light {
    --tw-bg-opacity: 1;
    background-color: rgb(246 247 248 / var(--tw-bg-opacity, 1));
}
.bg-transparent {
    background-color: transparent;
}
.bg-warning {
    --tw-bg-opacity: 1;
    background-color: rgb(255 167 11 / var(--tw-bg-opacity, 1));
}
.bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-yellow-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}
.bg-slate-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}
.bg-cfp-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}
.bg-opacity-0 {
    --tw-bg-opacity: 0;
}
.bg-opacity-10 {
    --tw-bg-opacity: 0.1;
}
.bg-gradient-to-tr {
    background-image: linear-gradient(to top right, var(--tw-gradient-stops));
}
.from-\[\#ff80b5\] {
    --tw-gradient-from: #ff80b5 var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(255 128 181 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.to-\[\#9089fc\] {
    --tw-gradient-to: #9089fc var(--tw-gradient-to-position);
}
.bg-cover {
    background-size: cover;
}
.fill-cfp-500 {
    fill: #337e81;
}
.fill-current {
    fill: currentColor;
}
.fill-meta-3 {
    fill: #10b981;
}
.fill-meta-5 {
    fill: #259ae6;
}
.fill-red-500 {
    fill: #ef4444;
}
.stroke-current {
    stroke: currentColor;
}
.object-cover {
    -o-object-fit: cover;
    object-fit: cover;
}
.object-center {
    -o-object-position: center;
    object-position: center;
}
.\!p-0 {
    padding: 0px !important;
}
.p-0 {
    padding: 0px;
}
.p-1 {
    padding: 0.25rem;
}
.p-1\.5 {
    padding: 0.375rem;
}
.p-10 {
    padding: 2.5rem;
}
.p-2 {
    padding: 0.5rem;
}
.p-2\.5 {
    padding: 0.625rem;
}
.p-3 {
    padding: 0.75rem;
}
.p-4 {
    padding: 1rem;
}
.p-5 {
    padding: 1.25rem;
}
.p-6 {
    padding: 1.5rem;
}
.p-8 {
    padding: 2rem;
}
.px-0 {
    padding-left: 0px;
    padding-right: 0px;
}
.px-1 {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
}
.px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
}
.px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}
.px-2\.5 {
    padding-left: 0.625rem;
    padding-right: 0.625rem;
}
.px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}
.px-3\.5 {
    padding-left: 0.875rem;
    padding-right: 0.875rem;
}
.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}
.px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
}
.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}
.px-7\.5 {
    padding-left: 1.875rem;
    padding-right: 1.875rem;
}
.py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}
.py-1\.5 {
    padding-top: 0.375rem;
    padding-bottom: 0.375rem;
}
.py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
}
.py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
}
.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}
.py-2\.5 {
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
}
.py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
}
.py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
}
.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
}
.py-4\.5 {
    padding-top: 1.125rem;
    padding-bottom: 1.125rem;
}
.py-5 {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem;
}
.py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
}
.py-7\.5 {
    padding-top: 1.875rem;
    padding-bottom: 1.875rem;
}
.pb-10 {
    padding-bottom: 2.5rem;
}
.pb-12 {
    padding-bottom: 3rem;
}
.pb-2 {
    padding-bottom: 0.5rem;
}
.pb-2\.5 {
    padding-bottom: 0.625rem;
}
.pb-4 {
    padding-bottom: 1rem;
}
.pb-5 {
    padding-bottom: 1.25rem;
}
.pb-8 {
    padding-bottom: 2rem;
}
.pe-4 {
    padding-inline-end: 1rem;
}
.pl-0 {
    padding-left: 0px;
}
.pl-1 {
    padding-left: 0.25rem;
}
.pl-16 {
    padding-left: 4rem;
}
.pl-2 {
    padding-left: 0.5rem;
}
.pl-3 {
    padding-left: 0.75rem;
}
.pl-5 {
    padding-left: 1.25rem;
}
.pl-9 {
    padding-left: 2.25rem;
}
.pr-1 {
    padding-right: 0.25rem;
}
.pr-10 {
    padding-right: 2.5rem;
}
.pr-16 {
    padding-right: 4rem;
}
.pr-19 {
    padding-right: 4.75rem;
}
.pr-3 {
    padding-right: 0.75rem;
}
.pr-32 {
    padding-right: 8rem;
}
.pr-4 {
    padding-right: 1rem;
}
.pr-5 {
    padding-right: 1.25rem;
}
.pr-6 {
    padding-right: 1.5rem;
}
.ps-3 {
    padding-inline-start: 0.75rem;
}
.pt-0 {
    padding-top: 0px;
}
.pt-0\.5 {
    padding-top: 0.125rem;
}
.pt-1 {
    padding-top: 0.25rem;
}
.pt-10 {
    padding-top: 2.5rem;
}
.pt-14 {
    padding-top: 3.5rem;
}
.pt-2 {
    padding-top: 0.5rem;
}
.pt-20 {
    padding-top: 5rem;
}
.pt-3 {
    padding-top: 0.75rem;
}
.pt-4 {
    padding-top: 1rem;
}
.pt-6 {
    padding-top: 1.5rem;
}
.pt-8 {
    padding-top: 2rem;
}
.text-left {
    text-align: left;
}
.\!text-center {
    text-align: center !important;
}
.text-center {
    text-align: center;
}
.text-right {
    text-align: right;
}
.text-start {
    text-align: start;
}
.text-end {
    text-align: end;
}
.align-middle {
    vertical-align: middle;
}
.font-mono {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
        "Liberation Mono", "Courier New", monospace;
}
.font-sans {
    font-family: Poppins, ui-sans-serif, system-ui, sans-serif,
        "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
        "Noto Color Emoji";
}
.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
}
.text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
}
.text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
}
.text-\[11px\] {
    font-size: 11px;
}
.text-base {
    font-size: 1rem;
    line-height: 1.5rem;
}
.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}
.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}
.text-title-md {
    font-size: 24px;
    line-height: 30px;
}
.text-title-md2 {
    font-size: 26px;
    line-height: 30px;
}
.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}
.text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
}
.font-bold {
    font-weight: 700;
}
.font-extrabold {
    font-weight: 800;
}
.font-medium {
    font-weight: 500;
}
.font-normal {
    font-weight: 400;
}
.font-semibold {
    font-weight: 600;
}
.uppercase {
    text-transform: uppercase;
}
.lowercase {
    text-transform: lowercase;
}
.capitalize {
    text-transform: capitalize;
}
.italic {
    font-style: italic;
}
.leading-4 {
    line-height: 1rem;
}
.leading-5 {
    line-height: 1.25rem;
}
.leading-6 {
    line-height: 1.5rem;
}
.leading-7 {
    line-height: 1.75rem;
}
.leading-8 {
    line-height: 2rem;
}
.leading-none {
    line-height: 1;
}
.leading-normal {
    line-height: 1.5;
}
.leading-tight {
    line-height: 1.25;
}
.tracking-tight {
    letter-spacing: -0.025em;
}
.tracking-wider {
    letter-spacing: 0.05em;
}
.tracking-widest {
    letter-spacing: 0.1em;
}
.text-cfp-300 {
    --tw-text-opacity: 1;
    color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.text-cfp-500 {
    --tw-text-opacity: 1;
    color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-cfp-500 {
    --tw-text-opacity: 1;
    color: rgb(51 126 129 / var(--tw-text-opacity, 1));
}
.text-cfp-800 {
    --tw-text-opacity: 1;
    color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-body {
    --tw-text-opacity: 1;
    color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}
.text-danger {
    --tw-text-opacity: 1;
    color: rgb(211 64 83 / var(--tw-text-opacity, 1));
}
.text-gray-200 {
    --tw-text-opacity: 1;
    color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.text-gray-300 {
    --tw-text-opacity: 1;
    color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-50 {
    --tw-text-opacity: 1;
    color: rgb(249 250 251 / var(--tw-text-opacity, 1));
}
.text-gray-500 {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600 {
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700 {
    --tw-text-opacity: 1;
    color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800 {
    --tw-text-opacity: 1;
    color: rgb(36 48 63 / var(--tw-text-opacity, 1));
}
.text-gray-900 {
    --tw-text-opacity: 1;
    color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green-400 {
    --tw-text-opacity: 1;
    color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.text-green-500 {
    --tw-text-opacity: 1;
    color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.text-green-600 {
    --tw-text-opacity: 1;
    color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-green-700 {
    --tw-text-opacity: 1;
    color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}
.text-green-800 {
    --tw-text-opacity: 1;
    color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-cfp-500 {
    --tw-text-opacity: 1;
    color: rgb(51 126 129 / var(--tw-text-opacity, 1));
}
.text-cfp-500 {
    --tw-text-opacity: 1;
    color: rgb(67 56 202 / var(--tw-text-opacity, 1));
}
.text-meta-1 {
    --tw-text-opacity: 1;
    color: rgb(220 53 69 / var(--tw-text-opacity, 1));
}
.text-meta-3 {
    --tw-text-opacity: 1;
    color: rgb(16 185 129 / var(--tw-text-opacity, 1));
}
.text-meta-5 {
    --tw-text-opacity: 1;
    color: rgb(37 154 230 / var(--tw-text-opacity, 1));
}
.text-red-500 {
    --tw-text-opacity: 1;
    color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600 {
    --tw-text-opacity: 1;
    color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-secondary-dark {
    --tw-text-opacity: 1;
    color: rgb(16 45 68 / var(--tw-text-opacity, 1));
}
.text-slate-500 {
    --tw-text-opacity: 1;
    color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}
.text-slate-700 {
    --tw-text-opacity: 1;
    color: rgb(51 65 85 / var(--tw-text-opacity, 1));
}
.text-slate-800 {
    --tw-text-opacity: 1;
    color: rgb(30 41 59 / var(--tw-text-opacity, 1));
}
.text-slate-900 {
    --tw-text-opacity: 1;
    color: rgb(15 23 42 / var(--tw-text-opacity, 1));
}
.text-success {
    --tw-text-opacity: 1;
    color: rgb(33 150 83 / var(--tw-text-opacity, 1));
}
.text-ternary-dark {
    --tw-text-opacity: 1;
    color: rgb(30 56 81 / var(--tw-text-opacity, 1));
}
.text-warning {
    --tw-text-opacity: 1;
    color: rgb(255 167 11 / var(--tw-text-opacity, 1));
}
.text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-yellow-600 {
    --tw-text-opacity: 1;
    color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}
.text-yellow-800 {
    --tw-text-opacity: 1;
    color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.underline {
    text-decoration-line: underline;
}
.antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.placeholder-body::-moz-placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(100 116 139 / var(--tw-placeholder-opacity, 1));
}
.placeholder-body::placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(100 116 139 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-600::-moz-placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(75 85 99 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-600::placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(75 85 99 / var(--tw-placeholder-opacity, 1));
}
.\!opacity-100 {
    opacity: 1 !important;
}
.opacity-0 {
    opacity: 0;
}
.opacity-100 {
    opacity: 1;
}
.opacity-25 {
    opacity: 0.25;
}
.opacity-30 {
    opacity: 0.3;
}
.opacity-50 {
    opacity: 0.5;
}
.opacity-75 {
    opacity: 0.75;
}
.shadow {
    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color),
        0 1px 2px -1px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-1 {
    --tw-shadow: 0px 1px 3px rgba(0, 0, 0, 0.08);
    --tw-shadow-colored: 0px 1px 3px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-default {
    --tw-shadow: 0px 8px 13px -3px rgba(0, 0, 0, 0.07);
    --tw-shadow-colored: 0px 8px 13px -3px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-inner {
    --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
    --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),
        0 4px 6px -4px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color),
        0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
    --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1),
        0 2px 4px -2px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color),
        0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
    --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-switch-1 {
    --tw-shadow: 0px 0px 5px rgba(0, 0, 0, 0.15);
    --tw-shadow-colored: 0px 0px 5px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
    --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1),
        0 8px 10px -6px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color),
        0 8px 10px -6px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none {
    outline: 2px solid transparent;
    outline-offset: 2px;
}
.outline {
    outline-style: solid;
}
.ring-1 {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow, 0 0 #0000);
}
.ring-black {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1));
}
.ring-gray-300 {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity, 1));
}
.ring-gray-900\/10 {
    --tw-ring-color: rgb(17 24 39 / 0.1);
}
.ring-gray-900\/5 {
    --tw-ring-color: rgb(17 24 39 / 0.05);
}
.ring-opacity-5 {
    --tw-ring-opacity: 0.05;
}
.blur-3xl {
    --tw-blur: blur(64px);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast)
        var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert)
        var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.grayscale {
    --tw-grayscale: grayscale(100%);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast)
        var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert)
        var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast)
        var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert)
        var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.transition {
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke, opacity, box-shadow, transform,
        filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke, opacity, box-shadow, transform,
        filter, backdrop-filter;
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke, opacity, box-shadow, transform,
        filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}
.transition-transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}
.duration-150 {
    transition-duration: 150ms;
}
.duration-200 {
    transition-duration: 200ms;
}
.duration-300 {
    transition-duration: 300ms;
}
.duration-500 {
    transition-duration: 500ms;
}
.duration-75 {
    transition-duration: 75ms;
}
.ease-in {
    transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out {
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.scrollbar::-webkit-scrollbar-track {
    background-color: var(--scrollbar-track);
    border-radius: var(--scrollbar-track-radius);
}
.scrollbar::-webkit-scrollbar-track:hover {
    background-color: var(--scrollbar-track-hover, var(--scrollbar-track));
}
.scrollbar::-webkit-scrollbar-track:active {
    background-color: var(
        --scrollbar-track-active,
        var(--scrollbar-track-hover, var(--scrollbar-track))
    );
}
.scrollbar::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb);
    border-radius: var(--scrollbar-thumb-radius);
}
.scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: var(--scrollbar-thumb-hover, var(--scrollbar-thumb));
}
.scrollbar::-webkit-scrollbar-thumb:active {
    background-color: var(
        --scrollbar-thumb-active,
        var(--scrollbar-thumb-hover, var(--scrollbar-thumb))
    );
}
.scrollbar::-webkit-scrollbar-corner {
    background-color: var(--scrollbar-corner);
    border-radius: var(--scrollbar-corner-radius);
}
.scrollbar::-webkit-scrollbar-corner:hover {
    background-color: var(--scrollbar-corner-hover, var(--scrollbar-corner));
}
.scrollbar::-webkit-scrollbar-corner:active {
    background-color: var(
        --scrollbar-corner-active,
        var(--scrollbar-corner-hover, var(--scrollbar-corner))
    );
}
.scrollbar {
    scrollbar-width: auto;
    scrollbar-color: var(--scrollbar-thumb, initial)
        var(--scrollbar-track, initial);
}
.scrollbar::-webkit-scrollbar {
    display: block;
    width: var(--scrollbar-width, 16px);
    height: var(--scrollbar-height, 16px);
}
.scrollbar-thin::-webkit-scrollbar-track {
    background-color: var(--scrollbar-track);
    border-radius: var(--scrollbar-track-radius);
}
.scrollbar-thin::-webkit-scrollbar-track:hover {
    background-color: var(--scrollbar-track-hover, var(--scrollbar-track));
}
.scrollbar-thin::-webkit-scrollbar-track:active {
    background-color: var(
        --scrollbar-track-active,
        var(--scrollbar-track-hover, var(--scrollbar-track))
    );
}
.scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb);
    border-radius: var(--scrollbar-thumb-radius);
}
.scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: var(--scrollbar-thumb-hover, var(--scrollbar-thumb));
}
.scrollbar-thin::-webkit-scrollbar-thumb:active {
    background-color: var(
        --scrollbar-thumb-active,
        var(--scrollbar-thumb-hover, var(--scrollbar-thumb))
    );
}
.scrollbar-thin::-webkit-scrollbar-corner {
    background-color: var(--scrollbar-corner);
    border-radius: var(--scrollbar-corner-radius);
}
.scrollbar-thin::-webkit-scrollbar-corner:hover {
    background-color: var(--scrollbar-corner-hover, var(--scrollbar-corner));
}
.scrollbar-thin::-webkit-scrollbar-corner:active {
    background-color: var(
        --scrollbar-corner-active,
        var(--scrollbar-corner-hover, var(--scrollbar-corner))
    );
}
.scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb, initial)
        var(--scrollbar-track, initial);
}
.scrollbar-thin::-webkit-scrollbar {
    display: block;
    width: 8px;
    height: 8px;
}
.scrollbar-track-gray-100 {
    --scrollbar-track: #f3f4f6 !important;
}
.scrollbar-thumb-gray-700\/20 {
    --scrollbar-thumb: rgb(55 65 81 / 0.2) !important;
}
.scrollbar-thumb-rounded {
    --scrollbar-thumb-radius: 0.25rem;
}
.scrollbar-w-2 {
    --scrollbar-width: 0.5rem;
}
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}
::-webkit-scrollbar-thumb {
    background-color: rgba(128, 128, 128, 0.5) !important;
    --scrollbar-track: #eef2ff !important;
    /* border-radius: 10px; */
}
::-webkit-scrollbar-thumb:hover {
    background-color: rgba(128, 128, 128, 0.5) !important;
}
/* Chrome, Safari and Opera */

.scrollbar-thin::-webkit-scrollbar {
    width: 4px;
    height: 4px;
}

#nprogress .bar {
    background: var(--primary-color, #337e81) !important;
    height: 5px !important;
}

#nprogress .spinner-icon {
    border-top-color: var(--primary-color, #337e81) !important;
    border-left-color: var(--primary-color, #337e81) !important;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 10px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: #555;
}
button.swal2-confirm.swal2-styled {
    background-color: var(--primary-color, #337e81) !important;
    align-items: center !important;
    border-radius: 0.375rem !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
    font-size: 0.75rem !important;
    line-height: 1rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.1em !important;
}

/* third-party libraries CSS */

.tableCheckbox:checked ~ div span {
    opacity: 1;
}

.tableCheckbox:checked ~ div {
    --tw-border-opacity: 1;
    border-color: rgb(51 126 129 / var(--tw-border-opacity, 1));
    --tw-bg-opacity: 1;
    background-color: rgb(51 126 129 / var(--tw-bg-opacity, 1));
}

.apexcharts-legend-text {
    --tw-text-opacity: 1 !important;
    color: rgb(100 116 139 / var(--tw-text-opacity, 1)) !important;
}

.apexcharts-text {
    fill: #64748b !important;
}

.apexcharts-xcrosshairs {
    fill: #e2e8f0 !important;
}

.apexcharts-gridline {
    stroke: #e2e8f0 !important;
}

.apexcharts-legend-series {
    display: inline-flex !important;
    gap: 0.375rem;
}

.apexcharts-tooltip-series-group {
    padding-left: 0.375rem !important;
}

.flatpickr-wrapper {
    width: 100%;
}

.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg {
    fill: #337e81 !important;
}

.flatpickr-calendar {
    padding: 1.5rem !important;
}

@media (min-width: 375px) {
    .flatpickr-calendar {
        width: auto !important;
    }
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
    top: 1.75rem !important;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month.flatpickr-prev-month {
    left: 1.75rem !important;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,
.flatpickr-months .flatpickr-next-month.flatpickr-next-month {
    right: 1.75rem !important;
}

.flatpickr-day.inRange {
    box-shadow: -5px 0 0 #f3f4f6, 5px 0 0 #f3f4f6 !important;
}

.flatpickr-day.inRange,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.today.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day:hover,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day:focus,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.nextMonthDay:focus {
    --tw-border-opacity: 1 !important;
    border-color: rgb(243 244 246 / var(--tw-border-opacity, 1)) !important;
    --tw-bg-opacity: 1 !important;
    background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1)) !important;
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
    background: var(--primary-color, #337e81);
    --tw-border-opacity: 1 !important;
    border-color: rgb(51 126 129 / var(--tw-border-opacity, 1)) !important;
    --tw-bg-opacity: 1 !important;
    background-color: rgb(51 126 129 / var(--tw-bg-opacity, 1)) !important;
}

.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.inRange:hover,
.flatpickr-day.startRange.inRange:hover,
.flatpickr-day.endRange.inRange:hover,
.flatpickr-day.selected:focus:hover,
.flatpickr-day.startRange:focus:hover,
.flatpickr-day.endRange:focus:hover,
.flatpickr-day.selected:hover:hover,
.flatpickr-day.startRange:hover:hover,
.flatpickr-day.endRange:hover:hover,
.flatpickr-day.selected.prevMonthDay:hover,
.flatpickr-day.startRange.prevMonthDay:hover,
.flatpickr-day.endRange.prevMonthDay:hover,
.flatpickr-day.selected.nextMonthDay:hover,
.flatpickr-day.startRange.nextMonthDay:hover,
.flatpickr-day.endRange.nextMonthDay:hover {
    --tw-border-opacity: 1 !important;
    border-color: rgb(51 126 129 / var(--tw-border-opacity, 1)) !important;
    --tw-bg-opacity: 1 !important;
    background-color: rgb(51 126 129 / var(--tw-bg-opacity, 1)) !important;
}

.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n + 1)) {
    box-shadow: -10px 0 0 var(--primary-color, #337e81);
}

.map-btn .jvm-zoom-btn {
    display: flex;
    height: 1.875rem;
    width: 1.875rem;
    align-items: center;
    justify-content: center;
    border-radius: 0.25rem;
    border-width: 1px;
    --tw-border-opacity: 1;
    border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
    padding-left: 0px;
    padding-right: 0px;
    padding-top: 0px;
    padding-bottom: 0.125rem;
    font-size: 1.5rem;
    line-height: 2rem;
    line-height: 1;
    --tw-text-opacity: 1;
    color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}

.map-btn .jvm-zoom-btn:hover {
    --tw-border-opacity: 1;
    border-color: rgb(51 126 129 / var(--tw-border-opacity, 1));
    background-color: rgb(51 126 129 / 0.85);
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.mapOne .jvm-zoom-btn {
    top: auto !important;
    bottom: 0px;
    left: auto;
}

.mapOne .jvm-zoom-btn.jvm-zoomin {
    right: 2.5rem;
}

.mapOne .jvm-zoom-btn.jvm-zoomout {
    right: 0px;
}

.taskCheckbox:checked ~ .box span {
    opacity: 1;
}

.taskCheckbox:checked ~ p {
    text-decoration-line: line-through;
}

.taskCheckbox:checked ~ .box {
    --tw-border-opacity: 1;
    border-color: rgb(51 126 129 / var(--tw-border-opacity, 1));
    --tw-bg-opacity: 1;
    background-color: rgb(51 126 129 / var(--tw-bg-opacity, 1));
}

.custom-input-date::-webkit-calendar-picker-indicator {
    background: transparent;
}

input[type="search"]::-webkit-search-cancel-button {
    -webkit-appearance: none;
    appearance: none;
}

.custom-input-date::-webkit-calendar-picker-indicator {
    background-position: center;
    background-repeat: no-repeat;
    background-size: 20px;
}

.custom-input-date-1::-webkit-calendar-picker-indicator {
    background-image: url(../images/icon/icon-calendar.svg);
}

.custom-input-date-2::-webkit-calendar-picker-indicator {
    background-image: url(../images/icon/icon-arrow-down.svg);
}

[x-cloak] {
    display: none !important;
}

@media (min-width: 640px) {
    .sm\:container {
        width: 100%;
        padding-right: 1rem;
        padding-left: 1rem;
    }

    @media (min-width: 375px) {
        .sm\:container {
            max-width: 375px;
        }
    }

    @media (min-width: 425px) {
        .sm\:container {
            max-width: 425px;
        }
    }

    @media (min-width: 640px) {
        .sm\:container {
            max-width: 640px;
            padding-right: 2rem;
            padding-left: 2rem;
        }
    }

    @media (min-width: 768px) {
        .sm\:container {
            max-width: 768px;
        }
    }

    @media (min-width: 1024px) {
        .sm\:container {
            max-width: 1024px;
            padding-right: 5rem;
            padding-left: 5rem;
        }
    }

    @media (min-width: 1280px) {
        .sm\:container {
            max-width: 1280px;
            padding-right: 6rem;
            padding-left: 6rem;
        }
    }

    @media (min-width: 1536px) {
        .sm\:container {
            max-width: 1536px;
            padding-right: 8rem;
            padding-left: 8rem;
        }
    }

    @media (min-width: 2000px) {
        .sm\:container {
            max-width: 2000px;
        }
    }
}

.default\:col-span-full:default {
    grid-column: 1 / -1;
}

.default\:row-span-1:default {
    grid-row: span 1 / span 1;
}

.hover\:-translate-y-1:hover {
    --tw-translate-y: -0.25rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:cursor-pointer:hover {
    cursor: pointer;
}

.hover\:rounded-b-md:hover {
    border-bottom-right-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.hover\:rounded-t-md:hover {
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
}

.hover\:border-gray-300:hover {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.hover\:bg-cfp-600:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-cfp-500:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(51 126 129 / var(--tw-bg-opacity, 1));
}

.hover\:bg-cfp-500\/5:hover {
    background-color: rgb(51 126 129 / 0.05);
}

.hover\:bg-cfp-500\/85:hover {
    background-color: rgb(51 126 129 / 0.85);
}

.hover\:bg-gray-100:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100\/75:hover {
    background-color: rgb(243 244 246 / 0.75);
}

.hover\:bg-gray-200:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-300:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-500:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}

.hover\:bg-cfp-500:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(51 126 129 / var(--tw-bg-opacity, 1));
}

.hover\:bg-cfp-500\/80:hover {
    background-color: rgb(51 126 129 / 0.8);
}

.hover\:bg-red-600:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.hover\:bg-opacity-80:hover {
    --tw-bg-opacity: 0.8;
}

.hover\:bg-opacity-90:hover {
    --tw-bg-opacity: 0.9;
}

.hover\:text-danger:hover {
    --tw-text-opacity: 1;
    color: rgb(211 64 83 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-400:hover {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-50:hover {
    --tw-text-opacity: 1;
    color: rgb(249 250 251 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-500:hover {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-800:hover {
    --tw-text-opacity: 1;
    color: rgb(36 48 63 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-900:hover {
    --tw-text-opacity: 1;
    color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-500:hover {
    --tw-text-opacity: 1;
    color: rgb(99 102 241 / var(--tw-text-opacity, 1));
}

.hover\:text-cfp-500:hover {
    --tw-text-opacity: 1;
    color: rgb(51 126 129 / var(--tw-text-opacity, 1));
}

.hover\:text-red-800:hover {
    --tw-text-opacity: 1;
    color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-800:hover {
    --tw-text-opacity: 1;
    color: rgb(30 41 59 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
    text-decoration-line: underline;
}

.hover\:opacity-80:hover {
    opacity: 0.8;
}

.hover\:opacity-90:hover {
    opacity: 0.9;
}

.hover\:shadow-sm:hover {
    --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:ring-gray-900\/20:hover {
    --tw-ring-color: rgb(17 24 39 / 0.2);
}

.focus\:z-10:focus {
    z-index: 10;
}

.focus\:border-cfp-300:focus {
    --tw-border-opacity: 1;
    border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}

.focus\:border-gray-300:focus {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.focus\:border-indigo-500:focus {
    --tw-border-opacity: 1;
    border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
}

.focus\:border-cfp-500:focus {
    --tw-border-opacity: 1;
    border-color: rgb(51 126 129 / var(--tw-border-opacity, 1));
}

.focus\:border-cfp-500\/50:focus {
    border-color: rgb(51 126 129 / 0.5);
}

.focus\:bg-gray-100:focus {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.focus\:text-gray-500:focus {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.focus\:text-slate-800:focus {
    --tw-text-opacity: 1;
    color: rgb(30 41 59 / var(--tw-text-opacity, 1));
}

.focus\:placeholder-gray-400:focus::-moz-placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}

.focus\:placeholder-gray-400:focus::placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}

.focus\:outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

.focus\:ring:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-1:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-indigo-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));
}

.focus\:ring-cfp-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(51 126 129 / var(--tw-ring-opacity, 1));
}

.focus\:ring-cfp-500\/50:focus {
    --tw-ring-color: rgb(51 126 129 / 0.5);
}

.focus\:ring-cfp-900:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(49 46 129 / var(--tw-ring-opacity, 1));
}

.focus\:ring-red-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}

.focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
}

.focus-visible\:shadow-none:focus-visible {
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus-visible\:outline:focus-visible {
    outline-style: solid;
}

.focus-visible\:outline-2:focus-visible {
    outline-width: 2px;
}

.focus-visible\:outline-offset-2:focus-visible {
    outline-offset: 2px;
}

.focus-visible\:outline-cfp-500:focus-visible {
    outline-color: #337e81;
}

.active\:border-cfp-500:active {
    --tw-border-opacity: 1;
    border-color: rgb(51 126 129 / var(--tw-border-opacity, 1));
}

.active\:bg-gray-100:active {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.active\:text-gray-500:active {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.active\:text-gray-700:active {
    --tw-text-opacity: 1;
    color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.active\:text-slate-800:active {
    --tw-text-opacity: 1;
    color: rgb(30 41 59 / var(--tw-text-opacity, 1));
}

.disabled\:cursor-default:disabled {
    cursor: default;
}

.disabled\:bg-whiter:disabled {
    --tw-bg-opacity: 1;
    background-color: rgb(245 247 253 / var(--tw-bg-opacity, 1));
}

.disabled\:opacity-25:disabled {
    opacity: 0.25;
}

@media not all and (min-width: 768px) {
    .max-md\:relative {
        position: relative;
    }

    .max-md\:mb-4 {
        margin-bottom: 1rem;
    }

    .max-md\:mb-5 {
        margin-bottom: 1.25rem;
    }

    .max-md\:mt-0 {
        margin-top: 0px;
    }

    .max-md\:mt-5 {
        margin-top: 1.25rem;
    }

    .max-md\:block {
        display: block;
    }

    .max-md\:hidden {
        display: none;
    }

    .max-md\:w-35 {
        width: 8.75rem;
    }

    .max-md\:w-auto {
        width: auto;
    }

    .max-md\:justify-center {
        justify-content: center;
    }

    .max-md\:border-0 {
        border-width: 0px;
    }

    .max-md\:border-r-0 {
        border-right-width: 0px;
    }

    .max-md\:border-t-0 {
        border-top-width: 0px;
    }

    .max-md\:p-4 {
        padding: 1rem;
    }

    .max-md\:px-0 {
        padding-left: 0px;
        padding-right: 0px;
    }

    .max-md\:px-2 {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    .max-md\:px-4 {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .max-md\:py-1 {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem;
    }

    .max-md\:py-6 {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem;
    }

    .max-md\:pb-6 {
        padding-bottom: 1.5rem;
    }

    .max-md\:pt-6 {
        padding-top: 1.5rem;
    }

    .max-md\:text-base {
        font-size: 1rem;
        line-height: 1.5rem;
    }

    .max-md\:text-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }
}

@media (min-width: 425px) {
    .xsm\:px-4 {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .xsm\:text-base {
        font-size: 1rem;
        line-height: 1.5rem;
    }
}

@media (min-width: 640px) {
    .sm\:-top-80 {
        top: -20rem;
    }

    .sm\:left-\[calc\(50\%\+36rem\)\] {
        left: calc(50% + 36rem);
    }

    .sm\:left-\[calc\(50\%-30rem\)\] {
        left: calc(50% - 30rem);
    }

    .sm\:top-\[calc\(100\%-30rem\)\] {
        top: calc(100% - 30rem);
    }

    .sm\:col-span-1 {
        grid-column: span 1 / span 1;
    }

    .sm\:col-span-2 {
        grid-column: span 2 / span 2;
    }

    .sm\:col-span-8 {
        grid-column: span 8 / span 8;
    }

    .sm\:mx-0 {
        margin-left: 0px;
        margin-right: 0px;
    }

    .sm\:mx-4 {
        margin-left: 1rem;
        margin-right: 1rem;
    }

    .sm\:mx-auto {
        margin-left: auto;
        margin-right: auto;
    }

    .sm\:mb-0 {
        margin-bottom: 0px;
    }

    .sm\:mb-20 {
        margin-bottom: 5rem;
    }

    .sm\:mb-8 {
        margin-bottom: 2rem;
    }

    .sm\:ml-4 {
        margin-left: 1rem;
    }

    .sm\:mt-0 {
        margin-top: 0px;
    }

    .sm\:mt-10 {
        margin-top: 2.5rem;
    }

    .sm\:mt-2 {
        margin-top: 0.5rem;
    }

    .sm\:mt-20 {
        margin-top: 5rem;
    }

    .sm\:block {
        display: block;
    }

    .sm\:flex {
        display: flex;
    }

    .sm\:grid {
        display: grid;
    }

    .sm\:hidden {
        display: none;
    }

    .sm\:h-44 {
        height: 11rem;
    }

    .sm\:h-46 {
        height: 11.5rem;
    }

    .sm\:h-52 {
        height: 13rem;
    }

    .sm\:w-1\/3 {
        width: 33.333333%;
    }

    .sm\:w-1\/6 {
        width: 16.666667%;
    }

    .sm\:w-4\/12 {
        width: 33.333333%;
    }

    .sm\:w-\[72\.1875rem\] {
        width: 72.1875rem;
    }

    .sm\:w-full {
        width: 100%;
    }

    .sm\:max-w-2xl {
        max-width: 42rem;
    }

    .sm\:max-w-lg {
        max-width: 32rem;
    }

    .sm\:max-w-md {
        max-width: 28rem;
    }

    .sm\:max-w-screen-lg {
        max-width: 1024px;
    }

    .sm\:max-w-sm {
        max-width: 24rem;
    }

    .sm\:max-w-xl {
        max-width: 36rem;
    }

    .sm\:flex-1 {
        flex: 1 1 0%;
    }

    .sm\:translate-y-0 {
        --tw-translate-y: 0px;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y))
            rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
            skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
            scaleY(var(--tw-scale-y));
    }

    .sm\:scale-100 {
        --tw-scale-x: 1;
        --tw-scale-y: 1;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y))
            rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
            skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
            scaleY(var(--tw-scale-y));
    }

    .sm\:scale-95 {
        --tw-scale-x: 0.95;
        --tw-scale-y: 0.95;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y))
            rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
            skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
            scaleY(var(--tw-scale-y));
    }

    .sm\:grid-cols-1 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }

    .sm\:grid-cols-12 {
        grid-template-columns: repeat(12, minmax(0, 1fr));
    }

    .sm\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .sm\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .sm\:grid-cols-5 {
        grid-template-columns: repeat(5, minmax(0, 1fr));
    }

    .sm\:grid-cols-8 {
        grid-template-columns: repeat(8, minmax(0, 1fr));
    }

    .sm\:flex-row {
        flex-direction: row;
    }

    .sm\:items-center {
        align-items: center;
    }

    .sm\:justify-start {
        justify-content: flex-start;
    }

    .sm\:justify-center {
        justify-content: center;
    }

    .sm\:justify-between {
        justify-content: space-between;
    }

    .sm\:gap-4 {
        gap: 1rem;
    }

    .sm\:gap-6 {
        gap: 1.5rem;
    }

    .sm\:gap-8 {
        gap: 2rem;
    }

    .sm\:rounded-md {
        border-radius: 0.375rem;
    }

    .sm\:border-t-0 {
        border-top-width: 0px;
    }

    .sm\:p-0 {
        padding: 0px;
    }

    .sm\:p-12 {
        padding: 3rem;
    }

    .sm\:p-6 {
        padding: 1.5rem;
    }

    .sm\:px-0 {
        padding-left: 0px;
        padding-right: 0px;
    }

    .sm\:px-6 {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }

    .sm\:px-7\.5 {
        padding-left: 1.875rem;
        padding-right: 1.875rem;
    }

    .sm\:py-16 {
        padding-top: 4rem;
        padding-bottom: 4rem;
    }

    .sm\:py-2 {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }

    .sm\:py-32 {
        padding-top: 8rem;
        padding-bottom: 8rem;
    }

    .sm\:py-5 {
        padding-top: 1.25rem;
        padding-bottom: 1.25rem;
    }

    .sm\:pl-6 {
        padding-left: 1.5rem;
    }

    .sm\:pr-8 {
        padding-right: 2rem;
    }

    .sm\:pt-0 {
        padding-top: 0px;
    }

    .sm\:pt-2 {
        padding-top: 0.5rem;
    }

    .sm\:pt-20 {
        padding-top: 5rem;
    }

    .sm\:text-3xl {
        font-size: 1.875rem;
        line-height: 2.25rem;
    }

    .sm\:text-4xl {
        font-size: 2.25rem;
        line-height: 2.5rem;
    }

    .sm\:text-6xl {
        font-size: 3.75rem;
        line-height: 1;
    }

    .sm\:shadow-none {
        --tw-shadow: 0 0 #0000;
        --tw-shadow-colored: 0 0 #0000;
        box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
            var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
    }
}

@media (min-width: 768px) {
    .md\:sticky {
        position: sticky;
    }

    .md\:mt-10 {
        margin-top: 2.5rem;
    }

    .md\:mt-6 {
        margin-top: 1.5rem;
    }

    .md\:block {
        display: block;
    }

    .md\:inline {
        display: inline;
    }

    .md\:flex {
        display: flex;
    }

    .md\:hidden {
        display: none;
    }

    .md\:w-1\/2 {
        width: 50%;
    }

    .md\:w-1\/4 {
        width: 25%;
    }

    .md\:min-w-64 {
        min-width: 16rem;
    }

    .md\:max-w-80 {
        max-width: 20rem;
    }

    .md\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .md\:flex-row {
        flex-direction: row;
    }

    .md\:items-center {
        align-items: center;
    }

    .md\:justify-between {
        justify-content: space-between;
    }

    .md\:gap-2 {
        gap: 0.5rem;
    }

    .md\:gap-6 {
        gap: 1.5rem;
    }

    .md\:px-6 {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }

    .md\:py-10 {
        padding-top: 2.5rem;
        padding-bottom: 2.5rem;
    }

    .md\:pl-6 {
        padding-left: 1.5rem;
    }

    .md\:pr-0 {
        padding-right: 0px;
    }
}

@media (min-width: 1024px) {
    .lg\:mt-24 {
        margin-top: 6rem;
    }

    .lg\:block {
        display: block;
    }

    .lg\:inline-block {
        display: inline-block;
    }

    .lg\:hidden {
        display: none;
    }

    .lg\:w-\[12rem\] {
        width: 12rem;
    }

    .lg\:max-w-4xl {
        max-width: 56rem;
    }

    .lg\:max-w-none {
        max-width: none;
    }

    .lg\:grid-cols-1 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }

    .lg\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .lg\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .lg\:gap-8 {
        gap: 2rem;
    }

    .lg\:gap-y-16 {
        row-gap: 4rem;
    }

    .lg\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem;
    }

    .lg\:py-16 {
        padding-top: 4rem;
        padding-bottom: 4rem;
    }

    .lg\:text-center {
        text-align: center;
    }

    .lg\:text-2xl {
        font-size: 1.5rem;
        line-height: 2rem;
    }

    .lg\:text-base {
        font-size: 1rem;
        line-height: 1.5rem;
    }

    .lg\:text-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }

    .default\:lg\:col-span-6:default {
        grid-column: span 6 / span 6;
    }
}

@media (min-width: 1280px) {
    .xl\:col-span-12 {
        grid-column: span 12 / span 12;
    }

    .xl\:col-span-4 {
        grid-column: span 4 / span 4;
    }

    .xl\:col-span-5 {
        grid-column: span 5 / span 5;
    }

    .xl\:col-span-7 {
        grid-column: span 7 / span 7;
    }

    .xl\:col-span-8 {
        grid-column: span 8 / span 8;
    }

    .xl\:mx-auto {
        margin-left: auto;
        margin-right: auto;
    }

    .xl\:max-w-screen-xl {
        max-width: 1280px;
    }

    .xl\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }

    .xl\:p-5 {
        padding: 1.25rem;
    }

    .xl\:px-7\.5 {
        padding-left: 1.875rem;
        padding-right: 1.875rem;
    }

    .xl\:pb-1 {
        padding-bottom: 0.25rem;
    }

    .xl\:pl-11 {
        padding-left: 2.75rem;
    }
}

@media (min-width: 1536px) {
    .\32xl\:mt-7\.5 {
        margin-top: 1.875rem;
    }

    .\32xl\:gap-6 {
        gap: 1.5rem;
    }

    .\32xl\:gap-7\.5 {
        gap: 1.875rem;
    }

    .\32xl\:px-7\.5 {
        padding-left: 1.875rem;
        padding-right: 1.875rem;
    }
}

.ltr\:origin-top-left:where([dir="ltr"], [dir="ltr"] *) {
    transform-origin: top left;
}

.ltr\:origin-top-right:where([dir="ltr"], [dir="ltr"] *) {
    transform-origin: top right;
}

.rtl\:origin-top-left:where([dir="rtl"], [dir="rtl"] *) {
    transform-origin: top left;
}

.rtl\:origin-top-right:where([dir="rtl"], [dir="rtl"] *) {
    transform-origin: top right;
}

.rtl\:flex-row-reverse:where([dir="rtl"], [dir="rtl"] *) {
    flex-direction: row-reverse;
}

@media (prefers-color-scheme: dark) {
    .dark\:block {
        display: block;
    }

    .dark\:hidden {
        display: none;
    }

    .dark\:border {
        border-width: 1px;
    }

    .dark\:border-gray-600 {
        --tw-border-opacity: 1;
        border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
    }

    .dark\:border-gray-700 {
        --tw-border-opacity: 1;
        border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
    }

    .dark\:border-gray-800 {
        --tw-border-opacity: 1;
        border-color: rgb(36 48 63 / var(--tw-border-opacity, 1));
    }

    .dark\:border-gray-900 {
        --tw-border-opacity: 1;
        border-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
    }

    .dark\:border-strokedark {
        --tw-border-opacity: 1;
        border-color: rgb(46 58 71 / var(--tw-border-opacity, 1));
    }

    .dark\:border-l-red-500 {
        --tw-border-opacity: 1;
        border-left-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
    }

    .dark\:bg-boxdark {
        --tw-bg-opacity: 1;
        background-color: rgb(36 48 63 / var(--tw-bg-opacity, 1));
    }

    .dark\:bg-gray-800 {
        --tw-bg-opacity: 1;
        background-color: rgb(36 48 63 / var(--tw-bg-opacity, 1));
    }

    .dark\:bg-gray-900 {
        --tw-bg-opacity: 1;
        background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
    }

    .dark\:bg-gray-900\/80 {
        background-color: rgb(17 24 39 / 0.8);
    }

    .dark\:bg-gray-950\/95 {
        background-color: rgb(3 7 18 / 0.95);
    }

    .dark\:bg-red-500\/20 {
        background-color: rgb(239 68 68 / 0.2);
    }

    .dark\:text-gray-100 {
        --tw-text-opacity: 1;
        color: rgb(243 244 246 / var(--tw-text-opacity, 1));
    }

    .dark\:text-gray-300 {
        --tw-text-opacity: 1;
        color: rgb(209 213 219 / var(--tw-text-opacity, 1));
    }

    .dark\:text-gray-400 {
        --tw-text-opacity: 1;
        color: rgb(156 163 175 / var(--tw-text-opacity, 1));
    }

    .dark\:text-gray-600 {
        --tw-text-opacity: 1;
        color: rgb(75 85 99 / var(--tw-text-opacity, 1));
    }

    .dark\:text-gray-950 {
        --tw-text-opacity: 1;
        color: rgb(3 7 18 / var(--tw-text-opacity, 1));
    }

    .dark\:text-white {
        --tw-text-opacity: 1;
        color: rgb(255 255 255 / var(--tw-text-opacity, 1));
    }

    .dark\:ring-1 {
        --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
            var(--tw-ring-offset-width) var(--tw-ring-offset-color);
        --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
            calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
        box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
            var(--tw-shadow, 0 0 #0000);
    }

    .dark\:ring-gray-800 {
        --tw-ring-opacity: 1;
        --tw-ring-color: rgb(36 48 63 / var(--tw-ring-opacity, 1));
    }

    .dark\:hover\:bg-gray-700:hover {
        --tw-bg-opacity: 1;
        background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
    }

    .dark\:hover\:bg-gray-800:hover {
        --tw-bg-opacity: 1;
        background-color: rgb(36 48 63 / var(--tw-bg-opacity, 1));
    }

    .dark\:hover\:bg-gray-800\/75:hover {
        background-color: rgb(36 48 63 / 0.75);
    }

    .dark\:hover\:text-gray-300:hover {
        --tw-text-opacity: 1;
        color: rgb(209 213 219 / var(--tw-text-opacity, 1));
    }

    .dark\:hover\:text-gray-500:hover {
        --tw-text-opacity: 1;
        color: rgb(107 114 128 / var(--tw-text-opacity, 1));
    }

    .dark\:focus\:border-cfp-500:focus {
        --tw-border-opacity: 1;
        border-color: rgb(51 126 129 / var(--tw-border-opacity, 1));
    }

    .dark\:focus\:border-cfp-800:focus {
        --tw-border-opacity: 1;
        border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));
    }

    .dark\:focus\:text-gray-500:focus {
        --tw-text-opacity: 1;
        color: rgb(107 114 128 / var(--tw-text-opacity, 1));
    }

    .dark\:active\:bg-gray-700:active {
        --tw-bg-opacity: 1;
        background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
    }

    .dark\:active\:text-gray-300:active {
        --tw-text-opacity: 1;
        color: rgb(209 213 219 / var(--tw-text-opacity, 1));
    }
}
