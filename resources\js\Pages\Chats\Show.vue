<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { ref } from 'vue';
import { useForm, usePage, Head} from '@inertiajs/vue3';
import PageHeading from '@/Components/Global/PageHeading.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';


const { title, intent } = usePage().props;
const pageTitle = ref(title);

const trainingPhrases = ref(intent.training_phrases ? JSON.parse(intent.training_phrases) : []);
const responses = ref(intent.responses ? JSON.parse(intent.responses) : []);

const trainingForm = useForm({
    phrase: ''
});

const responseForm = useForm({
    response: ''
});

const addTrainingPhrase = () => {
    if (!trainingForm.phrase) return;

    // Check if phrase already exists
    const existingIndex = trainingPhrases.value.indexOf(trainingForm.phrase.toLowerCase());

    if (existingIndex !== -1) {
        // If exists, remove it from current position
        trainingPhrases.value.splice(existingIndex, 1);
        // Add it to the beginning
        trainingPhrases.value.unshift(trainingForm.phrase);
    } else {
        // If new, add new phrase at beginning
        trainingPhrases.value.unshift(trainingForm.phrase);
    }

    updateIntent('training_phrases', trainingPhrases.value);
    trainingForm.phrase = '';
};

const addResponseText = () => {
    if (!responseForm.response) return;

    // Check if response already exists
    const existingIndex = responses.value.indexOf(responseForm.response.toLowerCase());

    if (existingIndex !== -1) {
        // If exists, remove it from current position
        responses.value.splice(existingIndex, 1);
        // Add it to the beginning
        responses.value.unshift(responseForm.response);
    } else {
        // If new, add new response at beginning
        responses.value.unshift(responseForm.response);
    }

    updateIntent('responses', responses.value);
    responseForm.response = '';
};

const deleteTrainingPhrase = (phrase) => {
    const index = trainingPhrases.value.indexOf(phrase);
    if (index > -1) {
        trainingPhrases.value.splice(index, 1);
        updateIntent('training_phrases', trainingPhrases.value);
    }
};

const deleteResponseText = (response) => {
    const index = responses.value.indexOf(response);
    if (index > -1) {
        responses.value.splice(index, 1);
        updateIntent('responses', responses.value);
    }
};

const updateIntent = (field, data) => {
    axios.patch(route('phrases.update', intent.id), {
        [field]: JSON.stringify(data)
    })
    .catch(error => {
        console.error('Error updating intent:', error);
    });
};
</script>

<template>
    <Head :title="pageTitle" />

    <AuthenticatedLayout>
        <PageHeading :title="pageTitle">
            
        </PageHeading>
        <div class="flex justify-end">
            <ResponsiveNavLink :href="route('intents.index')" class="dk-cancle-btn">
                    Back
                </ResponsiveNavLink>
        </div>
        <div class="flex gap-6">
            
            <!-- Training Phrases Section -->
            <div class="flex-1">
                <div class="bg-white p-6 rounded-lg shadow">
                    <h2 class="text-xl font-semibold mb-4">Training Phrases</h2>
                    
                    <form @submit.prevent="addTrainingPhrase" class="mb-6">
                        <div class="mb-4">
                            <InputLabel for="phrase" value="Add User Expression" />
                            <TextInput
                                id="phrase"
                                type="text"
                                class="mt-1 block w-full"
                                v-model="trainingForm.phrase"
                                placeholder="Add user expression"
                            />
                        </div>
                        <PrimaryButton>Add Phrase</PrimaryButton>
                    </form>

                    <div class="space-y-2">
                        <div v-for="(phrase, index) in trainingPhrases" :key="index" 
                            class="flex justify-between items-center p-3 bg-gray-50 rounded">
                            <span>{{ phrase }}</span>
                            <button @click="deleteTrainingPhrase(phrase)" 
                                class="text-red-600 hover:text-red-800">
                                Delete
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Response Texts Section -->
            <div class="flex-1">
                <div class="bg-white p-6 rounded-lg shadow">
                    <h2 class="text-xl font-semibold mb-4">Response Phrases</h2>
                    
                    <form @submit.prevent="addResponseText" class="mb-6">
                        <div class="mb-4">
                            <InputLabel for="response" value="Add New Response" />
                            <TextInput
                                id="response"
                                type="text"
                                class="mt-1 block w-full"
                                v-model="responseForm.response"
                                placeholder="Add response text"
                            />
                        </div>
                        <PrimaryButton>Add Response</PrimaryButton>
                    </form>

                    <div class="space-y-2">
                        <div v-for="(response, index) in responses" :key="index" 
                            class="flex justify-between items-center p-3 bg-gray-50 rounded">
                            <span>{{ response }}</span>
                            <button @click="deleteResponseText(response)" 
                                class="text-red-600 hover:text-red-800">
                                Delete
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
