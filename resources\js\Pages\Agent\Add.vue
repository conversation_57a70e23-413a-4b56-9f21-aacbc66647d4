<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, usePage, useForm } from '@inertiajs/vue3';
import { ref, watch } from 'vue';
import InputError from '@/Components/InputError.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import BreadcrumbDefault from '@/Components/Breadcrumbs/BreadcrumbDefault.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import PageHeading from '@/Components/Global/PageHeading.vue';

const pageTitle = ref('Add Agent Detail')


const form = useForm({
    name: '',
    phone: '',
    email: '',
});

watch(() => form.recentlySuccessful, () => {
    form.name = '';
    form.phone = '';
    form.email = '';
});

function handleSubmit() {
    form.post(route('manage-agent.store'), {
        onSuccess: () => {
            // Reset the form after successful submission
            form.reset();
        }
    });
}


</script>

<template>

    <Head :title="pageTitle" />



    <AuthenticatedLayout>

        <!-- <PageHeading :title="pageTitle"></PageHeading> -->

        <div class="flex">


            <div class="flex-1">

                <div class="form-main-body">
                    <div class="border-stroke p-6 border rounded-md">
                        <div class="text-gray-900">


                            <form @submit.prevent="handleSubmit" class="">

                                <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
                                    leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
                                    <p v-if="form.recentlySuccessful" enter-active-class="transition ease-in-out"
                                        enter-from-class="opacity-0" leave-active-class="transition ease-in-out"
                                        leave-to-class="opacity-0" :class="$page.props.flash?.class">
                                        {{ $page.props.flash.message }}</p>
                                </Transition>


                                <div class="">
                                    <InputLabel for="name" value="Agent Name" />
                                    <TextInput id="name" name="name" type="text" class="mt-1" v-model="form.name" />
                                    <InputError :message="form.errors.name" class="mt-2" />
                                </div>


                                <div class="form-ttl-gap">
                                    <InputLabel for="phone" value="Phone" />
                                    <TextInput id="phone" name="phone" type="text" class="mt-1" v-model="form.phone" />
                                    <InputError :message="form.errors.phone" class="mt-2" />
                                </div>

                                <div class="form-ttl-gap">

                                    <InputLabel for="email" value="Email" />
                                    <TextInput id="email" name="email" type="text" class="mt-1" v-model="form.email" />
                                    <InputError :message="form.errors.email" class="mt-2" />
                                </div>



                                <div class="flex items-center gap-0 mt-4">
                                    <PrimaryButton :disabled="form.processing" class="">
                                        <template #default>
                                            <span v-if="form.processing">Loading...</span>
                                            <span v-else>Save</span>
                                        </template>
                                    </PrimaryButton>

                                    <div class="relative">
                                        <ResponsiveNavLink :href="route('manage-agent.list')" class="dk-cancle-btn">
                                            Cancel
                                        </ResponsiveNavLink>

                                    </div>

                                </div>
                            </form>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
