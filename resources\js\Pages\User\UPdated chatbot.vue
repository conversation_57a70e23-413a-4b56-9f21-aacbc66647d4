<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import ApplicationLogo from '@/Components/ApplicationLogo.vue';
import { Head } from '@inertiajs/vue3';
import axios from 'axios';
import { ref, watch, nextTick, onMounted } from 'vue'; // Import watch and nextTick
import { usePage } from '@inertiajs/vue3';
import BreadcrumbDefault from '@/Components/Breadcrumbs/BreadcrumbDefault.vue';
import { Inertia } from '@inertiajs/inertia';
import PageHeading from '@/Components/Global/PageHeading.vue';

const pageTitle = ref('Chat Support')

const { chatData, baseUrl, allAgents, agentType, authDetail, selectedAgent } = usePage().props;

const showInput = ref(true);
const selectedSlug = ref('');
const selectedSource = ref('Chatbot');
const selectedCompanyProfile = ref('');
const selectedCompanyName = ref('');
const inputText = ref(''); // Define a ref for input text
const messages = ref([]); // Define a ref for messages array
const status_fallback = ref(0);
const statusLoading = ref(false);

selectedSlug.value = agentType;

onMounted(() => {
    // Check if the URL contains ?source=
    if (!window.location.search.includes('?source=')) {
        // If not, hide the input element
        showInput.value = false;
    } else {
        showInput.value = true;
    }

    if (Object.keys(chatData).length !== 0) {
        Object.values(chatData).forEach(item => {
            if (item.message.trim() !== '' && item.id == authDetail.id) {
                messages.value.push({ text: item.message, timestamp: item.timestamp, sender: 'user' });
            }
            if (item.message.trim() !== '' && item.id != authDetail.id) {
                messages.value.push({ text: item.message, timestamp: item.timestamp, sender: 'server' });
            }
            status_fallback.value = item.status_fallback;
        });
    }

    if (selectedAgent) {
        selectedSource.value = selectedAgent.chatbot_name;
        selectedCompanyProfile.value = selectedAgent.profilePicture;
        selectedCompanyName.value = selectedAgent.companyName;
    }

});

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const formattedDate = `${date.getFullYear()}-${('0' + (date.getMonth() + 1)).slice(-2)}-${('0' + date.getDate()).slice(-2)} ${('0' + date.getHours()).slice(-2)}:${('0' + date.getMinutes()).slice(-2)}`;
    return formattedDate;
}

const timestamptoFormat = (timestamp) => {
    const date = new Date(timestamp * 1000); // Convert seconds to milliseconds
    const today = new Date();

    // Check if the date is today
    if (
        date.getDate() === today.getDate() &&
        date.getMonth() === today.getMonth() &&
        date.getFullYear() === today.getFullYear()
    ) {
        return 'Today';
    }

    // Check if the date is yesterday
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    if (
        date.getDate() === yesterday.getDate() &&
        date.getMonth() === yesterday.getMonth() &&
        date.getFullYear() === yesterday.getFullYear()
    ) {
        return 'Yesterday';
    }

    // Format as 'Y-m-d H:i'
    const formattedDate = date.toISOString().slice(0, 16).replace('T', ' ');
    return formattedDate;
};


const handleClick = (slug, user_id) => {

    statusLoading.value = true;
    // Update the URL with the parameter without refreshing the page
    window.history.pushState(null, null, `?source=${slug}` + `&umid=${user_id}`);

    // Set the selectedSlug to the clicked agent's slug
    selectedSlug.value = slug;

    // Find the selected agent by its slug
    const selectedAgent = allAgents.find(agent => agent.slug === slug);

    // Get the name of the selected agent
    const selectedAgentName = selectedAgent ? selectedAgent.name : '';

    selectedSource.value = selectedAgentName;
    selectedCompanyName.value = selectedAgent.company_name;
    selectedCompanyProfile.value = selectedAgent.profilePicture;

    showInput.value = true;

    // Make a Get request to your Laravel backend with the parameter in the URL
    axios.get(`/chatbot-json?source=${slug}` + `&umid=${user_id}`)
        .then(response => {
            let chatDataNew = response.data;
            messages.value = [];
            if (Object.keys(chatDataNew).length !== 0) {
                Object.values(chatDataNew).forEach(item => {
                    if (item.message.trim() !== '' && item.id == authDetail.id) {
                        messages.value.push({ text: item.message, timestamp: item.timestamp, sender: 'user' });
                    }
                    if (item.message.trim() !== '' && item.id != authDetail.id) {
                        messages.value.push({ text: item.message, timestamp: item.timestamp, sender: 'server' });
                    }

                    status_fallback.value = item.status_fallback;
                    if (status_fallback.value == 1) {
                        showInput.value = false;
                    }
                });
            }

            setTimeout(function () {
                let lastMessage = document.querySelector('#messages > div:nth-last-child(2)');
                if (lastMessage) {
                    //lastMessage.scrollIntoView({ behavior: 'smooth' });

                }
            }, 500); // 500 milliseconds = 1 second

            let sourceMessage = document.querySelector('#inputsource');
            if (sourceMessage) {
                sourceMessage.focus();
            }

            statusLoading.value = false;

        })
        .catch(error => {
            // Handle errors if the request fails
            console.error('Error:', error);
        });
}


const sendMessage = async () => {
    try {

        let timecurrent = () => Math.floor(Date.now() / 1000);
        showInput.value = false;

        if (inputText.value.trim() === '') {
            return false;
        }

        let botTyping = document.querySelector('.botTyping');
        botTyping.classList.remove('hidden');
        botTyping.scrollIntoView();

        setTimeout(() => {
            botTyping.scrollIntoView();
        }, 1000);

        // Push the user message

        if (inputText.value.trim() !== '') {
            messages.value.push({ text: inputText.value, timestamp: timecurrent(), sender: 'user' });
        }
        let inputMesage = inputText.value;
        inputText.value = '';

        let token = document.head.querySelector('meta[name="csrf-token"]').content;

        let requestBody = {
            status_fallback: status_fallback.value,
            message: inputMesage,
            slug: selectedSlug.value, // Pass the selectedSlug
            type: document.querySelector('input[name="type"]').value // Pass the type field
        };



        const response = await fetch('/dtadmin/send-message', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': token,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
        });

        const responseData = await response.json();



        // Push the server response
        // if(responseData.message.trim() !== ''){
        //     messages.value.push({ text: responseData.message, sender: 'server' });
        // }


        setTimeout(function () {
            let lastMessage = document.querySelector('#messages > div:nth-last-child(2)');
            if (lastMessage) {
                //lastMessage.scrollIntoView({ behavior: 'smooth' });
            }

            botTyping.classList.add('hidden');

            if (responseData.status_fallback == 1) {
                showInput.value = false;
                status_fallback.value = 1;
            } else {
                showInput.value = true;

            }

            if (responseData.message.trim() !== '') {
                let timesampes = (responseData.timestamp) ? responseData.timestamp : timecurrent();
                messages.value.push({ text: responseData.message, timestamp: timesampes, sender: 'server' });
            }


            nextTick(() => {
                let sourceMessage = document.querySelector('#inputsource');
                if (sourceMessage) {
                    sourceMessage.focus();
                }
            });

        }, 500);




    } catch (error) {
        console.error('Error sending message:', error);
    }
};





let authUserid = authDetail.id;

Echo.private('agent-channel').listen('ChatAgentMessage', (e) => {

    let socketMessage = e.message;

    let sourceMessage2 = document.querySelector('#inputsource');
    if (sourceMessage2) {
        sourceMessage2.focus();
    }

    Object.values(socketMessage).forEach(item => {

        //console.log(item);

        if (item.user_id === authUserid) {
        } else {
            if (item.text.trim() !== '') {
                let uid = encodeToBase64(authUserid);
                if ((item.chat_source.trim() === selectedSlug.value.trim()) && (item.chat_uid.trim() == uid)) {

                    if (item.status_fallback == 0) {
                        showInput.value = true;
                        status_fallback.value = 0;
                    }

                    let timesampes = (item.timestamp) ? item.timestamp : timecurrent();

                    messages.value.push({
                        text: item.text,
                        timestamp: timesampes,
                        sender: 'server'
                    });
                }

                let lastMessage = document.querySelector('#messages > div:nth-last-child(2)');
                if (lastMessage) {
                    lastMessage.scrollIntoView({ behavior: 'smooth' });

                }


            }
        }

    });
})

const encodeToBase64 = (projectId) => {
    return btoa(projectId); // Using btoa() to encode to base64
}




</script>

<style>
#messages::-webkit-scrollbar {
    width: 1px;
    /* Adjust the width of the scrollbar */
    border-radius: 20px;
    /* Adjust the radius of the scrollbar */
}

#messages::-webkit-scrollbar-thumb {
    background-color: #999;
    /* Change the color of the scrollbar thumb */
    border-radius: 20px;
    /* Adjust the radius of the scrollbar thumb */
}

#messages::-webkit-scrollbar-track {
    background-color: #edf2f7;
    /* Change the color of the scrollbar track */
}
</style>



<template>

    <Head :title="pageTitle" />

    <AuthenticatedLayout>

        <PageHeading :title="pageTitle"></PageHeading>

        <div class="block md:flex">
            <!-- Sidebar -->
            <div
                class="top-5 z-10 md:sticky bg-white scrollbar-thumb-gray-700/20 p-6 rounded-md w-full md:w-1/4 h-[calc(100vh-256px)] overflow-y-auto scrollbar-thin scrollbar-track-gray-100">
                <h3 class="mb-4 font-semibold text-lg">Company List</h3>

                <!-- Search field -->
                <div class="mb-4">
                    <input v-model="searchTerm" type="text" id="search" placeholder="Search Company"
                        class="block border-gray-300 mt-1 p-2 border rounded-md w-full">
                </div>

                <ul class="space-y-2" id="agents">
                    <li v-for="(agent, index) in allAgents" :key="index"
                        class="relative flex justify-between items-center border-gray-300 hover:bg-gray-50 p-2 border-b cursor-pointer"
                        @click="handleClick(agent.slug, encodeToBase64($page.props.auth.user.id))">

                        <!-- Left Section: Icon + Text + Description -->
                        <div class="flex justify-center items-center gap-4">
                            <div class="flex flex-shrink-0">
                                <img v-if="agent.profilePicture && agent.profilePicture.startsWith('http')"
                                    :src="agent.profilePicture" alt="User" class="rounded-full w-10" />
                                <img v-else-if="agent.profilePicture"
                                    :src="baseUrl + '/storage/' + agent.profilePicture" alt="User"
                                    class="rounded-full w-10" />
                                <img v-else src="@/assets/images/user/user-profile.jpeg" alt="User"
                                    class="rounded-full w-10" />
                            </div>
                            <div>
                                <h4 class="font-medium text-slate-800">{{ agent.company_name }}</h4>
                                <p class="text-slate-500 text-xs">
                                    {{ agent.last_message }}
                                </p>
                            </div>
                        </div>

                        <!-- Right Section: Last Message Time + Chatbot & User Icon -->
                        <div class="text-right justify-end items-center gap-4">
                            <div class="pb-2 text-gray-400 text-xs">{{ agent.last_msg_time }}
                            </div>
                            <div class="flex justify-end items-end gap-2">

                                <!-- Chatbot Icon -->
                                <i v-if="agent.chat_support == 'livechat' || agent.chat_support == 'both'"
                                    class="fa fa-user-secret" aria-hidden="true"></i>

                                <!-- User Icon -->
                                <i v-if="agent.chat_support == 'chatbot' || agent.chat_support == 'both'"
                                    class="fa fa-user-plus" aria-hidden="true"></i>

                            </div>
                        </div>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="flex-1 px-0 md:px-6 p-4 pt-0 md:pr-0 md:pl-6">

                <div v-if="selectedSource != 'Chatbot'" class="bg-white shadow-md mt-4 p-6 rounded-md">

                    <div v-if="statusLoading" class="flex items-end">
                        <div class="flex flex-col items-start space-y-2 order-2 mx-2 text-md leading-tight">
                            <div><img :src="baseUrl + '/microsoft-microsoft365.gif'" alt="..." class="ml-6 pb-5 w-16">
                            </div>
                        </div>
                    </div>
                    <div v-else class="flex flex-col h-full">
                        <div class="flex items-center">
                            <div class="mr-4.5 rounded-full w-full max-w-13 h-13 overflow-hidden">

                                <img v-if="selectedCompanyProfile && selectedCompanyProfile.startsWith('http')"
                                    :src="selectedCompanyProfile" alt="User"
                                    class="w-full h-full object-center object-cover" />
                                <img v-else-if="selectedCompanyProfile"
                                    :src="baseUrl + '/storage/' + selectedCompanyProfile" alt="User"
                                    class="w-full h-full object-center object-cover" />
                                <img v-else src="@/assets/images/user/user-profile.jpeg" alt="User"
                                    class="w-full h-full object-center object-cover" />

                            </div>
                            <div>
                                <h5 class="font-medium text-slate-800"><span class="source_name">{{
                                    selectedCompanyName
                                        }}</span></h5>
                                <p class="font-medium text-sm">start to chatbot</p>
                            </div>
                        </div>

                        <div v-if="selectedSource != 'Chatbot'" class="flex flex-col flex-1 justify-between">
                            <div id="messages"
                                class="flex flex-col space-y-4 scrollbar-thumb-blue scrollbar-thumb-rounded scrollbar-w-2 h-115 max-h-full overflow-y-auto scrollbar-track-blue-lighter scrolling-touch">
                                <!-- Message display -->
                                <div v-for="(message, index) in messages" :key="index"
                                    :class="message.sender + '_message'">
                                    <div class="flex"
                                        :class="message.sender === 'user' ? 'items-end justify-end ' : 'items-start justify-left'">

                                        <template class="flex justify-end items-start space-x-4"
                                            v-if="message.sender === 'user'">

                                            <div
                                                class="items-center space-x-4 shadow ml-auto px-4 py-2 rounded-md max-w-125 sendermessage">
                                                <div class="chat-receiver-cls">
                                                    <p>{{ message.text }}</p>
                                                </div>
                                                <p class="text-right font-medium text-[11px] text-gray-400">{{
                                                    timestamptoFormat(message.timestamp)
                                                }}
                                                </p>
                                            </div>
                                            <!-- <div
                                                    class="flex flex-col items-end space-y-2 bg-cfp-500 mx-2 px-4 py-3 rounded-xl max-w-lg text-md text-white leading-tight">
                                                    <div>
                                                        <span class="">{{ message.text }}</span>
                                                    </div>
                                                </div> -->

                                        </template>
                                        <template class="flex justify-start items-start space-x-4" v-else>

                                            <div
                                                class="items-center space-x-4 shadow mb-4 px-4 py-2 rounded-md max-w-125 receiverMessage">
                                                <div class="chat-sender-cls">
                                                    <p>{{ message.text }}</p>
                                                </div>
                                                <p class="font-medium text-[11px] text-gray-400">{{
                                                    timestamptoFormat(message.timestamp) }}</p>
                                            </div>

                                            <!-- <div
                                                    class="flex flex-col items-start space-y-2 bg-green-500 mx-2 px-4 py-3 rounded-xl max-w-lg text-md text-white leading-tight">
                                                    <div>
                                                        <span class="">{{ message.text }}</span>
                                                    </div>
                                                </div> -->
                                        </template>
                                    </div>
                                </div>
                                <!-- Typing indicator -->
                                <div class="hidden botTyping">
                                    <div class="flex items-end">
                                        <div
                                            class="flex flex-col items-start space-y-2 order-2 mx-2 text-md leading-tight">
                                            <div><img :src="baseUrl + '/microsoft-microsoft365.gif'" alt="..."
                                                    class="ml-6 pb-5 w-16"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Input area -->
                            <div class="border-gray-200 mb-2 sm:mb-0 px-4 pt-4 border-t-2">
                                <div class="relative flex">
                                    <input type="hidden" v-model="status_fallback"
                                        class="border-2 border-gray-200 focus:border-cfp-500 bg-gray-100 py-2 pr-16 pl-5 rounded-full w-full text-gray-600 text-md focus:outline-none focus:placeholder-gray-400 placeholder-gray-600" />
                                    <input ref="textInput" v-model="inputText" @keyup.enter="sendMessage" type="text"
                                        placeholder="Say something..." autocomplete="off" autofocus="true"
                                        id="inputsource" :disabled="!showInput"
                                        class="border-2 border-gray-200 focus:border-cfp-500 bg-gray-100 py-2 pr-16 pl-5 rounded-full w-full text-gray-600 text-md focus:outline-none focus:placeholder-gray-400 placeholder-gray-600" />
                                    <input type="hidden" name="type" :value="selectedSlug">
                                    <div class="right-2 absolute inset-y-1 sm:flex items-center">
                                        <button @click="sendMessage" type="button"
                                            class="inline-flex justify-center items-center bg-cfp-500 hover:bg-cfp-500/85 rounded-full w-8 h-8 text-white transition duration-200 ease-in-out focus:outline-none"><i
                                                class="fa-arrow-right text-xl leading-none fa"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <div v-else class="bg-white shadow-md p-6 rounded-md">
                    <p class="text-slate-800">Select a company to view chat details.</p>
                </div>
            </div>
        </div>







    </AuthenticatedLayout>
</template>
