<script setup>
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import Button from '../reusable/Button.vue';
import { Link, useForm, usePage } from '@inertiajs/vue3';
import InputError from '@/Components/InputError.vue';
import FormInput from '../reusable/FormInput.vue';
import { ref, onMounted } from 'vue';

const page = usePage();
const message = page.props.flash?.message;

const form = useForm({
	email: '',
	password: '',
	remember: false,
});

const submit = () => {
	form.post(route('login'), {
		onFinish: () => form.reset('password'),
	});
};

// Google Sign-In configuration
const googleSignInLoaded = ref(false);
const googleSignInError = ref(null);

// Function to handle Google Sign-In response
const handleGoogleSignIn = (response) => {
	const idToken = response.credential;
	
	// Send the ID token to your API
	fetch('/web-google-login', {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			'Accept': 'application/json',
			'X-CSRF-TOKEN': document.head.querySelector('meta[name="csrf-token"]').content
		},
		body: JSON.stringify({ id_token: idToken })
	})
	.then(res => res.json())
	.then(data => {
		if (data.status) {
			// Redirect to dashboard or home page
			window.location.href = route('dashboard');
		} else {
			// Handle error
			googleSignInError.value = data.message || 'Failed to login with Google';
		}
	})
	.catch(err => {
		googleSignInError.value = 'An error occurred during login';
		console.error(err);
	});
};

// Load Google Sign-In script
onMounted(() => {
	// Load Google Sign-In script if not already loaded
	if (!document.getElementById('google-signin-script')) {
		const script = document.createElement('script');
		script.src = 'https://accounts.google.com/gsi/client';
		script.id = 'google-signin-script';
		script.async = true;
		script.defer = true;
		script.onload = () => {
			googleSignInLoaded.value = true;
			
			// Initialize Google Sign-In button
			window.google?.accounts.id.initialize({
				client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
				callback: handleGoogleSignIn
			});
			
			// Render the button
			window.google?.accounts.id.renderButton(
				document.getElementById('google-signin-button'),
				{ theme: 'outline', size: 'large', width: '100%' }
			);
		};
		document.head.appendChild(script);
	}
});
</script>

<style lang="scss" scoped></style>

<template>
	<div class="mx-auto w-full md:w-1/2">
		<div class="login-body-cls">
			<div v-if="message" class="mb-4 p-4 bg-green-100 text-green-700 rounded">
				{{ message }}
			</div>

			<p class="mb-4 font-general-medium text-2xl text-center text-cfp-500-dark">
				Login
			</p>
			<form @submit.prevent="submit" class="space-y-5 pt-4 border-t font-general-regular">

				<FormInput v-model="form.email" label="Email" inputIdentifier="email" name="email" inputType="email"
					requiredstatus="required" />
				<InputError class="text-danger" :message="form.errors.email" />
				<FormInput v-model="form.password" label="Password" inputIdentifier="password" name="password"
					inputType="password" />
				<InputError class="text-danger" :message="form.errors.password" />
				<div class="text-center">
					<Button title="Submit" class="w-60 max-md:w-auto dk-update-btn" type="submit" aria-label="Submit" />
				</div>
			</form>
			
			<!-- Google Sign-In button -->
			<div class="mt-6 border-t border-cfp-500-light pt-4">
				<p class="text-center text-gray-600 mb-4">Or sign in with</p>
				
				<!-- Google Sign-In error message -->
				<div v-if="googleSignInError" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
					{{ googleSignInError }}
				</div>
				
				<!-- Google Sign-In button container -->
				<div id="google-signin-button" class="flex justify-center"></div>
				
				<!-- Fallback if Google script fails to load -->
				<div v-if="!googleSignInLoaded" class="mt-4 text-center text-sm text-gray-500">
					Loading Google Sign-In...
				</div>
			</div>
			
			<div class="mt-7 pt-4 border-t border-cfp-500-light text-center">

				<Link :href="route('password.request')" class="underline text-sm text-gray-600  hover:text-gray-900  rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ">
					Forgot your password?
				</Link>

				<div class="pt-1 font-normal text-lg">
					<ResponsiveNavLink :href="route('user-signup')"
						class="inline mb-2 sm:py-2 pt-3 sm:pt-2 border-t sm:border-t-0 max-md:border-t-0 border-cfp-500-light font-general-medium font-medium text-base text-center text-cfp-500-dark max-md:text-sm hover:text-cfp-500"
						aria-label="Login">Signup for Personal Account</ResponsiveNavLink>
				</div>
				<div class="pt-1 font-normal text-lg">
					<ResponsiveNavLink :href="route('user-signup-business')"
						class="inline max-md:bottom-t-0 max-md:border-0 mb-2 sm:py-2 pt-3 sm:pt-2 border-t sm:border-t-0 border-cfp-500-light font-general-medium font-medium text-base text-center text-cfp-500-dark max-md:text-sm hover:text-cfp-500"
						aria-label="Login">Signup for Business Account</ResponsiveNavLink>
				</div>
			</div>
		</div>
	</div>
</template>
