<template>
  <input type="file" id="profilePicture"
    class="block border-stroke focus:border-cfp-500/50 bg-transparent focus-visible:shadow-none mt-1 px-4 py-2 border rounded-md focus:ring-cfp-500/50 w-full text-slate-800 focus:outline-none outline-none"
    name="profilePicture" @change="handleFileChange" ref="input" />
</template>

<script setup>
import { ref, defineExpose, onMounted } from 'vue';

const input = ref(null);

onMounted(() => {
  // If autofocus attribute is present, focus the input
  if (input.value.hasAttribute('autofocus')) {
    input.value.focus();
  }
});

const handleFileChange = (event) => {
  // Access the selected file using event.target.files[0]
  const selectedFile = event.target.files[0];
  // You can perform any necessary operations with the selected file here
  console.log('Selected File:', selectedFile);
};

defineExpose({
  focus: () => input.value.focus()
});
</script>