<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\PaymentTransaction;
use App\Models\Invoice;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class ReportsController extends Controller
{
    /**
     * Show the admin reports dashboard.
     */
    public function index(Request $request): Response
    {
        $period = $request->get('period', '30'); // Default to 30 days
        $startDate = Carbon::now()->subDays((int)$period);
        $endDate = Carbon::now();
        

        return Inertia::render('Admin/Reports/Index', [
            'metrics' => $this->getMetrics($startDate, $endDate),
            'charts' => $this->getChartData($startDate, $endDate),
            'period' => $period,
        ]);
    }

    /**
     * Get subscription metrics.
     */
    public function subscriptionMetrics(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = Carbon::now()->subDays((int)$period);
        $endDate = Carbon::now();

        return response()->json([
            'metrics' => $this->getSubscriptionMetrics($startDate, $endDate),
            'charts' => $this->getSubscriptionCharts($startDate, $endDate),
        ]);
    }

    /**
     * Get revenue metrics.
     */
    public function revenueMetrics(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = Carbon::now()->subDays((int)$period);
        $endDate = Carbon::now();

        return response()->json([
            'metrics' => $this->getRevenueMetrics($startDate, $endDate),
            'charts' => $this->getRevenueCharts($startDate, $endDate),
        ]);
    }

    /**
     * Get churn analysis.
     */
    public function churnAnalysis(Request $request)
    {
        $period = $request->get('period', '90');
        $startDate = Carbon::now()->subDays((int)$period);
        $endDate = Carbon::now();

        return response()->json([
            'metrics' => $this->getChurnMetrics($startDate, $endDate),
            'charts' => $this->getChurnCharts($startDate, $endDate),
        ]);
    }

    /**
     * Get overall metrics.
     */
    private function getMetrics(Carbon $startDate, Carbon $endDate): array
    {
        return [
            'subscription' => $this->getSubscriptionMetrics($startDate, $endDate),
            'revenue' => $this->getRevenueMetrics($startDate, $endDate),
            'churn' => $this->getChurnMetrics($startDate, $endDate),
            'users' => $this->getUserMetrics($startDate, $endDate),
        ];
    }

    /**
     * Get subscription metrics.
     */
    private function getSubscriptionMetrics(Carbon $startDate, Carbon $endDate): array
    {
        $totalSubscriptions = Subscription::count();
        $activeSubscriptions = Subscription::where('status', 'active')->count();
        $trialSubscriptions = Subscription::where('status', 'trial')->count();
        $cancelledSubscriptions = Subscription::where('status', 'cancelled')->count();
        
        $newSubscriptions = Subscription::whereBetween('created_at', [$startDate, $endDate])->count();
        $previousPeriodStart = $startDate->copy()->subDays($endDate->diffInDays($startDate));
        $previousNewSubscriptions = Subscription::whereBetween('created_at', [$previousPeriodStart, $startDate])->count();
        
        $newSubscriptionsGrowth = $previousNewSubscriptions > 0 
            ? (($newSubscriptions - $previousNewSubscriptions) / $previousNewSubscriptions) * 100 
            : 0;

        return [
            'total' => $totalSubscriptions,
            'active' => $activeSubscriptions,
            'trial' => $trialSubscriptions,
            'cancelled' => $cancelledSubscriptions,
            'new_subscriptions' => $newSubscriptions,
            'new_subscriptions_growth' => round($newSubscriptionsGrowth, 2),
            'conversion_rate' => $trialSubscriptions > 0 ? round(($activeSubscriptions / ($activeSubscriptions + $trialSubscriptions)) * 100, 2) : 0,
        ];
    }

    /**
     * Get revenue metrics.
     */
    private function getRevenueMetrics(Carbon $startDate, Carbon $endDate): array
    {
        $totalRevenue = PaymentTransaction::where('status', 'completed')
            ->sum('amount');
            
        $periodRevenue = PaymentTransaction::where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');
            
        $previousPeriodStart = $startDate->copy()->subDays($endDate->diffInDays($startDate));
        $previousRevenue = PaymentTransaction::where('status', 'completed')
            ->whereBetween('created_at', [$previousPeriodStart, $startDate])
            ->sum('amount');
            
        $revenueGrowth = $previousRevenue > 0 
            ? (($periodRevenue - $previousRevenue) / $previousRevenue) * 100 
            : 0;

        $averageRevenuePerUser = $totalRevenue > 0 && Subscription::count() > 0 
            ? $totalRevenue / Subscription::count() 
            : 0;

        $monthlyRecurringRevenue = Subscription::join('subscription_plans', 'subscriptions.subscription_plan_id', '=', 'subscription_plans.id')
            ->where('subscriptions.status', 'active')
            ->where('subscription_plans.billing_interval', 'month')
            ->sum('subscription_plans.price');

        return [
            'total' => round($totalRevenue, 2),
            'period' => round($periodRevenue, 2),
            'growth' => round($revenueGrowth, 2),
            'average_per_user' => round($averageRevenuePerUser, 2),
            'monthly_recurring' => round($monthlyRecurringRevenue, 2),
        ];
    }

    /**
     * Get churn metrics.
     */
    private function getChurnMetrics(Carbon $startDate, Carbon $endDate): array
    {
        $totalActiveStart = Subscription::where('status', 'active')
            ->where('created_at', '<', $startDate)
            ->count();
            
        $cancelledInPeriod = Subscription::where('status', 'cancelled')
            ->whereBetween('updated_at', [$startDate, $endDate])
            ->count();
            
        $churnRate = $totalActiveStart > 0 ? ($cancelledInPeriod / $totalActiveStart) * 100 : 0;
        
        $retentionRate = 100 - $churnRate;

        return [
            'churn_rate' => round($churnRate, 2),
            'retention_rate' => round($retentionRate, 2),
            'cancelled_in_period' => $cancelledInPeriod,
            'active_at_start' => $totalActiveStart,
        ];
    }

    /**
     * Get user metrics.
     */
    private function getUserMetrics(Carbon $startDate, Carbon $endDate): array
    {
        $totalUsers = User::count();
        $newUsers = User::whereBetween('created_at', [$startDate, $endDate])->count();
        $companyUsers = User::where('profileType', 'company')->count();
        $regularUsers = User::where('profileType', 'user')->count();

        return [
            'total' => $totalUsers,
            'new' => $newUsers,
            'companies' => $companyUsers,
            'regular' => $regularUsers,
        ];
    }

    /**
     * Get chart data.
     */
    private function getChartData(Carbon $startDate, Carbon $endDate): array
    {
        return [
            'subscription_growth' => $this->getSubscriptionGrowthChart($startDate, $endDate),
            'revenue_trend' => $this->getRevenueTrendChart($startDate, $endDate),
            'plan_distribution' => $this->getPlanDistributionChart(),
            'payment_methods' => $this->getPaymentMethodsChart($startDate, $endDate),
        ];
    }

    /**
     * Get subscription growth chart data.
     */
    private function getSubscriptionGrowthChart(Carbon $startDate, Carbon $endDate): array
    {
        $data = Subscription::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'labels' => $data->pluck('date')->map(fn($date) => Carbon::parse($date)->format('M d'))->toArray(),
            'datasets' => [
                [
                    'label' => 'New Subscriptions',
                    'data' => $data->pluck('count')->toArray(),
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                ]
            ]
        ];
    }

    /**
     * Get revenue trend chart data.
     */
    private function getRevenueTrendChart(Carbon $startDate, Carbon $endDate): array
    {
        $data = PaymentTransaction::selectRaw('DATE(created_at) as date, SUM(amount) as total')
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'labels' => $data->pluck('date')->map(fn($date) => Carbon::parse($date)->format('M d'))->toArray(),
            'datasets' => [
                [
                    'label' => 'Revenue',
                    'data' => $data->pluck('total')->toArray(),
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                ]
            ]
        ];
    }

    /**
     * Get plan distribution chart data.
     */
    private function getPlanDistributionChart(): array
    {
        $data = Subscription::join('subscription_plans', 'subscriptions.subscription_plan_id', '=', 'subscription_plans.id')
            ->selectRaw('subscription_plans.name, COUNT(*) as count')
            ->where('subscriptions.status', 'active')
            ->groupBy('subscription_plans.name')
            ->get();

        return [
            'labels' => $data->pluck('name')->toArray(),
            'datasets' => [
                [
                    'data' => $data->pluck('count')->toArray(),
                    'backgroundColor' => [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(34, 197, 94, 0.8)',
                        'rgba(251, 191, 36, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(168, 85, 247, 0.8)',
                    ],
                ]
            ]
        ];
    }

    /**
     * Get payment methods chart data.
     */
    private function getPaymentMethodsChart(Carbon $startDate, Carbon $endDate): array
    {
        $data = PaymentTransaction::selectRaw('gateway, COUNT(*) as count')
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('gateway')
            ->get();

        return [
            'labels' => $data->pluck('gateway')->map(fn($method) => ucfirst($method))->toArray(),
            'datasets' => [
                [
                    'data' => $data->pluck('count')->toArray(),
                    'backgroundColor' => [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(34, 197, 94, 0.8)',
                        'rgba(251, 191, 36, 0.8)',
                    ],
                ]
            ]
        ];
    }

    /**
     * Get subscription charts.
     */
    private function getSubscriptionCharts(Carbon $startDate, Carbon $endDate): array
    {
        return [
            'growth' => $this->getSubscriptionGrowthChart($startDate, $endDate),
            'status_distribution' => $this->getSubscriptionStatusChart(),
        ];
    }

    /**
     * Get subscription status chart.
     */
    private function getSubscriptionStatusChart(): array
    {
        $data = Subscription::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get();

        return [
            'labels' => $data->pluck('status')->map(fn($status) => ucfirst($status))->toArray(),
            'datasets' => [
                [
                    'data' => $data->pluck('count')->toArray(),
                    'backgroundColor' => [
                        'rgba(34, 197, 94, 0.8)', // active - green
                        'rgba(59, 130, 246, 0.8)', // trial - blue
                        'rgba(239, 68, 68, 0.8)',  // cancelled - red
                        'rgba(251, 191, 36, 0.8)', // pending - yellow
                    ],
                ]
            ]
        ];
    }

    /**
     * Get revenue charts.
     */
    private function getRevenueCharts(Carbon $startDate, Carbon $endDate): array
    {
        return [
            'trend' => $this->getRevenueTrendChart($startDate, $endDate),
            'by_plan' => $this->getRevenueByPlanChart($startDate, $endDate),
        ];
    }

    /**
     * Get revenue by plan chart.
     */
    private function getRevenueByPlanChart(Carbon $startDate, Carbon $endDate): array
    {
        $data = PaymentTransaction::join('subscriptions', 'payment_transactions.subscription_id', '=', 'subscriptions.id')
            ->join('subscription_plans', 'subscriptions.subscription_plan_id', '=', 'subscription_plans.id')
            ->selectRaw('subscription_plans.name, SUM(payment_transactions.amount) as total')
            ->where('payment_transactions.status', 'completed')
            ->whereBetween('payment_transactions.created_at', [$startDate, $endDate])
            ->groupBy('subscription_plans.name')
            ->get();

        return [
            'labels' => $data->pluck('name')->toArray(),
            'datasets' => [
                [
                    'data' => $data->pluck('total')->toArray(),
                    'backgroundColor' => [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(34, 197, 94, 0.8)',
                        'rgba(251, 191, 36, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(168, 85, 247, 0.8)',
                    ],
                ]
            ]
        ];
    }

    /**
     * Get churn charts.
     */
    private function getChurnCharts(Carbon $startDate, Carbon $endDate): array
    {
        return [
            'monthly_churn' => $this->getMonthlyChurnChart(),
            'churn_reasons' => $this->getChurnReasonsChart($startDate, $endDate),
        ];
    }

    /**
     * Get monthly churn chart.
     */
    private function getMonthlyChurnChart(): array
    {
        $data = Subscription::selectRaw('YEAR(updated_at) as year, MONTH(updated_at) as month, COUNT(*) as count')
            ->where('status', 'cancelled')
            ->where('updated_at', '>=', Carbon::now()->subMonths(12))
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        return [
            'labels' => $data->map(fn($item) => Carbon::createFromDate($item->year, $item->month, 1)->format('M Y'))->toArray(),
            'datasets' => [
                [
                    'label' => 'Cancelled Subscriptions',
                    'data' => $data->pluck('count')->toArray(),
                    'borderColor' => 'rgb(239, 68, 68)',
                    'backgroundColor' => 'rgba(239, 68, 68, 0.1)',
                ]
            ]
        ];
    }

    /**
     * Get churn reasons chart (placeholder - would need to implement cancellation reasons).
     */
    private function getChurnReasonsChart(Carbon $startDate, Carbon $endDate): array
    {
        // This would require implementing cancellation reasons in the subscription model
        return [
            'labels' => ['Price', 'Features', 'Support', 'Other'],
            'datasets' => [
                [
                    'data' => [30, 25, 20, 25], // Placeholder data
                    'backgroundColor' => [
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(251, 191, 36, 0.8)',
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(156, 163, 175, 0.8)',
                    ],
                ]
            ]
        ];
    }
}
