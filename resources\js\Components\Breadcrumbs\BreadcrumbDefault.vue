<script setup>
import { Link } from '@inertiajs/vue3';
const props = defineProps(['pageTitle']);

</script>

<template>
  <div class="flex sm:flex-row flex-col sm:justify-between sm:items-center gap-3 mb-6">
    <h2 class="font-semibold text-slate-800 text-title-md2">
      {{ props.pageTitle }}
    </h2>

    <nav>
      <ol class="flex items-center gap-2">
        <li>
          <Link :href="route('dashboard')" class="font-medium"> Dashboard &nbsp;/ </Link>
        </li>
        <li class="font-medium text-cfp-500">{{ props.pageTitle }}</li>
      </ol>
    </nav>
  </div>
</template>
