import React from 'react';
import { Link } from '@inertiajs/react';

export default function Header({ currentPage = '', variant = 'default', user = null }) {
    const isHomePage = currentPage === 'home';
    const isCompaniesPage = currentPage === 'companies';
    const isDealsPage = currentPage === 'deals';
    const isContactPage = currentPage === 'contact';
    
    // Different header styles based on variant
    const getHeaderClasses = () => {
        switch (variant) {
            case 'transparent':
                return 'relative z-10 px-6 py-4'; // For homepage with gradient background
            case 'solid':
            default:
                return 'bg-white shadow-sm border-b px-6 py-4'; // For other pages
        }
    };

    const getLinkClasses = (isActive) => {
        const baseClasses = 'transition-colors font-medium';
        if (variant === 'transparent') {
            return `${baseClasses} ${isActive ? 'text-cf-primary-600' : 'text-gray-700 hover:text-cf-primary-600'}`;
        }
        return `${baseClasses} ${isActive ? 'text-cf-primary-600' : 'text-gray-700 hover:text-cf-primary-600'}`;
    };

    return (
        <nav className={getHeaderClasses()}>
            <div className="max-w-7xl mx-auto flex items-center justify-between">
                {/* Logo */}
                <Link href="/" className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gradient-to-r from-cf-primary-500 to-cf-primary-600 rounded-lg flex items-center justify-center">
                        <span className="text-white font-bold text-sm">C</span>
                    </div>
                    <span className="text-xl font-bold text-gray-900">ChatHi</span>
                </Link>
                
                {/* Navigation Links */}
                <div className="hidden md:flex items-center gap-8">
                    <Link 
                        href="/" 
                        className={getLinkClasses(isHomePage)}
                    >
                        Home
                    </Link>
                    <Link 
                        href="/companies" 
                        className={getLinkClasses(isCompaniesPage)}
                    >
                        Companies
                    </Link>
                    <Link 
                        href="/deals" 
                        className={getLinkClasses(isDealsPage)}
                    >
                        Deals
                    </Link>
                    <Link
                        href="/contact"
                        className={getLinkClasses(isContactPage)}
                    >
                        Contact
                    </Link>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center gap-4">
                    {user ? (
                        <Link
                            href="/dashboard"
                            className="bg-cf-primary-600 hover:bg-cf-primary-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
                        >
                            Dashboard
                        </Link>
                    ) : (
                        <Link
                            href="/login"
                            className="bg-cf-primary-600 hover:bg-cf-primary-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
                        >
                            Login
                        </Link>
                    )}
                    
                    {/* Mobile Menu Button */}
                    <button className="md:hidden p-2 text-gray-600 hover:text-cf-primary-600 transition-colors">
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
            
            {/* Mobile Menu (hidden by default) */}
            <div className="md:hidden mt-4 pb-4 border-t border-gray-200 hidden">
                <div className="flex flex-col gap-4 pt-4">
                    <Link 
                        href="/" 
                        className={getLinkClasses(isHomePage)}
                    >
                        Home
                    </Link>
                    <Link 
                        href="/companies" 
                        className={getLinkClasses(isCompaniesPage)}
                    >
                        Companies
                    </Link>
                    <Link 
                        href="/deals" 
                        className={getLinkClasses(isDealsPage)}
                    >
                        Deals
                    </Link>
                    <Link
                        href="/contact"
                        className={getLinkClasses(isContactPage)}
                    >
                        Contact
                    </Link>
                </div>
            </div>
        </nav>
    );
}
