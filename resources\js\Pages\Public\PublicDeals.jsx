import React, { useState, useEffect } from 'react';
import { Link } from '@inertiajs/react';
import PublicLayout from '@/Components/PublicLayout';

export default function PublicDeals() {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [deals, setDeals] = useState([]);
    const [filteredDeals, setFilteredDeals] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const dealsPerPage = 9;

    // Mock deals data
    const mockDeals = [
        {
            id: 1,
            title: "Macy's Last Act Sales",
            description: "75% off 1,000s of items buy now, the deal is for a short period of time.",
            discount: "75%",
            originalPrice: "$299.99",
            salePrice: "$74.99",
            category: "Fashion",
            image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop",
            link: "https://akademi.eruditetechnology.com/deal/1",
            endDate: "2025-01-31",
            isHot: true,
            rating: 4.8,
            reviews: 1250
        },
        {
            id: 2,
            title: "Amazon Prime Day Special",
            description: "Exclusive deals for Prime members with lightning fast delivery.",
            discount: "50%",
            originalPrice: "$199.99",
            salePrice: "$99.99",
            category: "Electronics",
            image: "https://images.unsplash.com/photo-1523474253046-8cd2748b5fd2?w=400&h=300&fit=crop",
            link: "https://amazon.com/prime-day",
            endDate: "2025-02-15",
            isHot: false,
            rating: 4.6,
            reviews: 890
        },
        {
            id: 3,
            title: "Black Friday Mega Sale",
            description: "Biggest sale of the year with up to 80% off on selected items.",
            discount: "80%",
            originalPrice: "$499.99",
            salePrice: "$99.99",
            category: "Home & Garden",
            image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop",
            link: "https://example.com/black-friday",
            endDate: "2025-01-25",
            isHot: true,
            rating: 4.9,
            reviews: 2100
        },
        {
            id: 4,
            title: "Tech Gadgets Clearance",
            description: "Latest tech gadgets at unbeatable prices. Limited stock available.",
            discount: "60%",
            originalPrice: "$149.99",
            salePrice: "$59.99",
            category: "Electronics",
            image: "https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400&h=300&fit=crop",
            link: "https://example.com/tech-clearance",
            endDate: "2025-02-10",
            isHot: false,
            rating: 4.4,
            reviews: 567
        },
        {
            id: 5,
            title: "Fashion Week Special",
            description: "Designer clothing and accessories at incredible discounts.",
            discount: "70%",
            originalPrice: "$399.99",
            salePrice: "$119.99",
            category: "Fashion",
            image: "https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=300&fit=crop",
            link: "https://example.com/fashion-week",
            endDate: "2025-01-28",
            isHot: true,
            rating: 4.7,
            reviews: 934
        },
        {
            id: 6,
            title: "Home Decor Bonanza",
            description: "Transform your space with beautiful home decor items on sale.",
            discount: "55%",
            originalPrice: "$249.99",
            salePrice: "$112.49",
            category: "Home & Garden",
            image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop",
            link: "https://example.com/home-decor",
            endDate: "2025-02-05",
            isHot: false,
            rating: 4.5,
            reviews: 678
        }
    ];

    const categories = ['all', 'Fashion', 'Electronics', 'Home & Garden', 'Sports', 'Books'];

    useEffect(() => {
        // Simulate API loading
        setTimeout(() => {
            setDeals(mockDeals);
            setFilteredDeals(mockDeals);
            setIsLoading(false);
        }, 1000);
    }, []);

    useEffect(() => {
        let filtered = deals;

        // Filter by search term
        if (searchTerm) {
            filtered = filtered.filter(deal =>
                deal.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                deal.description.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // Filter by category
        if (selectedCategory !== 'all') {
            filtered = filtered.filter(deal => deal.category === selectedCategory);
        }

        setFilteredDeals(filtered);
        setCurrentPage(1);
    }, [searchTerm, selectedCategory, deals]);

    const totalPages = Math.ceil(filteredDeals.length / dealsPerPage);
    const startIndex = (currentPage - 1) * dealsPerPage;
    const currentDeals = filteredDeals.slice(startIndex, startIndex + dealsPerPage);

    const handlePageChange = (page) => {
        setCurrentPage(page);
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = date - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays < 0) return 'Expired';
        if (diffDays === 0) return 'Ends today';
        if (diffDays === 1) return 'Ends tomorrow';
        return `${diffDays} days left`;
    };

    return (
        <PublicLayout currentPage="deals">
            {/* Enhanced Hero Section */}
            <div className="relative bg-gradient-to-r from-cf-primary-600 to-blue-600 text-white px-6 py-20 overflow-hidden">
                <div className="absolute inset-0">
                    <img
                        src="https://images.unsplash.com/photo-1607082348824-0a96f2a4b9da?w=1200&h=600&fit=crop&crop=center"
                        alt="Shopping deals and discounts"
                        className="w-full h-full object-cover opacity-20"
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-cf-primary-600/80 to-blue-600/80"></div>
                </div>

                <div className="relative max-w-7xl mx-auto">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                        <div className="text-center lg:text-left">
                            <div className="inline-block bg-orange-500 text-white px-4 py-2 rounded-full text-sm font-medium mb-6">
                                🔥 Hot Deals Available Now
                            </div>
                            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                                Amazing Deals &<br />
                                <span className="text-yellow-300">Exclusive Offers</span>
                            </h1>
                            <p className="text-xl md:text-2xl mb-8 opacity-90 max-w-lg mx-auto lg:mx-0">
                                Discover incredible savings on top brands and products. Limited time offers you don't want to miss!
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                                <button className="bg-white text-cf-primary-600 hover:bg-gray-100 px-8 py-4 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105">
                                    Browse All Deals
                                </button>
                                <button className="border-2 border-white text-white hover:bg-white hover:text-cf-primary-600 px-8 py-4 rounded-lg font-semibold transition-all duration-200">
                                    Today's Specials
                                </button>
                            </div>
                        </div>

                        <div className="relative">
                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-4">
                                    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 transform rotate-3 hover:rotate-0 transition-transform">
                                        <div className="text-2xl font-bold text-yellow-300">75% OFF</div>
                                        <div className="text-sm opacity-90">Fashion Items</div>
                                    </div>
                                    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 transform -rotate-2 hover:rotate-0 transition-transform">
                                        <div className="text-2xl font-bold text-green-300">50% OFF</div>
                                        <div className="text-sm opacity-90">Electronics</div>
                                    </div>
                                </div>
                                <div className="space-y-4 mt-8">
                                    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 transform -rotate-3 hover:rotate-0 transition-transform">
                                        <div className="text-2xl font-bold text-blue-300">60% OFF</div>
                                        <div className="text-sm opacity-90">Home & Garden</div>
                                    </div>
                                    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 transform rotate-2 hover:rotate-0 transition-transform">
                                        <div className="text-2xl font-bold text-purple-300">40% OFF</div>
                                        <div className="text-sm opacity-90">Sports</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Floating Elements */}
                <div className="absolute top-20 left-10 w-20 h-20 bg-yellow-400/20 rounded-full animate-pulse"></div>
                <div className="absolute top-40 right-20 w-16 h-16 bg-green-400/20 rounded-full animate-bounce"></div>
                <div className="absolute bottom-20 left-20 w-12 h-12 bg-blue-400/20 rounded-full animate-ping"></div>

                <div className="relative max-w-7xl mx-auto mt-12">
                    
                    {/* Search and Filter */}
                    <div className="max-w-4xl mx-auto space-y-4">
                        <div className="relative">
                            <svg className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                            <input
                                type="text"
                                placeholder="Search for deals..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-white focus:border-transparent text-lg text-gray-900"
                            />
                        </div>
                        
                        <div className="flex flex-wrap justify-center gap-2">
                            {categories.map((category) => (
                                <button
                                    key={category}
                                    onClick={() => setSelectedCategory(category)}
                                    className={`px-4 py-2 rounded-full font-medium transition-colors ${
                                        selectedCategory === category
                                            ? 'bg-white text-cf-primary-600'
                                            : 'bg-white/20 text-white hover:bg-white/30'
                                    }`}
                                >
                                    {category === 'all' ? 'All Categories' : category}
                                </button>
                            ))}
                        </div>
                    </div>
                </div>
            </div>

            {/* Deals Grid */}
            <div className="px-6 py-12">
                <div className="max-w-7xl mx-auto">
                    {isLoading ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                            {[...Array(6)].map((_, index) => (
                                <div key={index} className="bg-white rounded-xl shadow-sm animate-pulse">
                                    <div className="h-48 bg-gray-200 rounded-t-xl"></div>
                                    <div className="p-6 space-y-3">
                                        <div className="h-6 bg-gray-200 rounded"></div>
                                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <>
                            <div className="mb-8 flex items-center justify-between">
                                <p className="text-gray-600">
                                    Showing {startIndex + 1}-{Math.min(startIndex + dealsPerPage, filteredDeals.length)} of {filteredDeals.length} deals
                                </p>
                                {(searchTerm || selectedCategory !== 'all') && (
                                    <button
                                        onClick={() => {
                                            setSearchTerm('');
                                            setSelectedCategory('all');
                                        }}
                                        className="text-cf-primary-600 hover:text-cf-primary-700 font-medium"
                                    >
                                        Clear filters
                                    </button>
                                )}
                            </div>

                            {filteredDeals.length === 0 ? (
                                <div className="text-center py-12">
                                    <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <h3 className="text-xl font-medium text-gray-900 mb-2">No deals found</h3>
                                    <p className="text-gray-600">Try adjusting your search terms or filters</p>
                                </div>
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                    {currentDeals.map((deal) => (
                                        <div 
                                            key={deal.id} 
                                            className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden group"
                                        >
                                            <div className="relative">
                                                <img 
                                                    src={deal.image} 
                                                    alt={deal.title}
                                                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                                />
                                                <div className="absolute top-3 left-3">
                                                    <div className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                                                        {deal.discount} OFF
                                                    </div>
                                                </div>
                                                {deal.isHot && (
                                                    <div className="absolute top-3 right-3">
                                                        <div className="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center gap-1">
                                                            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                                                                <path d="M13.5.67s.74 2.65.74 4.8c0 2.06-1.35 3.73-3.41 3.73-2.07 0-3.63-1.67-3.63-3.73l.03-.36C5.21 7.51 4 10.62 4 14c0 4.42 3.58 8 8 8s8-3.58 8-8C20 8.61 17.41 3.8 13.5.67zM11.71 19c-1.78 0-3.22-1.4-3.22-3.14 0-1.62 1.05-2.76 2.81-3.12 1.77-.36 3.6-1.21 4.62-2.58.39 1.29.59 2.65.59 4.04 0 2.65-2.15 4.8-4.8 4.8z"/>
                                                            </svg>
                                                            HOT
                                                        </div>
                                                    </div>
                                                )}
                                                <div className="absolute bottom-3 right-3">
                                                    <div className="bg-black/70 text-white px-2 py-1 rounded text-xs">
                                                        {formatDate(deal.endDate)}
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="p-6">
                                                <div className="mb-3">
                                                    <h3 className="text-lg font-bold text-gray-900 mb-2 line-clamp-1">
                                                        {deal.title}
                                                    </h3>
                                                    <p className="text-gray-600 text-sm line-clamp-2">
                                                        {deal.description}
                                                    </p>
                                                </div>

                                                <div className="mb-4">
                                                    <div className="flex items-center gap-2 mb-2">
                                                        <span className="text-2xl font-bold text-cf-primary-600">
                                                            {deal.salePrice}
                                                        </span>
                                                        <span className="text-gray-500 line-through">
                                                            {deal.originalPrice}
                                                        </span>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <div className="flex items-center">
                                                            {[...Array(5)].map((_, i) => (
                                                                <svg 
                                                                    key={i} 
                                                                    className={`w-4 h-4 ${i < Math.floor(deal.rating) ? 'text-yellow-400' : 'text-gray-300'}`} 
                                                                    fill="currentColor" 
                                                                    viewBox="0 0 20 20"
                                                                >
                                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                                </svg>
                                                            ))}
                                                        </div>
                                                        <span className="text-sm text-gray-600">
                                                            {deal.rating} ({deal.reviews} reviews)
                                                        </span>
                                                    </div>
                                                </div>

                                                <div className="flex items-center justify-between">
                                                    <span className="inline-block bg-cf-primary-100 text-cf-primary-700 px-3 py-1 rounded-full text-sm font-medium">
                                                        {deal.category}
                                                    </span>
                                                    <a
                                                        href={deal.link}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className="bg-cf-primary-600 hover:bg-cf-primary-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                                                    >
                                                        Get Deal
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </>
                    )}
                </div>
            </div>

            {/* Pagination */}
            {!isLoading && filteredDeals.length > dealsPerPage && (
                <div className="px-6 pb-12">
                    <div className="max-w-7xl mx-auto">
                        <div className="flex items-center justify-center gap-2">
                            <button
                                onClick={() => handlePageChange(currentPage - 1)}
                                disabled={currentPage === 1}
                                className="px-4 py-2 text-gray-600 hover:text-cf-primary-600 disabled:text-gray-300 disabled:cursor-not-allowed transition-colors"
                            >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                </svg>
                            </button>

                            {[...Array(totalPages)].map((_, index) => {
                                const page = index + 1;
                                const isCurrentPage = page === currentPage;
                                const isNearCurrentPage = Math.abs(page - currentPage) <= 2;
                                const isFirstOrLast = page === 1 || page === totalPages;

                                if (!isNearCurrentPage && !isFirstOrLast) {
                                    if (page === currentPage - 3 || page === currentPage + 3) {
                                        return <span key={page} className="px-2 text-gray-400">...</span>;
                                    }
                                    return null;
                                }

                                return (
                                    <button
                                        key={page}
                                        onClick={() => handlePageChange(page)}
                                        className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                                            isCurrentPage
                                                ? 'bg-cf-primary-600 text-white'
                                                : 'text-gray-600 hover:text-cf-primary-600 hover:bg-cf-primary-50'
                                        }`}
                                    >
                                        {page}
                                    </button>
                                );
                            })}

                            <button
                                onClick={() => handlePageChange(currentPage + 1)}
                                disabled={currentPage === totalPages}
                                className="px-4 py-2 text-gray-600 hover:text-cf-primary-600 disabled:text-gray-300 disabled:cursor-not-allowed transition-colors"
                            >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                            </button>
                        </div>

                        <div className="text-center mt-4 text-gray-600">
                            Page {currentPage} of {totalPages}
                        </div>
                    </div>
                </div>
            )}

            {/* Newsletter Section */}
            <div className="bg-cf-primary-600 text-white px-6 py-16">
                <div className="max-w-4xl mx-auto text-center">
                    <h2 className="text-3xl font-bold mb-4">
                        Never Miss a Deal!
                    </h2>
                    <p className="text-xl mb-8 opacity-90">
                        Subscribe to our newsletter and get the best deals delivered to your inbox
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                        <input
                            type="email"
                            placeholder="Enter your email"
                            className="flex-1 px-4 py-3 rounded-lg text-gray-900 focus:ring-2 focus:ring-white focus:outline-none"
                        />
                        <button className="bg-white text-cf-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                            Subscribe
                        </button>
                    </div>
                </div>
            </div>

        </PublicLayout>
    );
}
