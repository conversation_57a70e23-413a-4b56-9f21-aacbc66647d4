<?php

namespace App\Http\Controllers;

use App\Models\Company;
use App\Models\Complaint;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class Usercontroller extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    public function search(Request $request)
    {
        $search = $request->input('query');

        if (trim($search) != '') {

            $users = User::where('id', '<>', Auth::id())
                ->where('profileType', 'user')
                ->where(function ($query) use ($search) {
                    $query->where('name', 'like', "%$search%")
                        ->orWhere('email', 'like', "%$search%")
                        ->orWhere('phone', 'like', "%$search%");

                })->get();

            if (count($users) > 0) {
                return response()->json(['users' => $users]);
            } else {
                return response()->json(['users' => []]);
            }

        } else {
            return response()->json(['users' => []]);
        }

    }

    public function searchAuthUser(Request $request)
    {
        $search = trim($request->input('query'));
        if ($search == '') {
            return response()->json(['users' => []]);
        }
        $user        = Auth::user();
        $profileType = $user->profileType;
        $ownerID     = ($profileType === 'agent') ? $user->parent_id : Auth::id();

        // If profile type is 'company' or 'agent', get company users
        if (in_array($profileType, ['company', 'agent'])) {
            $companyDetail = Company::where('userId', $ownerID)->first();

            // If no company found, return empty response early
            if (! $companyDetail) {
                return response()->json(['users' => []]);
            }

            $allUsers = Complaint::where('companyId', $companyDetail->id)
                ->whereNotNull('assignedToUserId') // Avoid null values
                ->distinct()
                ->pluck('assignedToUserId')
                ->toArray();

            $usersQuery = User::where('id', '<>', Auth::id())
                ->where('profileType', 'user')
                ->when(isset($allUsers), function ($query) use ($allUsers) {
                    return $query->whereIn('id', $allUsers);
                })
                ->when($search, function ($query) use ($search) {
                    return $query->where(function ($q) use ($search) {
                        $q->where('name', 'like', "%$search%")
                            ->orWhere('email', 'like', "%$search%")
                            ->orWhere('phone', 'like', "%$search%");
                    });
                });

        } else {

            $usersQuery = User::where('id', '<>', Auth::id())
                ->where('profileType', 'user')
                ->when($search, function ($query) use ($search) {
                    return $query->where(function ($q) use ($search) {
                        $q->where('name', 'like', "%$search%")
                            ->orWhere('email', 'like', "%$search%")
                            ->orWhere('phone', 'like', "%$search%");
                    });
                });
        }

        // Start query for users

        // if ($profileType == 'company') {
        //     $usersQuery->where('vid', $user->id);
        // }

        return response()->json(['users' => $usersQuery->get()]);
    }
}
