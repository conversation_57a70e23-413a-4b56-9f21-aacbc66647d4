import React, { useState } from 'react';
import { useForm, usePage } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { Input } from '@/Components/FormElements';
import Button from '@/Components/Button';
import FormError from '@/Components/FormError';

export default function Profile({ user, status }) {
    const { errors } = usePage().props;
    const { data, setData, put, processing } = useForm({
        name: user.name || '',
        email: user.email || '',
    });
    const [success, setSuccess] = useState(status || '');

    function handleSubmit(e) {
        e.preventDefault();
        put('/profile', {
            onSuccess: () => setSuccess('Profile updated successfully!'),
        });
    }

    return (
        <DashboardLayout title="Profile">
            <title>Profile</title>
            <div className="max-w-full mx-auto px-4 py-8">
                {/* Profile Header */}
                <div className="bg-gradient-to-r from-cf-primary-600 to-blue-600 rounded-2xl p-8 text-white mb-8 relative overflow-hidden">
                    <div className="absolute inset-0 opacity-10">
                        <img
                            src="https://images.unsplash.com/photo-1557804506-669a67965ba0?w=800&h=400&fit=crop&crop=center"
                            alt="Profile background"
                            className="w-full h-full object-cover"
                        />
                    </div>
                    <div className="relative flex flex-col md:flex-row items-center gap-6">
                        <div className="relative">
                            <img
                                src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
                                alt="Profile avatar"
                                className="w-24 h-24 rounded-full object-cover border-4 border-white/20"
                            />
                            <div className="absolute -bottom-2 -right-2 bg-green-500 w-6 h-6 rounded-full border-2 border-white"></div>
                        </div>
                        <div className="text-center md:text-left">
                            <h1 className="text-3xl font-bold mb-2">{user.name || 'User Name'}</h1>
                            <p className="text-xl opacity-90 mb-2">{user.email}</p>
                            <div className="flex flex-wrap gap-2 justify-center md:justify-start">
                                <span className="bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 text-sm">
                                    Premium Member
                                </span>
                                <span className="bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 text-sm">
                                    Active Since 2024
                                </span>
                            </div>
                        </div>
                        <div className="md:ml-auto">
                            <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4 text-center">
                                <div className="text-2xl font-bold">98%</div>
                                <div className="text-sm opacity-80">Profile Complete</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Profile Information */}
                    <div className="bg-white rounded-2xl shadow p-6 flex flex-col gap-4">
                        <h2 className="text-lg font-bold text-cf-primary mb-2">Profile Information</h2>
                        <p className="text-xs text-gray-500 mb-2">Update your account profile information and email address.</p>
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <Input
                                label="Name"
                                name="name"
                                value={data.name}
                                onChange={e => setData('name', e.target.value)}
                                error={errors.name}
                                autoFocus
                            />
                            <Input
                                label="Company Name"
                                name="company"
                                value="Chatbot LTD"
                                readOnly
                            />
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Upload company logo</label>
                                <input type="file" className="block w-full text-sm text-gray-500" disabled />
                            </div>
                            <Input
                                label="Email"
                                name="email"
                                type="email"
                                value={data.email}
                                onChange={e => setData('email', e.target.value)}
                                error={errors.email}
                            />
                            <FormError error={errors.general} />
                            <div className="flex justify-end">
                                <Button type="submit" loading={processing} className="mt-2">Save</Button>
                            </div>
                        </form>
                    </div>
                    {/* Update Password */}
                    <div className="bg-white rounded-2xl shadow p-6 flex flex-col gap-4">
                        <h2 className="text-lg font-bold text-cf-primary mb-2">Update Password</h2>
                        <p className="text-xs text-gray-500 mb-2">Update your account password using a strong, random password to stay secure.</p>
                        <form className="space-y-4">
                            <Input label="Current Password" name="current_password" type="password" value="" disabled />
                            <Input label="New Password" name="new_password" type="password" value="" disabled />
                            <Input label="Confirm Password" name="confirm_password" type="password" value="" disabled />
                            <div className="flex justify-end">
                                <Button type="button" className="mt-2" disabled>Save</Button>
                            </div>
                        </form>
                    </div>
                    {/* Update Business Account */}
                    <div className="bg-white rounded-2xl shadow p-6 flex flex-col gap-4 col-span-1">
                        <h2 className="text-lg font-bold text-cf-primary mb-2">Update Business Account</h2>
                        <p className="text-xs text-gray-500 mb-2">Update your company information and company address.</p>
                        <form className="space-y-4">
                            <Input label="User Agent" name="user_agent" value="live:herel" readOnly />
                            <Input label="Phone Number" name="phone" value="**********" readOnly />
                            <Input label="Website URL" name="website" value="www.site.com" readOnly />
                            <Input label="Company Address" name="company_address" value="12370 Ave Av, Norwick" readOnly />
                            <Input label="City" name="city" value="Norwick" readOnly />
                            <Input label="State" name="state" value="CA" readOnly />
                            <Input label="Select Country" name="country" value="UNITED STATES" readOnly />
                            <Input label="Zip Code" name="zip" value="56915" readOnly />
                            <div className="flex justify-end">
                                <Button type="button" className="mt-2" disabled>Save</Button>
                            </div>
                        </form>
                    </div>
                    {/* OAuth Details */}
                    <div className="bg-white rounded-2xl shadow p-6 flex flex-col gap-4 col-span-1">
                        <h2 className="text-lg font-bold text-cf-primary mb-2">OAuth Details</h2>
                        <p className="text-xs text-gray-500 mb-2">Make sure your details to configure login with OAuth.</p>
                        <form className="space-y-4">
                            <Input label="Client Id" name="client_id" value="vendor_client" readOnly />
                            <Input label="Secret Id" name="secret_id" value="vendor_secret" readOnly />
                            <Input label="Redirect URL" name="redirect_url" value="https://vendor.websoftify.com/callback.php" readOnly />
                            <div className="flex justify-end">
                                <Button type="button" className="mt-2" disabled>Save</Button>
                            </div>
                        </form>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">OAuth Login Script</label>
                            <textarea className="w-full bg-gray-50 border border-gray-200 rounded-lg p-2 text-xs font-mono resize-none" rows={6} readOnly>{`<div data-site="site-login"
data-client-id="vendor_client"
data-secret-id="vendor_secret"
data-redirect-url="https://vendor.websoftify.com/callback.php"
data-type="login"
data-theme="light"
data-lang="en"
data-width="300"
data-height="400"
data-title="Login with OAuth"
data-logo="/logo.png"
></div>`}</textarea>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Chatbot Script</label>
                            <textarea className="w-full bg-gray-50 border border-gray-200 rounded-lg p-2 text-xs font-mono resize-none" rows={6} readOnly>{`<script src="https://cdn.chatbot.com/widget.js"></script>
<script>
  window.ChatbotWidget.init({
    app_id: '12345',
    position: 'right',
    color: '#337e81',
    greeting: 'Hello! How can I help you?',
    avatar: '/avatar.png',
  });
</script>`}</textarea>
                        </div>
                    </div>
                </div>
                {/* Delete Account */}
                <div className="bg-white rounded-2xl shadow p-6 mt-6">
                    <h2 className="text-lg font-bold text-red-600 mb-2">Delete Account</h2>
                    <p className="text-xs text-gray-500 mb-4">Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain.</p>
                    <Button type="button" className="border border-red-200 text-red-600 hover:bg-red-50" disabled>Delete Account</Button>
                </div>
            </div>
        </DashboardLayout>
    );
} 