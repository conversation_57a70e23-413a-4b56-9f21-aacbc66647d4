<template>
  <!-- Topbar -->
  <Topbar />
  <div class="flex min-h-[calc(100vh-72px)]">

    <!-- Main Content Area -->
    <div class="flex flex-1">
      <!-- Sidebar -->
      <Sidebar />

      <!-- Page Content -->
      <main class="flex-1 p-6">
        <slot />
      </main>
    </div>
  </div>
</template>

<script setup>
import Sidebar from '@/Components/Sidebar.vue';
import Topbar from '@/Components/Topbar.vue';
</script>

<style scoped>
/* Adjust Topbar to stretch across the full width */
.Topbar {
  width: 100%;
}

/* Adjust Main Content Area to take the remaining width */
.MainContent {
  flex: 1;
}
</style>