import React from 'react';
import clsx from 'clsx';
import { Card } from './Card';

export const DataTable = ({ children, className = '', ...props }) => {
    return (
        <Card padding="p-0" className={clsx('overflow-hidden', className)} {...props}>
            <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                    {children}
                </table>
            </div>
        </Card>
    );
};

export const DataTableHeader = ({ children, className = '', ...props }) => {
    return (
        <thead className={clsx('bg-gray-50', className)} {...props}>
            {children}
        </thead>
    );
};

export const DataTableBody = ({ children, className = '', ...props }) => {
    return (
        <tbody className={clsx('bg-white divide-y divide-gray-200', className)} {...props}>
            {children}
        </tbody>
    );
};

export const DataTableRow = ({ children, className = '', hover = true, ...props }) => {
    return (
        <tr 
            className={clsx(
                hover && 'hover:bg-gray-50 transition-colors duration-150',
                className
            )} 
            {...props}
        >
            {children}
        </tr>
    );
};

export const DataTableHeaderCell = ({ children, className = '', ...props }) => {
    return (
        <th 
            className={clsx(
                'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',
                className
            )}
            {...props}
        >
            {children}
        </th>
    );
};

export const DataTableCell = ({ children, className = '', ...props }) => {
    return (
        <td 
            className={clsx(
                'px-6 py-4 whitespace-nowrap text-sm text-gray-900',
                className
            )}
            {...props}
        >
            {children}
        </td>
    );
};

export const DataTableActions = ({ children, className = '', ...props }) => {
    return (
        <td
            className={clsx(
                'px-6 py-4 whitespace-nowrap text-right text-sm font-medium',
                className
            )}
            {...props}
        >
            <div className="flex items-center justify-end gap-1">
                {children}
            </div>
        </td>
    );
};
