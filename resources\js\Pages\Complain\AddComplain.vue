<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, useForm, usePage } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import TextInput from '@/Components/TextInput.vue';
import TextAreaInput from '@/Components/TextAreaInput.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import DefaultCard from '@/Components/Forms/DefaultCard.vue';
import SelectInput from '@/Components/SelectInput.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';

const pageTitle = ref('Add Complaint')
const { companyList } = usePage().props;
var baseurl = window.location.origin;

const form = useForm({
  company: '',
  complain_title: '',
  complain_description: '',
  priority: '0',
  response: '',
  name: '',
  email: '',
  phone: '',
  selectedUserId: '',
});

const resetForm = () => {
  form.company = '';
  form.complain_title = '';
  form.complain_description = '';
  form.priority = '0';
  form.name = '';
  form.email = '';
  form.phone = '';
  form.selectedUserId = '';
};

const submitForm = () => {
  form.post(route('complaints.store'), {
    onSuccess: (response) => {
      if (response.props.flash?.status === true) {
        resetForm();
      }
    },
    onError: (errors) => {
      // Don't reset form on error to preserve user input
    }
  });
};

const loading = ref(false);
const searchQuery = ref('');
const searchResults = ref([]);
const selectedUser = ref('');
const searchInitiated = ref(false);

const searchUser = () => {
  selectedUser.value = '';
  loading.value = true;
  searchInitiated.value = true;

  if(!searchQuery.value){
    searchInitiated.value = false;
  }

  fetch(`/dtadmin/searchauthuser?query=${searchQuery.value}`)
    .then(response => response.json())
    .then(data => {
      searchResults.value = data.users;
      loading.value = false;
    })
    .catch(error => {
      console.error(error);
      loading.value = false;
    });
};

const handleUserClick = (user) => {
  selectedUser.value = user;
  form.selectedUserId = user.id;
  searchQuery.value = '';
  searchResults.value = []; // Reset searchResults
  searchInitiated.value = false; // Reset searchInitiated flag
};

const resetSelected = () => {
  selectedUser.value = '';
  searchQuery.value = '';
  form.selectedUserId = '';
  searchResults.value = [];
};

const hideNewUserAdd = computed(() => {
  return selectedUser.value !== '';
});

</script>

<template>
  <Head :title="pageTitle" />
  <AuthenticatedLayout>
    <form @submit.prevent="submitForm" class="form-main-body">
      <TextInput id="selectedUserId" name="selectedUserId" class="hidden" type="hidden" placeholder="Search User..."
        v-model="form.selectedUserId" />
      <div class="grid gap-6 md:grid-cols-3">
        <div class="md:col-span-2 space-5 border rounded">
          <DefaultCard cardTitle="Submit Your Complaints">
            <div class="px-5 py-6 space-y-4">
              <div v-if="$page.props.auth.user.profileType == 'admin' || $page.props.auth.user.profileType == 'user'"
                class="mt-2 mb-4">
                <InputLabel for="company" value="Select Company" />
                <SelectInput id="company" v-model="form.company" autocomplete="company" name="company">
                  <option value="" disabled selected>Select your company</option>
                  <option v-for="(brand, key) in companyList" :value="brand.id" :key="key">{{ brand.companyName }}
                  </option>
                </SelectInput>
                <InputError class="mt-2" :message="form.errors.company" />
              </div>
              <div class="mt-2">
                <InputLabel for="complain_title" value="Title" />
                <TextInput id="complain_title" name="complain_title" type="text" placeholder="Title"
                  v-model="form.complain_title" />
                <InputError :message="form.errors.complain_title" class="mt-2" />
              </div>
              <div class="space-y-2">
                <InputLabel for="priority" value="Priority" />
                <div class="grid grid-cols-3 gap-3">
                  <label
                    class="flex items-center justify-center p-3 border-2 rounded-lg cursor-pointer transition-all"
                    :class="form.priority == 0 ? 'bg-green-100 text-green-800 border-green-200' : 'border-gray-200 hover:border-gray-300'"
                  >
                    <input type="radio" name="priority" v-model="form.priority" required value="0" checked class="sr-only">
                    <span class="font-medium">Low</span>
                  </label>
                  <label
                    class="flex items-center justify-center p-3 border-2 rounded-lg cursor-pointer transition-all"
                    :class="form.priority == 1 ? 'bg-yellow-100 text-yellow-800 border-yellow-200' : 'border-gray-200 hover:border-gray-300'"
                  >
                    <input type="radio" name="priority" v-model="form.priority" required value="1" class="sr-only">
                    <span class="font-medium">Medium</span>
                  </label>
                  <label
                    class="flex items-center justify-center p-3 border-2 rounded-lg cursor-pointer transition-all"
                    :class="form.priority == 2 ? 'bg-red-100 text-red-800 border-red-200' : 'border-gray-200 hover:border-gray-300'"
                  >
                    <input type="radio" name="priority" v-model="form.priority" required value="2" class="sr-only">
                    <span class="font-medium">High</span>
                  </label>
                </div>
                <InputError class="mt-2" :message="form.errors.priority" />
              </div>
              <div class="mt-2">
                <InputLabel for="complain_description" value="Description" />
                <TextAreaInput id="complain_description" name="complain_description" placeholder="Description"
                  type="text" v-model="form.complain_description" />
                <InputError :message="form.errors.complain_description" class="mt-2" />
              </div>

              <button class="dk-update-btn">Create </button>
              <ResponsiveNavLink :href="route('complaints')" class="dk-cancle-btn">Cancel</ResponsiveNavLink>

              <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
                leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
                <p v-if="$page.props.flash?.message"
                  :class="[
                    'mb-4 p-2 rounded-md',
                    $page.props.flash.status === false 
                      ? 'bg-red-100 text-red-700' 
                      : 'bg-green-100 text-green-700'
                  ]">
                  {{ $page.props.flash.message }}
                </p>
              </Transition>
            </div>
          </DefaultCard>
        </div>

        <div
          v-if="$page.props.auth.user.profileType == 'admin' || $page.props.auth.user.profileType == 'company' || $page.props.auth.user.profileType == 'agent'"
          class="border rounded"
        >
            <div class="px-5 pt-3">
              <div class="relative mt-2">
                <TextInput id="searchname" name="searchname" type="text" placeholder="Search User..."
                  v-model="searchQuery" @keyup="searchUser" autocomplete="off"/>
                <div class="z-10 absolute bg-white shadow-md mt-2 rounded-md w-full max-h-40 overflow-y-auto"
                  v-if="loading">
                  <p class="p-2">Loading...</p>
                </div>
                <div class="z-10 absolute bg-white shadow-md mt-2 rounded-md w-full max-h-40 overflow-y-auto"
                  v-if="!loading && searchInitiated && searchResults.length === 0">
                  <p class="p-2">No records found</p>
                </div>
                <div class="z-10 absolute bg-white shadow-md mt-2 rounded-md w-full max-h-40 overflow-y-auto"
                  v-if="!loading && searchResults.length > 0">
                  <ul>
                    <li v-for="user in searchResults" :key="user.id" @click="handleUserClick(user)"
                      class="hover:bg-gray-100 px-4 py-2 cursor-pointer">
                      <div :data-userid="user.id" class="flex items-center gap-5 hover:bg-gray-3 my-1">
                        <div class="relative rounded-full w-14 h-14">
                          <img v-if="user.profilePicture && user.profilePicture.startsWith('http')"
                            :src="user.profilePicture" alt="User" class="rounded-full" />
                          <img v-else-if="user.profilePicture" :src="baseurl + '/storage/' + user.profilePicture"
                            alt="User" class="rounded-full" />
                          <img v-else src="@/assets/images/user/user-profile.jpeg" alt="User" class="rounded-full" />
                        </div>
                        <div class="flex flex-1 justify-between items-center">
                          <div>
                            <h5 class="font-medium text-slate-800">{{ user.name }}</h5>
                            <p>
                              <span class="text-slate-800 text-sm">{{ user.email }}</span>
                              <span v-if="user.phone !== '0' && user.phone !== '' && user.phone !== null"> | <span class="text-xs">{{
                                  user.phone }}</span></span>
                            </p>
                          </div>
                        </div>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>

              <!-- Search End  -->
              <div class="mt-5 userlist">
                <div class="mt-5 mb-5 selectedUser" v-if="selectedUser">
                  <ul>
                    <li>
                      <div :data-userid="selectedUser.id" class="flex items-center gap-5 hover:bg-gray-3 my-1">
                        <div class="relative rounded-full w-14 h-14">
                          <img v-if="selectedUser.profilePicture && selectedUser.profilePicture.startsWith('http')"
                            :src="selectedUser.profilePicture" alt="User" class="rounded-full" />
                          <img v-else-if="selectedUser.profilePicture"
                            :src="baseurl + '/storage/' + selectedUser.profilePicture" alt="User"
                            class="rounded-full" />
                          <img v-else src="@/assets/images/user/user-profile.jpeg" alt="User" class="rounded-full" />
                        </div>
                        <div class="flex flex-1 justify-between items-center">
                          <div>
                            <h5 class="font-medium text-slate-800">{{ selectedUser.name }}</h5>
                            <p>
                              <span class="text-slate-800 text-sm">{{ selectedUser.email }}</span>
                              <span v-if="selectedUser.phone !== '0' && selectedUser.phone !== ''">
                                | <span class="text-xs">{{ selectedUser.phone }}</span>
                              </span>
                            </p>
                          </div>
                        </div>
                        <div class="flex justify-between text-end">
                          <div>
                            <button type="button" @click="resetSelected()" class="text-red-500">
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="feather feather-x-circle">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="15" y1="9" x2="9" y2="15"></line>
                                <line x1="9" y1="9" x2="15" y2="15"></line>
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                    </li>
                  </ul>
                </div>

                <div class="newUserAdd" :class="{ 'hidden': hideNewUserAdd }">
                  <div class="mt-2">
                    <label class="block mb-2.5 font-medium text-slate-800" for="complain_title">
                      <span>Add New User</span>
                    </label>
                    <TextInput id="full_name" name="full_name" type="text" placeholder="User Name" v-model="form.name"
                      @keyup="resetSelected()" />
                    <InputError :message="form.errors.name" class="mt-2" />
                  </div>

                  <div class="mt-2">
                    <TextInput id="email" name="email" type="email" placeholder="Email" v-model="form.email"
                      @keyup="resetSelected()" />
                    <InputError :message="form.errors.email" class="mt-2" />
                  </div>

                  <div class="mt-2">
                    <TextInput id="uphone" name="uphone" type="tel" placeholder="Phone" v-model="form.phone"
                      @keyup="resetSelected()" />
                    <InputError :message="form.errors.phone" class="mt-2" />
                  </div>
                </div>
              </div>
            </div>
        </div>
      </div>
    </form>
  </AuthenticatedLayout>
</template>
