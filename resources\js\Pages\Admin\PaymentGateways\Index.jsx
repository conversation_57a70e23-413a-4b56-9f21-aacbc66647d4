import React from 'react';
import { Head, Link, router } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import Button from '@/Components/Button';

export default function Index({ gateways }) {
    const handleToggleStatus = (gateway) => {
        router.post(route('admin.payment-gateways.toggle-status', gateway.id), {}, {
            preserveScroll: true,
            onSuccess: () => {
                // Handle success
            }
        });
    };

    const handleToggleTestMode = (gateway) => {
        router.post(route('admin.payment-gateways.toggle-test-mode', gateway.id), {}, {
            preserveScroll: true,
            onSuccess: () => {
                // Handle success
            }
        });
    };

    const handleTestConnection = async (gateway) => {
        try {
            const response = await fetch(route('admin.payment-gateways.test-connection', gateway.id), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
            });

            const data = await response.json();

            if (data.success) {
                alert('Connection successful!');
            } else {
                alert('Connection failed: ' + data.message);
            }
        } catch (error) {
            alert('Connection test failed: ' + error.message);
        }
    };

    const getStatusBadge = (gateway) => {
        if (!gateway.is_configured) {
            return <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">Not Configured</span>;
        }

        if (gateway.is_active) {
            return <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Active</span>;
        }

        return <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">Inactive</span>;
    };

    const getModeBadge = (gateway) => {
        if (gateway.test_mode) {
            return <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">Test Mode</span>;
        }

        return <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">Live Mode</span>;
    };

    return (
        <DashboardLayout>
            <Head title="Payment Gateways" />

            <div className="py-6">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="md:flex md:items-center md:justify-between">
                        <div className="flex-1 min-w-0">
                            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                                Payment Gateways
                            </h2>
                            <p className="mt-1 text-sm text-gray-500">
                                Configure and manage payment gateway settings for your subscription system.
                            </p>
                        </div>
                    </div>

                    <div className="mt-8">
                        <div className="bg-white shadow overflow-hidden sm:rounded-md">
                            <ul className="divide-y divide-gray-200">
                                {gateways.map((gateway) => (
                                    <li key={gateway.id}>
                                        <div className="px-4 py-4 sm:px-6">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center">
                                                    <div className="flex-shrink-0">
                                                        <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                            <span className="text-sm font-medium text-gray-700">
                                                                {gateway.display_name.charAt(0)}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div className="ml-4">
                                                        <div className="flex items-center">
                                                            <p className="text-sm font-medium text-gray-900">
                                                                {gateway.display_name}
                                                            </p>
                                                            <div className="ml-2 flex space-x-2">
                                                                {getStatusBadge(gateway)}
                                                                {getModeBadge(gateway)}
                                                            </div>
                                                        </div>
                                                        <p className="text-sm text-gray-500">
                                                            {gateway.is_configured
                                                                ? 'Gateway is properly configured'
                                                                : `Missing: ${gateway.required_fields.join(', ')}`
                                                            }
                                                        </p>
                                                    </div>
                                                </div>
                                                <div className="flex items-center space-x-2">
                                                    {gateway.is_configured && (
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => handleTestConnection(gateway)}
                                                        >
                                                            Test Connection
                                                        </Button>
                                                    )}

                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => handleToggleTestMode(gateway)}
                                                    >
                                                        {gateway.test_mode ? 'Switch to Live' : 'Switch to Test'}
                                                    </Button>

                                                    {gateway.is_configured && (
                                                        <Button
                                                            variant={gateway.is_active ? "danger" : "primary"}
                                                            size="sm"
                                                            onClick={() => handleToggleStatus(gateway)}
                                                        >
                                                            {gateway.is_active ? 'Deactivate' : 'Activate'}
                                                        </Button>
                                                    )}

                                                    <Link
                                                        href={route('admin.payment-gateways.edit', gateway.id)}
                                                        className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                                    >
                                                        Configure 
                                                    </Link>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>

                    <div className="mt-8 bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-blue-800">
                                    Payment Gateway Information
                                </h3>
                                <div className="mt-2 text-sm text-blue-700">
                                    <ul className="list-disc pl-5 space-y-1">
                                        <li>Configure your payment gateways to enable subscription billing</li>
                                        <li>Test mode allows you to test payments without processing real transactions</li>
                                        <li>Live mode processes real payments - ensure your credentials are correct</li>
                                        <li>Only properly configured gateways can be activated</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    );
}