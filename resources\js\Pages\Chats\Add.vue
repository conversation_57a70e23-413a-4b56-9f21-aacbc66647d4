<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head } from '@inertiajs/vue3';
import { ref, watch } from 'vue';
import InputError from '@/Components/InputError.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SelectInput from '@/Components/SelectInput.vue';
import TextInput from '@/Components/TextInput.vue';
import { usePage, useForm } from '@inertiajs/vue3';
import PageHeading from '@/Components/Global/PageHeading.vue';
import DtIconGlobal from '@/Components/DtIcon/DtIconGlobal.vue';
import { ElTooltip } from 'element-plus'
import 'element-plus/dist/index.css'

const { title, dataInfo } = usePage().props;
const pageTitle = ref(title);

const form = useForm({
  name: '',
  context: '',
  description: '',
  training_phrases: [],  // Added field for training phrases
  responses: []  // Added field for responses
});

const trainingPhrases = ref([]);
const responses = ref([]);

const trainingForm = useForm({
  phrase: ''
});

const responseForm = useForm({
  response: ''
});

const editingPhraseIndex = ref(null);
const editingResponseIndex = ref(null);

const addTrainingPhrase = () => {
  if (!trainingForm.phrase) return;

  // Check if phrase already exists
  const existingIndex = trainingPhrases.value.indexOf(trainingForm.phrase.toLowerCase());

  if (existingIndex !== -1) {
    // If exists, remove it from current position
    trainingPhrases.value.splice(existingIndex, 1);
    // Add it to the beginning
    trainingPhrases.value.unshift(trainingForm.phrase);
  } else {
    // If new, add new phrase at beginning
    trainingPhrases.value.unshift(trainingForm.phrase);
  }

  form.training_phrases = trainingPhrases.value;  // Update form data
  trainingForm.phrase = '';
};

const addResponseText = () => {
  if (!responseForm.response) return;

  // Check if response already exists
  const existingIndex = responses.value.indexOf(responseForm.response.toLowerCase());

  if (existingIndex !== -1) {
    // If exists, remove it from current position
    responses.value.splice(existingIndex, 1);
    // Add it to the beginning
    responses.value.unshift(responseForm.response);
  } else {
    // If new, add new response at beginning
    responses.value.unshift(responseForm.response);
  }

  form.responses = responses.value;  // Update form data
  responseForm.response = '';
};

const processInput = (value, fieldName) => {
  // Convert to lowercase and replace spaces with underscores
  let processed = value.toLowerCase().replace(/\s+/g, '_');
  // Remove special characters except underscores
  processed = processed.replace(/[^a-z0-9_]/g, '');
  // Update the form field
  form[fieldName] = processed;
};

const deleteTrainingPhrase = (phrase) => {
  const index = trainingPhrases.value.indexOf(phrase);
  if (index > -1) {
    trainingPhrases.value.splice(index, 1);
    form.training_phrases = trainingPhrases.value;  // Update form data
  }
};

const deleteResponseText = (response) => {
  const index = responses.value.indexOf(response);
  if (index > -1) {
    responses.value.splice(index, 1);
    form.responses = responses.value;  // Update form data
  }
};

const suggestions = ref([]); // Array to store the search results

watch(() => responseForm.response, async (newValue) => {
  const match = newValue.match(/@[\w.-]+$/); // Match the last word starting with "@"

  if (match) {
    await searchApi(match[0]); // Trigger API call
  } else {
    suggestions.value = []; // Clear suggestions if no match
  }
});

const searchApi = async (query) => {
  try {
    // Make API call with search query
    const response = await axios.get(route('vendorapi.search'), {
      params: { search: query },
    });

    // Process response data
    const exactMatches = [];
    const partialMatches = [];
    const [queryName, queryParam] = query.split('.');

    response.data.forEach(item => {
      const responseParams = JSON.parse(item.response_parameters || '[]'); // Parse JSON
      responseParams.forEach(paramObj => {
        const fullParam = `${item.name}.${paramObj.param}`;
        if (queryName === item.name && queryParam === paramObj.param) {
          exactMatches.push(fullParam); // Exact match
        } else if (fullParam.includes(query)) {
          partialMatches.push(fullParam); // Partial match
        }
      });
    });

    // Combine exact and partial matches
    suggestions.value = [...exactMatches, ...partialMatches];

  } catch (error) {
    console.error('API call failed:', error);
  }
};

// Select suggestion from the list
const selectSuggestion = (suggestion) => {
  const inputElement = document.getElementById('response');

  if (inputElement) {
    const cursorPosition = inputElement.selectionStart; // Get the current cursor position
    const textBeforeCursor = responseForm.response.substring(0, cursorPosition);
    const textAfterCursor = responseForm.response.substring(cursorPosition);

    // Find the last "@" mention in the text before the cursor
    const lastAtIndex = textBeforeCursor.lastIndexOf('@');

    if (lastAtIndex !== -1) {
      // Replace the text starting from the last "@" with the selected suggestion
      const beforeAtText = textBeforeCursor.substring(0, lastAtIndex);
      responseForm.response = beforeAtText + suggestion + ' ' + textAfterCursor; // Add a space after the suggestion

      // Update the cursor position after the newly added suggestion
      const newCursorPosition = beforeAtText.length + suggestion.length + 1;
      setTimeout(() => {
        inputElement.setSelectionRange(newCursorPosition, newCursorPosition);
        inputElement.focus(); // Ensure the input regains focus
      }, 0);
    } else {
      // Fallback if no "@" is found: append suggestion at the cursor position
      responseForm.response = textBeforeCursor + suggestion + ' ' + textAfterCursor;

      const newCursorPosition = cursorPosition + suggestion.length + 1;
      setTimeout(() => {
        inputElement.setSelectionRange(newCursorPosition, newCursorPosition);
        inputElement.focus();
      }, 0);
    }
  }

  suggestions.value = []; // Clear the suggestion list after selection
};

const sanitizeInput = () => {
  // Allow only letters (a-z, A-Z), numbers (0-9), spaces, and the characters . ? !
  trainingForm.phrase = trainingForm.phrase.replace(/[^a-zA-Z0-9 .?!]/g, '');
};

// Add this new function to reset everything
const resetForm = () => {
  form.reset();
  trainingPhrases.value = [];
  responses.value = [];
  trainingForm.phrase = '';
  responseForm.response = '';
};



// Function to start editing a training phrase
const startEditingPhrase = (index) => {
    editingPhraseIndex.value = index;
};

// Function to save the edited training phrase
const saveEditedPhrase = (index) => {
    editingPhraseIndex.value = null; // Reset editing index
};

// Function to start editing a response
const startEditingResponse = (index) => {
    editingResponseIndex.value = index;
};

// Function to save the edited response
const saveEditedResponse = (index) => {
    editingResponseIndex.value = null; // Reset editing index
};

// Modify your form submit handler
const submit = () => {

addTrainingPhrase()
addResponseText()
form.post(route('intents.store'), {
  onSuccess: () => {
    resetForm();
  },
});
};

</script>

<style scoped>
/* Style the suggestion box to prevent overlap */
.relative {
  position: relative;
}

.bg-white {
  background-color: white;
}

.shadow-lg {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.rounded-md {
  border-radius: 8px;
}

.z-10 {
  z-index: 10;
}

.left-0 {
  left: 0;
}

.top-full {
  top: 100%;
}

.cursor-pointer {
  cursor: pointer;
}

.hover\:bg-gray-200:hover {
  background-color: #f7fafc;
}

.p-2 {
  padding: 8px;
}

.title-tooltip {
    margin-left: 5px;
    color: #666;
    cursor: help;
}

.title-tooltip:hover {
    color: #333;
}
</style>

<template>

  <Head :title="pageTitle" />

  <AuthenticatedLayout>
    <!-- <PageHeading :title="pageTitle"></PageHeading> -->

    <div class=" form-main-body ">
      <form @submit.prevent="submit" class="space-y-6 ">

        <!-- Success Message -->
        <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
          leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
          <p v-if="form.recentlySuccessful" :class="$page.props.flash?.class">{{ $page.props.flash.message }}</p>
        </Transition>

        <!-- Intent Name -->
        <div class="border-gray-200 p-5 border rounded-md">
          <div class="space-y-4">
            <div>
              <div class="flex items-center">
                <InputLabel for="name" value="Intent Name" />
                <el-tooltip
                  v-if="dataInfo.intent_name"
                  class="ml-2"
                  effect="dark"
                  placement="top"
                  :content="dataInfo.intent_name"
                  :show-after="100"
                >
                  <i class="fa fa-question-circle text-gray-400 hover:text-gray-600 transition-colors mx-2"></i>
                </el-tooltip>
              </div>
              
              <TextInput id="name" name="name" type="text"
                class="border-stroke focus:border-cfp-500 bg-transparent focus-visible:shadow-none mt-2 px-4 py-2 border rounded-md w-full text-slate-800 outline-none"
                v-model="form.name" @blur="(e) => processInput(e.target.value, 'name')" />
              <InputError :message="form.errors.name" class="mt-2" />
            </div>

            <!-- Context -->
            <div>
              <div class="flex items-center">
                <InputLabel for="context" value="Context" />
                <el-tooltip
                  v-if="dataInfo.intent_context"
                  class="ml-2"
                  effect="dark"
                  placement="top"
                  :content="dataInfo.intent_context"
                  :show-after="100"
                ><i class="fa fa-question-circle text-gray-400 hover:text-gray-600 transition-colors mx-2"></i>
                </el-tooltip>
              </div>
              
              <TextInput id="context" name="context" type="text"
                class="border-stroke focus:border-cfp-500 bg-transparent focus-visible:shadow-none mt-2 px-4 py-2 border rounded-md w-full text-slate-800 outline-none"
                v-model="form.context" @blur="(e) => processInput(e.target.value, 'context')" />
              <InputError :message="form.errors.context" class="mt-2" />
            </div>

            <!-- Description -->
            <div>
              <div class="flex items-center">
                <InputLabel for="description" value="Description" />
                <el-tooltip
                  v-if="dataInfo.intent_description"
                  class="ml-2"
                  effect="dark"
                  placement="top"
                  :content="dataInfo.intent_description"
                  :show-after="100"
                >
                  <i class="fa fa-question-circle text-gray-400 hover:text-gray-600 transition-colors mx-2"></i>
                </el-tooltip>
              </div>
              <TextInput id="description" name="description" type="text"
                class="border-stroke focus:border-cfp-500 bg-transparent focus-visible:shadow-none mt-2 px-4 py-2 border rounded-md w-full text-slate-800 outline-none"
                v-model="form.description" />
              <InputError :message="form.errors.description" class="mt-2" />
            </div>
          </div>
        </div>

        <!-- Training Phrases Section -->
        <div class="border-gray-200 border rounded-md ">
            <h2 class="mb-2 font-medium px-4 py-2 text-lg border-b">Training Phrases<el-tooltip
              v-if="dataInfo.intent_training_phrases"
              class="ml-2"
              effect="dark"
              placement="top"
              :content="dataInfo.intent_training_phrases"
              :show-after="100"
            >
              <i class="fa fa-question-circle text-gray-400 hover:text-gray-600 transition-colors mx-2"></i>
            </el-tooltip></h2>

          <form @submit.prevent="addTrainingPhrase" class="mb-2 px-4 py-2">
            <div class="flex space-x-4">
              <TextInput id="phrase" type="text" placeholder="Add a user expression"
                class="flex-1 border-stroke focus:border-cfp-500 bg-transparent focus-visible:shadow-none px-4 py-2 border rounded-md w-full text-slate-800 outline-none"
                v-model="trainingForm.phrase" @input="sanitizeInput"/>
            </div>
            <InputError :message="form.errors.training_phrases" class="mt-2" />
          </form>

          <div class="px-4 mb-4">
            <div :class="{ 'border rounded-lg': trainingPhrases.length > 0 }">
              <div v-for="(phrase, index) in trainingPhrases" :key="index"
                class="flex justify-between items-center px-4 py-2"
                :class="{ 'border-b': index !== trainingPhrases.length - 1 }">
                <span v-if="editingPhraseIndex !== index" class="text-gray-800" @click="startEditingPhrase(index)">
                  {{ phrase }}
                </span>
                <TextInput v-else
                  v-model="trainingPhrases[index]"
                  @blur="saveEditedPhrase(index)"
                  class="text-gray-800"
                  @keyup.enter="saveEditedPhrase(index)" />
                <button type="button" @click="deleteTrainingPhrase(phrase)" class="hover:text-red-800">
                  <DtIconGlobal :type="'delete'" />
                </button>
              </div>
            </div>
          </div>

        </div>

        <!-- Response Texts Section -->
        <div class="border-gray-200 border rounded-md ">
            <h2 class="mb-2 font-medium px-4 py-2 text-lg border-b">Response Phrases<el-tooltip
              v-if="dataInfo.intent_responses"
              class="ml-2"
              effect="dark"
              placement="top"
              :content="dataInfo.intent_responses"
              :show-after="100"
            >
              <i class="fa fa-question-circle text-gray-400 hover:text-gray-600 transition-colors mx-2"></i>
            </el-tooltip></h2>
            

          <form @submit.prevent="addResponseText" class="mb-2 px-4 py-2">
            <div class="flex space-x-4 relative">
              <TextInput id="response" type="text" placeholder="Add response text"
                class="flex-1 border-stroke focus:border-cfp-500 bg-transparent focus-visible:shadow-none px-4 py-2 border rounded-md w-full text-slate-800 outline-none"
                v-model="responseForm.response" autocomplete="off" />
                <!-- Show suggestions if available -->
                <div v-if="suggestions.length > 0"
                    class="mt-2 bg-white shadow-lg border rounded-md w-full absolute z-10 left-0 top-full">
                    <ul class="list-none p-2">
                        <li v-for="(suggestion, index) in suggestions" :key="index"
                            @click="selectSuggestion(suggestion)"
                            class="p-2 hover:bg-gray-200 cursor-pointer">
                            {{ suggestion }}
                        </li>
                    </ul>
                </div>
            </div>
            <InputError :message="form.errors.responses" class="mt-2" />
          </form>

          <div class="px-4 mb-4">
            <div :class="{ 'border rounded-lg': responses.length > 0 }">
              <div v-for="(response, index) in responses" :key="index"
                class="flex justify-between items-center px-4 py-2"
                :class="{ 'border-b': index !== responses.length - 1 }">
                <span v-if="editingResponseIndex !== index" class="text-gray-800" @click="startEditingResponse(index)">
                  {{ response }}
                </span>
                <TextInput v-else
                  v-model="responses[index]"
                  @blur="saveEditedResponse(index)"
                  class="text-gray-800"
                  @keyup.enter="saveEditedResponse(index)" />
                <button type="button" @click="deleteResponseText(response)" class="hover:text-red-800">
                  <DtIconGlobal :type="'delete'" />
                </button>
              </div>
            </div>
          </div>

        </div>

        <!-- Submit Button -->
        <div class="flex justify-start items-center gap-4">
          <PrimaryButton :disabled="form.processing">
            <span v-if="form.processing">Loading...</span>
            <span v-else>Save</span>
          </PrimaryButton>

          <div class="relative">
            <ResponsiveNavLink :href="route('intents.index')" class="dk-cancle-btn">Back</ResponsiveNavLink>
          </div>
        </div>

      </form>
    </div>
  </AuthenticatedLayout>
</template>