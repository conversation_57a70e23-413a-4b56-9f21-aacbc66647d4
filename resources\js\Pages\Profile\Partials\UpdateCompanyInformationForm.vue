<script setup>
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import FileInput from '@/Components/FileInput.vue';
import SelectInput from '@/Components/SelectInput.vue';
import { ref, onMounted } from 'vue';
import { Link, useForm, usePage } from '@inertiajs/vue3';

defineProps({
    mustVerifyEmail: {
        type: Boolean,
    },
    status: {
        type: String,
    },
});


const user = usePage().props.auth.user;
const { categoryList, countryList, companyDetail, userDetail } = usePage().props;
const ViewProfiledata = ref('');

const form = useForm({
    name: userDetail.name,
    email: userDetail.email,
    phone: userDetail.phone,
    websiteUrl: (companyDetail) ? companyDetail.websiteUrl : '', // Added the second expression ''
    city: (companyDetail) ? companyDetail.city : '',
    state: (companyDetail) ? companyDetail.state : '',
    zipCode: (companyDetail) ? companyDetail.zipCode : '',
    countryId: (companyDetail) ? companyDetail.countryId : '',
    chatSupport: (companyDetail) ? companyDetail.chatSupport : '',
    catId: (companyDetail) ? companyDetail.catId : '',
    companyAdd: (companyDetail) ? companyDetail.companyAdd : '',
    profilePicture: null,
    companyProfile: userDetail.profilePicture
});

ViewProfiledata.value = userDetail.profilePicture;



const onFileChange = (event) => {
    const file = event.target.files[0];
    form.profilePicture = file;
};

onMounted(() => {
    document.getElementById('profilePicture').value = '';
    form.profilePicture = null;
    form.companyProfile = userDetail.profilePicture;
    ViewProfiledata.value = userDetail.profilePicture;
});


var baseurl = window.location.origin;

let chatsupportList = ['chatbot','livechat','both']


</script>

<template>
    <section>
        <header>
            <h2 v-if="companyDetail" class="font-medium text-gray-900 text-lg">Update Business Account</h2>
            <h2 v-else class="font-medium text-gray-900 text-lg">Complete Business Account</h2>

            <p class="mt-1 text-gray-600 text-sm">
                Save your company information and company address.
            </p>
        </header>

       
        <form @submit.prevent="form.post(route('company.update'))" class="space-y-6 mt-6" enctype="multipart/form-data">

            <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
            leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
            <p v-if="form.recentlySuccessful" enter-active-class="transition ease-in-out"
                enter-from-class="opacity-0" leave-active-class="transition ease-in-out"
                leave-to-class="opacity-0" :class="$page.props.flash?.class">
                {{ $page.props.flash.message }}</p>
        </Transition>

            <!-- <div>
                <InputLabel for="profilePicture" value="Upload Company logo" />

                <FileInput id="profilePicture" type="file" class="block mt-1 w-full" v-model="form.profilePicture"
                    @input="onFileChange" autocomplete="profilePicture" />

                <InputError class="mt-2" :message="form.errors.profilePicture" />
            </div>

            <div v-if="ViewProfiledata != null">
                <img v-if="ViewProfiledata && ViewProfiledata.startsWith('http')" :src="ViewProfiledata" alt="User"
                    class="rounded-full w-15 h-15" />
                <img v-else-if="ViewProfiledata" :src="baseurl + '/storage/' + ViewProfiledata" alt="User"
                    class="rounded-full w-15 h-15" />
                <img v-else src="@/assets/images/user/user-profile.jpeg" alt="User" class="rounded-full w-15 h-15" />
            </div> -->

            <!-- <div>
                <InputLabel for="category" value="Select Category" />

                <SelectInput id="category" class="block mt-1 w-full" v-model="form.catId" required
                    autocomplete="category" name="catId">
                    <option v-for="(option, index) in categoryList" :key="index" :value="option.catId">
                        {{ option.catName }}
                    </option>
                </SelectInput>


                <InputError class="mt-2" :message="form.errors.category" />
            </div> -->

            <div>
                <InputLabel for="chatsupport" value="Chat support" />

                <SelectInput id="chatSupport" class="block mt-1 w-full" v-model="form.chatSupport" required
                    autocomplete="chatSupport" name="chatSupport">
                    <option v-for="(option, index) in chatsupportList" :key="index" :value="option">
                        {{ option }}
                    </option>
                </SelectInput>

                <InputError class="mt-2" :message="form.errors.chatsupport" />
            </div>


            <div>
                <InputLabel for="phone" value="Phone Number" />

                <TextInput id="phone" type="text" class="block mt-1 w-full" v-model="form.phone" required
                    autocomplete="phone" />

                <InputError class="mt-2" :message="form.errors.phone" />
            </div>

            <div>
                <InputLabel for="url" value="Website URL" />

                <TextInput id="url" type="text" class="block mt-1 w-full" v-model="form.websiteUrl" required
                    autocomplete="url" />

                <InputError class="mt-2" :message="form.errors.url" />
            </div>

            <div>
                <InputLabel for="address" value="Company Address" />

                <TextInput id="address" type="text" class="block mt-1 w-full" v-model="form.companyAdd" required
                    autocomplete="address" />

                <InputError class="mt-2" :message="form.errors.address" />
            </div>

            <div>
                <InputLabel for="city" value="City" />

                <TextInput id="city" type="text" class="block mt-1 w-full" v-model="form.city" required
                    autocomplete="city" />

                <InputError class="mt-2" :message="form.errors.city" />
            </div>

            <div>
                <InputLabel for="state" value="State" />

                <TextInput id="state" type="text" class="block mt-1 w-full" v-model="form.state" required
                    autocomplete="state" />

                <InputError class="mt-2" :message="form.errors.state" />
            </div>

            <div>
                <InputLabel for="country" value="Select country" />

                <SelectInput id="country" class="block mt-1 w-full" v-model="form.countryId" required
                    autocomplete="country" name="countryId">
                    <option v-for="(option, index) in countryList" :key="index" :value="option.id">
                        {{ option.name }}
                    </option>
                </SelectInput>


                <InputError class="mt-2" :message="form.errors.category" />
            </div>


            <div>
                <InputLabel for="zipcode" value="Zip Code" />

                <TextInput id="zipcode" type="text" class="block mt-1 w-full" v-model="form.zipCode" required
                    autocomplete="zipcode" />

                <InputError class="mt-2" :message="form.errors.zipcode" />
            </div>

            <!-- <div v-if="mustVerifyEmail && user.email_verified_at === null">
                <p class="mt-2 text-slate-800 text-sm">
                    Your email address is unverified.
                    <Link :href="route('verification.send')" method="post" as="button"
                        class="rounded-md focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 text-gray-600 text-sm hover:text-gray-900 underline focus:outline-none">
                    Click here to re-send the verification email.
                    </Link>
                </p>

                <div v-show="status === 'verification-link-sent'" class="mt-2 font-medium text-green-600 text-sm">
                    A new verification link has been sent to your email address.
                </div>
            </div> -->

            <div class="flex items-center gap-4">
                <PrimaryButton :disabled="form.processing">Save</PrimaryButton>

            </div>
        </form>
    </section>
</template>
