import React from 'react';
import clsx from 'clsx';

const statusVariants = {
    success: 'bg-green-100 text-green-800 border-green-200',
    warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    error: 'bg-red-100 text-red-800 border-red-200',
    info: 'bg-blue-100 text-blue-800 border-blue-200',
    primary: 'bg-cf-primary-100 text-cf-primary-800 border-cf-primary-200',
    secondary: 'bg-gray-100 text-gray-800 border-gray-200',
};

const priorityVariants = {
    high: 'bg-red-100 text-red-800 border-red-200',
    medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    low: 'bg-green-100 text-green-800 border-green-200',
};

export const StatusBadge = ({ 
    children, 
    variant = 'secondary', 
    priority,
    size = 'sm',
    className = '', 
    ...props 
}) => {
    const variantClass = priority ? priorityVariants[priority.toLowerCase()] : statusVariants[variant];
    
    return (
        <span 
            className={clsx(
                'inline-flex items-center border rounded-full font-medium',
                size === 'xs' && 'px-2 py-0.5 text-xs',
                size === 'sm' && 'px-2.5 py-1 text-xs',
                size === 'md' && 'px-3 py-1.5 text-sm',
                variantClass,
                className
            )}
            {...props}
        >
            {children}
        </span>
    );
};

export const PriorityBadge = ({ priority, ...props }) => {
    return (
        <StatusBadge priority={priority} {...props}>
            {priority}
        </StatusBadge>
    );
};
