<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, Link, useForm, usePage } from '@inertiajs/vue3';
import { ref, computed, onMounted } from 'vue';
import TextInput from '@/Components/TextInput.vue';
import TextAreaInput from '@/Components/TextAreaInput.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import DefaultCard from '@/Components/Forms/DefaultCard.vue';
import SelectInput from '@/Components/SelectInput.vue';
import FileInput from '@/Components/FileInput.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import { Inertia } from '@inertiajs/inertia';
import BreadcrumbDefault from '@/Components/Breadcrumbs/BreadcrumbDefault.vue';
import PageHeading from '@/Components/Global/PageHeading.vue';

const pageTitle = ref('Edit Company')


const { categoryList, countryList, companyDetail, userDetail } = usePage().props;
var baseurl = window.location.origin;
const ViewProfiledata = ref('');

const form = useForm({
    company_id: companyDetail.id,
    name: userDetail.name,
    email: userDetail.email,
    phone: userDetail.phone,
    websiteUrl: (companyDetail) ? companyDetail.websiteUrl : '', // Added the second expression ''
    city: (companyDetail) ? companyDetail.city : '',
    state: (companyDetail) ? companyDetail.state : '',
    zipCode: (companyDetail) ? companyDetail.zipCode : '',
    countryId: (companyDetail) ? companyDetail.countryId : '',
    catId: (companyDetail) ? companyDetail.catId : '',
    companyAdd: (companyDetail) ? companyDetail.companyAdd : '',
    profilePicture: null,
    companyProfile: userDetail.profilePicture
});

ViewProfiledata.value = userDetail.profilePicture;

const onFileChange = (event) => {
    const file = event.target.files[0];
    form.profilePicture = file;
};


const resetForm = () => {

    // Clear file input by replacing it with a new one
    const fileInput = document.getElementById('profilePicture');
    fileInput.value = ''; // Clear the value for better browser compatibility
    const newFileInput = document.createElement('input');
    newFileInput.type = 'file';
    newFileInput.id = 'profilePicture';
    newFileInput.classList.add('mt-1', 'block', 'w-full');
    newFileInput.addEventListener('change', onFileChange); // Reattach the change event listener
    fileInput.parentNode.replaceChild(newFileInput, fileInput);
};

onMounted(() => {
    document.getElementById('profilePicture').value = '';
    form.profilePicture = null;
    form.companyProfile = userDetail.profilePicture;
    ViewProfiledata.value = userDetail.profilePicture;
});


const submitForm = () => {
    form.post(route('companies.save'), {
        onSuccess: (response) => {
            resetForm();
            ViewProfiledata.value = response.props.userDetail.profilePicture;

        },
        onError: (errors) => {
        }
    });
};


</script>

<template>

    <Head :title="pageTitle" />

    <AuthenticatedLayout>


        <PageHeading :title="pageTitle"></PageHeading>


        <form @submit.prevent="submitForm" enctype="multipart/form-data" class="form-main-body">

            <TextInput id="company_id" name="company_id" class="hidden" type="hidden" v-model="form.company_id" />

            <div class="add-form-main-bcls">

                <div class="flex flex-col gap-4 col-span-12 pr-5 xl:pb-1">


                    <!-- Contact Form Start -->
                    <DefaultCard cardTitle=" Edit Company">


                        <div class="p-6">

                            <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
                                leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
                                <p v-if="form.recentlySuccessful" enter-active-class="transition ease-in-out"
                                    enter-from-class="opacity-0" leave-active-class="transition ease-in-out"
                                    leave-to-class="opacity-0" :class="$page.props.flash?.class">
                                    {{ $page.props.flash.message }}</p>
                            </Transition>

                            <div class="mb-3">
                                <InputLabel for="profilePicture" value="Upload Company logo" />
                                <FileInput id="profilePicture" type="file" class="block mt-1 w-full"
                                    v-model="form.profilePicture" @input="onFileChange" autocomplete="profilePicture" />
                                <InputError class="mt-2" :message="form.errors.profilePicture" />
                            </div>

                            <div class="mb-3" v-if="ViewProfiledata != null">
                                <img v-if="ViewProfiledata && ViewProfiledata.startsWith('http')" :src="ViewProfiledata"
                                    alt="User" class="rounded-full w-15 h-15" />
                                <img v-else-if="ViewProfiledata" :src="baseurl + '/storage/' + ViewProfiledata"
                                    alt="User" class="rounded-full w-15 h-15" />
                                <img v-else src="@/assets/images/user/user-profile.jpeg" alt="User"
                                    class="rounded-full w-15 h-15" />
                            </div>

                            <div class="mb-3">
                                <InputLabel for="name" value="Name" />

                                <TextInput id="name" type="text" class="block mt-1 w-full" v-model="form.name" required
                                    autofocus autocomplete="name" />

                                <InputError class="mt-2" :message="form.errors.name" />
                            </div>

                            <div class="mb-3">
                                <InputLabel for="email" value="Email" />

                                <TextInput id="email" type="email" class="block mt-1 w-full" v-model="form.email"
                                    required autocomplete="username" />

                                <InputError class="mt-2" :message="form.errors.email" />
                            </div>


                            <div class="mb-3">
                                <InputLabel for="category" value="Select Category" />
                                <SelectInput id="category" class="block mt-1 w-full" v-model="form.catId" required
                                    autocomplete="category" name="catId">
                                    <option v-for="(option, index) in categoryList" :key="index" :value="option.catId">
                                        {{ option.catName }}
                                    </option>
                                </SelectInput>
                                <InputError class="mt-2" :message="form.errors.category" />
                            </div>



                            <div class="mb-3">
                                <InputLabel for="phone" value="Phone Number" />

                                <TextInput id="phone" type="text" class="block mt-1 w-full" v-model="form.phone"
                                    required autocomplete="phone" />

                                <InputError class="mt-2" :message="form.errors.phone" />
                            </div>

                            <div class="mb-3">
                                <InputLabel for="url" value="Website URL" />

                                <TextInput id="url" type="text" class="block mt-1 w-full" v-model="form.websiteUrl"
                                    required autocomplete="url" />

                                <InputError class="mt-2" :message="form.errors.url" />
                            </div>

                            <div class="mb-3">
                                <InputLabel for="address" value="Company Address" />

                                <TextInput id="address" type="text" class="block mt-1 w-full" v-model="form.companyAdd"
                                    required autocomplete="address" />

                                <InputError class="mt-2" :message="form.errors.address" />
                            </div>

                            <div class="mb-3">
                                <InputLabel for="city" value="City" />

                                <TextInput id="city" type="text" class="block mt-1 w-full" v-model="form.city" required
                                    autocomplete="city" />

                                <InputError class="mt-2" :message="form.errors.city" />
                            </div>

                            <div class="mb-3">
                                <InputLabel for="state" value="State" />

                                <TextInput id="state" type="text" class="block mt-1 w-full" v-model="form.state"
                                    required autocomplete="state" />

                                <InputError class="mt-2" :message="form.errors.state" />
                            </div>

                            <div class="mb-3">
                                <InputLabel for="country" value="Select country" />

                                <SelectInput id="country" class="block mt-1 w-full" v-model="form.countryId" required
                                    autocomplete="country" name="countryId">
                                    <option v-for="(option, index) in countryList" :key="index" :value="option.id">
                                        {{ option.name }}
                                    </option>
                                </SelectInput>


                                <InputError class="mt-2" :message="form.errors.category" />
                            </div>


                            <div class="mb-3">
                                <InputLabel for="zipcode" value="Zip Code" />

                                <TextInput id="zipcode" type="text" class="block mt-1 w-full" v-model="form.zipCode"
                                    required autocomplete="zipcode" />

                                <InputError class="mt-2" :message="form.errors.zipcode" />
                            </div>

                            <button class="dk-update-btn">
                                Update </button>

                            <ResponsiveNavLink :href="route('companies')" class="dk-cancle-btn">
                                Cancel
                            </ResponsiveNavLink>



                            <!-- <transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
                                leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
                                <p v-if="$page.props.flash?.message" :class="$page.props.flash?.class">{{
                                    $page.props.flash.message }}</p>
                            </transition> -->

                        </div>

                    </DefaultCard>

                    <!-- Contact Form End -->
                </div>

            </div>
        </form>

    </AuthenticatedLayout>
</template>
