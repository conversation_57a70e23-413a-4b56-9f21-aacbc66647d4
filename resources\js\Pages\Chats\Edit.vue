<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import InputError from '@/Components/InputError.vue';
import SelectInput from '@/Components/SelectInput.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import { ref, watch } from 'vue';
import { Head, usePage, useForm } from '@inertiajs/vue3';
import PageHeading from '@/Components/Global/PageHeading.vue';
import DtIconGlobal from '@/Components/DtIcon/DtIconGlobal.vue';
import { ElTooltip } from 'element-plus'
import 'element-plus/dist/index.css'

const { title, intent, flash, dataInfo } = usePage().props;

const pageTitle = ref(title);
const showSuccess = ref(false);

// const trainingPhrases = ref(intent.training_phrases ? JSON.parse(intent.training_phrases) : []);
// const responses = ref(intent.responses ? JSON.parse(intent.responses) : []);

const trainingPhrases = ref(
    intent.training_phrases ? intent.training_phrases.split(',').map(item => item.trim()) : []
);
const responses = ref(
    intent.responses ? intent.responses.split(',').map(item => item.trim()) : []
);


const form = useForm({
    name: intent.name || '',
    context: intent.context || '',
    description: intent.description || '',
    training_phrases: trainingPhrases.value,
    responses: responses.value,
});

const processInput = (value, fieldName) => {
    let processed = value.toLowerCase().replace(/\s+/g, '_');
    processed = processed.replace(/[^a-z0-9_]/g, '');
    form[fieldName] = processed;
};

const trainingForm = useForm({
    phrase: ''
});

const responseForm = useForm({
    response: ''
});

const addTrainingPhrase = () => {
    if (!trainingForm.phrase) return;

    const existingIndex = trainingPhrases.value.indexOf(trainingForm.phrase.toLowerCase());

    if (existingIndex !== -1) {
        trainingPhrases.value.splice(existingIndex, 1);
        trainingPhrases.value.unshift(trainingForm.phrase);
    } else {
        trainingPhrases.value.unshift(trainingForm.phrase);
    }

    form.training_phrases = trainingPhrases.value;
    trainingForm.phrase = '';
};

const addResponseText = () => {
    if (!responseForm.response) return;

    const existingIndex = responses.value.indexOf(responseForm.response.toLowerCase());

    if (existingIndex !== -1) {
        responses.value.splice(existingIndex, 1);
        responses.value.unshift(responseForm.response);
    } else {
        responses.value.unshift(responseForm.response);
    }

    form.responses = responses.value;
    responseForm.response = '';
};

const deleteTrainingPhrase = (phrase) => {
    const index = trainingPhrases.value.indexOf(phrase);
    if (index > -1) {
        trainingPhrases.value.splice(index, 1);
        form.training_phrases = trainingPhrases.value;
    }
};

const deleteResponseText = (response) => {
    const index = responses.value.indexOf(response);
    if (index > -1) {
        responses.value.splice(index, 1);
        form.responses = responses.value;
    }
};

const suggestions = ref([]); // Array to store the search results

watch(() => responseForm.response, async (newValue) => {
    const match = newValue.match(/@[\w.-]+$/); // Match the last word starting with "@"

    if (match) {
        await searchApi(match[0]); // Trigger API call
    } else {
        suggestions.value = []; // Clear suggestions if no match
    }
});

const searchApi = async (query) => {
    try {
        // Make API call with search query
        const response = await axios.get(route('vendorapi.search'), {
            params: { search: query },
        });

        // Process response data
        const exactMatches = [];
        const partialMatches = [];
        const [queryName, queryParam] = query.split('.');

        response.data.forEach(item => {
            const responseParams = JSON.parse(item.response_parameters || '[]'); // Parse JSON
            responseParams.forEach(paramObj => {
                const fullParam = `${item.name}.${paramObj.param}`;
                if (queryName === item.name && queryParam === paramObj.param) {
                    exactMatches.push(fullParam); // Exact match
                } else if (fullParam.includes(query)) {
                    partialMatches.push(fullParam); // Partial match
                }
            });
        });

        // Combine exact and partial matches
        suggestions.value = [...exactMatches, ...partialMatches];

    } catch (error) {
        console.error('API call failed:', error);
    }
};

// Select suggestion from the list
const selectSuggestion = (suggestion) => {
    const inputElement = document.getElementById('response');

    if (inputElement) {
        const cursorPosition = inputElement.selectionStart; // Get the current cursor position
        const textBeforeCursor = responseForm.response.substring(0, cursorPosition);
        const textAfterCursor = responseForm.response.substring(cursorPosition);

        // Find the last "@" mention in the text before the cursor
        const lastAtIndex = textBeforeCursor.lastIndexOf('@');

        if (lastAtIndex !== -1) {
            // Replace the text starting from the last "@" with the selected suggestion
            const beforeAtText = textBeforeCursor.substring(0, lastAtIndex);
            responseForm.response = beforeAtText + suggestion + ' ' + textAfterCursor; // Add a space after the suggestion

            // Update the cursor position after the newly added suggestion
            const newCursorPosition = beforeAtText.length + suggestion.length + 1;
            setTimeout(() => {
                inputElement.setSelectionRange(newCursorPosition, newCursorPosition);
                inputElement.focus(); // Ensure the input regains focus
            }, 0);
        } else {
            // Fallback if no "@" is found: append suggestion at the cursor position
            responseForm.response = textBeforeCursor + suggestion + ' ' + textAfterCursor;

            const newCursorPosition = cursorPosition + suggestion.length + 1;
            setTimeout(() => {
                inputElement.setSelectionRange(newCursorPosition, newCursorPosition);
                inputElement.focus();
            }, 0);
        }
    }

    suggestions.value = []; // Clear the suggestion list after selection
};

const sanitizeInput = () => {
  // Allow only letters (a-z, A-Z), numbers (0-9), spaces, and the characters . ? !
  trainingForm.phrase = trainingForm.phrase.replace(/[^a-zA-Z0-9 .?!]/g, '');
};

const editingPhraseIndex = ref(null);
const editingResponseIndex = ref(null);


// Function to start editing a training phrase
const startEditingPhrase = (index) => {
    editingPhraseIndex.value = index;
};

// Function to save the edited training phrase
const saveEditedPhrase = (index) => {
    editingPhraseIndex.value = null; // Reset editing index
};

// Function to start editing a response
const startEditingResponse = (index) => {
    editingResponseIndex.value = index;
};

// Function to save the edited response
const saveEditedResponse = (index) => {
    editingResponseIndex.value = null; // Reset editing index
};

const IntentFormsubmit = () => {
    addTrainingPhrase()
    addResponseText()
    form.patch(route('intents.update', intent.id))
};

// Add this watch effect to handle flash messages
watch(() => usePage().props.flash, (newFlash) => {
    if (newFlash?.message) {
        showSuccess.value = true;
        setTimeout(() => {
            showSuccess.value = false;
        }, 3000);
    }
}, { immediate: true });

</script>

<style scoped>
/* Style the suggestion box to prevent overlap */
.relative {
    position: relative;
}

.bg-white {
    background-color: white;
}

.shadow-lg {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.rounded-md {
    border-radius: 8px;
}

.z-10 {
    z-index: 10;
}

.left-0 {
    left: 0;
}

.top-full {
    top: 100%;
}

.cursor-pointer {
    cursor: pointer;
}

.hover\:bg-gray-200:hover {
    background-color: #f7fafc;
}

.p-2 {
    padding: 8px;
}
</style>



<template>

    <Head :title="pageTitle" />

    <AuthenticatedLayout>
        <!-- <PageHeading :title="pageTitle" /> -->

        <div class="form-main-body">
            <form @submit.prevent="IntentFormsubmit" class="space-y-6">
                <div class="bg-white p-5 rounded-md  border border-gray-200">
                    <!-- Success Message -->
                     
                    <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
                        <p v-if="showSuccess" enter-active-class="transition ease-in-out"
                            enter-from-class="opacity-0" leave-active-class="transition ease-in-out"
                            leave-to-class="opacity-0" :class="$page.props.flash?.class">
                            {{ $page.props.flash.message }}</p>
                    </Transition>

                    <!-- Intent Name -->
                    <div class="space-y-4">
                        <div>
                            <div class="flex items-center">
                                <InputLabel for="name" value="Intent Name" />
                                <el-tooltip
                                v-if="dataInfo.intent_name"
                                class="ml-2"
                                effect="dark"
                                placement="top"
                                :content="dataInfo.intent_name"
                                :show-after="100"
                                >
                                <i class="fa fa-question-circle text-gray-400 hover:text-gray-600 transition-colors mx-2"></i>
                                </el-tooltip>
                            </div>
                            <TextInput id="name" name="name" type="text" class="mt-2 w-full" v-model="form.name" :readonly="form.name === 'welcome' || form.name === 'fallback'"
                                @blur="(e) => processInput(e.target.value, 'name')" :class="{'!bg-gray-100': form.name === 'welcome' || form.name === 'fallback'}" />
                            <InputError :message="form.errors.name" class="mt-2" />
                        </div>

                        <!-- Context -->
                        <div>
                            <div class="flex items-center">
                                <InputLabel for="context" value="Context" />
                                <el-tooltip
                                v-if="dataInfo.intent_context"
                                class="ml-2"
                                effect="dark"
                                placement="top"
                                :content="dataInfo.intent_context"
                                :show-after="100"
                                ><i class="fa fa-question-circle text-gray-400 hover:text-gray-600 transition-colors mx-2"></i>
                                </el-tooltip>
                            </div>
                            <TextInput id="context" name="context" type="text" class="mt-2 w-full"
                                v-model="form.context" @blur="(e) => processInput(e.target.value, 'context')" />
                            <InputError :message="form.errors.context" class="mt-2" />
                        </div>

                        <!-- Description -->
                        <div>
                            <div class="flex items-center">
                                <InputLabel for="description" value="Description" />
                                <el-tooltip
                                v-if="dataInfo.intent_description"
                                class="ml-2"
                                effect="dark"
                                placement="top"
                                :content="dataInfo.intent_description"
                                :show-after="100"
                                >
                                <i class="fa fa-question-circle text-gray-400 hover:text-gray-600 transition-colors mx-2"></i>
                                </el-tooltip>
                            </div>
                            <TextInput id="description" name="description" type="text" class="mt-2 w-full"
                                v-model="form.description" />
                            <InputError :message="form.errors.description" class="mt-2" />
                        </div>
                    </div>
                </div>

                <!-- Training Phrases Section -->
                <div class="bg-white p-5 rounded-md border border-gray-200">
                    <h2 class="b-2 font-medium  pb-2 text-lg border-b mb-4">Training Phrases<el-tooltip
                        v-if="dataInfo.intent_training_phrases"
                        class="ml-2"
                        effect="dark"
                        placement="top"
                        :content="dataInfo.intent_training_phrases"
                        :show-after="100"
                        >
                        <i class="fa fa-question-circle text-gray-400 hover:text-gray-600 transition-colors mx-2"></i>
                        </el-tooltip>
                    </h2>

                    <form @submit.prevent="addTrainingPhrase" class="mb-4">
                        <div class="flex space-x-4">
                            <TextInput id="phrase" type="text" class="flex-1" v-model="trainingForm.phrase"
                                placeholder="Add a user expression" @input="sanitizeInput"/>
                        </div>
                        <InputError :message="form.errors.training_phrases" class="mt-2" />
                    </form>

                    <div class="">
                        <div class="border rounded-lg">
                            <div v-for="(phrase, index) in trainingPhrases" :key="index"
                                class="flex justify-between items-center px-4 py-2"
                                :class="{ 'border-b': index !== trainingPhrases.length - 1 }">
                                <span v-if="editingPhraseIndex !== index" class="text-gray-800" @click="startEditingPhrase(index)">
                                    {{ phrase }}
                                </span>
                                <TextInput v-else
                                    v-model="trainingPhrases[index]"
                                    @blur="saveEditedPhrase(index)"
                                    class="text-gray-800"
                                    @keyup.enter="saveEditedPhrase(index)" />
                                <button type="button" @click="deleteTrainingPhrase(phrase)" class=" hover:text-red-800">
                                    <DtIconGlobal :type="'delete'" />
                                </button>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="bg-white p-5 rounded-md border border-gray-200">
                    <h2 class="b-2 font-medium  pb-2 text-lg border-b mb-4">Response Phrases<el-tooltip
                        v-if="dataInfo.intent_responses"
                        class="ml-2"
                        effect="dark"
                        placement="top"
                        :content="dataInfo.intent_responses"
                        :show-after="100"
                        >
                        <i class="fa fa-question-circle text-gray-400 hover:text-gray-600 transition-colors mx-2"></i>
                        </el-tooltip>
                    </h2>

                    <form @submit.prevent="addResponseText" class="mb-4">
                        <div class="flex space-x-4 relative">
                            <TextInput id="response" type="text" class="flex-1" v-model="responseForm.response"
                                placeholder="Add response text" autocomplete="off" />
                            <!-- Show suggestions if available -->
                            <div v-if="suggestions.length > 0"
                                class="mt-2 bg-white shadow-lg border rounded-md w-full absolute z-10 left-0 top-full">
                                <ul class="list-none p-2">
                                    <li v-for="(suggestion, index) in suggestions" :key="index"
                                        @click="selectSuggestion(suggestion)"
                                        class="p-2 hover:bg-gray-200 cursor-pointer">
                                        {{ suggestion }}
                                    </li>
                                </ul>
                            </div>

                        </div>
                        <InputError :message="form.errors.responses" class="mt-2" />
                    </form>

                    <div class="">
                        <div :class="{ ' border rounded-md': responses.length > 0 }">
                            <div v-for="(response, index) in responses" :key="index"
                                class="flex justify-between items-center px-4 py-2"
                                :class="{ 'border-b': index !== responses.length - 1 }">
                                <span v-if="editingResponseIndex !== index" class="text-gray-800" @click="startEditingResponse(index)">
                                    {{ response }}
                                </span>
                                <TextInput v-else
                                    v-model="responses[index]"
                                    @blur="saveEditedResponse(index)"
                                    class="text-gray-800"
                                    @keyup.enter="saveEditedResponse(index)" />
                                <button type="button" @click="deleteResponseText(response)" class=" hover:text-red-800">
                                    <DtIconGlobal :type="'delete'" />
                                </button>
                            </div>
                        </div>
                    </div>


                </div>

                <!-- Submit Button -->
                <div class="flex items-center justify-start gap-4">
                    <PrimaryButton :disabled="form.processing">
                        <span v-if="form.processing">Loading...</span>
                        <span v-else>Save</span>
                    </PrimaryButton>

                    <div class="relative">
                        <ResponsiveNavLink :href="route('intents.index')" class="dk-cancle-btn">Back</ResponsiveNavLink>
                    </div>
                </div>
            </form>
        </div>
    </AuthenticatedLayout>
</template>
