<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PaymentGateway;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\WebhookEndpoint;

class StripeConnectController extends Controller
{
    /**
     * Initiate Stripe Connect OAuth flow.
     */
    public function connect(Request $request)
    {
        $stripeGateway = PaymentGateway::where('name', 'stripe')->first();
        
        if (!$stripeGateway) {
            return back()->withErrors(['stripe' => 'Stripe gateway not found.']);
        }

        // Generate a state parameter for security
        $state = bin2hex(random_bytes(16));
        session(['stripe_connect_state' => $state]);

        // Stripe Connect OAuth URL
        $clientId = config('services.stripe.connect_client_id');
        $redirectUri = route('admin.stripe.callback');
        
        $connectUrl = "https://connect.stripe.com/oauth/authorize?" . http_build_query([
            'response_type' => 'code',
            'client_id' => $clientId,
            'scope' => 'read_write',
            'redirect_uri' => $redirectUri,
            'state' => $state,
        ]);

        return redirect($connectUrl);
    }

    /**
     * Handle Stripe Connect OAuth callback.
     */
    public function callback(Request $request)
    {
        // Verify state parameter
        if ($request->state !== session('stripe_connect_state')) {
            return redirect()->route('admin.payment-gateways.index')
                ->withErrors(['stripe' => 'Invalid state parameter.']);
        }

        if ($request->has('error')) {
            return redirect()->route('admin.payment-gateways.index')
                ->withErrors(['stripe' => 'Stripe Connect authorization failed: ' . $request->error_description]);
        }

        if (!$request->has('code')) {
            return redirect()->route('admin.payment-gateways.index')
                ->withErrors(['stripe' => 'Authorization code not received.']);
        }

        try {
            // Exchange authorization code for access token
            $response = Http::post('https://connect.stripe.com/oauth/token', [
                'client_secret' => config('services.stripe.secret'),
                'code' => $request->code,
                'grant_type' => 'authorization_code',
            ]);

            if (!$response->successful()) {
                throw new \Exception('Failed to exchange authorization code: ' . $response->body());
            }

            $data = $response->json();

            // Update Stripe gateway configuration
            $stripeGateway = PaymentGateway::where('name', 'stripe')->first();
            
            $currentConfig = $stripeGateway->getCurrentConfig();
            $newConfig = array_merge($currentConfig, [
                'publishable_key' => $data['stripe_publishable_key'],
                'secret_key' => $data['access_token'],
                'stripe_user_id' => $data['stripe_user_id'],
                'refresh_token' => $data['refresh_token'] ?? null,
                'connected_at' => now()->toISOString(),
            ]);

            if ($stripeGateway->test_mode) {
                $stripeGateway->update(['test_config' => $newConfig]);
            } else {
                $stripeGateway->update(['live_config' => $newConfig]);
            }

            // Set up webhook endpoint
            $this->setupWebhook($stripeGateway, $data['access_token']);

            return redirect()->route('admin.payment-gateways.index')
                ->with('success', 'Stripe Connect integration successful! Your account is now connected.');

        } catch (\Exception $e) {
            Log::error('Stripe Connect callback error: ' . $e->getMessage());
            
            return redirect()->route('admin.payment-gateways.index')
                ->withErrors(['stripe' => 'Failed to complete Stripe Connect integration: ' . $e->getMessage()]);
        }
    }

    /**
     * Set up Stripe webhook endpoint.
     */
    private function setupWebhook(PaymentGateway $gateway, string $accessToken): void
    {
        try {
            Stripe::setApiKey($accessToken);

            $webhookEndpoint = WebhookEndpoint::create([
                'url' => route('webhooks.stripe'),
                'enabled_events' => [
                    'invoice.payment_succeeded',
                    'invoice.payment_failed',
                    'customer.subscription.created',
                    'customer.subscription.updated',
                    'customer.subscription.deleted',
                    'payment_intent.succeeded',
                    'payment_intent.payment_failed',
                ],
            ]);

            // Store webhook secret in gateway config
            $currentConfig = $gateway->getCurrentConfig();
            $currentConfig['webhook_secret'] = $webhookEndpoint->secret;

            if ($gateway->test_mode) {
                $gateway->update(['test_config' => $currentConfig]);
            } else {
                $gateway->update(['live_config' => $currentConfig]);
            }

        } catch (\Exception $e) {
            Log::warning('Failed to setup Stripe webhook: ' . $e->getMessage());
            // Don't fail the entire process if webhook setup fails
        }
    }

    /**
     * Disconnect Stripe Connect account.
     */
    public function disconnect(Request $request)
    {
        try {
            $stripeGateway = PaymentGateway::where('name', 'stripe')->first();
            
            if (!$stripeGateway) {
                return back()->withErrors(['stripe' => 'Stripe gateway not found.']);
            }

            $config = $stripeGateway->getCurrentConfig();
            
            if (isset($config['stripe_user_id'])) {
                // Revoke access token
                Http::post('https://connect.stripe.com/oauth/deauthorize', [
                    'client_secret' => config('services.stripe.secret'),
                    'stripe_user_id' => $config['stripe_user_id'],
                ]);
            }

            // Clear Stripe configuration
            $clearedConfig = [
                'publishable_key' => '',
                'secret_key' => '',
                'webhook_secret' => '',
            ];

            if ($stripeGateway->test_mode) {
                $stripeGateway->update(['test_config' => $clearedConfig]);
            } else {
                $stripeGateway->update(['live_config' => $clearedConfig]);
            }

            return redirect()->route('admin.payment-gateways.index')
                ->with('success', 'Stripe account disconnected successfully.');

        } catch (\Exception $e) {
            Log::error('Stripe disconnect error: ' . $e->getMessage());
            
            return back()->withErrors(['stripe' => 'Failed to disconnect Stripe account: ' . $e->getMessage()]);
        }
    }
}
