<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Company extends Model
{
    use HasFactory;

    protected $table = 'companies';

    protected $fillable = [
        'userId',
        'catId',
        'countryId',
        'companyName',
        'companyPhone',
        'companyEmail',
        'companyAdd',
        'companyAddress',
        'city',
        'state',
        'country',
        'zipCode',
        'zipcode',
        'websiteUrl',
        'chatSupport',
        'status',
    ];

    protected $casts = [
        'chatSupport' => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'userId');
    }

    public function complaints(): HasMany
    {
        return $this->hasMany(Complaint::class, 'companyId');
    }
}
