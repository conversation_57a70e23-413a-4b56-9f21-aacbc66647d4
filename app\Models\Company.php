<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Company extends Model
{
    use HasFactory;

    protected $table = 'company';

    protected $fillable = ['userId', 'catId', 'countryId', 'companyName', 'companyAdd', 'city', 'state', 'zipCode', 'websiteUrl', 'chatSupport'];

    public function user()
    {
        return $this->belongsTo(User::class, 'userId', 'id');
    }

    // Company.php
    public function complaints()
    {
        return $this->hasMany(Complaint::class, 'companyId');
    }

    public static function companyDetails($id)
    {
        $company = Company::find($id);

        if (!$company) {
            return [];
        }

        return [ // return plain array, not response()->json()
            'id'           => $company->id ?? '',
            'userId'       => $company->userId ?? '',
            'countryId'    => $company->countryId ?? '',
            'companyName'  => $company->companyName ?? '',
            'companyAdd'   => $company->companyAdd ?? '',
            'city'         => $company->city ?? '',
            'state'        => $company->state ?? '',
            'zipCode'      => $company->zipCode ?? '',
            'websiteUrl'   => $company->websiteUrl ?? '',
            'chatSupport'  => $company->chatSupport ?? '',
        ];
    }



}
