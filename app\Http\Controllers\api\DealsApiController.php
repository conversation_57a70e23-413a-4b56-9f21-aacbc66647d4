<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Models\Deals;
use Exception;
use Illuminate\Support\Facades\DB;

class DealsApiController extends Controller
{
    public function LoadDeals()
    {

        try {

            $deals = Deals::join('company', 'company.id', '=', 'deals.companyId')
            ->join('users', 'users.id', '=', 'company.userId')
            ->where('deals.deal_status', 1)
            ->select([
                'deals.id',
                DB::raw('IFNULL(company.companyName, "") as companyName'),
                DB::raw('IFNULL(deals.dealTitle, "") as dealTitle'),
                DB::raw('IFNULL(deals.dealContent, "") as dealContent'),
                DB::raw('IFNULL(deals.dealExpiresOn, "") as dealExpiresOn'),
                DB::raw('IFNULL(deals.dealStartOn, "") as dealStartOn'),
                DB::raw('IFNULL(users.profilePicture, "") as profilePicture'),
                DB::raw('IFNULL(deals.deal_link, "") as deal_link'),
            ])
            ->orderBy('deals.id', 'desc')
            ->get();
            $deals->transform(function ($company) {
                $company->profilePicture = url('/storage/'.$company->profilePicture);

                return $company;
            });

            if ($deals->isNotEmpty()) {
                return response()->json(['status' => true, 'data' => $deals]);
            } else {
                return response()->json(['status' => false]);
            }

            // $deals = Deals::join('company', 'company.id', '=', 'deals.companyId')
            //     ->join('users', 'users.id', '=', 'company.userId')
            //     ->select('deals.id', 'company.companyName', 'deals.dealTitle', 'deals.dealContent', 'deals.dealExpiresOn', 'users.profilePicture')
            //     ->orderBy('deals.id', 'desc')
            //     ->get();

            // if ($deals->isNotEmpty()) {
            //     // Modify profilePicture to include the full URL
            //     $deals->transform(function ($deal) {
            //         $deal->profilePicture = url('storage/'.$deal->profilePicture);
            //         return $deal;
            //     });

            //     return response()->json(['status' => true, 'data' => $deals]);
            // } else {
            //     return response()->json(['status' => false]);
            // }
        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }

    }
}
