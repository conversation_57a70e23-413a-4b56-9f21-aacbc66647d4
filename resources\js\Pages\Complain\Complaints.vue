<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { Head, Link, usePage, router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import axios from 'axios';
import { Inertia } from '@inertiajs/inertia';

const pageTitle = ref('Complaints')
const props = usePage().props;

var adminBaseurl = window.location.origin + '/dtadmin';

// Initialize complaintsData with proper array handling
const complaintsData = ref([]);

// State to track selected complaint and form inputs
const searchTerm = ref('');
const filterStatus = ref('open');

// Pagination state from Inertia props
const currentPage = computed(() => props.Complaints?.current_page || 1);
const perPage = computed(() => props.Complaints?.per_page || 10);
const totalItems = computed(() => props.Complaints?.total || 0);
const lastPage = computed(() => props.Complaints?.last_page || 1);
const links = computed(() => props.Complaints?.links || []);

// Total counts for tabs
const totalOpen = computed(() => props.totalOpen || 0);
const totalResolved = computed(() => props.totalResolved || 0);

// Function to handle page change
const changePage = (url) => {
    if (!url) return;
    
    // Create URL object to modify the URL
    const urlObj = new URL(url);
    // Add or update status parameter
    urlObj.searchParams.set('status', filterStatus.value);
    // Add search parameter if exists
    if (searchTerm.value) {
        urlObj.searchParams.set('search', searchTerm.value);
    }
    
    router.visit(urlObj.toString());
};

const filterComplaints = (status) => {
    filterStatus.value = status;
    router.visit(route('complaints', {
        status: status,
        search: searchTerm.value,
        page: 1
    }));
};

// Function to handle search
const handleSearch = () => {
    router.visit(route('complaints', {
        status: filterStatus.value,
        search: searchTerm.value,
        page: 1
    }));
};

// Watch for changes in search - only for clearing search
watch(searchTerm, (newVal) => {
    if (!newVal) {
        router.visit(route('complaints', {
            status: filterStatus.value,
            page: 1
        }));
    }
});

// Watch for changes in props.Complaints
watch(() => props.Complaints, (newVal) => {
    if (newVal && newVal.data) {
        complaintsData.value = newVal.data;
        // Force update the current page
        currentPage.value = newVal.current_page;
    }
}, { deep: true, immediate: true });

// Initialize data from URL parameters
onMounted(() => {
    const params = new URLSearchParams(window.location.search);
    const page = params.get('page');
    const status = params.get('status');
    const search = params.get('search');
    
    if (status) filterStatus.value = status;
    if (search) searchTerm.value = search;
    if (page) {
        complaintsData.value = props.Complaints.data;
    }
});

// Function to get the display label for `progressStatus`
const getDisplayStatus = (status) => {
    const statusMap = {
        new: 'New',
        inprogress: 'In Progress',
        need_user_input: 'Need User Input',
        resolved: 'Resolved',
    };
    return statusMap[status] || 'Unknown';
};

// Mapping for `priority` values
const priorityMap = {
    0: 'Low',
    1: 'Medium',
    2: 'High',
};
// Function to get the display label for `priority`
const getDisplayPriority = (priority) => priorityMap[priority] || 'Undefined';

// Format time ago (e.g., "2 hours ago")
const formatTimeAgo = (dateString) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    const now = new Date();
    const seconds = Math.floor((now - date) / 1000);
    
    let interval = Math.floor(seconds / 31536000);
    if (interval >= 1) {
        return interval + ' year' + (interval === 1 ? '' : 's') + ' ago';
    }
    
    interval = Math.floor(seconds / 2592000);
    if (interval >= 1) {
        return interval + ' month' + (interval === 1 ? '' : 's') + ' ago';
    }
    
    interval = Math.floor(seconds / 86400);
    if (interval >= 1) {
        return interval + ' day' + (interval === 1 ? '' : 's') + ' ago';
    }
    
    interval = Math.floor(seconds / 3600);
    if (interval >= 1) {
        return interval + ' hour' + (interval === 1 ? '' : 's') + ' ago';
    }
    
    interval = Math.floor(seconds / 60);
    if (interval >= 1) {
        return interval + ' minute' + (interval === 1 ? '' : 's') + ' ago';
    }
    
    return 'just now';
};

// Function to truncate text to three lines
const truncateToThreeLines = (text) => {
    if (!text) return '';
    const maxLength = 550; // Maximum characters to show
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
};

const resolveComplaint = async (complaintId) => {
    try {
        const confirmation = await Swal.fire({
            title: 'Are you sure?',
            text: 'You want to resolve this complaint',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes',
            cancelButtonText: 'Cancel',
            customClass: {
                confirmButton: 'dk-update-btn',
                cancelButton: 'dk-cancle-btn',
            },
            buttonsStyling: false,
        });

        if (confirmation.isConfirmed) {
            const requestData = {
                complaint_id: complaintId,
                progressStatus: 'resolved',
            };

            const response = await axios.post(route('complaints.update-progress-status'), requestData);

            if (response.data.success) {
                // Refresh the current page to show updated data
                Inertia.reload({ only: ['Complaints'] });
            } else {
                throw new Error('Failed to update status');
            }
        }
    } catch (error) {
        console.error(error);
        Swal.fire('Error', 'An error occurred while resolving the complaint.', 'error');
    }
};
</script>

<template>
    <Head :title="pageTitle" />

    <AuthenticatedLayout>
        <!-- Flash Messages -->
        <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
            leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
            <p v-if="$page.props.flash?.message" 
               class="bg-green-100 mb-4 p-2 rounded-md text-green-700" 
               :class="$page.props.flash.class">
                {{ $page.props.flash.message }}
            </p>
        </Transition>

        <!-- Page Header -->
        <div class="">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Complaints</h1>
                    <p class="text-gray-500">Manage customer complaints</p>
                </div>
                <Link :href="route('addcomplain')" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/80 transition-colors">
                    New Complaint
                </Link>
            </div>

            <!-- Tabs -->
            <div class="border-b border-gray-200 mb-6">
                <nav class="-mb-px flex space-x-8">
                    <button 
                        @click="filterComplaints('open')" 
                        :class="[
                            'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm',
                            filterStatus === 'open' 
                                ? 'border-black text-black font-semibold' 
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        ]">
                        Open ({{ totalOpen }})
                    </button>
                    <button 
                        @click="filterComplaints('close')" 
                        :class="[
                            'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm',
                            filterStatus === 'close' 
                                ? 'border-black text-black font-semibold' 
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        ]">
                        Resolved ({{ totalResolved }})
                    </button>
                </nav>
            </div>

            <!-- Search -->
            <div class="mb-6">
                <div class="relative rounded-md shadow-sm max-w-md flex gap-2">
                    <div class="relative flex-1">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <input 
                            v-model="searchTerm" 
                            type="text" 
                            class="focus:ring-black focus:border-black block w-full pl-10 sm:text-sm border-gray-300 rounded-md" 
                            placeholder="Search complaints..."
                            @keyup.enter="handleSearch"
                        >
                    </div>
                    <button 
                        @click="handleSearch"
                        class="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/80 transition-colors"
                    >
                        Search
                    </button>
                </div>
            </div>

            <!-- Complaints List -->
            <div class="bg-white overflow-hidden sm:rounded-lg">
                <div class="space-y-4">
                    <template v-if="complaintsData && complaintsData.length > 0">
                        <div v-for="complain in complaintsData" :key="complain.id" class="border border-gray-200 rounded-md">
                            <div 
                                class="block hover:bg-gray-50 p-6 space-y-4"
                            >
                                <div class="flex items-start justify-between gap-4">
                                    <div class="space-y-1 flex-1 min-w-0">
                                        <h2 class="text-xl font-medium">{{ complain.complainTitle }} <small>#{{ complain.cmp_id || complain.id }}</small></h2>
                                        <p class="text-sm text-gray-500 mt-1">
                                            Submitted {{ formatTimeAgo(complain.created_at) }}
                                        </p>
                                        <p class="whitespace-pre-line">
                                            {{ truncateToThreeLines(complain.complainDetail) }}
                                        </p>
                                    </div>
                                    <div class="flex-shrink-0 whitespace-nowrap">
                                        <span>{{ getDisplayStatus(complain.progressStatus) }} | </span>
                                        <span 
                                            class="font-medium"
                                            :class="{
                                                'text-red-600': complain.priority === '2' || complain.priority === 2,
                                                'text-yellow-600': complain.priority === '1' || complain.priority === 1,
                                                'text-green-600': complain.priority === '0' || complain.priority === 0
                                            }"
                                        >
                                            {{ getDisplayPriority(complain.priority) }}
                                        </span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div>
                                        <Link :href="`${adminBaseurl}/complaints/show/${complain.id}`" class="bg-transparent border border-primary text-primary px-3 py-2 text-sm rounded-md hover:bg-primary hover:text-white transition-colors">
                                            View Details
                                        </Link>
                                    </div>
                                    <div v-if="complain.complainStatus == 'open' && ($page.props.auth.user.profileType == 'admin' || $page.props.auth.user.profileType == 'company' || $page.props.auth.user.profileType == 'agent')">
                                        <button 
                                            @click="resolveComplaint(complain.id)"
                                            class="bg-primary text-white px-3 py-2 text-sm rounded-md hover:bg-primary/80 transition-colors">
                                            Resolve
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                    <div v-else class="px-4 py-12 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No complaints found</h3>
                        <p class="mt-1 text-sm text-gray-500">
                            {{ searchTerm ? 'Try adjusting your search' : 'Get started by creating a new complaint' }}
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Pagination -->
            <div class="bg-white py-4 flex items-center justify-between border-t border-gray-200">
                <div class="hidden sm:block">
                    <p class="text-sm text-gray-700">
                        Showing
                        <span class="font-medium">{{ (props.Complaints?.current_page - 1) * props.Complaints?.per_page + 1 }}</span>
                        to
                        <span class="font-medium">{{ Math.min(props.Complaints?.current_page * props.Complaints?.per_page, props.Complaints?.total) }}</span>
                        of
                        <span class="font-medium">{{ props.Complaints?.total || 0 }}</span>
                        results
                    </p>
                </div>
                <div class="flex-1 flex justify-between sm:justify-end">
                    <div class="pagination-links">
                        <ul class="flex justify-items-end place-content-end">
                            <li v-for="page in props.Complaints?.links" :key="page.url">
                                <button 
                                    @click="changePage(page.url)"
                                    :class="{ 
                                        'bg-primary text-white': page.active, 
                                        'hover:bg-primary hover:text-white': !page.active 
                                    }"
                                    class="px-3 py-1 rounded-full focus:outline-none mx-1"
                                    v-html="page.label"
                                ></button>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
