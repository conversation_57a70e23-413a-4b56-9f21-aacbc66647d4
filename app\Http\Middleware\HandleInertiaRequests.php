<?php

namespace App\Http\Middleware;

use App\Models\Company;
use Illuminate\Http\Request;
use Inertia\Middleware;
use App\Models\Notifications;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that is loaded on the first page visit.
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determine the current asset version.
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        $user = $request->user();
        $companyDetails = null;
        $notificationData = [];
        
        if ($user) {
            if($user->parent_id){
                $companyDetails = Company::where('userId', $user->parent_id)->first();
            }

           $notificationData = Notifications::where('receiver_id', $user->id)->orderBy('created_at', 'desc')->take(10)->get();
           
        }
        
        return [
            ...parent::share($request),
            'auth' => [
                'user' => $user,
                'company' => $companyDetails,
                
            ],
            'notifications' => function () use ($user) {
                if ($user) {
                    return Notifications::where('receiver_id', $user->id)
                        ->orderBy('created_at', 'desc')
                        ->take(10)
                        ->get()
                        ->toArray(); // <-- force array
                }
                return [];
            },
            'unseen_notifications_count' => function () use ($user) {
                if ($user) {
                    return Notifications::where('receiver_id', $user->id)
                        ->where('status', 'unseen')
                        ->count();
                }
                return 0;
            },
            
        ];
    }
}
