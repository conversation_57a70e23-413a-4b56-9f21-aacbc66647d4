<script setup lang="ts">
import { ref } from 'vue'

import ProductOne from '@/assets/images/product/product-01.png'
import ProductTwo from '@/assets/images/product/product-02.png'
import ProductThree from '@/assets/images/product/product-03.png'
import ProductFour from '@/assets/images/product/product-04.png'

const products = ref([
  {
    id: 1,
    name: 'Apple Watch Series 7',
    category: 'Electronics',
    price: 269,
    sold: 22,
    profit: 45,
    imageSrc: ProductOne
  },
  {
    id: 2,
    name: 'Macbook Pro M1',
    category: 'Electronics',
    price: 546,
    sold: 34,
    profit: 125,
    imageSrc: ProductTwo
  },
  {
    id: 3,
    name: 'Dell Inspiron 15',
    category: 'Electronics',
    price: 443,
    sold: 64,
    profit: 247,
    imageSrc: ProductThree
  },
  {
    id: 4,
    name: 'HP Probook 450',
    category: 'Electronics',
    price: 499,
    sold: 72,
    profit: 103,
    imageSrc: ProductFour
  }
])
</script>

<template>
  <div class="border-stroke bg-white shadow-default border rounded-sm">
    <div class="px-4 md:px-6 xl:px-7.5 py-6">
      <h4 class="font-semibold text-slate-800 text-xl">Top Products</h4>
    </div>

    <!-- Table Header -->
    <div class="border-stroke grid grid-cols-6 sm:grid-cols-8 px-4 md:px-6 2xl:px-7.5 py-4.5 border-t">
      <div class="flex items-center col-span-3">
        <p class="font-medium">Product Name</p>
      </div>
      <div class="sm:flex items-center hidden col-span-2">
        <p class="font-medium">Category</p>
      </div>
      <div class="flex items-center col-span-1">
        <p class="font-medium">Price</p>
      </div>
      <div class="flex items-center col-span-1">
        <p class="font-medium">Sold</p>
      </div>
      <div class="flex items-center col-span-1">
        <p class="font-medium">Profit</p>
      </div>
    </div>

    <!-- Table Rows -->
    <div v-for="product in products" :key="product.id"
      class="border-stroke grid grid-cols-6 sm:grid-cols-8 px-4 md:px-6 2xl:px-7.5 py-4.5 border-t">
      <div class="flex items-center col-span-3">
        <div class="flex sm:flex-row flex-col sm:items-center gap-4">
          <div class="rounded-md w-15 h-12.5">
            <img :src="product.imageSrc" :alt="`Product: ${product.name}`" />
          </div>
          <p class="font-medium text-slate-800 text-sm">{{ product.name }}</p>
        </div>
      </div>
      <div class="sm:flex items-center hidden col-span-2">
        <p class="font-medium text-slate-800 text-sm">{{ product.category }}</p>
      </div>
      <div class="flex items-center col-span-1">
        <p class="font-medium text-slate-800 text-sm">${{ product.price }}</p>
      </div>
      <div class="flex items-center col-span-1">
        <p class="font-medium text-slate-800 text-sm">{{ product.sold }}</p>
      </div>
      <div class="flex items-center col-span-1">
        <p class="font-medium text-meta-3 text-sm">${{ product.profit }}</p>
      </div>
    </div>
  </div>
</template>
