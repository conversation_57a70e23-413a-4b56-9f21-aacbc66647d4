<!--- <template>
	<a href="#" @click.prevent="toggleTheme" aria-label="Theme Switcher">
	  <i
		v-if="theme === 'light'"
		data-feather="moon"
		class="dark:hover:text-liborder-cfp-500-light w-5 text-liText-ternary-dark hover:text-gray-400 dark:text-liText-ternary-light"
	  ></i>
	  <i
		v-else
		data-feather="sun"
		class="w-5 text-gray-200 hover:text-gray-50"
	  ></i>
	</a>
  </template> -->

<script setup>
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
	theme: {
		type: String,
		required: true,
	},
});

//const emit = defineEmits(['theme-changed']);

const toggleTheme = () => {
	const newTheme = props.theme === 'light' ? 'dark' : 'light';
	localStorage.setItem('theme', newTheme);

	//emit('theme-changed', newTheme);
	// if (newTheme === 'dark') {
	// 	document.querySelector('body').classList.add('bg-cfp-500-dark');
	// 	document.querySelector('body').classList.add('dark');
	// 	document.querySelector('body').classList.remove('bg-secondary-light');
	// } else {
	// 	document.querySelector('body').classList.remove('dark');
	// 	document.querySelector('body').classList.add('bg-secondary-light');
	// 	document.querySelector('body').classList.remove('bg-cfp-500-dark');
	// }
	window.location.reload();
};
</script>

<style scoped>
/* Your scoped styles here */
</style>