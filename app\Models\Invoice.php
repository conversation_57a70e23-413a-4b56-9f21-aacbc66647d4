<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Invoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'subscription_id',
        'plan_name',
        'user_id',
        'invoice_number',
        'stripe_invoice_id',
        'paypal_invoice_id',
        'status',
        'amount_due',
        'amount_paid',
        'tax_amount',
        'currency',
        'billing_reason',
        'invoice_pdf',
        'due_date',
        'paid_at',
        'period_start',
        'period_end',
        'paypal_sale_id',
        'invoice_data',
        'amount',
    ];

    protected $casts = [
        'amount_due' => 'integer',
        'amount_paid' => 'integer',
        'amount' => 'decimal:2',
        'tax_amount' => 'integer',
        'due_date' => 'datetime',
        'paid_at' => 'datetime',
        'period_start' => 'datetime',
        'period_end' => 'datetime',
        'invoice_data' => 'array',
    ];

    /**
     * Get the subscription that owns the invoice.
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * Get the user that owns the invoice.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the payment transactions for this invoice.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(PaymentTransaction::class);
    }

    /**
     * Scope to get paid invoices.
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope to get unpaid invoices.
     */
    public function scopeUnpaid($query)
    {
        return $query->whereIn('status', ['draft', 'open']);
    }

    /**
     * Scope to get overdue invoices.
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'open')
                    ->where('due_date', '<', now());
    }

    /**
     * Check if the invoice is paid.
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    /**
     * Check if the invoice is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->status === 'open' &&
               $this->due_date &&
               $this->due_date->isPast();
    }

    /**
     * Get the formatted amount due.
     */
    public function getFormattedAmountDueAttribute(): string
    {
        return '$' . number_format($this->amount_due / 100, 2);
    }

    /**
     * Get the formatted amount paid.
     */
    public function getFormattedAmountPaidAttribute(): string
    {
        return '$' . number_format($this->amount_paid / 100, 2);
    }

    /**
     * Get the formatted tax amount.
     */
    public function getFormattedTaxAmountAttribute(): string
    {
        return '$' . number_format($this->tax_amount / 100, 2);
    }

    /**
     * Get the remaining balance.
     */
    public function getRemainingBalanceAttribute(): int
    {
        return max(0, $this->amount_due - $this->amount_paid);
    }

    /**
     * Get the formatted remaining balance.
     */
    public function getFormattedRemainingBalanceAttribute(): string
    {
        return '$' . number_format($this->remaining_balance / 100, 2);
    }

    /**
     * Mark the invoice as paid.
     */
    public function markAsPaid(?int $amountPaid = null): void
    {
        $this->update([
            'status' => 'paid',
            'amount_paid' => $amountPaid ?? $this->amount_due,
            'paid_at' => now(),
        ]);
    }

    /**
     * Generate a unique invoice number.
     */
    public static function generateInvoiceNumber(): string
    {
        $prefix = 'INV-';
        $year = now()->year;
        $month = now()->format('m');

        $lastInvoice = static::whereYear('created_at', $year)
                           ->whereMonth('created_at', $month)
                           ->orderBy('id', 'desc')
                           ->first();

        $sequence = $lastInvoice ?
            (int) substr($lastInvoice->invoice_number, -4) + 1 : 1;

        return $prefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
