<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('company', function (Blueprint $table) {
            $table->id();
            $table->foreignId('userId')->constrained('users');
            $table->foreignId('catId')->constrained('category', 'catId');
            $table->foreignId('countryId')->constrained('country');
            $table->string('companyName', 180);
            $table->string('companyAdd', 180);
            $table->string('city', 80);
            $table->string('state', 80);
            $table->string('zipCode', 80);
            $table->string('websiteUrl', 180);
            $table->enum('chatSupport', ['chatbot', 'livechat', 'both'])->default('chatbot');
            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('company');
    }
};
