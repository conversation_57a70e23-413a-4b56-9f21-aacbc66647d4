import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { Card } from '@/Components/UI/Card';
import { 
    FiBar<PERSON>hart, 
    FiTrendingUp, 
    FiUsers, 
    FiDollarSign, 
    FiCalendar,
    FiRefreshCw,
    FiDownload
} from 'react-icons/fi';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    BarElement,
    Title,
    Tooltip,
    Legend,
    ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    BarElement,
    Title,
    Tooltip,
    Legend,
    ArcElement
);

export default function ReportsIndex({ metrics, charts, period }) {
    const [selectedPeriod, setSelectedPeriod] = useState(period);
    const [isLoading, setIsLoading] = useState(false);

    const periodOptions = [
        { value: '7', label: 'Last 7 days' },
        { value: '30', label: 'Last 30 days' },
        { value: '90', label: 'Last 90 days' },
        { value: '365', label: 'Last year' },
    ];

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };

    const formatPercentage = (value) => {
        return `${value >= 0 ? '+' : ''}${value}%`;
    };

    const MetricCard = ({ title, value, change, icon, color = 'blue' }) => (
        <Card>
            <div className="p-6">
                <div className="flex items-center">
                    <div className={`flex-shrink-0 p-3 rounded-lg bg-${color}-100`}>
                        <div className={`text-${color}-600`}>
                            {icon}
                        </div>
                    </div>
                    <div className="ml-4 flex-1">
                        <p className="text-sm font-medium text-gray-500">{title}</p>
                        <p className="text-2xl font-semibold text-gray-900">{value}</p>
                        {change !== undefined && (
                            <p className={`text-sm ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {formatPercentage(change)} from previous period
                            </p>
                        )}
                    </div>
                </div>
            </div>
        </Card>
    );

    const chartOptions = {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            },
        },
        scales: {
            y: {
                beginAtZero: true,
            },
        },
    };

    const doughnutOptions = {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom',
            },
        },
    };

    return (
        <DashboardLayout>
            <Head title="Reports & Analytics" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                        <FiBarChart className="h-8 w-8 text-cf-primary-600" />
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
                            <p className="text-gray-600">
                                Comprehensive insights into your subscription business
                            </p>
                        </div>
                    </div>

                    <div className="flex items-center space-x-4">
                        <select
                            value={selectedPeriod}
                            onChange={(e) => setSelectedPeriod(e.target.value)}
                            className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-cf-primary-500 focus:border-cf-primary-500"
                        >
                            {periodOptions.map((option) => (
                                <option key={option.value} value={option.value}>
                                    {option.label}
                                </option>
                            ))}
                        </select>
                        
                        <button
                            onClick={() => window.location.reload()}
                            disabled={isLoading}
                            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cf-primary-500"
                        >
                            <FiRefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                            Refresh
                        </button>
                    </div>
                </div>

                {/* Key Metrics */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <MetricCard
                        title="Total Revenue"
                        value={formatCurrency(metrics.revenue.total)}
                        change={metrics.revenue.growth}
                        icon={<FiDollarSign className="h-6 w-6" />}
                        color="green"
                    />
                    <MetricCard
                        title="Active Subscriptions"
                        value={metrics.subscription.active.toLocaleString()}
                        change={metrics.subscription.new_subscriptions_growth}
                        icon={<FiUsers className="h-6 w-6" />}
                        color="blue"
                    />
                    <MetricCard
                        title="Monthly Recurring Revenue"
                        value={formatCurrency(metrics.revenue.monthly_recurring)}
                        icon={<FiTrendingUp className="h-6 w-6" />}
                        color="purple"
                    />
                    <MetricCard
                        title="Churn Rate"
                        value={`${metrics.churn.churn_rate}%`}
                        icon={<FiBarChart className="h-6 w-6" />}
                        color="red"
                    />
                </div>

                {/* Subscription Metrics */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <Card className="lg:col-span-1">
                        <div className="p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Subscription Overview</h3>
                            <div className="space-y-4">
                                <div className="flex justify-between">
                                    <span className="text-sm text-gray-500">Total Subscriptions</span>
                                    <span className="text-sm font-medium">{metrics.subscription.total}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-gray-500">Active</span>
                                    <span className="text-sm font-medium text-green-600">{metrics.subscription.active}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-gray-500">Trial</span>
                                    <span className="text-sm font-medium text-blue-600">{metrics.subscription.trial}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-gray-500">Cancelled</span>
                                    <span className="text-sm font-medium text-red-600">{metrics.subscription.cancelled}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-gray-500">Conversion Rate</span>
                                    <span className="text-sm font-medium">{metrics.subscription.conversion_rate}%</span>
                                </div>
                            </div>
                        </div>
                    </Card>

                    <Card className="lg:col-span-2">
                        <div className="p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Subscription Growth</h3>
                            <div className="h-64">
                                <Line data={charts.subscription_growth} options={chartOptions} />
                            </div>
                        </div>
                    </Card>
                </div>

                {/* Revenue Analytics */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card>
                        <div className="p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Revenue Trend</h3>
                            <div className="h-64">
                                <Line data={charts.revenue_trend} options={chartOptions} />
                            </div>
                        </div>
                    </Card>

                    <Card>
                        <div className="p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Plan Distribution</h3>
                            <div className="h-64">
                                <Doughnut data={charts.plan_distribution} options={doughnutOptions} />
                            </div>
                        </div>
                    </Card>
                </div>

                {/* Payment Methods & User Stats */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card>
                        <div className="p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Methods</h3>
                            <div className="h-64">
                                <Doughnut data={charts.payment_methods} options={doughnutOptions} />
                            </div>
                        </div>
                    </Card>

                    <Card>
                        <div className="p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">User Statistics</h3>
                            <div className="space-y-4">
                                <div className="flex justify-between">
                                    <span className="text-sm text-gray-500">Total Users</span>
                                    <span className="text-sm font-medium">{metrics.users.total}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-gray-500">New Users (Period)</span>
                                    <span className="text-sm font-medium text-green-600">{metrics.users.new}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-gray-500">Company Users</span>
                                    <span className="text-sm font-medium text-blue-600">{metrics.users.companies}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-gray-500">Regular Users</span>
                                    <span className="text-sm font-medium">{metrics.users.regular}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-gray-500">Average Revenue Per User</span>
                                    <span className="text-sm font-medium">{formatCurrency(metrics.revenue.average_per_user)}</span>
                                </div>
                            </div>
                        </div>
                    </Card>
                </div>

                {/* Churn Analysis */}
                <Card>
                    <div className="p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Churn Analysis</h3>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                            <div className="text-center">
                                <p className="text-2xl font-bold text-red-600">{metrics.churn.churn_rate}%</p>
                                <p className="text-sm text-gray-500">Churn Rate</p>
                            </div>
                            <div className="text-center">
                                <p className="text-2xl font-bold text-green-600">{metrics.churn.retention_rate}%</p>
                                <p className="text-sm text-gray-500">Retention Rate</p>
                            </div>
                            <div className="text-center">
                                <p className="text-2xl font-bold text-gray-900">{metrics.churn.cancelled_in_period}</p>
                                <p className="text-sm text-gray-500">Cancelled (Period)</p>
                            </div>
                            <div className="text-center">
                                <p className="text-2xl font-bold text-gray-900">{metrics.churn.active_at_start}</p>
                                <p className="text-sm text-gray-500">Active at Start</p>
                            </div>
                        </div>
                    </div>
                </Card>
            </div>
        </DashboardLayout>
    );
}
