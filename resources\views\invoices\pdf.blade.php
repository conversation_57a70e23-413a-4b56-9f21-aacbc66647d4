<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice #{{ $invoice->id }}</title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-info {
            text-align: right;
            float: right;
            width: 50%;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .invoice-title {
            font-size: 28px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        .invoice-meta {
            margin-bottom: 30px;
        }
        
        .invoice-meta table {
            width: 100%;
        }
        
        .invoice-meta td {
            padding: 5px 0;
            vertical-align: top;
        }
        
        .label {
            font-weight: bold;
            width: 120px;
        }
        
        .customer-info {
            background-color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .customer-info h3 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: #1f2937;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .items-table th,
        .items-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .items-table th {
            background-color: #f3f4f6;
            font-weight: bold;
            color: #374151;
        }
        
        .items-table .amount {
            text-align: right;
        }
        
        .totals {
            float: right;
            width: 300px;
            margin-top: 20px;
        }
        
        .totals table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .totals td {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .totals .label {
            text-align: right;
            font-weight: bold;
        }
        
        .totals .amount {
            text-align: right;
            width: 120px;
        }
        
        .total-row {
            background-color: #f3f4f6;
            font-weight: bold;
            font-size: 14px;
        }
        
        .payment-info {
            clear: both;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }
        
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status.paid {
            background-color: #d1fae5;
            color: #065f46;
        }
        
        .status.pending {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        .status.failed {
            background-color: #fee2e2;
            color: #991b1b;
        }
        
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 10px;
            color: #6b7280;
        }
        
        .clearfix::after {
            content: "";
            display: table;
            clear: both;
        }
    </style>
</head>
<body>
    <div class="header clearfix">
        <div class="company-info">
            <div class="company-name">{{ config('app.name') }}</div>
            <div>Subscription Services</div>
        </div>
        
        <div class="invoice-title">INVOICE</div>
    </div>

    <div class="invoice-meta">
        <table>
            <tr>
                <td class="label">Invoice Number:</td>
                <td>INV-{{ str_pad($invoice->id, 6, '0', STR_PAD_LEFT) }}</td>
                <td class="label">Status:</td>
                <td>
                    <span class="status {{ $invoice->status }}">{{ ucfirst($invoice->status) }}</span>
                </td>
            </tr>
            <tr>
                <td class="label">Invoice Date:</td>
                <td>{{ $invoice->created_at->format('M d, Y') }}</td>
                <td class="label">Due Date:</td>
                <td>{{ $invoice->due_date ? $invoice->due_date->format('M d, Y') : 'Upon receipt' }}</td>
            </tr>
            @if($invoice->paid_at)
            <tr>
                <td class="label">Paid Date:</td>
                <td>{{ $invoice->paid_at->format('M d, Y') }}</td>
                <td></td>
                <td></td>
            </tr>
            @endif
        </table>
    </div>

    <div class="customer-info">
        <h3>Bill To:</h3>
        <div><strong>{{ $user->name }}</strong></div>
        <div>{{ $user->email }}</div>
        @if($company && $company->companyName)
            <div>{{ $company->companyName }}</div>
        @endif
        @if($company && $company->companyAddress)
            <div>{{ $company->companyAddress }}</div>
            @if($company->city || $company->state || $company->zipcode)
                <div>
                    {{ implode(', ', array_filter([$company->city, $company->state, $company->zipcode])) }}
                </div>
            @endif
            @if($company->country)
                <div>{{ $company->country }}</div>
            @endif
        @endif
    </div>
        
    <table class="items-table">
        <thead>
            <tr>
                <th>Description</th>
                <th>Period</th>
                <th class="amount">Amount</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    @if($plan)
                    <strong>{{ $plan->name }}</strong>
                    @if($plan->description)
                        <br><small>{{ $plan->description }}</small>
                    @endif
                    @endif
                </td>
                <td>
                    @if($invoice->period_start && $invoice->period_end)
                        {{ $invoice->period_start->format('M d, Y') }} - {{ $invoice->period_end->format('M d, Y') }}
                    @else
                        @if($plan)
                        {{ ucfirst($plan->billing_interval) }}ly subscription
                        @endif
                    @endif
                </td>
                <td class="amount">{{ strtoupper($invoice->currency) }} {{ number_format($invoice->amount, 2) }}</td>
            </tr>
        </tbody>
    </table>

    <div class="totals">
        <table>
            <tr>
                <td class="label">Subtotal:</td>
                <td class="amount">{{ strtoupper($invoice->currency) }} {{ number_format($invoice->amount, 2) }}</td>
            </tr>
            @if($invoice->tax_amount)
            <tr>
                <td class="label">Tax:</td>
                <td class="amount">{{ strtoupper($invoice->currency) }} {{ number_format($invoice->tax_amount, 2) }}</td>
            </tr>
            @endif
            <tr class="total-row">
                <td class="label">Total:</td>
                <td class="amount">{{ strtoupper($invoice->currency) }} {{ number_format($invoice->amount + ($invoice->tax_amount ?? 0), 2) }}</td>
            </tr>
        </table>
    </div>

    <div class="payment-info">
        <h3>Payment Information</h3>
        @if($invoice->stripe_invoice_id)
            <p><strong>Payment Method:</strong> Stripe</p>
            <p><strong>Transaction ID:</strong> {{ $invoice->stripe_invoice_id }}</p>
        @elseif($invoice->paypal_sale_id)
            <p><strong>Payment Method:</strong> PayPal</p>
            <p><strong>Transaction ID:</strong> {{ $invoice->paypal_sale_id }}</p>
        @endif
        
        @if($invoice->status === 'paid')
            <p><strong>Payment Status:</strong> <span class="status paid">Paid</span></p>
        @elseif($invoice->status === 'pending')
            <p><strong>Payment Status:</strong> <span class="status pending">Pending</span></p>
        @else
            <p><strong>Payment Status:</strong> <span class="status failed">{{ ucfirst($invoice->status) }}</span></p>
        @endif
    </div>

    <div class="footer">
        <p>Thank you for your business!</p>
        <p>This is a computer-generated invoice. No signature required.</p>
    </div>
</body>
</html>
