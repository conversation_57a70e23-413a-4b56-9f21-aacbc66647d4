<?php

use App\Http\Controllers\api\Apicontroller;
use App\Http\Controllers\api\CategoryApiController;
use App\Http\Controllers\api\ChatApicontroller;
use App\Http\Controllers\api\CompanyApiController;
use App\Http\Controllers\api\ComplaintController;
use App\Http\Controllers\api\CountryApiController;
use App\Http\Controllers\api\DealsApiController;
use App\Http\Controllers\api\MessageApiController;
use App\Http\Controllers\api\UserController;
use App\Http\Controllers\ChatbotController;
use App\Http\Controllers\OAuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::post('get-api-access-token', [ApiController::class, 'getAPIAccessToken']);

Route::post('/oauth/login', [OAuthController::class, 'login']);
Route::post('/oauth/token', [OAuthController::class, 'getAccessToken']);

Route::post('/oauth/chatbot', [ChatbotController::class, 'getjavachatresponse']);

Route::middleware(['apiaccess'])->group(function () {

    Route::get('/user', function (Request $request) {
        return $request->user();
    })->middleware('auth:sanctum');

    // Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    //     return $request->user();
    // });

    Route::group(['prefix' => 'user'], function () {
        Route::post('/add', [UserController::class, 'SaveUser']);
        Route::post('/login', [UserController::class, 'userLogin']);
        Route::post('/auth-login', [UserController::class, 'userAuthLogin']);
        Route::post('/google-login', [UserController::class, 'googleLogin']);
        Route::post('/update-password', [UserController::class, 'changePassword']);
        Route::get('/user-status/{id}', [UserController::class, 'getUserStatus']);
    });

    Route::post('send-otp', [ApiController::class, 'sendOtp']);
    Route::post('verify-otp', [ApiController::class, 'verifyMailOTP']);

    // Route::post('send-mail', [ApiController::class, 'sendmail']);
    // Route::post('verify-mail', [ApiController::class, 'verifyMail']);

    Route::group(['prefix' => 'company'], function () {
        Route::post('/save', [CompanyApiController::class, 'SaveCompany']);

    });

    Route::group(['prefix' => 'category'], function () {
        Route::post('/list', [CategoryApiController::class, 'LoadCategory']);
    });

    Route::middleware(['auth.jwt'])->group(function () {

        Route::group(['prefix' => 'user'], function () {
            Route::post('/update', [UserController::class, 'UpdateUser']);
            Route::post('/delete-current-picture', [UserController::class, 'deletePicture']);
            Route::post('/get-user-detail', [UserController::class, 'getUserDetailApi']);
            Route::post('/logout', [UserController::class, 'Logout']);
            Route::post('/save-password', [UserController::class, 'updatePassword']);
            // Route::post('/update-profile', [UserController::class, 'UpdateUserWithImage']);
        });

        Route::group(['prefix' => 'complaint'], function () {
            Route::post('/addComplain', [ComplaintController::class, 'store']);
            Route::post('/showComplain', [ComplaintController::class, 'showComplain']);
            Route::post('/get-single-complain', [ComplaintController::class, 'getsingleComplain']);
            Route::post('/closeComplain', [ComplaintController::class, 'closeComplain']);
            Route::post('/sendComplainMessage', [ComplaintController::class, 'sendComplainMessage']);
            Route::post('/getComplainMessages', [ComplaintController::class, 'getComplainMessage']);
        });

        Route::group(['prefix' => 'category'], function () {
            Route::post('/add', [CategoryApiController::class, 'AddCategory']);
        });

        Route::post('/get-user-inquery', [Apicontroller::class, 'getallUserEnquery']);

        Route::post('/get-chatbots', [Apicontroller::class, 'chatbotList']);

        Route::post('/send-message', [Apicontroller::class, 'sendChatmessage']);
        Route::post('/company-message-list', [Apicontroller::class, 'companyConversationList']);

        Route::post('/get-users-chat-list', [Apicontroller::class, 'getUserChatInboxList']);
        Route::post('/replay-message-company', [Apicontroller::class, 'sendCompanyResponseMessage']);

        Route::post('/get-single-message-data', [Apicontroller::class, 'getSingleMessageData']);

        Route::post('/send-message-by-agent', [Apicontroller::class, 'sendChatmessageByAgent']);

        Route::post('/get-chatbot-history', [Apicontroller::class, 'chatbotJson']);
        Route::post('/get-last-chathistory', [Apicontroller::class, 'retriveLastMessage']);
        Route::post('/get-chat-usrelist', [ChatApicontroller::class, 'chatUserList']);

        Route::group(['prefix' => 'messages'], function () {
            Route::post('/last-messages', [MessageApiController::class, 'LoadLastMessages']);
            Route::post('/save-last-message', [MessageApiController::class, 'SaveLastMessage']);
        });

        Route::group(['prefix' => 'user'], function () {
            Route::post('/search', [ChatbotController::class, 'searchUser']);
            Route::post('/list', [UserController::class, 'loadUser']);
            Route::post('/authorised-user', [UserController::class, 'authorisedUser']);

        });

        Route::group(['prefix' => 'company'], function () {
            Route::post('/list', [Apicontroller::class, 'loadCompany']);
            Route::post('/load-business', [CompanyApiController::class, 'LoadBusiness']);
            Route::post('/business-detail', [CompanyApiController::class, 'ShowBusiness']);
            Route::post('/business-category-based', [CompanyApiController::class, 'ShowBusinessCategoryBased']);

        });

        Route::group(['prefix' => 'country'], function () {
            Route::post('/list', [CountryApiController::class, 'LoadCountry']);
        });

        Route::group(['prefix' => 'deals'], function () {
            Route::post('/list', [DealsApiController::class, 'LoadDeals']);
        });

    });

});

/*
Route::group(['prefix'=>'live-chat'],function(){
    Route::post('/get-chat-usrelist', [ChatApicontroller::class, 'chatUserList']);
    Route::post('/send-message', [ChatApicontroller::class, 'sendChatmessage']);
    Route::post('/get-chat-history', [ChatApicontroller::class, 'livechatJson']);
});*/

