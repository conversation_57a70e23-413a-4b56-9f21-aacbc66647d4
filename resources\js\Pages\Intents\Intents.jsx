import React from 'react';
import DashboardLayout from '../../Layouts/DashboardLayout';
import Button from '../../Components/Button';
import { Link } from '@inertiajs/react';
import {
    DataTable,
    DataTableHeader,
    DataTableBody,
    DataTableRow,
    DataTableHeaderCell,
    DataTableCell,
    DataTableActions,
    PageHeader,
    StatusBadge,
    ActionButton,
    EditIcon,
    DeleteIcon,
    ViewIcon,
    PlusIcon
} from '@/Components/UI';

export default function Intents() {
    // Enhanced placeholder data
    const intents = [
        {
            id: 1,
            name: 'welcome',
            description: 'Greets users when they start a conversation',
            category: 'Greeting',
            status: 'active',
            trainingPhrases: 12,
            lastUpdated: '2024-01-15'
        },
        {
            id: 2,
            name: 'Content',
            description: 'Handles content-related queries and requests',
            category: 'Information',
            status: 'active',
            trainingPhrases: 8,
            lastUpdated: '2024-01-14'
        },
        {
            id: 3,
            name: 'fallback',
            description: 'Default response when no other intent matches',
            category: 'System',
            status: 'active',
            trainingPhrases: 5,
            lastUpdated: '2024-01-10'
        },
        {
            id: 4,
            name: 'check_balance',
            description: 'Allows users to check their account balance',
            category: 'Banking',
            status: 'active',
            trainingPhrases: 15,
            lastUpdated: '2024-01-12'
        },
        {
            id: 5,
            name: 'fgfdg',
            description: 'Test intent for development purposes',
            category: 'Testing',
            status: 'draft',
            trainingPhrases: 2,
            lastUpdated: '2024-01-08'
        },
        {
            id: 6,
            name: 'full_order_detail',
            description: 'This intent is providing full detail of an order',
            category: 'Orders',
            status: 'active',
            trainingPhrases: 20,
            lastUpdated: '2024-01-16'
        },
        {
            id: 7,
            name: 'how_are_you',
            description: 'Responds to casual conversation and wellbeing inquiries',
            category: 'Conversation',
            status: 'active',
            trainingPhrases: 10,
            lastUpdated: '2024-01-13'
        },
        {
            id: 8,
            name: 'my_email',
            description: 'Handles email-related queries and requests',
            category: 'Account',
            status: 'inactive',
            trainingPhrases: 6,
            lastUpdated: '2024-01-09'
        },
        {
            id: 9,
            name: 'my_name',
            description: 'Manages user name and identity information',
            category: 'Account',
            status: 'active',
            trainingPhrases: 7,
            lastUpdated: '2024-01-11'
        },
    ];

    const getStatusVariant = (status) => {
        switch (status.toLowerCase()) {
            case 'active': return 'success';
            case 'inactive': return 'secondary';
            case 'draft': return 'warning';
            default: return 'secondary';
        }
    };

    const getCategoryVariant = (category) => {
        switch (category.toLowerCase()) {
            case 'greeting': return 'primary';
            case 'system': return 'info';
            case 'banking': return 'success';
            case 'orders': return 'warning';
            case 'testing': return 'error';
            default: return 'secondary';
        }
    };

    return (
        <DashboardLayout title="Intents">
            <title>Intents</title>

            <div className="space-y-6">
                <PageHeader
                    title="Intent Management"
                    subtitle="Manage chatbot intents and their training phrases"
                >
                    <Link href="/intents/create">
                        <Button type="button" className="flex items-center gap-2">
                            <PlusIcon className="w-4 h-4" />
                            Create Intent
                        </Button>
                    </Link>
                </PageHeader>

                <DataTable>
                    <DataTableHeader>
                        <DataTableRow>
                            <DataTableHeaderCell>Intent Name</DataTableHeaderCell>
                            <DataTableHeaderCell>Description</DataTableHeaderCell>
                            <DataTableHeaderCell>Category</DataTableHeaderCell>
                            <DataTableHeaderCell>Status</DataTableHeaderCell>
                            <DataTableHeaderCell>Training Phrases</DataTableHeaderCell>
                            <DataTableHeaderCell>Last Updated</DataTableHeaderCell>
                            <DataTableHeaderCell>Actions</DataTableHeaderCell>
                        </DataTableRow>
                    </DataTableHeader>
                    <DataTableBody>
                        {intents.map((intent) => (
                            <DataTableRow key={intent.id}>
                                <DataTableCell className="font-medium text-gray-900">
                                    <div className="flex items-center gap-2">
                                        <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                                            {intent.name}
                                        </code>
                                    </div>
                                </DataTableCell>
                                <DataTableCell className="max-w-xs">
                                    <p className="truncate" title={intent.description}>
                                        {intent.description || 'No description provided'}
                                    </p>
                                </DataTableCell>
                                <DataTableCell>
                                    <StatusBadge variant={getCategoryVariant(intent.category)}>
                                        {intent.category}
                                    </StatusBadge>
                                </DataTableCell>
                                <DataTableCell>
                                    <StatusBadge variant={getStatusVariant(intent.status)}>
                                        {intent.status}
                                    </StatusBadge>
                                </DataTableCell>
                                <DataTableCell>
                                    <div className="flex items-center gap-2">
                                        <span className="text-gray-900">{intent.trainingPhrases}</span>
                                        <span className="text-gray-500 text-sm">phrases</span>
                                    </div>
                                </DataTableCell>
                                <DataTableCell className="text-gray-500">
                                    {new Date(intent.lastUpdated).toLocaleDateString()}
                                </DataTableCell>
                                <DataTableActions>
                                    <ActionButton variant="primary" size="sm" icon={ViewIcon} title="View Details" />
                                    <Link href={`/intents/edit/${intent.id}`}>
                                        <ActionButton variant="secondary" size="sm" icon={EditIcon} title="Edit Intent" />
                                    </Link>
                                    <ActionButton variant="danger" size="sm" icon={DeleteIcon} title="Delete Intent" />
                                </DataTableActions>
                            </DataTableRow>
                        ))}
                    </DataTableBody>
                </DataTable>

                {/* Empty State */}
                {intents.length === 0 && (
                    <div className="text-center py-12">
                        <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" strokeWidth="1" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No intents found</h3>
                        <p className="text-gray-500 mb-4">Get started by creating your first intent.</p>
                        <Link href="/intents/create">
                            <Button type="button">Create Intent</Button>
                        </Link>
                    </div>
                )}
            </div>
        </DashboardLayout>
    );
} 