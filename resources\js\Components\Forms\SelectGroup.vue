<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps({
  label: {
    type: String,
    default: 'Subject',
  },
  placeholder: {
    type: String,
    default: 'Type your subject',
  },
  options: {
    type: Array as () => { value: string; text: string }[],
    required: true,
  },
});

const selectedOption = ref<string>('');
const isOptionSelected = ref<boolean>(false);

watch(selectedOption, (newValue) => {
  isOptionSelected.value = !!newValue;
});
</script>

<template>
  <div class="mb-4.5">
    <label class="block mb-2.5 text-slate-800">{{ label }}</label>

    <div class="relative z-20 bg-transparent">
      <select v-model="selectedOption"
        class="relative z-20 border-stroke focus:border-cfp-500 bg-transparent px-4 py-2 border active:border-cfp-500 rounded w-full transition appearance-none outline-none"
        :class="{ 'text-slate-800': isOptionSelected }">
        <option value="" disabled selected>{{ placeholder }}</option>
        <option v-for="option in options" :key="option.value" :value="option.value">
          {{ option.text }}
        </option>
      </select>
    </div>
  </div>
</template>
