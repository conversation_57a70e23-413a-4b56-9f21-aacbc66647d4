<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Mail\SendOTP;
use App\Models\SessionDomain;
use App\Models\User;
use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Inertia\Response;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): Response
    {

        // if (isset($_REQUEST['triggerDomain']) && isset($_REQUEST['randomToken'])) {
        //     $triggerDomain = $_REQUEST['triggerDomain'];
        //     $token_id      = $_REQUEST['randomToken'];
        //     $data          = ['session_domain' => $triggerDomain, 'token_id' => $token_id, 'status' => 0];

        //     // Check if the domain already exists in the database
        //     if (! SessionDomain::where('session_domain', $triggerDomain)->first()) {
        //         SessionDomain::create($data);
        //     }

        //     $domainStatusQry = SessionDomain::where('session_domain', $triggerDomain)->first();

        //     $curl = curl_init();

        //     curl_setopt_array($curl, [
        //         CURLOPT_URL            => 'http://bots.eruditetechnology.com/api/get-user-token-detail/'.$token_id,
        //         CURLOPT_RETURNTRANSFER => true,
        //         CURLOPT_ENCODING       => '',
        //         CURLOPT_MAXREDIRS      => 10,
        //         CURLOPT_TIMEOUT        => 0,
        //         CURLOPT_FOLLOWLOCATION => true,
        //         CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
        //         CURLOPT_CUSTOMREQUEST  => 'GET',
        //     ]);

        //     $response = curl_exec($curl);
        //     curl_close($curl);

        //     // Decode the JSON response into a PHP array
        //     $data = json_decode($response, true);

        //     // Check if decoding was successful
        //     if (json_last_error() === JSON_ERROR_NONE) {
        //         // Access the data
        //         $useremail = $data['useremail'];
        //         $userName  = $data['name'];
        //         $userPhone = $data['phone'];
        //         $userid    = $data['userid'];
        //         $token     = $data['token'];

        //         if ($token == $token_id) {
        //             $domainStatusQry->status = 1;
        //             $domainStatusQry->save();

        //             $checkUser = User::where('email', $useremail)->first();
        //             if ($checkUser) {
        //                 $saveUser                     = User::find($checkUser->id);
        //                 $saveUser->name               = $userName;
        //                 $saveUser->phone              = $userPhone;
        //                 $saveUser->verificationStatus = 'verified';
        //                 $saveUser->vendor_domain      = $triggerDomain;
        //                 $saveUser->save();
        //                 Auth::login($saveUser);

        //                 return redirect()->intended(route('chatbot'));
        //             } else {
        //                 $saveUser                     = new User;
        //                 $saveUser->name               = $userName;
        //                 $saveUser->email              = $useremail;
        //                 $saveUser->phone              = $userPhone;
        //                 $saveUser->verificationStatus = 'verified';
        //                 $saveUser->vendor_domain      = $triggerDomain;
        //                 $saveUser->password           = Hash::make(rand(1111, 2222));
        //                 $saveUser->save();

        //                 Auth::login($saveUser);

        //                 return redirect()->intended(route('chatbot'));
        //             }
        //         }
        //     }
        // }

        return Inertia::render('Auth/Login', [
            'canResetPassword' => Route::has('password.request'),
            'status'           => session('status'),
        ]);
    }

    public function userAuth(Request $request)
    {
        if ($request->has(['vid', 'uid', 'type'])) {
            $vendorid     = $request->vid;
            $vendorDetail = User::find($vendorid);

            if (! $vendorDetail) {
                return response()->json(['status' => false, 'message' => 'invalid vendor request']);
            }

            $userid       = base64_decode($request->uid);
            $checkAlready = User::where('vid', $vendorid)->where('uid', $userid)->first();
            if ($checkAlready) {
                return response()->json(['status' => false, 'message' => 'Your verification has already succeeded.']);
                exit;
            }

        } else {
            return response()->json(['status' => false, 'message' => 'invalid request parameter']);
        }

        return Inertia::render('Auth/Auth', [
            'vendorDetail' => $vendorDetail,
            'vid'          => $request->vid,
            'uid'          => $request->uid,
        ]);
    }

    public function sendAuthOtp(Request $request)
    {

        try {

            $vendorid     = $request->vid;
            $vendorDetail = User::find($vendorid);

            if (trim($request->email) != '' && trim($request->otp) != '') {

                $userDetail = User::where('email', $request->email)->where('profileType', 'user')->first();

                if (! $userDetail) {
                    return response()->json(['status' => 'unverified', 'message' => 'Invalid authentication']);
                }

                if (! $vendorDetail) {
                    return response()->json(['status' => 'unverified', 'message' => 'Invalid authentication']);
                }

                $userDetail = User::where('email', $request->email)
                    ->where('profileType', 'user')
                    ->where('otp', $request->otp)
                    ->first();

                $uid = base64_decode($request->uid);

                if ($userDetail) {
                    $userDetail->otp = '';
                    $userDetail->vid = $vendorid;
                    $userDetail->uid = $uid;
                    $userDetail->save();

                    return response()->json(['status' => 'verified', 'message' => 'Verification Success']);
                } else {
                    return response()->json(['status' => 'unverified', 'message' => 'Invalid authentication']);
                }

            } else {

                if (! $vendorDetail) {
                    return response()->json(['status' => false, 'message' => 'Invalid request']);
                }

                $userDetail = User::where('email', $request->email)->where('profileType', 'user')->first();

                if (! $userDetail) {
                    return response()->json(['status' => false, 'message' => 'Invalid Email id']);
                } else {
                    $OTP      = rand(11111, 99999);
                    $mailData = [
                        'otp'     => $OTP,
                        'subject' => 'Please verify your OTP',
                    ];

                    $userDetail->otp = $OTP;
                    $userDetail->save();

                    return response()->json(['status' => true, 'message' => 'Verification OTP sent successfully!']);
                    $sendMailid = '<EMAIL>';
                    Mail::to($sendMailid)->send(new SendOTP($mailData));

                    return response()->json(['status' => true, 'message' => 'Verification OTP sent successfully!']);
                }

            }

        } catch (Exception $ex) {
            return response()->json(['status' => false, 'message' => $ex->getMessage()]);
        }

    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        $request->authenticate();

        $user = Auth::user();

        if ($user->verificationStatus !== 'verified' || $user->status !== 'active') {
            Auth::logout();

            return back()->withErrors([
                'email' => 'Your account is not verified or not active.',
            ]);
        }

        $request->session()->regenerate();

        return redirect()->intended(route('dashboard', absolute: false));
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {

        if (session()->has('impersonate')) {
            $adminId = session()->get('impersonate');
            session()->forget('impersonate');
            Auth::logout();
            Auth::loginUsingId($adminId);

            return redirect()->route('dashboard');
        } else {
            Auth::guard('web')->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return redirect()->route('user-login');
        }

    }
}
