<?php

use App\Http\Middleware\HandleInvalidToken;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

// Load local environment if running locally
// if (in_array(gethostname(), ['DESKTOP-Q6I8U6U', 'localhost', '127.0.0.1'])) {
//     if (file_exists(__DIR__.'/../.env.local')) {
//         copy(__DIR__.'/../.env.local', __DIR__.'/../.env');
//     }
// } else {
//     if (file_exists(__DIR__.'/../.env.production')) {
//         copy(__DIR__.'/../.env.production', __DIR__.'/../.env');
//     }
// }

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        channels: __DIR__.'/../routes/channels.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->web(append: [
            \App\Http\Middleware\HandleInertiaRequests::class,
            \Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets::class,
            \App\Http\Middleware\UpdateLastActivity::class,
        ]);

        $middleware->validateCsrfTokens(except: [
            'dtadmin/send-message',
            'dtadmin/send-agent-message',
            'send-message-guest',
            'dtadmin/cancel-agent-conversaction',
            'auth',
            '*',
        ]);

        $middleware->alias([
            'checkRole' => \App\Http\Middleware\checkRole::class,
            'auth.jwt'  => HandleInvalidToken::class,
            'apiaccess' => \App\Http\Middleware\ValidateApiAccessToken::class,
        ]);

        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
