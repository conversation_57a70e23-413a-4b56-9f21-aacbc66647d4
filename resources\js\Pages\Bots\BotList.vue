<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import ApplicationLogo from '@/Components/ApplicationLogo.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import BreadcrumbDefault from '@/Components/Breadcrumbs/BreadcrumbDefault.vue';
import axios from 'axios';
import { ref, watch, nextTick, onMounted } from 'vue'; // Import watch and nextTick
import { usePage, Head } from '@inertiajs/vue3';
import PageHeading from '@/Components/Global/PageHeading.vue';
import DtIconGlobal from '@/Components/DtIcon/DtIconGlobal.vue';


const pageTitle = ref('BotList')

const { allAgents, success } = usePage().props;

let successMessage = ref(success ? success : null);


const deleteAgent = async (agent) => {
    try {
        // Show a SweetAlert confirmation popup
        const confirmation = await Swal.fire({
            title: 'Are you sure?',
            text: 'You are about to delete this bot. This action cannot be undone. Are you sure you want to proceed?',
            icon: 'warning',
            showCancelButton: true,

            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel',
            customClass: {
                confirmButton: 'dk-update-btn',
                cancelButton: 'dk-cancle-btn',
            },
            buttonsStyling: false,
        });

        // If user confirms deletion
        if (confirmation.isConfirmed) {
            const response = await axios.delete(route('chatbots.delete', { id: agent.id }));
            if (response.data.success) {
                // Remove the deleted agent from the allAgents array
                const index = allAgents.findIndex(a => a.id === agent.id);
                if (index !== -1) {
                    allAgents.splice(index, 1);
                }
                // Show success message
                //Swal.fire('Deleted!', 'The bot has been deleted.', 'success');
            } else {
                throw new Error('Failed to delete bot');
            }
        }
    } catch (error) {
        console.error(error);
        // Show error message if deletion fails
        Swal.fire('Error', 'An error occurred while deleting the bot.', 'error');
    }
};


onMounted(() => {
    setTimeout(() => {
        successMessage = null;
    }, 1000);
});


</script>


<template>

    <Head :title="pageTitle" />

    <AuthenticatedLayout>

        <PageHeading :title="pageTitle" :href="route('chatbots.add')" buttonLabel="Add Bot"></PageHeading>


        <Transition v-if="successMessage" name="fade">
            <p class="mb-4 text-green-400 text-sm successMessages">{{ successMessage }}</p>
        </Transition>

        <div class="flex">


            <div class="flex-1">

                <div class="p-6 border rounded-md">

                    <div class="max-w-full overflow-x-auto">
                        <table class="dt-deals-table">
                            <thead>
                                <tr class="bg-gray-2 text-left">

                                    <th class="dt-deals-th">
                                        Chatbot Name
                                    </th>
                                    <th class="dt-deals-th">
                                        Address
                                    </th>
                                    <th class="dt-deals-th">
                                        Project ID
                                    </th>
                                    <th class="dt-deals-th">
                                        Client Email
                                    </th>

                                    <th class="dt-deals-th">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="agent in allAgents" :key="agent.id" class="dt-deals-tr">
                                    <td class="dt-deals-td">
                                        <div class="text-gray-900 text-sm">{{ agent.chatbot_name }}
                                        </div>
                                    </td>
                                    <td class="dt-deals-td">
                                        <div class="text-gray-900 text-sm">{{ agent.companyAdd }} ,
                                            {{ agent.city }} , {{ agent.state }} , {{ agent.zipCode }}</div>
                                    </td>
                                    <td class="dt-deals-td">
                                        <div class="text-gray-900 text-sm">{{
                                            agent.chatbot_project_id }}</div>
                                    </td>
                                    <td class="dt-deals-td">
                                        <div class="text-gray-900 text-sm">{{
                                            agent.chatbot_client_email }}</div>
                                    </td>
                                    <td class="dt-deals-td">
                                        <div class="dt-deals-actions">
                                            <!-- edit button  -->
                                            <a :href="route('chatbots.edit', { id: agent.id })"
                                                class="dt-deals-action-btn">
                                                <DtIconGlobal :type="'edit'" />
                                            </a>

                                            <!-- delete button  -->
                                            <button @click="deleteAgent(agent)"
                                                class="dt-deals-action-btn dt-deals-delete-btn">
                                                <DtIconGlobal :type="'delete'" />
                                            </button>
                                        </div>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
        </div>


    </AuthenticatedLayout>
</template>
