<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('subscription_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('invoice_number')->unique();
            $table->string('stripe_invoice_id')->nullable()->unique();
            $table->string('paypal_invoice_id')->nullable()->unique();
            $table->enum('status', ['draft', 'open', 'paid', 'void', 'uncollectible'])->default('draft');
            $table->unsignedBigInteger('amount_due')->default(0); // Amount in cents
            $table->unsignedBigInteger('amount_paid')->default(0); // Amount in cents
            $table->unsignedBigInteger('tax_amount')->default(0); // Tax amount in cents
            $table->string('currency', 3)->default('USD');
            $table->string('billing_reason')->nullable(); // subscription_create, subscription_cycle, etc.
            $table->string('invoice_pdf')->nullable(); // PDF file path
            $table->timestamp('due_date')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index(['status', 'due_date']);
            $table->index('invoice_number');
        });
    }
};
