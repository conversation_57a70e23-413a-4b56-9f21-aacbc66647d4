<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Company>
 */
class CompanyFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'userId' => User::factory(),
            'catId' => fake()->numberBetween(1, 10),
            'countryId' => fake()->numberBetween(1, 50),
            'companyName' => fake()->company(),
            'companyAdd' => fake()->address(),
            'city' => fake()->city(),
            'state' => fake()->state(),
            'zipCode' => fake()->postcode(),
            'websiteUrl' => fake()->url(),
            'chatSupport' => fake()->boolean(),
        ];
    }
}
