<script setup>
import { Head, usePage } from '@inertiajs/vue3';
import { ref } from 'vue';
import App from '../App.vue';
import CompanyList from '../components/shared/CompanyList.vue';
import ChatPopup from '../components/shared/ChatPopup.vue';

const pageTitle = ref('Companies');
const { singleCompany } = usePage().props;

const isPopupOpen = ref(true); // Set to true to open the popup by default
const showIcon = ref(false);

const selectedCompanyName = ref(singleCompany.companyName); // Store the selected company name
const selectedChatProjectId = ref(singleCompany.chatbot_project_id);
const selectedUserId = ref(singleCompany.userId);

const encodeToBase64 = (value) => {
  return btoa(value); // Using btoa() to encode to base64
};

const closePopup = () => {
  isPopupOpen.value = false;
  showIcon.value = false;
};

</script>

<style>
.bg-white.p-6.rounded-md.border.border-solid.w-full.max-w-lg.h-\[60vh\].flex.flex-col.transition-transform.transform {
  border: none !important;
}

.bg-white.p-6.rounded-md.border.border-solid.w-full.max-w-lg.h-\[60vh\].flex.flex-col.transition-transform.transform {
  height: 100vh !important;
}

.cirledigicricle {
  bottom: -4px;
  left: 40px;
  position: absolute;
  z-index: 1000;
  border: 0px;
  margin: 0;
  border-radius: 5px;
  overflow: hidden;
  font: inherit;
  color: inherit;
  text-transform: none;
  display: inline-block;
  padding: .5rem 1rem;
  font-size: 1rem;
  border: 1px solid #03a84e;
  border-color: var(--tawk-header-background-color);
  line-height: 1.5rem;
  text-decoration: none;
  background-color: #03a84e;
  background-color: red;
  color: #fff;
  color: var(--tawk-header-text-color);
  width: 60px;
  height: 60px;
  line-height: 3.75rem;
  padding: 0;
  border-radius: 100%;
  font-size: .937rem;
  box-sizing: border-box;
  vertical-align: middle;
  text-align: center;
}

body {
  background: transparent !important;
  position: fixed;
  right: 0;
  bottom: 0;
}

.mainsectionpop {}

i.fa.fa-times {
  display: none;
}
</style>
<template>

  <Head :title="pageTitle" />

  <div class="mx-auto container">
    <ChatPopup :isOpen="isPopupOpen" :companyName="selectedCompanyName" :source="encodeToBase64(selectedChatProjectId)"
      :umid="encodeToBase64(selectedUserId)" @close="closePopup" />
  </div>
</template>
