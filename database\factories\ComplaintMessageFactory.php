<?php

namespace Database\Factories;

use App\Models\Complaint;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ComplaintMessage>
 */
class ComplaintMessageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $messages = [
            'Thank you for reporting this issue. We are looking into it.',
            'I have reproduced the issue and escalated it to our development team.',
            'Could you please provide more details about when this issue occurs?',
            'We have identified the root cause and are working on a fix.',
            'The issue has been resolved. Please test and confirm.',
            'We need additional information to proceed with the investigation.',
            'This has been marked as a high priority issue.',
            'A temporary workaround has been implemented.',
            'The fix has been deployed to production. Please verify.',
            'We apologize for the inconvenience caused by this issue.',
            'This issue is related to a recent system update.',
            'We are monitoring the system to ensure the fix is working.',
            'Additional testing is required before we can close this ticket.',
            'The development team is working on a permanent solution.',
            'We have updated our documentation to prevent similar issues.',
            'This issue affects multiple users and is being prioritized.',
            'A patch has been released to address this problem.',
            'We are conducting a thorough investigation of this issue.',
            'The issue has been resolved and preventive measures implemented.',
            'Thank you for your patience while we worked on this issue.',
        ];

        return [
            'complainId' => Complaint::factory(),
            'senderId' => User::factory(),
            'content' => fake()->randomElement($messages),
        ];
    }
}
