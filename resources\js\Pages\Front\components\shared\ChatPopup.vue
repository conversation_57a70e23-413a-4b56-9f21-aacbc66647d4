<script setup>
import { ref, nextTick, reactive } from 'vue';
import { useForm } from '@inertiajs/vue3'; // Import useForm for form handling

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true
  },
  source: {
    type: String,
    required: true
  },
  company_id: {
    type: String,
    required: true
  },
  umid: {
    type: String,
    required: true
  },
  companyName: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['close']);

const beforeEnter = (el) => {
  el.style.opacity = 0;
  el.style.transform = 'translate(100%, 100%) scale(0.95)';
};

const enter = (el, done) => {
  el.offsetHeight; // trigger reflow
  el.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
  el.style.opacity = 1;
  el.style.transform = 'translate(0, 0) scale(1)';
  done();
};

const leave = (el, done) => {
  el.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
  el.style.opacity = 0;
  el.style.transform = 'translate(100%, 100%) scale(0.95)';
  setTimeout(done, 300); // Match the transition duration
};

const messages = ref([]);
const showInput = ref(true);
const inputText = ref('');
const status_fallback = ref(0);
const showTypingIndicator = ref(false);
const baseurl = window.location.origin;
const textInput = ref(null);

// Inertia form setup
const form = useForm({
  name: '',
  email: '',
  complaint_detail: '',
  company_id: props.company_id
});

const formErrors = reactive({
  name: null,
  email: null,
  complaint_detail: null
});

const csrfToken = document.querySelector('meta[name="csrf-token"]').content;

const validateForm = () => {
  let isValid = true;

  // Reset error messages
  formErrors.name = null;
  formErrors.email = null;
  formErrors.complaint_detail = null;

  // Simple validation
  if (form.name.trim() === '') {
    formErrors.name = 'Name is required';
    isValid = false;
  }
  if (form.email.trim() === '' || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    formErrors.email = 'Valid email is required';
    isValid = false;
  }
  if (form.complaint_detail.trim() === '') {
    formErrors.complaint_detail = 'Query detail is required';
    isValid = false;
  }

  return isValid;
};

const submitComplaintForm = async () => {
  if (!validateForm()) {
    return; // Stop if form validation fails
  }

  try {
    form.post('/store-guest-complain', {
      onSuccess: () => {
        alert('Complaint submitted successfully');
        form.reset();
      },
      onError: (errors) => {
        formErrors.name = errors.name?.[0];
        formErrors.email = errors.email?.[0];
        formErrors.complaint_detail = errors.complaint_detail?.[0];
      }
    });
  } catch (error) {
    console.error('Error submitting complaint:', error);
  }
};

const sendMessage = async () => {
  try {
    showInput.value = false;

    if (inputText.value.trim() === '') {
      return false;
    }

    showTypingIndicator.value = true;

    if (inputText.value.trim() !== '') {
      messages.value.push({ text: inputText.value, sender: 'user' });
    }
    const inputMesage = inputText.value;
    inputText.value = '';

    const requestBody = {
      status_fallback: status_fallback.value,
      message: inputMesage,
      company_id: document.querySelector('input[name="company_id"]').value,
      type: document.querySelector('input[name="type"]').value // Pass the type field
    };

    const response = await fetch('/send-guest-msg', {
      method: 'POST',
      headers: {
        'X-CSRF-TOKEN': csrfToken,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    const responseData = await response.json();

    setTimeout(() => {
      let lastMessage = document.querySelector('#messages > div:nth-last-child(2)');
      if (lastMessage) {
        lastMessage.scrollIntoView({ behavior: 'smooth' });
      }

      showTypingIndicator.value = false;

      if (responseData.status_fallback == 1) {
        showInput.value = false;
        status_fallback.value = 1;
      } else {
        showInput.value = true;
        status_fallback.value = responseData.status_fallback;
      }

      if (responseData.message.trim() !== '') {
        messages.value.push({ text: responseData.message, sender: 'server' });
      }

      nextTick(() => {
        textInput.value.focus(); // Focus the input field
      });

    }, 500);
  } catch (error) {
    console.error('Error sending message:', error);
    showTypingIndicator.value = false;
  }
};

const closePopup = () => {
  messages.value = [];
  showInput.value = true;
  status_fallback.value = 0;
  emit('close');
};

</script>


<template>
  <transition name="slide-fade" @before-enter="beforeEnter" @enter="enter" @leave="leave">
    <div v-if="isOpen" class="fixed inset-0 flex justify-end items-end bg-gray-100 bg-opacity-0">
      <div class="flex flex-col bg-white  rounded-tl-md w-full max-w-[400px] h-[50vh] transform transition-transform">
        <div class="right-5 absolute flex justify-end mb-4">
          <button @click="closePopup" class="text-white hover:text-red-500 top-2 left-2 relative">
            <i class="fa fa-times" aria-hidden="true"></i>
          </button>
        </div>
        <h2 class="pb-2 border-b font-medium text-base text-white px-4 py-2 rounded-tl-md rounded-tr-md bg-cfp">
          Chat with {{
            companyName
          }}
        </h2>
        <div class="flex flex-col flex-1 h-[40vh] p-4 border-t-0 border border-solid">
          <div id="messages"
            class="flex flex-col flex-1 space-y-4 scrollbar-thumb-blue scrollbar-thumb-rounded scrollbar-w-2 overflow-y-auto scrollbar-track-blue-lighter scrolling-touch">
            <!-- Message display -->
            <div v-for="(message, index) in messages" :key="index" :class="message.sender + '_message'">
              <div class="flex"
                :class="message.sender === 'user' ? 'items-end justify-end' : 'items-start justify-left'">
                <template v-if="message.sender === 'user'">
                  <div class="flex flex-col items-end space-y-2 bg-cfp-500/10 px-4 py-2 rounded-md max-w-lg text-sm">
                    <div>
                      <span v-html="message.text"></span>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="flex flex-col items-start space-y-2 bg-gray-100 px-4 py-2 rounded-md max-w-lg text-sm">
                    <div>
                      <span v-html="message.text"></span>
                    </div>
                  </div>
                </template>
              </div>
            </div>
            <!-- Typing indicator -->
            <div v-if="showTypingIndicator">
              <div class="flex items-end">
                <div class="flex flex-col items-start space-y-2 order-2 text-md leading-tight">
                  <div><img :src="baseurl + '/microsoft-microsoft365.gif'" alt="..." class="ml-6 pb-5 w-8"></div>
                </div>
              </div>
            </div>
          </div>
          <!-- Input area -->
          <div class="border-gray-200 mb-2 sm:mb-0 pt-4 border-t">
            <div class="relative flex">
              <input type="hidden" v-model="status_fallback"
                class="border-gray-200  py-2 pr-16 pl-5 border focus:ring-cfp focus:ring-0 focus:shadow-none focus:border-none rounded-md w-full text-gray-600 text-md placeholder-gray-600" />
              <input ref="textInput" v-model="inputText" @keyup.enter="sendMessage" type="text"
                placeholder="Say something..." autocomplete="off" autofocus="true" id="inputsource"
                :disabled="!showInput"
                class="border-gray-200 text-sm py-2 pr-16 pl-3 border rounded-md w-full text-gray-600 text-md placeholder-gray-600 focus:outline-none focus:ring-0 focus:ring-cfp" />
              <input type="hidden" name="type" :value="source">
              <input type="hidden" name="company_id" :value="company_id">
              <div class="right-2 absolute inset-y-1 sm:flex items-center">
                <button @click="sendMessage" type="button"
                  class="inline-flex justify-center items-center w-6 h-6 text-white transition duration-200 ease-in-out">
                  <svg viewBox="0 0 32 32" fill="currentColor" class="fill-primary">
                    <path
                      d="M9.05674797,7.10056554 L9.13703813,7.13553157 L25.4390381,15.1015316 L25.5284558,15.1506535 L25.6286153,15.2222405 C25.7452987,15.313793 25.8339182,15.4266828 25.895416,15.5505399 L25.9423517,15.6622033 L25.9751927,15.7773803 L25.9891204,15.8509608 L25.998657,15.9475578 L25.9972397,16.0748669 L25.9800642,16.201216 L25.9701282,16.2435678 C25.9550365,16.3071288 25.9331784,16.3694784 25.9050831,16.4294253 L25.8937351,16.4490792 C25.8488724,16.5422577 25.7878083,16.6290528 25.7112518,16.7055442 L25.609137,16.7931281 L25.539527,16.8424479 L25.4390381,16.8984684 L9.05674797,24.8994345 C8.4880852,25.1179893 7.84373932,24.9716543 7.42618713,24.5298922 C7.02348961,24.1049956 6.89354829,23.48994 7.08502271,22.9526995 L9.44381329,15.9994998 L7.08997091,9.06153122 C6.90991684,8.5560159 7.00409914,7.99707209 7.33051276,7.58090053 L7.4252609,7.47108641 C7.84373932,7.02834566 8.4880852,6.8820107 9.05674797,7.10056554 Z">
                    </path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<style scoped>
/* Transition classes for slide and fade effect */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.slide-fade-enter,
.slide-fade-leave-to

/* .slide-fade-leave-active in <2.1.8 */
  {
  opacity: 0;
  transform: translate(100%, 100%) scale(0.95);
}
</style>
