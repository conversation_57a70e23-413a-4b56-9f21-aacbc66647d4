<script setup>
import { ref } from 'vue'
import { Link } from '@inertiajs/vue3';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import PageHeading from '../Global/PageHeading.vue';
import DtIconGlobal from '../DtIcon/DtIconGlobal.vue';

const props = defineProps(['deals']);

let dealsData = props.deals.data;

var adminBaseurl = window.location.origin + '/dtadmin';

let deals = props.deals;

const formatDate = (dateString) => {
  const date = new Date(dateString);
  const formattedDate = `${date.getFullYear()}-${('0' + (date.getMonth() + 1)).slice(-2)}-${('0' + date.getDate()).slice(-2)} ${('0' + date.getHours()).slice(-2)}:${('0' + date.getMinutes()).slice(-2)}`;
  return formattedDate;
}

const deleteItem = async (item) => {
  try {
    // Show a SweetAlert confirmation popup
    const confirmation = await Swal.fire({
      title: 'Are you sure?',
      text: 'You are about to delete this item. This action cannot be undone. Are you sure you want to proceed?',
      icon: 'warning',
      showCancelButton: true,

      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'Cancel',
      customClass: {
        confirmButton: 'dk-update-btn',
        cancelButton: 'dk-cancle-btn',
      },
      buttonsStyling: false,
    });

    // If user confirms deletion
    if (confirmation.isConfirmed) {
      const response = await axios.delete(route('deals.delete', { id: item.id }));
      if (response.data.success) {
        // Remove the deleted item from the allitems array
        const index = dealsData.findIndex(a => a.id === item.id);
        if (index !== -1) {
          dealsData.splice(index, 1);
        }
        // Show success message
        //Swal.fire('Deleted!', 'The item has been deleted.', 'success');
      } else {
        throw new Error('Failed to delete item');
      }
    }
  } catch (error) {
    console.error(error);
    // Show error message if deletion fails
    Swal.fire('Error', 'An error occurred while deleting the item.', 'error');
  }
};

</script>

<template>
  <!-- <PageHeading :href="route('deal.add')" title="Add Deal" buttonLabel="Add Deal"></PageHeading> -->

  <div class="border-stroke overflow-x-auto w-full bg-white p-6 max-md:p-4 border rounded-md ">
    <div class="max-w-full ">
      <div class="dt-deals-wrapper">
        <table class="dt-deals-table">
          <thead>
            <tr>
              <th scope="col" class="hidden dt-deals-th-checkbox">
                <input type="checkbox" class="dt-deals-checkbox">
              </th>
              <th scope="col" class="dt-deals-th">Title</th>
              <th scope="col" class="dt-deals-th">Content</th>
              <th scope="col" class="dt-deals-th">Start Date</th>
              <th scope="col" class="dt-deals-th">End Date</th>
              <th scope="col" class="dt-deals-th">Status</th>
              <th scope="col" class="dt-deals-th">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in dealsData" :key="index" class="dt-deals-tr">
              <td class="hidden dt-deals-td-checkbox">
                <input type="checkbox" :id="'dt-deals-checkbox-' + item.id" class="dt-deals-checkbox">
              </td>
              <td class="dt-deals-td">
                <div class="dt-deals-title-wrap">
                  <a :href="item.deal_link" class="dt-deals-title-link" target="_blank">
                    {{ item.dealTitle }}
                  </a>
                  <span class="">{{ item.desc }}</span>
                </div>
              </td>
              <td class="dt-deals-td">
                <div class="">{{ item.dealContent }}</div>
              </td>
              <td class="dt-deals-td">
                <div class="">{{ formatDate(item.dealStartOn) }}</div>
              </td>
              <td class="dt-deals-td">
                <div class="">{{ formatDate(item.dealExpiresOn) }}</div>
              </td>
              <td class="dt-deals-td">
                <span class="dt-deals-status" :class="{
                  'dt-deals-status-pending': item.deal_status === 0,
                  'dt-deals-status-active': item.deal_status === 1,
                  'dt-deals-status-ended': item.deal_status === 2
                }">
                  {{ item.deal_status === 0 ? 'Pending' : item.deal_status === 1 ? 'Start' : 'End' }}
                </span>
              </td>
              <td class="dt-deals-td">
                <div class="dt-deals-actions">
                  <!-- edit button  -->
                  <Link :href="adminBaseurl + `/deals/edit/${item.id}`" class="dt-deals-action-btn">

                  <DtIconGlobal :type="'edit'" />

                  </Link>
                  <!-- view button  -->
                  <Link :href="adminBaseurl + `/deals/show/${item.id}`" class="dt-deals-action-btn">
                  <DtIconGlobal :type="'view'" />
                  </Link>
                  <!-- delete button  -->
                  <button @click="deleteItem(item)" class="dt-deals-action-btn dt-deals-delete-btn">
                    <DtIconGlobal :type="'delete'" />

                  </button>
                </div>
              </td>
            </tr>
            <tr v-if="dealsData.length === 0">
              <td colspan="7" class="dt-deals-empty">
                <div>No record found</div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div v-if="dealsData.length > 0" class="dt-table-pagi">
        <div class="content-center row-span-2 row-start-2">
          Showing <b>{{ (deals.current_page - 1) * deals.per_page + 1 }}</b>-
          <b>{{ Math.min(deals.current_page * deals.per_page, deals.total) }}</b>
          from <b>{{ deals.total }}</b> data
        </div>

        <div class="row-start-2 row-end-4 text-end">
          <div class="pagination-links">
            <ul class="flex justify-items-end place-content-end">
              <li v-for="page in deals.links" :key="page.url">
                <button @click="$inertia.visit(page.url)"
                  :class="{ 'bg-cfp-500 text-white': page.active, 'hover:bg-cfp-500 hover:text-white': !page.active }"
                  class="px-3 py-1 rounded-full focus:outline-none mx-1" v-html="page.label"></button>
              </li>
            </ul>
          </div>

        </div>
      </div>

    </div>
  </div>



</template>
