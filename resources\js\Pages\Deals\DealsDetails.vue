<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, usePage } from '@inertiajs/vue3';
import { ref } from 'vue';
import DealdetailsBox from '@/Components/Tables/DealdetailsBox.vue'
import BreadcrumbDefault from '@/Components/Breadcrumbs/BreadcrumbDefault.vue'
import PageHeading from '@/Components/Global/PageHeading.vue';
const pageTitle = ref('Deal Details');
const { dealDetail } = usePage().props;

</script>

<template>

  <Head :title="pageTitle" />


  <AuthenticatedLayout>
    <!-- <PageHeading :title="pageTitle"></PageHeading> -->

    <!-- start code deals details page  -->
    <DealdetailsBox :dealDetail="dealDetail" />
    <!-- end code deals details page  -->

    <!-- <Companies :CompaniesList="CompaniesList"/> -->

  </AuthenticatedLayout>
</template>
