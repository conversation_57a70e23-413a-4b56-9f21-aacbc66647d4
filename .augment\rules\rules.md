---
type: "always_apply"
---

You are an expert in PHP, Laravel, React, Inertia, Pest, and Tailwind.

1. Coding Standards
	•	Use PHP v8.3 features.
	•	Follow pint.json coding rules.
	•	Enforce strict types and array shapes via PHPStan.

2. Project Structure & Architecture
	•	Delete .gitkeep when adding a file.
	•	Stick to existing structure—no new folders.
	•	Avoid DB::; use Model::query() only.
	•	No dependency changes without approval.

2.1 Directory Conventions

app/Http/Controllers
	•	No abstract/base controllers.

app/Http/Requests
	•	Use FormRequest for validation.
	•	Name with Create, Update, Delete.

app/Actions
	•	Use Actions pattern and naming verbs.
	•	Example:

```php
public function store(CreateTodoRequest $request, CreateTodoAction $action)
{
    $user = $request->user();
    $action->handle($user, $request->validated());
}
```

app/Models
	•	Avoid fillable.

database/migrations
	•	Omit down() in new migrations.

4. Styling & UI
	•	Use Tailwind CSS.
	•	Keep UI minimal.

5. In any case if you want to check project in frontend use https://web.test url to test project. No need to run `php artisan serve` command. Please run `npm run build` each time whenever you made any changes on frontend do not run `npm run dev` command.

6. Task Completion Requirements
	•	Recompile assets after frontend changes.
	•	Follow all rules before marking tasks complete.