<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('email');
            $table->enum('profileType', ['user', 'company', 'admin', 'agent'])->default('user')->after('password');
            $table->boolean('verificationStatus')->default(false)->after('profileType');
            $table->boolean('status')->default(true)->after('verificationStatus');
            $table->string('profilePicture')->nullable()->after('status');
            $table->string('otp')->nullable()->after('profilePicture');
            $table->unsignedBigInteger('parent_id')->nullable()->after('otp');
            $table->timestamp('last_activity')->nullable()->after('parent_id');
            $table->timestamp('last_seen')->nullable()->after('last_activity');

            // Add foreign key constraint for parent_id
            $table->foreign('parent_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['parent_id']);
            $table->dropColumn([
                'phone',
                'profileType',
                'verificationStatus',
                'status',
                'profilePicture',
                'otp',
                'parent_id',
                'last_activity',
                'last_seen'
            ]);
        });
    }
};
