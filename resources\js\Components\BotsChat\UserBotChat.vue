<script setup>
import { ref, onMounted, nextTick, watch } from 'vue';
import { Inertia } from '@inertiajs/inertia';
import { debounce } from 'lodash';

const props = defineProps(['authDetail', 'chatData', 'sourceID', 'umid', 'userData']);
let authDetail = props.authDetail;
let authUserid = authDetail.id;
let chatData = props.chatData;
let sourceID = props.sourceID;
let userData = props.userData;
let umid = props.umid;
const messages = ref([]);
const status_fallback = ref(0);
const messagesContainer = ref(null);
const inputText = ref('');
const selectedSlug = ref('');
const showInput = ref(true);


watch(
  () => props.chatData,
  (newChatData, oldChatData) => {
    if (newChatData && JSON.stringify(newChatData) !== JSON.stringify(oldChatData)) {
      messages.value = newChatData;
      chatData = messages.value;
      userData = props.userData;
      sourceID = props.sourceID;
      umid = props.umid;
      realuserid = decodeToBase64(props.umid);
      realsourceID = props.sourceID;
      nextTick(() => scrollToBottom());

      if (Object.keys(newChatData).length > 0) {
        debouncedFetchConversations();
      }
    }
  },
  {
    deep: true,
    immediate: false
  }
);

var baseurl = window.location.origin;
var adminBaseurl = window.location.origin + '/dtadmin';

selectedSlug.value = sourceID;

const decodeToBase64 = (projectId) => {
  return atob(projectId); // Using atob() to decode from base64
}

const encodeToBase64 = (projectId) => {
  return btoa(projectId); // Using btoa() to encode to base64
}

let realuserid = decodeToBase64(umid);
let realsourceID = sourceID;



const timestamptoFormat = (timestamp) => {
  const date = new Date(timestamp * 1000); // Convert seconds to milliseconds
  const today = new Date();

  // Check if the date is today
  if (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  ) {
    return 'Today';
  }

  // Check if the date is yesterday
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);
  if (
    date.getDate() === yesterday.getDate() &&
    date.getMonth() === yesterday.getMonth() &&
    date.getFullYear() === yesterday.getFullYear()
  ) {
    return 'Yesterday';
  }

  // Format as 'Y-m-d H:i'
  const formattedDate = date.toISOString().slice(0, 16).replace('T', ' ');
  return formattedDate;
};

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};



onMounted(() => {


  if (Object.keys(chatData).length !== 0) {
    Object.values(chatData).forEach(item => {
      if (item.message.trim() !== '' && item.id == realuserid) {
        messages.value.push({ text: item.message, timestamp: item.timestamp, sender: 'user' });

      } else if (item.message.trim() !== '' && item.id != realuserid) {
        messages.value.push({ text: item.message, timestamp: item.timestamp, sender: 'server' });
      }
      status_fallback.value = item.status_fallback;
    });
  }
  nextTick(() => scrollToBottom());

});


const sendMessage = async () => {
  try {

    if (inputText.value.trim() === '') {
      return false;
    }

    // Push the user message


    let inputMesage = inputText.value;
    if (inputText.value.trim() !== '') {
      let timecurrent = () => Math.floor(Date.now() / 1000);
      messages.value.push({ text: inputText.value, timestamp: timecurrent(), sender: 'server' });
      nextTick(() => scrollToBottom());
    }
    inputText.value = '';


    let token = document.head.querySelector('meta[name="csrf-token"]').content;

    let requestBody = {
      status_fallback: status_fallback.value,
      message: inputMesage,
      slug: selectedSlug.value, // Pass the selectedSlug
      type: sourceID, // Pass the type field
      userid: realuserid
    };

    const response = await fetch('/dtadmin/send-agent-message', {
      method: 'POST',
      headers: {
        'X-CSRF-TOKEN': token,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (response.status == 500) {
      let errorMessage = await response.json();
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: errorMessage.message,
      }).then(() => {
        var chatUrl = window.location.origin + '/chats';
        Inertia.replace(chatUrl);
      });
      return false;
    }



    const responseData = await response.json();


    setTimeout(function () {

      nextTick(() => scrollToBottom());

      if (responseData.message.trim() !== '') {
        messages.value.push({ text: responseData.message, timestamp: responseData.timestamp, sender: 'server' });
      }


      nextTick(() => {
        let sourceMessage = document.querySelector('#inputsource');
        if (sourceMessage) {
          sourceMessage.focus();
        }
      });

    }, 500);


  } catch (error) {
    console.error('Error sending message:', error);
  }
};

let matchFound = false;
let responseArray = 0;

var elements = document.getElementsByClassName('source_' + sourceID + 'umid_' + umid);
for (var i = 0; i < elements.length; i++) {
  elements[i].style.opacity = '0';
}

const debouncedFetchConversations = debounce(() => {
  fetchLatestConversations();
}, 1000); // Wait 1 second between calls



const fetchLatestConversations = async () => {
  try {
    showInput.value = true;
    const response = await fetch('/dtadmin/get-agent-last-conversation', { method: 'GET' });
    const responseData = await response.json();

    let conversactionList = responseData.botConverList;
    let counter1 = conversactionList.length;
    let counter = 0;
    conversactionList.forEach(conversation => {
        let sourceType = conversation.company_user_id+'_'+conversation.company_id;
      if (sourceType == realsourceID && conversation.user_id == realuserid) {

        if (conversation.status == 2) {
          matchFound = true;
          showInput.value = false;
          messages.value.push({ text: 'User conversation is closed', sender: 'stop' });

        }

        if (conversation.agent_id != authUserid && conversation.status == 1) {
          matchFound = true;
          showInput.value = false;
          messages.value.push({ text: 'The user is already associated with the service provider (' + conversation.agent_name + ')', sender: 'stop' });
        }

      }
      counter++;
    });



    if (counter == counter1) {
      responseArray = 1;
      var elements = document.getElementsByClassName('source_' + sourceID + 'umid_' + umid);
      for (var i = 0; i < elements.length; i++) {
        elements[i].style.opacity = '1';
      }

      nextTick(() => {
        let sourceMessage = document.querySelector('#inputsource');
        if (sourceMessage) {
          sourceMessage.focus();
        }
      });


    }


  } catch (error) {
    console.error('Error fetching latest conversations:', error);
  }



};


fetchLatestConversations();

Echo.private('agent-channel').listen('ChatAgentMessage', (e) => {

  if (matchFound == false) {
    fetchLatestConversations();
  }


  let socketMessage = e.message;
  Object.values(socketMessage).forEach(item => {
    if (item.chat_uid === umid && item.chat_source == sourceID) {
      if (item.text.trim() !== '') {
          let timecurrent = () => Math.floor(Date.now() / 1000);

          if(item.user_id !== authUserid){
              messages.value.push({ text: item.text, timestamp: timecurrent(), sender: 'user' });
          }

      }
      nextTick(() => scrollToBottom());
    }

  });


})




const cancelConversationFun = async () => {
  try {

    let token = document.head.querySelector('meta[name="csrf-token"]').content;

    let requestBody = {
      umid: umid,
      sourceID: sourceID,
    };

    const response = await fetch('/dtadmin/cancel-agent-conversaction', {
      method: 'POST',
      headers: {
        'X-CSRF-TOKEN': token,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    const responseData = await response.json();
    var chatUrl = window.location.origin + '/dtadmin/chats';
    Inertia.replace(chatUrl);

  } catch (error) {
    console.error('Error fetching latest conversations:', error);
  }
};

const cancelConversation = () => {
  if (sourceID && umid) {
    cancelConversationFun();
  }

  // Add your cancel conversation logic here
};







</script>

<template>
  <div class="flex flex-col relative h-full">
    <div class="sticky flex justify-between items-center border-stroke pb-4 border-b">

      <div v-if="chatData.length > 0" class="flex items-center w-full">
        <div class="mr-4.5 rounded-full w-full max-w-13 h-13 overflow-hidden">
          <img v-if="userData.profilePicture && userData.profilePicture.startsWith('http')"
            :src="userData.profilePicture" alt="User" class="w-full h-full object-center object-cover" />
          <img v-else-if="userData.profilePicture" :src="baseurl + '/storage/' + userData.profilePicture" alt="User"
            class="w-full h-full object-center object-cover" />
          <img v-else src="@/assets/images/user/user-profile.jpeg" alt="User"
            class="w-full h-full object-center object-cover" />
        </div>
        <div class="flex flex-col">
          <h5 class="font-medium text-slate-800"> {{ userData.name }} </h5>
          <p class="font-medium text-sm">Unique ID : #{{ userData.id }}</p>
          <!-- <p class="font-medium text-sm"><a :href="adminBaseurl+'/userdetail/'+userData.id" class="underline" target="_blank"> Click to view Profile </a></p> -->
        </div>
      </div>



      <div v-else class="flex items-center">
        Please select a user to start a conversation.
      </div>

      <!-- <div v-if="chatData.length > 0" class="relative">
      <button
        @click="cancelConversation"
        class="bg-red-500 hover:bg-red-600 ml-4 px-4 py-2 rounded focus:ring-2 focus:ring-red-500 text-white focus:outline-none"
      >Cancel
      </button>
      </div> -->

    </div>
    <div class="space-y-3.5 pt-7.5 h-screen max-h-full overflow-auto" :class="'source_' + sourceID + 'umid_' + umid"
      id="messagesContainer" ref="messagesContainer">

      <div v-for="(message, index) in messages" :key="index" :class="message.sender + '_message'">

        <template v-if="message.sender != 'user'">

          <div v-if="message.sender == 'stop'">
            <div class="user_message">
              <div class="max-w-125">
                <div class="bg-cyan-100 mb-2.5 px-5 py-3 rounded-md rounded-tl-none">
                  <p v-html="message.text"></p>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="ml-auto max-w-125">
            <div class="bg-cfp-500/10 mb-2.5 px-5 py-1.5 rounded-md rounded-br-none">
              <p v-html="message.text"></p>
              <p class="font-medium text-[11px] text-gray-400">{{
                timestamptoFormat(message.timestamp)
              }}
              </p>
            </div>
          </div>

        </template>

        <template v-else>
          <div class="max-w-125">
            <div class="bg-gray-100 mb-2.5 px-5 py-1.5 rounded-md rounded-tl-none">
              <p v-html="message.text"></p>
              <p class="font-medium text-[11px] text-gray-400">{{
                timestamptoFormat(message.timestamp)
              }}
              </p>
            </div>
          </div>
        </template>


      </div>

    </div>
    <div v-if="chatData.length > 0" class="absoute bottom-0 w-full border-stroke bg-white px-6 py-5 border-t">
      <div class="flex justify-between items-center space-x-4.5">
        <div class="relative w-full">
          <input type="text" autofocus="true" v-model="inputText" @keyup.enter="sendMessage"
            placeholder="Type something here" id="inputsource" :disabled="!showInput"
            class="border-stroke focus:border-cfp-500 bg-gray pr-19 pl-5 border rounded-md focus:ring-cfp-500/50 w-full h-13 font-medium text-slate-800 outline-none placeholder-body"
            fdprocessedid="98p4pc">

        </div>
        <button @click="sendMessage" :disabled="!showInput" type="button"
          class="flex justify-center items-center bg-cfp-500 hover:bg-opacity-90 rounded-md w-full max-w-13 h-13 text-white"
          fdprocessedid="d1dei">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M22 2L11 13" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="white" stroke-width="2" stroke-linecap="round"
              stroke-linejoin="round"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>
