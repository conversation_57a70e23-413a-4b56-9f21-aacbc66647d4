<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Mail\SendOTP;
use App\Models\Company;
use App\Models\Person;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use PHPOpenSourceSaver\JWTAuth\Exceptions\JWTException;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;
use Illuminate\Support\Str;

class UserController extends Controller
{
    public function Logout(Request $request)
    {
        try {

            $user = Auth::user();

            if ($user) {
                $user->last_seen     = now();
                $user->last_activity = null;
                $user->save();
            }

            // Invalidate the token to log the user out
            JWTAuth::invalidate(JWTAuth::getToken());

            return response()->json([
                'status'  => true,
                'message' => 'Successfully logged out',
            ], 200);

        } catch (JWTException $e) {
            // Something went wrong while trying to invalidate the token
            return response()->json([
                'status'  => false,
                'message' => 'Failed to log out, please try again',
            ], 500);
        }
    }

    public function authorisedUser()
    {

        $allUser    = User::where('vid', '<>', 0)->where('uid', '<>', 0)->get();
        $totalCount = $allUser->count();

        if ($allUser->isEmpty()) {
            return response()->json(['status' => false, 'message' => 'No users found', 'data' => []], 404);
        }

        // Prepare the response data for each user
        $userData = $allUser->map(function ($user) {
            return User::getUserDetail($user);
        });

        // Return the response with user data
        return response()->json([
            'status'      => true,
            'message'     => 'Users retrieved successfully',
            'total_count' => $totalCount,
            'data'        => $userData,
        ], 200);
    }

    public function userLogin(Request $request)
    {

        try {

            $request->validate([
                'email'    => 'required|string',
                'password' => 'required|string',
            ]);

            $user = User::where('email', $request->email)->first();

            if ($user) {

                $profileType = strtolower($user->profileType);

                if ($profileType == 'admin') {

                    return response()->json(['status' => false, 'message' => 'You are not authorised to login as '.$profileType.' role']);

                }

                if ($user->verificationStatus == 'not_verified') {
                    return response()->json(['status' => false, 'message' => 'Your account is not varified']);
                }

                if ($user->status == 'inactive') {
                    return response()->json(['status' => false, 'message' => 'Your account is inactive']);
                }

            } else {
                return response()->json(['status' => false, 'message' => 'Invalid authentication']);
            }

            $credentials = $request->only('email', 'password');

            if (! $token = JWTAuth::attempt($credentials)) {
                return response()->json(['status' => false, 'message' => 'Invalid authentication']);
            }

            $user = User::where('email', $request->email)->first();

            try {
                if (! $token = JWTAuth::fromUser($user)) {
                    return response()->json(['status' => false, 'message' => 'Invalid authentication']);
                }
            } catch (JWTException $e) {
                return response()->json(['status' => false, 'message' => $e->getMessage()]);
            }

            if ($user) {

                $responceData = User::getUserDetail($user);

                return response()->json([
                    'status'       => true,
                    'message'      => 'User logged in successfully',
                    'data'         => $responceData,
                    'token_type'   => 'bearer',
                    'access_token' => $token,
                ]);

            } else {
                return response()->json(['status' => false, 'message' => 'Invalid authentication']);
            }
        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }

    }

    public function userAuthLogin(Request $request)
    {

        try {

            $request->validate([
                'email'    => 'required|string',
                'password' => 'required|string',
            ]);

            $user = User::where('email', $request->email)->first();

            if ($user) {

                $profileType = strtolower($user->profileType);

                if ($profileType == 'admin' || $profileType == 'company') {

                    return response()->json(['status' => false, 'message' => 'You are not authorised to login as '.$profileType.' user']);

                }

                if ($user->verificationStatus == 'not_verified') {
                    return response()->json(['status' => false, 'message' => 'Your account is not varified']);
                }

                if ($user->status == 'inactive') {
                    return response()->json(['status' => false, 'message' => 'Your account is inactive']);
                }

            } else {
                return response()->json(['status' => false, 'message' => 'Invalid authentication']);
            }

            $credentials = $request->only('email', 'password');

            if (! $token = JWTAuth::attempt($credentials)) {
                return response()->json(['status' => false, 'message' => 'Invalid authentication']);
            }

            try {
                if (! $token = JWTAuth::fromUser($user)) {
                    return response()->json(['status' => false, 'message' => 'Invalid authentication']);
                }
            } catch (JWTException $e) {
                return response()->json(['status' => false, 'message' => $e->getMessage()], 500);
            }

            if ($user) {

                $responceData = User::getUserDetail($user);

                return response()->json([
                    'status'       => true,
                    'message'      => 'User logged in successfully',
                    'data'         => $responceData,
                    'token_type'   => 'bearer',
                    'access_token' => $token,
                ]);

            } else {
                return response()->json(['status' => false, 'message' => 'Invalid authentication']);
            }
        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }

    }

    public function SaveUser(Request $request)
    {
        try {
            // Validation rules
            $rules = [
                'firstName'             => 'required|string|max:255',
                'lastName'              => 'required|string|max:255',
                'email'                 => 'required|email|unique:users,email',
                'password'              => 'required|string|min:6|confirmed',
                'password_confirmation' => 'required|string|min:6',
            ];

            $validator = Validator::make($request->all(), $rules);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'errors' => $validator->errors(),
                ], 400);
            }

            $otpReal = rand(111111, 999999);

            // Create new user
            $user                     = new User;
            $user->name               = $request->firstName;
            $user->email              = $request->email;
            $user->phone              = $request->phone ?? ''; // Optional phone field
            $user->password           = Hash::make($request->password);
            $user->profileType        = 'user';
            $user->verificationStatus = 'not_verified';
            $user->status             = 'active';
            $user->profilePicture     = ''; // No profile picture initially
            $user->otp                = $otpReal; // No OTP initially
            $user->save();

            // Create corresponding Person record
            $person            = new Person;
            $person->userId    = $user->id;
            $person->firstName = $request->firstName;
            $person->lastName  = $request->lastName;
            $person->save();

            // Prepare response data
            $userData     = User::find($user->id);
            $responceData = User::getUserDetail($userData);

            try {

                $setmailids = $request->email;
                $mailData   = [
                    'otp'     => $otpReal,
                    'subject' => 'Please verify your email',
                ];

                Mail::to($setmailids)->send(new SendOTP($mailData));

                return response()->json([
                    'status'  => true,
                    'message' => 'User saved successfully',
                    'data'    => $responceData,
                ], 201);

            } catch (Exception $ex) {

                return response()->json([
                    'status'  => true,
                    'message' => 'User saved successfully',
                    'data'    => $responceData,
                ], 201);

            }

        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function UpdateUser(Request $request)
    {
        try {
            $id   = Auth::id();
            $user = User::find($id);

            if (! $user) {
                return response()->json(['status' => false, 'message' => 'User not found.'], 404);
            }

            $profileType = strtolower($user->profileType);

            // Validation rules
            $rules = [
                'email'     => 'required|email|unique:users,email,'.$id,
                'firstName' => 'required|string|max:255',
                'lastName'  => 'required|string|max:255',
                'profile'   => 'nullable|file|mimes:jpg,jpeg,png|max:5120',
            ];

            if ($profileType == 'company') {
                $rules['companyName'] = 'required|string|max:255';
            }

            $validator = Validator::make($request->all(), $rules);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'errors' => $validator->errors(),
                ]);
            }

            $profilePicture = $request->file('profile');

            if ($request->hasFile('profile')) {
                $folder_name = 'images/profile';
                $profileUrl  = $profilePicture->store($folder_name, 'public');

                // Delete previous profile picture if exists
                if ($user->profilePicture != '') {
                    Storage::disk('public')->delete($user->profilePicture);
                }
                $user->profilePicture = $profileUrl;
            }

            // Update user details
            $user->email = $request->email;
            $user->name  = $request->firstName;
            if ($request->phone && $request->phone != '') {
                $user->phone = $request->phone;
            }

            $user->save();

            $person = Person::where('userId', Auth::id())->first();

            if ($person) {
                $person->firstName = $request->firstName;
                $person->lastName  = $request->lastName;
                $person->save();
            } else {
                $person            = new Person;
                $person->userId    = $id;
                $person->firstName = $request->firstName;
                $person->lastName  = $request->lastName;
                $person->save();
            }

            if ($profileType == 'user') {

            } elseif ($profileType == 'company') {

                $company = Company::where('userId', $id)->first();

                if ($company) {
                    Company::where('userId', $id)::update(['companyName' => $request->companyName]);
                }

                $user->company_name = $request->companyName;
                $user->save();

            }

            // Get updated user details
            $responceData = User::getUserDetail($user);

            return response()->json([
                'status'  => true,
                'message' => 'Profile updated successfully',
                'data'    => $responceData,
            ]);

        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function deletePicture(Request $request)
    {
        try {
            $id   = Auth::id();
            $user = User::find($id);

            if (! $user) {
                return response()->json(['status' => false, 'message' => 'User not found.'], 404);
            }

            if ($user->profilePicture != '') {
                Storage::disk('public')->delete($user->profilePicture);

            }

            $user->profilePicture = '';
            $user->save();

            // Get updated user details
            $responceData = User::getUserDetail($user);

            return response()->json([
                'status'  => true,
                'message' => 'Profile image deleted successfully',
                'data'    => $responceData,
            ]);

        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function loadUser(Request $request)
    {
        try {

            $filterData = $request->filterData;
            // Fetch all users with the required profile type and verification status
            $users = User::where('profileType', 'user')
                ->where('verificationStatus', 'verified')
                ->where(function ($query) use ($filterData) {
                    $query->where('name', 'like', '%'.$filterData.'%')
                        ->orWhere('email', 'like', '%'.$filterData.'%')
                        ->orWhere('phone', 'like', '%'.$filterData.'%');
                })->orderBy('id', 'desc')->get();

            if ($users->isEmpty()) {
                return response()->json(['status' => false, 'message' => 'No verified users found', 'data' => []], 404);
            }

            // Prepare the response data for each user
            $userData = $users->map(function ($user) {
                return User::getUserDetail($user);
            });

            // Return the response with user data
            return response()->json([
                'status'  => true,
                'message' => 'Users retrieved successfully',
                'data'    => $userData,
            ], 200);

        } catch (\Exception $e) {
            // Return a generic error message
            return response()->json(['status' => false, 'message' => 'Failed to load users', 'error' => $e->getMessage()], 500);
        }
    }

    public function getUserDetailApi()
    {

        try {

            $userID = Auth::id();
            $user   = User::find($userID);

            $responceData = User::getUserDetail($user);

            return response()->json([
                'status'  => true,
                'message' => 'My profile detail',
                'data'    => $responceData,
            ]);

        } catch (Exception $e) {
            return response()->json(['status' => 0, 'message' => $e->getMessage()]);
        }

    }

    public function changePassword(Request $request)
    {

        try {

            $rules = [
                'new_password'     => 'required|string|min:6',
                'confirm_password' => 'required|string|min:6|same:new_password',
                'email'            => 'required|exists:users,email',
            ];

            $validator = Validator::make($request->all(), $rules);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'errors' => $validator->errors(),
                ], 400);
            }
            $user = User::where('email', $request->email)->first();
            // Hash the new password
            $password = Hash::make($request->password);

            $user->password = $password;
            $user->save();

            $responceData = User::getUserDetail($user);

            return response()->json([
                'status'  => true,
                'message' => 'Password updated successfully',
                'data'    => $responceData,
            ]);

        } catch (\Exception $e) {
            return response()->json(['status' => 0, 'message' => $e->getMessage()]);
        }

    }

    public function updatePassword(Request $request)
    {

        try {

            $rules = [
                'new_password'     => 'required|string|min:6',
                'confirm_password' => 'required|string|min:6|same:new_password',
            ];

            $validator = Validator::make($request->all(), $rules);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'errors' => $validator->errors(),
                ], 400);
            }

            $userID = Auth::id();
            $user   = User::find($userID);
            // Hash the new password
            $password = Hash::make($request->new_password);

            $user->password = $password;
            $user->save();

            $responceData = User::getUserDetail($user);

            return response()->json([
                'status'  => true,
                'message' => 'Password updated successfully',
                'data'    => $responceData,
            ]);

        } catch (Exception $e) {
            return response()->json(['status' => 0, 'message' => $e->getMessage()]);
        }

    }

    public function getUserStatus($id)
    {
        return User::getUserStatus($id);
    }

    public function googleLogin(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id_token' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'errors' => $validator->errors(),
                ], 400);
            }

            // Manually verify the Google ID token
            $client = new \Google_Client(['client_id' => config('services.google.client_id')]);
            $payload = $client->verifyIdToken($request->id_token);
            
            if (!$payload) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid Google token'
                ], 401);
            }
            
            // Extract user information from the payload
            $email = $payload['email'];
            $name = $payload['name'] ?? ($payload['given_name'] . ' ' . $payload['family_name']);
            
            // Check if user exists
            $user = User::where('email', $email)->first();
            
            if (!$user) {
                // Create new user
                $user = new User();
                $user->name = $name;
                $user->email = $email;
                $user->password = Hash::make(Str::random(16));
                $user->profileType = 'user';
                $user->verificationStatus = 'verified'; // Auto-verify Google users
                $user->status = 'active';
                $user->save();
                
                // Create person record
                $nameParts = explode(' ', $name, 2);
                $firstName = $nameParts[0];
                $lastName = isset($nameParts[1]) ? $nameParts[1] : '';
                
                $person = new Person();
                $person->userId = $user->id;
                $person->firstName = $firstName;
                $person->lastName = $lastName;
                $person->save();
            }
            
            // Generate JWT token
            try {
                if (!$token = JWTAuth::fromUser($user)) {
                    return response()->json(['status' => false, 'message' => 'Invalid authentication']);
                }
            } catch (JWTException $e) {
                return response()->json(['status' => false, 'message' => $e->getMessage()], 500);
            }
            
            $responseData = User::getUserDetail($user);
            
            return response()->json([
                'status' => true,
                'message' => 'User logged in successfully with Google',
                'data' => $responseData,
                'token_type' => 'bearer',
                'access_token' => $token,
            ]);
            
        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()], 500);
        }
    }
}




