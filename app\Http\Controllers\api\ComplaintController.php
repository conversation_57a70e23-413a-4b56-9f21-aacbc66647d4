<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\Complaint;
use App\Models\ComplaintMessages;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class ComplaintController extends Controller
{
    public function store(Request $request)
    {

        try {

            $result      = User::checkRolePermission(Auth::id(), 'company');
            $resultAgent = User::checkRolePermission(Auth::id(), 'agent');
            $resultUser  = User::checkRolePermission(Auth::id(), 'user');
            $resultAdmin = User::checkRolePermission(Auth::id(), 'admin');
            if ($result === true) {

                // Validation rules with custom error messages
                $validatedData = $request->validate([
                    'userid' => [
                        'required',
                        Rule::exists('users', 'id')->where(function ($query) {
                            $query->where('profileType', 'user');
                        }),
                    ],
                    'company_id' => [
                        'required',
                        Rule::exists('company', 'id')->where(function ($query) {
                            $query->where('userId', Auth::id());
                        }),
                    ],
                    'priority'         => 'required|in:0,1,2',
                    'complaint_title'  => 'required|string|max:255',
                    'complaint_detail' => 'required|string',
                    'progress_status'  => 'required|in:new,inprogress,need_user_input,resolved',
                    'complain_status'  => 'required|in:open,close',
                ], [
                    'userid.exists'       => 'Please select user',
                    'company_id.exists'   => 'The selected company ID is invalid.',
                    'priority.in'         => 'The priority field must be 0, 1, or 2.',
                    'complaint_title.max' => 'The complaint title may not be greater than 255 characters.',
                    'progress_status.in'  => 'The progress status field is invalid that must be new,inprogress,need_user_input or resolved',
                    'complain_status.in'  => 'The complain status field is invalid that must be open,close',
                ]);

                // Extracting validated data
                $userid           = $validatedData['userid'];
                $company_id       = $validatedData['company_id'];
                $priority         = $validatedData['priority'];
                $complaint_title  = $validatedData['complaint_title'];
                $complaint_detail = $validatedData['complaint_detail'];
                $progress_status  = $validatedData['progress_status'];
                $complain_status  = $validatedData['complain_status'];

                // Create new Complaint instance
                $saveData                   = new Complaint;
                $saveData->companyId        = $company_id;
                $saveData->assignedToUserId = $userid;
                $saveData->priority         = $priority;
                $saveData->progressStatus   = $progress_status;
                $saveData->complainTitle    = $complaint_title;
                $saveData->complainDetail   = $complaint_detail;
                $saveData->complainStatus   = $complain_status;
                $saveData->save();

                $cmpID            = $saveData->id;
                $curentYear       = date('Y');
                $finalString      = 'CP'.$curentYear.$cmpID;
                $saveData->cmp_id = $finalString;
                $saveData->save();

                return response()->json(['status' => true, 'data' => $saveData]);

            } elseif ($resultAgent === true) {

                // Validation rules with custom error messages
                $validatedData = $request->validate([
                    'userid' => [
                        'required',
                        Rule::exists('users', 'id')->where(function ($query) {
                            $query->where('profileType', 'user');
                        }),
                    ],
                    'company_id' => [
                        'required',
                        Rule::exists('company', 'id')->where(function ($query) {
                            $query->where('userId', Auth::user()->parent_id);
                        }),
                    ],
                    'priority'         => 'required|in:0,1,2',
                    'complaint_title'  => 'required|string|max:255',
                    'complaint_detail' => 'required|string',
                    'progress_status'  => 'required|in:new,inprogress,need_user_input,resolved',
                    'complain_status'  => 'required|in:open,close',
                ], [
                    'userid.exists'       => 'Please select user',
                    'company_id.exists'   => 'The selected company ID is invalid.',
                    'priority.in'         => 'The priority field must be 0, 1, or 2.',
                    'complaint_title.max' => 'The complaint title may not be greater than 255 characters.',
                    'progress_status.in'  => 'The progress status field is invalid that must be new,inprogress,need_user_input or resolved',
                    'complain_status.in'  => 'The complain status field is invalid that must be open,close',
                ]);

                // Extracting validated data
                $userid           = $validatedData['userid'];
                $company_id       = $validatedData['company_id'];
                $priority         = $validatedData['priority'];
                $complaint_title  = $validatedData['complaint_title'];
                $complaint_detail = $validatedData['complaint_detail'];
                $progress_status  = $validatedData['progress_status'];
                $complain_status  = $validatedData['complain_status'];

                // Create new Complaint instance
                $saveData                   = new Complaint;
                $saveData->companyId        = $company_id;
                $saveData->assignedToUserId = $userid;
                $saveData->priority         = $priority;
                $saveData->progressStatus   = $progress_status;
                $saveData->complainTitle    = $complaint_title;
                $saveData->complainDetail   = $complaint_detail;
                $saveData->complainStatus   = $complain_status;
                $saveData->save();

                $cmpID            = $saveData->id;
                $curentYear       = date('Y');
                $finalString      = 'CP'.$curentYear.$cmpID;
                $saveData->cmp_id = $finalString;
                $saveData->save();

                return response()->json(['status' => true, 'data' => $saveData]);

            } elseif ($resultUser === true) {

                // Validation rules with custom error messages
                $validatedData = $request->validate([
                    'company_id' => [
                        'required',
                        Rule::exists('company', 'id'), // Removed the where condition
                    ],
                    'priority'         => 'required|in:0,1,2',
                    'complaint_title'  => 'required|string|max:255',
                    'complaint_detail' => 'required|string',
                ], [
                    'company_id.exists'   => 'The selected company ID is invalid.',
                    'priority.in'         => 'The priority field must be 0, 1, or 2.',
                    'complaint_title.max' => 'The complaint title may not be greater than 255 characters.',

                ]);

                // Extracting validated data
                $userid           = Auth::id();
                $company_id       = $validatedData['company_id'];
                $priority         = $validatedData['priority'];
                $complaint_title  = $validatedData['complaint_title'];
                $complaint_detail = $validatedData['complaint_detail'];

                // Create new Complaint instance
                $saveData                   = new Complaint;
                $saveData->companyId        = $company_id;
                $saveData->assignedToUserId = $userid;
                $saveData->priority         = $priority;
                $saveData->complainTitle    = $complaint_title;
                $saveData->complainDetail   = $complaint_detail;
                $saveData->save();

                $cmpID            = $saveData->id;
                $curentYear       = date('Y');
                $finalString      = 'CP'.$curentYear.$cmpID;
                $saveData->cmp_id = $finalString;
                $saveData->save();

                $allDetailData = Complaint::find($saveData->id);

                return response()->json(['status' => true, 'data' => $allDetailData]);

            } elseif ($resultAdmin === true) {

                // Validation rules with custom error messages
                $validatedData = $request->validate([
                    'userid' => [
                        'required',
                        Rule::exists('users', 'id')->where(function ($query) {
                            $query->where('profileType', 'user');
                        }),
                    ],
                    'company_id' => [
                        'required',
                        Rule::exists('company', 'id'), // Removed the where condition
                    ],
                    'priority'         => 'required|in:0,1,2',
                    'complaint_title'  => 'required|string|max:255',
                    'complaint_detail' => 'required|string',
                    'progress_status'  => 'required|in:new,inprogress,need_user_input,resolved',
                    'complain_status'  => 'required|in:open,close',
                ], [
                    'userid.exists'       => 'The selected user ID is invalid or the user is not of type "user".',
                    'company_id.exists'   => 'The selected company ID is invalid.',
                    'priority.in'         => 'The priority field must be 0, 1, or 2.',
                    'complaint_title.max' => 'The complaint title may not be greater than 255 characters.',
                    'progress_status.in'  => 'The progress status field is invalid that must be new,inprogress,need_user_input or resolved',
                    'complain_status.in'  => 'The complain status field is invalid that must be open,close',
                ]);

                // Extracting validated data
                $userid           = $validatedData['userid'];
                $company_id       = $validatedData['company_id'];
                $priority         = $validatedData['priority'];
                $complaint_title  = $validatedData['complaint_title'];
                $complaint_detail = $validatedData['complaint_detail'];
                $progress_status  = $validatedData['progress_status'];
                $complain_status  = $validatedData['complain_status'];

                // Create new Complaint instance
                $saveData                   = new Complaint;
                $saveData->companyId        = $company_id;
                $saveData->assignedToUserId = $userid;
                $saveData->priority         = $priority;
                $saveData->progressStatus   = $progress_status;
                $saveData->complainTitle    = $complaint_title;
                $saveData->complainDetail   = $complaint_detail;
                $saveData->complainStatus   = $complain_status;
                $saveData->save();

                $cmpID            = $saveData->id;
                $curentYear       = date('Y');
                $finalString      = 'CP'.$curentYear.$cmpID;
                $saveData->cmp_id = $finalString;
                $saveData->save();

                return response()->json(['status' => true, 'data' => $saveData]);

            } else {
                return $result;
            }

        } catch (ValidationException $ex) {
            // Construct error response with only the first validation error message
            $errorMessage = $ex->errors()[array_key_first($ex->errors())][0];

            return response()->json(['status' => false, 'message' => $errorMessage], 422);
        } catch (Exception $ex) {
            // Return other exceptions
            return response()->json(['status' => false, 'message' => $ex->getMessage()], 500);
        }
    }

    public function showComplain(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|string|in:open,close,all', // Assuming status is one of these values
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'errors' => $validator->errors(),
            ], 400);
        }

        try {

            $user        = Auth::user();
            $profileType = $user->profileType;

            // Start building the query

            if ($request->status == 'all') {
                $complaintQuery = Complaint::with(['company', 'assignedUser']);
            } else {
                $complaintQuery = Complaint::where('complainStatus', $request->status)
                    ->with(['company', 'assignedUser']);
            }

            // Apply additional conditions based on profile type
            if ($profileType == 'admin') {
                // No additional where conditions for admin
            } elseif ($profileType == 'company') {
                $complaintQuery->whereHas('company', function ($query) use ($user) {
                    $query->where('userId', $user->id);
                });
            } elseif ($profileType == 'agent') {
                $complaintQuery->whereHas('company', function ($query) use ($user) {
                    $query->where('userId', $user->parent_id);
                });
            } else {
                $complaintQuery->where('assignedToUserId', $user->id);
            }

            // Execute the query and get the results
            $showComplaint = $complaintQuery->orderBy('id', 'desc')->get();

            $showComplaint->transform(function ($innerComplain) {
                $innerComplain->complainId    = $innerComplain->id;
                $innerComplain->total_message = ComplaintMessages::where('complainId', $innerComplain->id)->count();
                unset($innerComplain->id);

                return $innerComplain;
            });

            // Transform complaints to update only the profilePicture field
            $showComplaint->transform(function ($complaint) {
                if (isset($complaint->assignedUser) && ! empty($complaint->assignedUser->profilePicture)) {
                    $profilePicture = $complaint->assignedUser->profilePicture;

                    // Check if the profilePicture already has a full URL
                    if (! preg_match("/^http(s)?:\/\//", $profilePicture)) {
                        // If it's not a full URL, prepend the base URL
                        $complaint->assignedUser->profilePicture = url('/storage/'.$profilePicture);
                    }
                }

                return $complaint;
            });

            if (count($showComplaint) > 0) {

                return response()->json([
                    'status' => true,
                    'data'   => $showComplaint,
                ], 200);

            } else {

                return response()->json([
                    'status'  => false,
                    'message' => 'Complain not found',
                ], 404);

            }

        } catch (Exception $ex) {
            return response()->json([
                'status'  => false,
                'message' => $ex->getMessage(),
            ], 500);
        }
    }

    public function getsingleComplain(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'complain_id' => 'required|integer|exists:complain,id', // Assuming status is one of these values
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'errors' => $validator->errors(),
            ], 400);
        }

        try {

            $user        = Auth::user();
            $profileType = $user->profileType;

            // Start building the query

            $complaintQuery = Complaint::with(['company', 'assignedUser']);

            // Apply additional conditions based on profile type
            if ($profileType == 'admin') {
                // No additional where conditions for admin
            } elseif ($profileType == 'company') {

                $complaintQuery->whereHas('company', function ($query) use ($user) {
                    $query->where('userId', $user->id);
                });

            } elseif ($profileType == 'agent') {

                $complaintQuery->whereHas('company', function ($query) use ($user) {
                    $query->where('userId', $user->parent_id);
                });

            } else {

                $complaintQuery->where('assignedToUserId', $user->id);
            }

            $complaintQuery->where('complain.id', $request->complain_id);

            // Execute the query and get the results
            $showComplaint = $complaintQuery->first();

            if ($showComplaint) {
                if (isset($showComplaint->assignedUser) && ! empty($showComplaint->assignedUser->profilePicture)) {
                    $profilePicture = $showComplaint->assignedUser->profilePicture;

                    // Check if the profilePicture already has a full URL
                    if (! preg_match("/^http(s)?:\/\//", $profilePicture)) {
                        // If it's not a full URL, prepend the base URL
                        $showComplaint->assignedUser->profilePicture = url('/storage/'.$profilePicture);
                    }
                }
            }

            $commentCount = ComplaintMessages::where('complainId', $request->complain_id)->count();

            if ($showComplaint) {

                return response()->json([
                    'status'         => true,
                    'data'           => $showComplaint,
                    'total_comments' => $commentCount,
                ], 200);

            } else {

                return response()->json([
                    'status'  => false,
                    'message' => 'Complain not found',
                ], 404);

            }

        } catch (Exception $ex) {
            return response()->json([
                'status'  => false,
                'message' => $ex->getMessage(),
            ], 500);
        }
    }

    public function closeComplain(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'userid'      => 'required|integer|exists:users,id',
            'complain_id' => 'required|integer|exists:complain,id',
            'message'     => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'errors' => $validator->errors(),
            ], 400);
        }

        try {

            $user        = Auth::user();
            $profileType = $user->profileType;

            if ($profileType == 'company' || $profileType == 'agent') {

                $companyUserID = Auth::id();
                if ($profileType == 'agent') {
                    $companyUserID = Auth::user()->parent_id;
                }

                $checkComplaint = Complaint::where('id', $request->complain_id)
                    ->where('assignedToUserId', $request->userid)
                    ->first();

                if ($checkComplaint) {

                    $checkCompany = Company::where('id', $checkComplaint->companyId)->where('userId', $companyUserID)->first();
                    if ($checkCompany) {
                    } else {
                        return response()->json([
                            'status'  => false,
                            'message' => 'Invalid complain request',
                        ], 400);
                    }

                    if ($checkComplaint->complainStatus == 'close') {
                        return response()->json([
                            'status'  => false,
                            'message' => 'Your complaint is already closed',
                        ], 400);
                    }

                    $checkComplaint->complainStatus = 'close';
                    $checkComplaint->save();

                    $saveComplainMessage             = new ComplaintMessages;
                    $saveComplainMessage->complainId = $request->complain_id;
                    $saveComplainMessage->senderId   = $request->userid;
                    $saveComplainMessage->content    = $request->message;
                    $saveComplainMessage->save();

                    $allMessages = ComplaintMessages::where('complainId', $request->complain_id)
                        ->orderBy('id', 'desc')
                        ->get();

                    return response()->json([
                        'status'           => true,
                        'message'          => 'Your complaint has been closed successfully',
                        'complainDetail'   => $checkComplaint,
                        'complainMessages' => $allMessages,
                    ], 200);

                } else {
                    return response()->json([
                        'status'  => false,
                        'message' => 'Invalid complaint request',
                    ], 404);
                }

            } elseif ($profileType == 'admin') {

                $checkComplaint = Complaint::where('id', $request->complain_id)
                    ->where('assignedToUserId', $request->userid)
                    ->first();

                if ($checkComplaint) {

                    $checkCompany = Company::where('id', $checkComplaint->companyId)->first();
                    if ($checkCompany) {
                    } else {
                        return response()->json([
                            'status'  => false,
                            'message' => 'Invalid complain request',
                        ], 400);
                    }

                    if ($checkComplaint->complainStatus == 'close') {
                        return response()->json([
                            'status'  => false,
                            'message' => 'Your complaint is already closed',
                        ], 400);
                    }

                    $checkComplaint->complainStatus = 'close';
                    $checkComplaint->save();

                    $saveComplainMessage             = new ComplaintMessages;
                    $saveComplainMessage->complainId = $request->complain_id;
                    $saveComplainMessage->senderId   = $request->userid;
                    $saveComplainMessage->content    = $request->message;
                    $saveComplainMessage->save();

                    $allMessages = ComplaintMessages::where('complainId', $request->complain_id)
                        ->orderBy('id', 'desc')
                        ->get();

                    return response()->json([
                        'status'           => true,
                        'message'          => 'Complaint has been closed successfully',
                        'complainDetail'   => $checkComplaint,
                        'complainMessages' => $allMessages,
                    ], 200);

                } else {
                    return response()->json([
                        'status'  => false,
                        'message' => 'Invalid complaint request',
                    ], 404);
                }

            } else {

                return response()->json([
                    'status'  => false,
                    'message' => 'Invalid authentication',
                ], 401); // 401 Unauthorized

            }

        } catch (Exception $ex) {
            return response()->json([
                'status'  => false,
                'message' => $ex->getMessage(),
            ], 500);
        }
    }

    public function sendComplainMessage(Request $request)
    {

        try {

            $validator = Validator::make($request->all(), [
                'complain_id' => 'required|integer|exists:complain,id',
                'message'     => 'required|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'errors' => $validator->errors(),
                ], 400);
            }

            $user        = Auth::user();
            $profileType = $user->profileType;

            if ($profileType == 'admin') {

                return response()->json([
                    'status'  => false,
                    'message' => 'Invalid authentication',
                ], 401); // 401 Unauthorized

            }

            $id      = $request->complain_id;
            $message = $request->message;

            $complaintQuery = Complaint::join('company', 'company.id', '=', 'complain.companyId')
                ->join('users as complain_user', 'complain_user.id', '=', 'complain.assignedToUserId')
                ->select(
                    'company.companyName',
                    'company.city',
                    'company.state',
                    'complain.*',
                    'complain_user.email as complainant_email',
                    'complain_user.name as complainant_name',
                    'complain_user.phone as complainant_phone',
                    'complain_user.profilePicture as complainant_profilePicture'
                )
                ->where('complain.id', $id)
                ->orderBy('complain.id', 'desc');

            if ($profileType == 'admin') {
                // No additional where conditions for admin
            } elseif ($profileType == 'company') {
                $complaintQuery->where('company.userId', $user->id);
            } elseif ($profileType == 'agent') {
                $complaintQuery->where('company.userId', Auth::user()->parent_id);
            } else {
                $complaintQuery->where('complain.assignedToUserId', $user->id);
            }

            $Complaints = $complaintQuery->first();

            if ($Complaints) {

                $senderId = Auth::user()->id;

                $saveComplains             = new ComplaintMessages;
                $saveComplains->complainId = $id;
                $saveComplains->senderId   = $senderId;
                $saveComplains->content    = $message;
                $saveComplains->save();

                $complaintConversation = ComplaintMessages::join('users', 'users.id', '=', 'complainmessage.senderId')
                    ->where('complainmessage.complainId', $Complaints->id)
                    ->select('complainmessage.*', 'users.name', 'users.email')
                    ->orderBy('complainmessage.id', 'desc')
                    ->get();

                return response()->json([
                    'status'           => true,
                    'complainMessages' => $complaintConversation,
                ], 200);

            } else {

                return response()->json([
                    'status'  => false,
                    'message' => 'Invalid complaint request',
                ], 404);

            }

        } catch (Exception $ex) {
            return response()->json([
                'status'  => false,
                'message' => $ex->getMessage(),
            ], 500);
        }
    }

    public function getComplainMessage(Request $request)
    {

        try {

            $user        = Auth::user();
            $profileType = $user->profileType;

            if ($profileType == 'admin') {

                return response()->json([
                    'status'  => false,
                    'message' => 'Invalid authentication',
                ], 401); // 401 Unauthorized

            }

            if ($profileType == 'company') {

                $validator = Validator::make($request->all(), [
                    'userid'      => 'required|integer|exists:users,id',
                    'complain_id' => 'required|integer|exists:complain,id',
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'status' => false,
                        'errors' => $validator->errors(),
                    ], 400);
                }

                $userid = $request->userid;

            } elseif ($profileType == 'agent') {

                $validator = Validator::make($request->all(), [
                    'userid'      => 'required|integer|exists:users,id',
                    'complain_id' => 'required|integer|exists:complain,id',
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'status' => false,
                        'errors' => $validator->errors(),
                    ], 400);
                }

                $userid = $request->userid;

            } elseif ($profileType == 'admin') {

                $validator = Validator::make($request->all(), [
                    'userid'      => 'required|integer|exists:users,id',
                    'complain_id' => 'required|integer|exists:complain,id',
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'status' => false,
                        'errors' => $validator->errors(),
                    ], 400);
                }

                $userid = $request->userid;

            } elseif ($profileType == 'user') {

                $validator = Validator::make($request->all(), [
                    'complain_id' => 'required|integer|exists:complain,id',
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'status' => false,
                        'errors' => $validator->errors(),
                    ], 400);
                }

                $userid = Auth::id();

            } else {

                return response()->json([
                    'status'  => false,
                    'message' => 'Invalid authentication',
                ], 401); // 401 Unauthorized
            }

            $checkComplaint = Complaint::where('id', $request->complain_id)
                ->where('assignedToUserId', $userid)
                ->with(['company', 'assignedUser'])
                ->first();

            if ($checkComplaint) {
                $allMessages = ComplaintMessages::where('complainId', $request->complain_id)
                    ->with('sender')
                    ->orderBy('id', 'desc')
                    ->get();

                return response()->json([
                    'status'           => true,
                    'complainMessages' => $allMessages,
                    'complainDetail'   => $checkComplaint,
                ], 200);

            } else {
                return response()->json([
                    'status'  => false,
                    'message' => 'Invalid complaint request',
                ], 404);
            }

        } catch (Exception $ex) {
            return response()->json([
                'status'  => false,
                'message' => $ex->getMessage(),
            ], 500);
        }
    }
}
