<?php

namespace App\Http\Controllers;

use App\Models\OAuthAuthorizationCode;
use App\Models\OAuthClient;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;

class OAuthController extends Controller
{
    // Step 1: Handle Login and Issue Authorization Code
    public function login(Request $request)
    {
        $credentials = $request->only('email', 'password');
        if (! Auth::attempt($credentials)) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $user = Auth::user();

        if ($user->profileType == 'user') {

            if ($user->verificationStatus == 'not_verified') {
                Auth::logout();

                return response()->json(['error' => 'Your account is not varified']);
            }

            if ($user->status == 'inactive') {
                Auth::logout();

                return response()->json(['error' => 'Your account is inactive']);
            }

        } else {
            Auth::logout();

            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $client_id    = $request->input('client_id');
        $redirect_uri = $request->input('redirect_uri');
        $vendor_uid   = $request->input('vuserid');

        // Validate client
        $client = OAuthClient::where('client_id', $client_id)->first();
        if (! $client || $client->redirect_uri !== $redirect_uri) {
            return response()->json(['error' => 'Invalid client'], 400);
        }

        // Generate authorization code
        $auth_code = Str::random(40);
        $saveData  = OAuthAuthorizationCode::updateOrCreate(
            [
                'user_id'             => $user->id,
                'client_id'           => $client->id,
                'user_id_for_company' => $client->userId,
            ],
            [
                'vendor_uid' => $vendor_uid,
                'code'       => $auth_code,
                'expires_at' => now()->addMinutes(10),
            ]
        );

        // Ensure the data is stored before responding
        if ($saveData) {
            return response()->json(['code' => $auth_code], 201);
        } else {
            return response()->json(['error' => 'Failed to generate authorization code'], 500);
        }

    }

    // Step 2: Exchange Authorization Code for JWT Token
    public function getAccessToken(Request $request)
    {
        $request->validate([
            'client_id'     => 'required|string',
            'client_secret' => 'required|string',
            'code'          => 'required|string',
        ]);

        $client = OAuthClient::where('client_id', $request->client_id)
            ->where('client_secret', $request->client_secret)
            ->first();

        if (! $client) {
            return response()->json(['error' => 'Invalid client'], 400);
        }

        $auth_code = OAuthAuthorizationCode::where('code', $request->code)->first();

        if (! $auth_code || now()->greaterThan($auth_code->expires_at)) {
            return response()->json(['error' => 'Invalid or expired authorization code'], 400);
        }

        $user  = User::find($auth_code->user_id);
        $token = JWTAuth::fromUser($user);

        // Delete the authorization code after use
        // $auth_code->delete();

        return response()->json([
            'access_token' => $token,
            'token_type'   => 'Bearer',
            'user_id'      => $user->id,
            'name'         => $user->name,
            'phone'        => $user->phone,
            'email'        => $user->email,
        ]);
    }

    // update oauth detail

    public function updateOauth(Request $request)
    {
        try {
            $userId     = Auth::id();
            $checkOAuth = OAuthClient::where('userId', $userId)->first();

            $validated = $request->validate([
                'name'      => 'nullable|string|max:255',
                'client_id' => [
                    'required', 'string', 'max:255',
                    Rule::unique('oauth_clients', 'client_id')->ignore(optional($checkOAuth)->id),
                ],
                'client_secret' => [
                    'required', 'string', 'max:255',
                    Rule::unique('oauth_clients', 'client_secret')->ignore(optional($checkOAuth)->id),
                ],
                'redirect_uri' => 'required|url|max:255',
            ], [
                'name.required'          => 'The name field is required.',
                'client_id.required'     => 'The client ID field is required.',
                'client_id.unique'       => 'The client ID must be unique.',
                'client_secret.required' => 'The client secret field is required.',
                'client_secret.unique'   => 'The client secret must be unique.',
                'redirect_uri.required'  => 'The Redirect URL field is required.',
                'redirect_uri.url'       => 'The Redirect URL must be a valid URL.',
            ]);

            $validated['name'] = '';

            if ($checkOAuth) {
                $checkOAuth->update($validated);
            } else {
                $validated['userId'] = $userId;
                OAuthClient::create($validated);
            }

            return Redirect::back()->with([
                'status'  => true,
                'message' => 'OAuth details saved successfully',
                'class'   => 'flex items-center p-2 mb-4 text-green-700 rounded-md bg-green-100',
            ]);
        } catch (Exception $ex) {
            Log::error('Save OAuth error: '.$ex->getMessage());

            return Redirect::back()->with([
                'status'  => false,
                'message' => 'Server Error, please try again',
                'class'   => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
            ]);
        }
    }
}
