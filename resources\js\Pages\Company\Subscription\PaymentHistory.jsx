import React from 'react';
import { Head, <PERSON> } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { Card } from '@/Components/UI/Card';
import { StatusBadge } from '@/Components/UI/StatusBadge';
import { FiArrowLeft, FiCreditCard, FiCalendar, FiDollarSign, FiCheck, FiX, FiClock } from 'react-icons/fi';

export default function PaymentHistory({ transactions, subscription }) {
    const getStatusColor = (status) => {
        switch (status) {
            case 'completed':
            case 'succeeded':
                return 'success';
            case 'pending':
                return 'warning';
            case 'failed':
            case 'declined':
                return 'danger';
            default:
                return 'secondary';
        }
    };

    const getStatusIcon = (status) => {
        switch (status) {
            case 'completed':
            case 'succeeded':
                return <FiCheck className="h-4 w-4" />;
            case 'pending':
                return <FiClock className="h-4 w-4" />;
            case 'failed':
            case 'declined':
                return <FiX className="h-4 w-4" />;
            default:
                return <FiCreditCard className="h-4 w-4" />;
        }
    };

    const getPaymentMethodIcon = (method) => {
        switch (method?.toLowerCase()) {
            case 'stripe':
                return '💳';
            case 'paypal':
                return '🅿️';
            default:
                return '💳';
        }
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const formatCurrency = (amount, currency = 'USD') => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    };

    return (
        <DashboardLayout>
            <Head title="Payment History" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link
                            href="/company/subscription/dashboard"
                            className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
                        >
                            <FiArrowLeft className="h-5 w-5 mr-2" />
                            Back to Dashboard
                        </Link>
                    </div>
                </div>

                <div className="flex items-center space-x-3">
                    <FiCreditCard className="h-8 w-8 text-cf-primary-600" />
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Payment History</h1>
                        <p className="text-gray-600">
                            View all your payment transactions and billing history
                        </p>
                    </div>
                </div>

                {/* Subscription Info */}
                <Card>
                    <div className="p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Current Subscription
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-500">Plan</label>
                                <p className="mt-1 text-sm text-gray-900">{subscription.subscription_plan?.name}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-500">Status</label>
                                <div className="mt-1">
                                    <StatusBadge status={subscription.status} />
                                </div>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-500">Total Transactions</label>
                                <p className="mt-1 text-sm text-gray-900">{transactions.total}</p>
                            </div>
                        </div>
                    </div>
                </Card>

                {/* Payment History */}
                <Card>
                    <div className="p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-6">
                            Transaction History
                        </h3>

                        {transactions.data.length === 0 ? (
                            <div className="text-center py-12">
                                <FiCreditCard className="mx-auto h-12 w-12 text-gray-400" />
                                <h3 className="mt-2 text-sm font-medium text-gray-900">No transactions</h3>
                                <p className="mt-1 text-sm text-gray-500">
                                    You don't have any payment transactions yet.
                                </p>
                            </div>
                        ) : (
                            <div className="overflow-hidden">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Transaction
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Date
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Amount
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Method
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Status
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {transactions.data.map((transaction) => (
                                            <tr key={transaction.id} className="hover:bg-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center">
                                                        <div className="flex-shrink-0 h-10 w-10">
                                                            <div className="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                                                                {getStatusIcon(transaction.status)}
                                                            </div>
                                                        </div>
                                                        <div className="ml-4">
                                                            <div className="text-sm font-medium text-gray-900">
                                                                {transaction.transaction_id || `TXN-${transaction.id}`}
                                                            </div>
                                                            <div className="text-sm text-gray-500">
                                                                {transaction.subscription?.subscription_plan?.name || 'Subscription Payment'}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center text-sm text-gray-900">
                                                        <FiCalendar className="h-4 w-4 text-gray-400 mr-2" />
                                                        {formatDate(transaction.created_at)}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center text-sm text-gray-900">
                                                        <FiDollarSign className="h-4 w-4 text-gray-400 mr-1" />
                                                        {formatCurrency(transaction.amount, transaction.currency)}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center">
                                                        <span className="text-lg mr-2">
                                                            {getPaymentMethodIcon(transaction.payment_method)}
                                                        </span>
                                                        <span className="text-sm text-gray-900 capitalize">
                                                            {transaction.payment_method || 'Unknown'}
                                                        </span>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <StatusBadge 
                                                        status={getStatusColor(transaction.status)}
                                                        text={transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                                                    />
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>

                                {/* Pagination */}
                                {transactions.last_page > 1 && (
                                    <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                                        <div className="flex-1 flex justify-between sm:hidden">
                                            {transactions.prev_page_url && (
                                                <Link
                                                    href={transactions.prev_page_url}
                                                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                                >
                                                    Previous
                                                </Link>
                                            )}
                                            {transactions.next_page_url && (
                                                <Link
                                                    href={transactions.next_page_url}
                                                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                                >
                                                    Next
                                                </Link>
                                            )}
                                        </div>
                                        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                            <div>
                                                <p className="text-sm text-gray-700">
                                                    Showing{' '}
                                                    <span className="font-medium">{transactions.from}</span>
                                                    {' '}to{' '}
                                                    <span className="font-medium">{transactions.to}</span>
                                                    {' '}of{' '}
                                                    <span className="font-medium">{transactions.total}</span>
                                                    {' '}results
                                                </p>
                                            </div>
                                            <div>
                                                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                                    {transactions.links.map((link, index) => (
                                                        <Link
                                                            key={index}
                                                            href={link.url || '#'}
                                                            className={`relative inline-flex items-center px-2 py-2 border text-sm font-medium ${
                                                                link.active
                                                                    ? 'z-10 bg-cf-primary-50 border-cf-primary-500 text-cf-primary-600'
                                                                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                                            } ${
                                                                index === 0 ? 'rounded-l-md' : ''
                                                            } ${
                                                                index === transactions.links.length - 1 ? 'rounded-r-md' : ''
                                                            }`}
                                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                                        />
                                                    ))}
                                                </nav>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </Card>
            </div>
        </DashboardLayout>
    );
}
