<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, Link, useForm, usePage } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import TextInput from '@/Components/TextInput.vue';
import TextAreaInput from '@/Components/TextAreaInput.vue';
import InputError from '@/Components/InputError.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import InputLabel from '@/Components/InputLabel.vue';
import DefaultCard from '@/Components/Forms/DefaultCard.vue';
import SelectInput from '@/Components/SelectInput.vue';
import FileInput from '@/Components/FileInput.vue';
import { Inertia } from '@inertiajs/inertia';
import BreadcrumbDefault from '@/Components/Breadcrumbs/BreadcrumbDefault.vue';
import Flatpickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';
import PageHeading from '@/Components/Global/PageHeading.vue';

const pageTitle = ref('Edit Deal')


const { companyList, dealDetail } = usePage().props;
var baseurl = window.location.origin;

const formatDate = (dateString) => {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

const form = useForm({
  deal_id: dealDetail.id,
  dealImage: null,
  dealViewImage: dealDetail.deal_file,
  deal_title: dealDetail.dealTitle,
  deal_link: dealDetail.deal_link,
  deal_description: dealDetail.dealContent,
  deal_status: dealDetail.deal_status,
  start_date: formatDate(dealDetail.dealStartOn),
  end_date: formatDate(dealDetail.dealExpiresOn),
  company: dealDetail.companyId,
  response: '',
});

const onFileChange = (event) => {
  const file = event.target.files[0];
  form.dealImage = file;
};


const resetForm = () => {

  // Clear file input by replacing it with a new one
  const fileInput = document.getElementById('dealImage');
  fileInput.value = ''; // Clear the value for better browser compatibility
  const newFileInput = document.createElement('input');
  newFileInput.type = 'file';
  newFileInput.id = 'dealImage';
  newFileInput.classList.add('mt-1', 'block', 'w-full');
  newFileInput.addEventListener('change', onFileChange); // Reattach the change event listener
  fileInput.parentNode.replaceChild(newFileInput, fileInput);

  form.deal_title = dealDetail.dealTitle;
  form.deal_link = dealDetail.deal_link;
  form.deal_description = dealDetail.dealContent;
  form.start_date = formatDate(dealDetail.dealStartOn);
  form.end_date = formatDate(dealDetail.dealExpiresOn);


};


const submitForm = () => {
  form.post(route('deal.update'), {
    onSuccess: (response) => {
      form.dealViewImage = response.props.dealDetail.deal_file;
      form.deal_status = response.props.dealDetail.deal_status;
      form.company = response.props.dealDetail.companyId;
      //resetForm();

    },
    onError: (errors) => {
    }
  });
};



</script>

<template>


  <Head :title="pageTitle" />

  <AuthenticatedLayout>
    <!-- <PageHeading :title="pageTitle"></PageHeading> -->


    <form @submit.prevent="submitForm" enctype="multipart/form-data" class="form-main-body">

      <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
        leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
        <p v-if="form.recentlySuccessful" enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
          leave-active-class="transition ease-in-out" leave-to-class="opacity-0" :class="$page.props.flash?.class">
          {{ $page.props.flash.message }}</p>
      </Transition>

      <TextInput id="deal_id" name="deal_id" class="hidden" type="hidden" v-model="form.deal_id" />

      <div class="add-form-main-bcls">


        <div class="flex flex-col gap-4 col-span-12 pr-5 xl:pb-1">


            <div class="p-6">

              <div class="mt-2 mb-4">
                <InputLabel for="dealImage" value="Upload Image" />
                <FileInput id="dealImage" type="file" class="" v-model="form.dealImage" @input="onFileChange"
                  autocomplete="dealImage" />
                <InputError class="mt-2" :message="form.errors.dealImage" />

                <div class="my-2" v-if="form.dealViewImage != '' && form.dealViewImage != null">
                  <img :src="baseurl + '/storage/' + form.dealViewImage" alt="Deal" class="rounded-full w-15 h-15" />
                </div>

              </div>

              <div v-if="$page.props.auth.user.profileType == 'admin'" class="mt-2 mb-4">
                <InputLabel for="company" value="Select Company" />
                <SelectInput id="company" class="block mt-1 w-full" v-model="form.company" autocomplete="company"
                  name="company">
                  <option value="" disabled selected>Select your company</option>
                  <option v-for="(brand, key) in companyList" :value="brand.id" :key="key">{{ brand.companyName }}
                  </option>
                </SelectInput>
                <InputError class="mt-2" :message="form.errors.company" />
              </div>

              <div class="mt-2 mb-4">
                <InputLabel for="deal_title" value="Title" />
                <TextInput id="deal_title" name="deal_title" type="text" placeholder="Title" class="block mt-1 w-full"
                  v-model="form.deal_title" />
                <InputError :message="form.errors.deal_title" class="mt-2" />
              </div>

              <div class="mt-2 mb-4">
                <InputLabel for="deal_link" value="Link" />
                <TextInput id="deal_link" name="deal_link" type="text" placeholder="Add link to your deal page"
                  class="block mt-1 w-full" v-model="form.deal_link" />
                <InputError :message="form.errors.deal_link" class="mt-2" />
              </div>


              <div class="mt-2 mb-4">
                <InputLabel for="deal_description" value="Description" />
                <TextAreaInput id="deal_description" name="deal_description" placeholder="Description" type="text"
                  class="block mt-1 w-full h-80" v-model="form.deal_description" />
                <InputError :message="form.errors.deal_description" class="mt-2" />
              </div>


              <div class="mt-2 mb-4">
                <InputLabel for="start_date" value="Start Date" />
                <Flatpickr v-model="form.start_date" :config="{ enableTime: true, dateFormat: 'Y-m-d H:i' }"
                  class="input-flt-datepicker" name="start_date" placeholder="Select Start Date" />
                <!-- <TextInput id="start_date" name="start_date" type="date" placeholder="Start Date"
                  class="block mt-1 w-full" v-model="form.start_date" /> -->
                <InputError :message="form.errors.start_date" class="mt-2" />
              </div>

              <div class="mt-2 mb-4">
                <InputLabel for="end_date" value="End Date" />
                <Flatpickr v-model="form.end_date" :config="{ enableTime: true, dateFormat: 'Y-m-d H:i' }"
                  class="input-flt-datepicker" name="end_date" placeholder="Select End Date" />
                <!-- <TextInput id="end_date" name="end_date" type="date" placeholder="End Date" class="block mt-1 w-full"
                  v-model="form.end_date" /> -->
                <InputError :message="form.errors.end_date" class="mt-2" />
              </div>

              <!-- <div class="mt-2 mb-3">
                <InputLabel for="deal_status" value="Deal status" />
                <SelectInput id="deal_status" class="block mt-1 w-full" v-model="form.deal_status" required
                  autocomplete="deal_status" name="deal_status">
                  <option value="" disabled>Status</option>
                  <option value="0" :selected="form.deal_status === '0'">Pending</option>
                  <option value="1" :selected="form.deal_status === '1'">Start</option>
                  <option value="2" :selected="form.deal_status === '2'">End</option>
                </SelectInput>
                <InputError class="mt-2" :message="form.errors.deal_status" />
              </div> -->

              <button class="dk-update-btn">
                Update </button>

              <ResponsiveNavLink :href="route('deals')" class="dk-cancle-btn">
                Cancel
              </ResponsiveNavLink>


            </div>


          <!-- Contact Form End -->
        </div>

      </div>
    </form>

  </AuthenticatedLayout>
</template>
