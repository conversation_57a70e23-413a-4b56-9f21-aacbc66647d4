<script setup>
import { ref, onMounted, computed } from 'vue';
import { formatDistanceToNow } from 'date-fns'
import ResponsiveNavLink from '../ResponsiveNavLink.vue';
import { Inertia } from '@inertiajs/inertia';
import PageHeading from '../Global/PageHeading.vue';

const props = defineProps(['botConverList', 'authDetail', 'activeTab', 'sourceID', 'umid']);
let UsersList = ref(props.botConverList);
let authDetail = props.authDetail;
let activeTab = props.activeTab;
let sourceID = props.sourceID;
let umid = props.umid;
var baseurl = window.location.origin;
var adminBaseurl = window.location.origin + '/dtadmin';
const selectedSlug = ref('');
const searchQuery = ref('');
const messages = ref([]); // Define a ref for messages array

const emit = defineEmits(['chatSelected']);

const formatDate = (timestamp) => {
  return formatDistanceToNow(new Date(timestamp), { addSuffix: true })
}

const timestamptoFormat = (timestamp) => {
  const date = new Date(timestamp * 1000); // Convert seconds to milliseconds
  const today = new Date();

  // Check if the date is today
  if (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  ) {
    return 'Today';
  }

  // Check if the date is yesterday
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);
  if (
    date.getDate() === yesterday.getDate() &&
    date.getMonth() === yesterday.getMonth() &&
    date.getFullYear() === yesterday.getFullYear()
  ) {
    return 'Yesterday';
  }

  // Format as 'Y-m-d H:i'
  const formattedDate = date.toISOString().slice(0, 16).replace('T', ' ');
  return formattedDate;
};


const handleClick = async (slug, user_id) => {
  window.history.pushState(null, null, `?source=${slug}&umid=${user_id}`);
  let realuserid = decodeToBase64(user_id);
  try {
    const response = await axios.get(`/chatbot-json?source=${slug}&umid=${user_id}`);
    const chatDataNew = response.data;
    messages.value = [];

    if (Object.keys(chatDataNew).length !== 0) {
      Object.values(chatDataNew).forEach(item => {
        if (item.message.trim() !== '' && item.id == realuserid) {
          messages.value.push({
            text: item.message,
            timestamp: item.timestamp,
            sender: 'user'
          });
        }
        if (item.message.trim() !== '' && item.id != realuserid) {
          messages.value.push({
            text: item.message,
            timestamp: item.timestamp,
            sender: 'server'
          });
        }

      });
    }

    const responseuserData = await axios.get(`/get-user-detail?userid=${realuserid}&activeTab=${activeTab}`);
    const userData = responseuserData.data.userData;

    // Emit event with chat data
    emit('chatSelected', {
      messages: messages.value,
      userData: userData,
      selectedUser: user_id,
      selectedSource: slug
    });

  } catch (error) {
    console.error('Error fetching chat data:', error);
  }
}



const encodeToBase64 = (projectId) => {
  return btoa(projectId); // Using btoa() to encode to base64
}

const decodeToBase64 = (projectId) => {
  return atob(projectId); // Using atob() to decode from base64
}


// Function to fetch the latest conversation data
const fetchLatestConversations = async () => {
  try {
    const response = await fetch('/dtadmin/get-agent-last-conversation', { method: 'GET' });
    const responseData = await response.json();

    // Create a new array with updated data while preserving old messages
    const updatedList = responseData.botConverList.map(newChat => {
      // Find corresponding chat in current list
      const existingChat = UsersList.value.find(
        oldChat => oldChat.user_id === newChat.user_id &&
          oldChat.company_id === newChat.company_id
      );

      // Preserve existing message data until new data is fetched
      return {
        ...newChat,
        last_message: existingChat?.last_message || newChat.last_message,
        last_timestamp: existingChat?.last_timestamp || newChat.last_timestamp
      };
    });

    // Update UsersList with the new array
    UsersList.value = updatedList;

    // Now fetch new messages for each chat
    for (const chat of UsersList.value) {
      try {
          let agentTypes = chat.company_user_id+'_'+chat.company_id;
        const responsefirebase = await fetch(`/dtadmin/getlast_message/${chat.user_id}/${agentTypes}`, { method: 'GET' });
        const responseDatafirebase = await responsefirebase.json();

        // Only update if we got valid new data
        if (responseDatafirebase.message) {
          chat.last_message = responseDatafirebase.message;
          chat.last_timestamp = responseDatafirebase.timestamp;
        }
      } catch (error) {
        console.error('Error fetching message for chat:', error);
        // On error, keep existing message data
      }
    }
  } catch (error) {
    console.error('Error fetching latest conversations:', error);
  }
};

Echo.private('agent-channel').listen('ChatAgentMessage', (e) => {
  let socketMessage = e.message;
  fetchLatestConversations(); // Fetch the latest conversations on receiving the event
});



let currentTab = ref(activeTab);

const setActiveTab = (tab) => {
  currentTab.value = tab;
};

// Computed property to filter the user list based on the search query
const filteredUsersList = computed(() => {
  return UsersList.value.filter(chat =>
    chat.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    chat.lastmessage.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

onMounted(() => {
  // Add class to hidden on mount
  const hiddenElementClass = `hidden_${umid}${sourceID}`;
  const hiddenElements = document.getElementsByClassName(hiddenElementClass);
  Array.from(hiddenElements).forEach(el => el.classList.add('hidden'));
});


// Fetch last message for each chat
const retriveLastMessage = async (uid, botid) => {
  try {
    const response = await fetch(`/dtadmin/getlast_message/${uid}/${botid}`, { method: 'GET' });
    const responseData = await response.json();
    let message = responseData.message;
    let timestamp = responseData.timestamp;
    return { message, timestamp };
  } catch (error) {
    console.error('Error fetching last message:', error);
    return 'No message found';
  }
};

// Fetch last message on component mount
onMounted(async () => {
  for (const chat of UsersList.value) {
      let agentTypes = chat.company_user_id+'_'+chat.company_id;
    let { message, timestamp } = await retriveLastMessage(chat.user_id, agentTypes);
      chat.lastMessage = message;
      chat.last_message = message;
      chat.last_timestamp = timestamp;
  }
});

const truncateMessage = (message) => {
    if (!message) return '';
    // Strip HTML tags for length calculation
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = message;
    const textContent = tempDiv.textContent || tempDiv.innerText;

    if (textContent.length > 10) {
        // For HTML content, try to preserve tags while truncating
        const truncated = message.substring(0, 10);
        // Check if we're cutting in the middle of an HTML tag
        const lastLtIndex = truncated.lastIndexOf('<');
        const lastGtIndex = truncated.lastIndexOf('>');

        if (lastLtIndex > lastGtIndex) {
            // We're in the middle of a tag, so truncate at the last complete tag
            return message.substring(0, lastLtIndex) + '...';
        }
        return truncated + '...';
    }
    return message;
}


</script>



<template>



  <form class="sticky mb-4">
    <input type="text"
      class="border-stroke focus:border-cfp-500 bg-gray-2 py-2.5 pr-10 pl-5 border rounded-md focus:ring-cfp-500/50 w-full text-sm outline-none search_users"
      placeholder="Search..." v-model="searchQuery">
    <button class="top-1/2 right-4 absolute -translate-y-1/2" fdprocessedid="8hdq5j">
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd"
          d="M8.25 3C5.3505 3 3 5.3505 3 8.25C3 11.1495 5.3505 13.5 8.25 13.5C11.1495 13.5 13.5 11.1495 13.5 8.25C13.5 5.3505 11.1495 3 8.25 3ZM1.5 8.25C1.5 4.52208 4.52208 1.5 8.25 1.5C11.9779 1.5 15 4.52208 15 8.25C15 11.9779 11.9779 15 8.25 15C4.52208 15 1.5 11.9779 1.5 8.25Z"
          fill="#637381"></path>
        <path fill-rule="evenodd" clip-rule="evenodd"
          d="M11.957 11.958C12.2499 11.6651 12.7247 11.6651 13.0176 11.958L16.2801 15.2205C16.573 15.5133 16.573 15.9882 16.2801 16.2811C15.9872 16.574 15.5124 16.574 15.2195 16.2811L11.957 13.0186C11.6641 12.7257 11.6641 12.2508 11.957 11.958Z"
          fill="#637381"></path>
      </svg>
    </button>
  </form>
  <!-- active tab  -->


  <!-- end active tab  -->
  <div
    class="border-stroke col-span-12 xl:col-span-4 pt-1 pb-4 border rounded-md min-h-[calc(100vh-170px)] overflow-auto">



    <!-- new code tab  -->
    <div class="flex space-x-2 max-md:space-x-0 mb-4 border-b w-full">
      <button @click="setActiveTab('active')" :class="[
        'flex flex-1 items-center px-4 py-1 border-b font-medium text-sm max-md:text-xs text-slate-800',
        currentTab === 'active' ? 'font-semibold border-b-2 border-cfp-500 text-cfp-500' : 'border-b-transparent'
      ]">
        <svg xmlns="http://www.w3.org/2000/svg" class="mr-1 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2m-4 0H7a2 2 0 01-2-2V10a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2z">
          </path>
        </svg>
        Active
      </button>

      <button @click="setActiveTab('inactive')" :class="[
        'flex  flex-1 items-center px-4 py-1 border-b font-medium text-sm max-md:text-xs',
        currentTab === 'inactive' ? 'font-semibold border-b-2 border-cfp-500 text-cfp-500' : 'text-gray-500 border-b-transparent'
      ]">
        <svg xmlns="http://www.w3.org/2000/svg" class="mr-1 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M15 10l4.28-4.28c.63-.63.18-1.72-.71-1.72H5a2 2 0 00-2 2v10c0 .55.22 1.05.59 1.41L10 17.41V19a2 2 0 002 2h3a1 1 0 001-1v-1l2.38-2.38c.35-.35.62-.84.62-1.39V9a1 1 0 00-1-1H16l-1-1z">
          </path>
        </svg>
        Inactive
      </button>

      <button @click="setActiveTab('closed')" :class="[
        'flex  flex-1 items-center px-4 py-1 border-b font-medium text-sm max-md:text-xs',
        currentTab === 'closed' ? 'font-semibold border-b-2 border-cfp-500 text-cfp-500' : 'text-gray-500 border-b-transparent'
      ]">
        <svg xmlns="http://www.w3.org/2000/svg" class="mr-1 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        Closed
      </button>
    </div>

    <!-- end new tab code  -->

    <div>
      <template v-for="(chat, index) in filteredUsersList" :key="index">
        <!-- Active Chats -->
        <div v-if="currentTab === 'active' && chat.status === 0" class="active_chat">
          <div @click="handleClick(chat.company_user_id+'_'+chat.company_id, encodeToBase64(chat.user_id))"
            class="flex items-center gap-5 hover:bg-gray-3 px-7.5 py-3 hover:cursor-pointer">
            <div class="relative rounded-full w-14 h-14">
              <img v-if="chat.profilePicture && chat.profilePicture.startsWith('http')" :src="chat.profilePicture"
                alt="User" class="rounded-full" />
              <img v-else-if="chat.profilePicture" :src="baseurl + '/storage/' + chat.profilePicture" alt="User"
                class="rounded-full" />
              <img v-else src="@/assets/images/user/user-profile.jpeg" alt="User" class="rounded-full" />

              <span v-if="chat.last_msg_type === 'user'"
                class="-top-0.5 right-0 z-1 absolute bg-green-400 rounded-full w-2 h-2"
                :class="'hidden_' + encodeToBase64(chat.user_id) + encodeToBase64(chat.company_id)">
                <span
                  class="inline-flex -z-1 absolute bg-meta-1 opacity-75 rounded-full w-full h-full animate-ping"></span>
              </span>

            </div>
            <div class="flex flex-1 justify-between items-center"
              :class="chat.last_msg_type == 'user' ? 'font-bold' : ''">
              <div>
                <h5 :class="chat.last_msg_type == 'user' ? 'font-bold' : 'font-medium text-slate-800'">{{ chat.name }}
                  (#{{ chat.user_id }})</h5>
                <p>

                  <span class="text-slate-800 text-sm" v-html="truncateMessage(chat.last_message)"></span>
                  <span class="text-xs"> &nbsp; {{ chat.last_timestamp ? timestamptoFormat(chat.last_timestamp) : '' }}
                  </span>

                </p>
              </div>
            </div>
          </div>
        </div>

          <!-- Inactive Chats Company Role -->
          <div
              v-if="currentTab === 'active' && chat.status === 1 && (chat.agent_id === authDetail.id && chat.company_user_id === authDetail.id)"
              class="inactive_chat">
              <div @click="handleClick(chat.company_user_id+'_'+chat.company_id, encodeToBase64(chat.user_id))"
                   class="flex items-center gap-5 hover:bg-gray-3 px-7.5 py-3 hover:cursor-pointer">
                  <div class="relative rounded-full w-14 h-14">
                      <img v-if="chat.profilePicture && chat.profilePicture.startsWith('http')" :src="chat.profilePicture"
                           alt="User" class="rounded-full" />
                      <img v-else-if="chat.profilePicture" :src="baseurl + '/storage/' + chat.profilePicture" alt="User"
                           class="rounded-full" />
                      <img v-else src="@/assets/images/user/user-profile.jpeg" alt="User" class="rounded-full" />
                      <span v-if="chat.last_msg_type === 'user'"
                            class="-top-0.5 right-0 z-1 absolute bg-green-400 rounded-full w-2 h-2"
                            :class="'hidden_' + encodeToBase64(chat.user_id) + encodeToBase64(chat.company_id)">
                <span
                    class="inline-flex -z-1 absolute bg-meta-1 opacity-75 rounded-full w-full h-full animate-ping"></span>
              </span>
                  </div>
                  <div class="flex flex-1 justify-between items-center"
                       :class="chat.last_msg_type == 'user' ? 'font-bold' : ''">
                      <div>
                          <h5 :class="chat.last_msg_type == 'user' ? 'font-bold' : 'font-medium text-slate-800'">{{ chat.name }}
                              (#{{ chat.user_id }})</h5>
                          <p>

                              <span class="text-slate-800 text-sm" v-html="truncateMessage(chat.last_message)"></span>
                              <span class="text-xs"> &nbsp; {{ chat.last_timestamp ? timestamptoFormat(chat.last_timestamp) : '' }}
                  </span>

                          </p>
                      </div>
                  </div>
              </div>
          </div>
        <!-- Inactive Chats for Agent -->
        <div
            v-if="currentTab === 'active' && chat.status === 1 && (chat.agent_id === authDetail.id && chat.company_user_id !== authDetail.id)"
          class="inactive_chat">
          <div @click="handleClick(chat.company_user_id+'_'+chat.company_id, encodeToBase64(chat.user_id))"
            class="flex items-center gap-5 hover:bg-gray-3 px-7.5 py-3 hover:cursor-pointer">
            <div class="relative rounded-full w-14 h-14">
              <img v-if="chat.profilePicture && chat.profilePicture.startsWith('http')" :src="chat.profilePicture"
                alt="User" class="rounded-full" />
              <img v-else-if="chat.profilePicture" :src="baseurl + '/storage/' + chat.profilePicture" alt="User"
                class="rounded-full" />
              <img v-else src="@/assets/images/user/user-profile.jpeg" alt="User" class="rounded-full" />
              <span v-if="chat.last_msg_type === 'user'"
                class="-top-0.5 right-0 z-1 absolute bg-green-400 rounded-full w-2 h-2"
                :class="'hidden_' + encodeToBase64(chat.user_id) + encodeToBase64(chat.company_id)">
                <span
                  class="inline-flex -z-1 absolute bg-meta-1 opacity-75 rounded-full w-full h-full animate-ping"></span>
              </span>
            </div>
            <div class="flex flex-1 justify-between items-center"
              :class="chat.last_msg_type == 'user' ? 'font-bold' : ''">
              <div>
                <h5 :class="chat.last_msg_type == 'user' ? 'font-bold' : 'font-medium text-slate-800'">{{ chat.name }}
                  (#{{ chat.user_id }})</h5>
                <p>

                  <span class="text-slate-800 text-sm" v-html="truncateMessage(chat.last_message)"></span>
                  <span class="text-xs"> &nbsp; {{ chat.last_timestamp ? timestamptoFormat(chat.last_timestamp) : '' }}
                  </span>

                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Inactive Chats (not the agent's) -->
        <div
          v-if="currentTab === 'inactive' && chat.status === 1 && (chat.agent_id !== authDetail.id)"
          class="inactive_chat">
          <div @click="handleClick(chat.company_user_id+'_'+chat.company_id, encodeToBase64(chat.user_id))"
            class="flex items-center gap-5 hover:bg-gray-3 px-7.5 py-3 hover:cursor-pointer">
            <div class="relative rounded-full w-14 h-14">
              <img v-if="chat.profilePicture && chat.profilePicture.startsWith('http')" :src="chat.profilePicture"
                alt="User" class="rounded-full" />
              <img v-else-if="chat.profilePicture" :src="baseurl + '/storage/' + chat.profilePicture" alt="User"
                class="rounded-full" />
              <img v-else src="@/assets/images/user/user-profile.jpeg" alt="User" class="rounded-full" />

            </div>
            <div class="flex flex-1 justify-between items-center">
              <div>
                <h5 class="font-medium text-slate-800">{{ chat.name }}  (#{{ chat.user_id }})</h5>
                <p>

                  <span class="text-slate-800 text-sm" v-html="truncateMessage(chat.last_message)"></span>
                  <span class="text-xs"> &nbsp; {{ chat.last_timestamp ? timestamptoFormat(chat.last_timestamp) : '' }}
                  </span>

                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Closed Chats -->
        <div v-if="currentTab === 'closed' && chat.status === 2" class="closed_chat">
          <div @click="handleClick(chat.company_user_id+'_'+chat.company_id, encodeToBase64(chat.user_id))"
            class="flex items-center gap-5 hover:bg-gray-3 px-7.5 py-3 hover:cursor-pointer">
            <div class="relative rounded-full w-14 h-14">
              <img v-if="chat.profilePicture && chat.profilePicture.startsWith('http')" :src="chat.profilePicture"
                alt="User" class="rounded-full" />
              <img v-else-if="chat.profilePicture" :src="baseurl + '/storage/' + chat.profilePicture" alt="User"
                class="rounded-full" />
              <img v-else src="@/assets/images/user/user-profile.jpeg" alt="User" class="rounded-full" />

            </div>
            <div class="flex flex-1 justify-between items-center">
              <div>
                <h5 class="font-medium text-slate-800">{{ chat.name }}</h5>
                <p>

                  <span class="text-slate-800 text-sm" v-html="truncateMessage(chat.last_message)"></span>
                  <span class="text-xs"> &nbsp; {{ chat.last_timestamp ? timestamptoFormat(chat.last_timestamp) : '' }}
                  </span>

                </p>
              </div>
            </div>
          </div>
        </div>

      </template>
    </div>
  </div>
</template>
