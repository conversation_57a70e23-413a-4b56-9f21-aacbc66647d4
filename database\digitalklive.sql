-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Aug 22, 2024 at 06:13 PM
-- Server version: 5.7.44-log
-- PHP Version: 8.3.7

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `digitalk`
--

-- --------------------------------------------------------

--
-- Table structure for table `botconversation`
--

CREATE TABLE `botconversation` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `bot_id` bigint(20) UNSIGNED NOT NULL,
  `company_user_id` int(11) NOT NULL,
  `lastmessage` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '0:new,1:ready,2:solved',
  `agent_id` int(11) NOT NULL DEFAULT '0',
  `last_msg_type` enum('agent','user','bot') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'user',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `botconversation`
--

INSERT INTO `botconversation` (`id`, `user_id`, `bot_id`, `company_user_id`, `lastmessage`, `status`, `agent_id`, `last_msg_type`, `created_at`, `updated_at`) VALUES
(2, 10, 1, 2, 'Hi how can i help you?', 1, 93, 'agent', '2024-08-13 12:25:10', '2024-08-13 12:26:51'),
(3, 6, 1, 2, 'Hi how can i help you?', 1, 93, 'agent', '2024-08-22 07:36:56', '2024-08-22 08:43:18');

-- --------------------------------------------------------

--
-- Table structure for table `bots`
--

CREATE TABLE `bots` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `company_id` int(11) NOT NULL DEFAULT '0',
  `chatbot_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `chatbot_project_id` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `chatbot_private_key_id` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `chatbot_client_email` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `chatbot_client_id` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `chatbot_private_key` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `bots`
--

INSERT INTO `bots` (`id`, `user_id`, `company_id`, `chatbot_name`, `chatbot_project_id`, `chatbot_private_key_id`, `chatbot_client_email`, `chatbot_client_id`, `chatbot_private_key`, `created_at`, `updated_at`) VALUES
(1, 2, 24, 'Order', 'chatbot-ddut', '85630e6439aaea5edfc8c74d53b26d859f20fd98', '<EMAIL>', '110800985403191458491', 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCiJoa4V5zjbmbb\\nGHbcEzdfDxV3ggODZm2VKgmrRYe8iUAHIQorxe7c/STN6TvZz2HA67V9v5UmiokZ\\niPblLO1Ke2u2GrVHQGFfLAI4RpjvLTsrg05PQMfC6mGV1c54QQWLXCMOXdBrDHTD\\nFK1ViE7QDI1RXQPfuen2T3BPHTR1c3MtKYeLe/EH+DJlQXOAliAb/Cc75gne8Enz\\nwTam22NFH7K6DeezuKKP3BExehozi0knDYLzRRxZ9onPfU+/rSMJvibojusbuodg\\n1gqh7u0SulzbbUjIQn44+W34hxkrRFzaKRROsIuB+KEepyqcIi1Q2k/uSt6ElreZ\\nuESBzZ0xAgMBAAECggEAAZcco6KyVnWu+w+cnNBwbtV8SamEBM4knX2lziD2sZXs\\nA5XinmbIzrCJ454ZuOtkg0qcg1D1csURjqdN+pXr9trKheTxqbL7MX6CQBCLL+9S\\n4Pye8D6LpwD694f7CCllF0aOTnYDlQUwgPhLMedOFJLG8QkXQGK3pXOGf7Qi31MA\\nRXKrzv7Oui1jNtEWuZwD5K/yJJcT9KNoXD3ZCL3v/9S21liGUESlL+Id3BIwrSYx\\n3+OVNbdxVrKdGoclEQTKHqv3Jr5gZlX1L+GBy5ZtrSpnslVJnFyUb5yfAPZyCYh7\\n/j5ZXObPYRMwl+R1K+7KeUJH03f1i9OdIKFx7wYF8QKBgQDi0bkkNQdKhQQIkcOU\\ngOOLf/GRwpvaQDMDM4LOJzooqUHORQU5s0eldT3LUUDLfbBgDE0JwXdr+hSOs1Qo\\n28GECOTBMW1GFakmptDNHnmzW4/2VtnRomuYfm0YnkxN+RxbhrdVY0TmPhpjmWcm\\nDoMp8FLNeWuaJVkQVnKe8GQ+iQKBgQC3AvE7V82tyU44SETMsrtw+0vfCxM0Nr2s\\n2j+xQAO1krh8YV+kbP3NMsr70aqfIQvKgKGREnogPNoxnoBW6yWd0umZNeArKZKD\\niqV7NebU6TfMSQnHiu3HrmCsvlHk/l7CZ7hmTNUuBgGe+AlTwbZZr7AAamctW0s2\\nph5KRdp/aQKBgQCAh1lDdC0yhBeaB4dtkm/er9yivUV1smV1ze20hjQFsIvSp/fj\\nD31s+/fNS7jM1udoNfA9s/zJGggmKOZq1MRe3m9KLoj+au3UMjkl0rwh76Ovct2x\\nVjlkS1fk2Gbi1GPGVZtz7nGviseHYvk5boXS8RTqzIA19C34/ROsjwovCQKBgHGa\\np/h4drrMXXvekKaoxGZL+DsKeu9Mk8165VK4NBVAcLj1ERPWfG4me456BnolX1Zj\\nJugRGjJs0909UNmjuoQP6uYqZW8lrUduh27GdPXxl6t+I0301cnAW06gDeIibNws\\n2nDWfPbGz7bIrbGKbBGcV5rKciXbqALSd7cPSaURAoGAVHujN9TUrJp8M/rcKS4U\\n7VNyXT2mjYBneQtYed+th85qVXQfvzx7cMZTbr7f8lJAtUbi2+0k0aX5cBmAWiIu\\nxLD8EKCkWa/Bnzay0R12WfbuvC6fviYi67m0LZmLWngKxpHYUfsCaNGsMXLxEjhs\\nUNVs/pOJEv2SzGrFt9hQaFQ=', '2024-03-11 14:31:52', '2024-07-30 03:57:06'),
(2, 37, 11, 'Reservation', 'chatbot-guard', '94b0eff8ef3065f7ed21969b2e31c0273befd406', '<EMAIL>', '116636574865870431820', 'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDxfR/vbPBeWR/v\\npQLw9BzKpZ57CRQp05N+bgh6dHPo8VPkbhr+BQdDOgTDPG2mFdq3bn5L/8TcbiNK\\nA3okl+rVTNjyb70IABTVOBJOr8MWQDI5NCb8qB8bJDmQm9VDDQuTUoMNP23tdx/e\\nluF/ipR7E16vEPzTHDGuDyjl5trk524ypL8rOGUk8ZSvKk7kvjJz/zprJ3sWQO7W\\nHy7BwGrvaxZcsyu1DIl/5C2/LjQBv0b/fYmpFx8L5cBMOPcFQZ25mfZnwXBjLMhi\\njADoqvqs4jjKWqH35PO0BhRA9pwFtaSeSoLxyTxBqUzGHFpf26+DxhruLvgWd2dQ\\nQsVTqQORAgMBAAECggEACmvkMCyzHds6R9kG7LeYM32cvoIIivqMu7DLcF9sriGW\\nBX5j71DvvIy5LmTB9aAtUWu1CxoVWI7vOm82evRMZ9qaZTIBxiHcxEvYbGlk4aWx\\nFStlQFOQDheM3WsAM9XRCVFPAwgY+h3BNBWuUxb7q+kM1cDFrGWj1jdkxDEngqfN\\nsNs4VIJJuaVy4lNqMopQwZclZVtm2UwRFu7sm4JYxJI50R4cIUaWHgXxuH6cufDO\\nLWCQ111evSHIs/hlGBFzeWcinr9wcC9VO9By72ANUfNtwo59jcuVuHFmtZVHrj6q\\nrwuLtZtsvkvYPRtdSV0l/9PxdiErS3xtEKj2AlZgfQKBgQD8oMCLw5IaVRQmd37V\\nNDzroNuFO8fR5kWLO0mtb3dPubXu8RWwcmMSdiwYpOiBiysAhgqoTsJmW17KEpiR\\nyO9Yxg3mENK28CV/iMu2s0xhc0RerV9MbnFMgUZsG+5PdSD0EdkNVcs7txOnswE7\\nv/q948GcjuTn0u6a/EDCVQkz1QKBgQD0tk8sTSP5Zj2OGWkgIOyCr8G/rz6x1hXU\\nbqN940uXz8PrCcZTpc1LzpWYvXbCUjwDHU4FUP0dqMNw5UevQgUOUOGTe/h16eVT\\neQql9jDkq1lnQCWeDyFfjYZHaGZAtHvlGKF0azcEhtH4WmZqV7LWsur2+jHKJUhB\\nj3OSDLt6zQKBgQCfaaT2zOtF+IflmiQLz5HdsjyzvzxhRGh/6mKcsajdQzf91l20\\nYQoJXU6IIo+hJzt0ciEJgeGEC4YuBzwFSoFWSGv+0IDW9YUEDENYP54kJENxZv+q\\n0EpoV28jyyA/PY8U1KpdktTTEryxZvCRTpyf9Ld43matrtnoLOOEfXp4HQKBgQC9\\nEo8hn4iIDeLJhAChfk6+uqVsQS6joNHFDKbtgUzKwxCQ7UB7KMPyKm+TT/AOPBN6\\nJI4/tU+cNuSqubwlQVE+umdP2J0kHc++Kut7RziHtYyiJgHqma4jcemqCpPWspx3\\nagx1MtGTgceBp/cxewkGsb6xjKYjlaK6ZOfalFPWkQKBgCVeme/ku2kH1lhiyo0t\\ngzdpuXf9hQaX7+/SaVvic1m9NUxLs4/3IwG5IzjdDTCeQ42eK62drzZRHV2LSZzJ\\nMgtkNadOjNTA1W97XdRO8OEGEQDrDPV6Pf+XgApTXQ6kaYKaNumvKUXA0zi7TVTk\\nnbIBG+1oaj3g5TGY70OJi1kl', '2024-03-11 14:32:27', '2024-07-30 09:22:01'),
(3, 2, 1, 'Food Order', 'food-order-431807', '4cbfb30a5827ded9d5ffbc660a09c9f09018e4df', '<EMAIL>', '118236518301740616387', 'MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCsMWM7/dc6PySc\\nYz0gcvU0IZFVEEFiO7EGr/MjliqzCp6A1jhDh8Fmo/F32Ve/vcqL3khYsqQ8Nrx3\\nqoXT3INmQjsovusa5hp7FuLos1yq6mfm1GiJUEOKoSxEU2RV1OomkIqpFI6BJv+S\\nliSpElDn3/ojcwBer4QV4OqbVNqj+h2n36gek7EQRhnQtyOcaQLB9wO7IVly460Z\\nxySO39zJFWWd7kUV2+TiAAKtnHzu76UUEZa1wy5mTNS9XPIFINfmjEEFyUtnb0dg\\nQphEXgAdNp54KUpHHxHTaVLLmizd/D6XymJBN3I2P7MfFVBMgjjBnzP66kcFr2rK\\nXCTgQhlFAgMBAAECggEAGER4mBCZB940xm6NA5j3+kId1ZduI45Gj2r2MQZ9m5CM\\nUJuAP/IuvUyvIAUrQvxYQpiP6SI6F7teGd5YMIjUIvIEHOS1RSv3QPAzh/iZP4Q8\\nIvG+DtYXQr8euj1nd1pDqq41wIkDYWvR2oKbBZHtgZRVEDBpta52RtAVOFF/49qF\\nibglOYUEaa5HmkIfuZzpC6wMPoMKkWOWAiLP77L+eJ29nlA87YeiSSNz5lh5flHm\\nGjYk8cT0GB0Q05djB3zlIpq6tvENsssQF05EGTbk1T+4jp/4IXAubVM7fDHi5J9E\\nHe+peSrw7RarjNpumLy4Lp45DPDPBzuIPJGebfq3QQKBgQDgitR0F3dbdrfd3aFY\\nHW3ysb4dxKpxzKoEO/UVte5lk/3rDcsNU3tZbXMKLV5V/7jlsIzrG4xfJK8rbUPA\\nl5C6aviQKxV13YUCZbJsb3DD+0pjJtPM3DoEP2prjRvpkxnXLVlVeNoBKE2jsHqm\\nsp1X8awkxv06UB2dhphC0ljpBQKBgQDEUQ5rgWXT2gUMb1Hbm1heAgPH/gJonGb6\\nxUg/aDa0YeWoeXumFDIVH9deYUJAcIz74LVipaSpi69OCW9sgo28KzVqudKk/WzF\\nSKjQbdAt6q56GcgbIQ/rwaQYHi+SfIAHt7jt4hFeu5iWG5JiK4mYSeY/4EX+dGfN\\n/xRva+pjQQKBgHkN36QG0q5Yvp+6ULW0iAYDlT9vO4TGlhJaULM3tiLFOmtysDmq\\n/CEmRJMECovKpeAO4qoN3AufN2DuM0ytabVW3lK7J4K3vnGgaiTTn7PI0j3KPP2A\\n7TZFChaZqKawS4oaRMMzuDRPiivphPZB7VnwTKI3NWYuswit+4Bq7swRAoGAWJz1\\n01TvL7dDfB9rZaMzOgvSxR1E3Q9r9FnEiMpzluxxi6B6Tbbj/GEJB9MC4q9rE+6U\\nMVjvgJBKqwDbSLGNZJhkXi5OhKbXyB1KBfrrcy5xXy/wAT7lPteDSFenxYlpCRLG\\n6KmYiZhIAcmkQ6e/TfxdcAu9zGOiz3sKMFkXzYECgYAEBaNN22nDePPHA9DtQCwU\\nyr0Gm8Bqr/1nCh87fjONZvq1zSTlq/VDdVy5qkKRvRSZoN0IrLxLwVQix3cY9+Hk\\nxVzkBLgHtfCdfrJQIpJwJczaR4QYQWYYiMX6swUagxW5NU5Ghk9ukRKnozdYZGu1\\nmkWQE0pAP/q+EFH0k6x87A==', '2024-08-08 11:13:54', '2024-08-08 11:13:54');

-- --------------------------------------------------------

--
-- Table structure for table `cache`
--

CREATE TABLE `cache` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `category`
--

CREATE TABLE `category` (
  `catId` bigint(20) UNSIGNED NOT NULL,
  `catName` varchar(180) COLLATE utf8mb4_unicode_ci NOT NULL,
  `catPicture` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `category`
--

INSERT INTO `category` (`catId`, `catName`, `catPicture`, `created_at`, `updated_at`) VALUES
(1, 'Server', '', '2023-10-31 10:35:15', '2023-10-31 10:35:15'),
(2, 'Website Management', '', '2023-10-31 10:35:15', '2023-10-31 10:35:15'),
(5, 'Test', 'https://firebasestorage.googleapis.com/v0/b/digitalk-26324.appspot.com/o/76C2A389-7996-4D78-BF1E-97327101C78A.jpg?alt=media&token=822ae5d5-d529-41f8-a48d-3dc174ae1f96', '2024-03-06 05:23:27', '2024-03-06 05:23:27'),
(6, 'Mobile Apps', 'https://firebasestorage.googleapis.com/v0/b/digitalk-26324.appspot.com/o/e72a2433-83ee-4706-ae68-339859765354.jpeg?alt=media&token=3a676711-c27a-423a-ac45-37d1b7b44446', '2024-03-11 18:36:47', '2024-03-11 18:36:47'),
(7, 'Correction Test', 'https://firebasestorage.googleapis.com/v0/b/digitalk-26324.appspot.com/o/13a04911-c187-4b99-b312-34b846e56cf5.jpeg?alt=media&token=087f94f3-831e-4a0f-8517-3e1a74043c63', '2024-03-12 18:33:51', '2024-03-12 18:33:51'),
(8, 'Books', 'https://firebasestorage.googleapis.com/v0/b/digitalk-26324.appspot.com/o/7f966e36-3a8d-4a3c-8035-42432b4f6ee3.jpeg?alt=media&token=358390b2-34b2-4543-8fb8-a0c8cecd8e39', '2024-03-16 06:09:46', '2024-03-16 06:09:46'),
(9, 'Www', 'https://firebasestorage.googleapis.com/v0/b/digitalk-26324.appspot.com/o/e73a6701-0c36-449d-8d25-9ccc9cf39628.jpeg?alt=media&token=452be3ca-b3d7-4117-a0b5-ae03c585aeec', '2024-03-16 17:26:09', '2024-03-16 17:26:09'),
(10, 'Test 2', 'category_pictures/jW2V1RddN8LIu6WgpLVR60pNLAcDdQobeE97ZSIf.png', '2024-08-13 10:13:55', '2024-08-13 10:13:55');

-- --------------------------------------------------------

--
-- Table structure for table `company`
--

CREATE TABLE `company` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `userId` bigint(20) UNSIGNED NOT NULL,
  `catId` bigint(20) UNSIGNED NOT NULL,
  `countryId` bigint(20) UNSIGNED NOT NULL,
  `companyName` varchar(180) COLLATE utf8mb4_unicode_ci NOT NULL,
  `companyAdd` varchar(180) COLLATE utf8mb4_unicode_ci NOT NULL,
  `city` varchar(80) COLLATE utf8mb4_unicode_ci NOT NULL,
  `state` varchar(80) COLLATE utf8mb4_unicode_ci NOT NULL,
  `zipCode` int(11) NOT NULL,
  `websiteUrl` varchar(180) COLLATE utf8mb4_unicode_ci NOT NULL,
  `chatSupport` enum('chatbot','livechat','both') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'chatbot',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `company`
--

INSERT INTO `company` (`id`, `userId`, `catId`, `countryId`, `companyName`, `companyAdd`, `city`, `state`, `zipCode`, `websiteUrl`, `chatSupport`, `created_at`, `updated_at`) VALUES
(1, 2, 8, 1, 'Jonson company', '122210 Abc Av, Norwalk', 'Norwalk', 'CA', 90705, 'www.abc.com', 'both', '2023-10-31 10:35:15', '2024-08-12 13:38:38'),
(4, 5, 5, 9, 'sdsd', 'sd', 'sd', 'sdsd', 360001, 'sd', '', '2024-02-27 05:55:25', '2024-07-30 01:39:38'),
(5, 5, 1, 1, 'test', 'test', 'rajkot', 'gujrat', 360001, 'https://www.google.com/', '', '2024-02-29 05:40:49', '2024-02-29 05:40:49'),
(7, 38, 1, 1, 'test', 'test', 'rajkot', 'gujrat', 360001, 'https://www.google.com/', '', '2024-03-06 06:34:04', '2024-03-06 06:34:04'),
(10, 41, 1, 1, 'test', 'test', 'rajkot', 'gujrat', 360004, 'https://www.test.com/', '', '2024-03-07 05:11:50', '2024-03-07 05:12:19'),
(11, 37, 1, 1, 'Microsoft', 'test', 'rajkot', 'gujrat', 360001, 'https://www.google.com/', 'chatbot', '2024-03-06 06:34:04', '2024-04-01 05:06:07'),
(12, 42, 1, 1, 'test', 'test', 'rajkot', 'gujrat', 360004, 'https://www.test.com/', '', '2024-03-07 06:41:53', '2024-03-07 06:41:53'),
(13, 1, 1, 1, 'John', '122210 Abc Av, Norwalk', 'Norwalk', 'CA', 90705, 'www.abc.com', 'both', '2023-10-31 10:35:15', '2024-03-13 20:11:36'),
(14, 43, 1, 1, 'test', 'test', 'rajkot', 'gujrat', 360004, 'https://www.test.com/', '', '2024-03-11 08:07:58', '2024-03-11 08:07:58'),
(15, 44, 6, 28, 'Dev', 'Ysysy', 'Dffcc', 'Cccc', 65000, 'Ususus.com', '', '2024-03-12 18:17:07', '2024-03-12 18:17:07'),
(16, 45, 2, 14, 'johndoe', 'Prahalad nagar', 'raj', 'guj', 360001, 'http://bots.eruditetechnology.com/profile', 'chatbot', '2024-03-13 20:28:54', '2024-03-13 20:28:54'),
(17, 49, 6, 1, 'Farhan Devs', 'Hshhs', 'Udus', 'Hdjsjs', 65000, 'Hello.com', '', '2024-03-15 02:41:05', '2024-03-15 02:41:05'),
(18, 50, 8, 5, 'pogila8450', 'Jsjsjsjs', 'Hshshsh', '7d7sus', 58000, 'Hshshs.com', '', '2024-03-16 05:43:06', '2024-03-16 06:09:51'),
(22, 57, 1, 1, 'test', 'test', 'rajkot', 'gujrat', 360001, 'https://www.google.com/', 'chatbot', '2024-05-06 16:41:01', '2024-05-06 16:41:01'),
(23, 2, 6, 18, 'Jonson company', 'test2', 'teste', 'eter', 360001, 'test', 'chatbot', '2024-07-30 01:55:29', '2024-08-12 13:38:38'),
(24, 2, 6, 9, 'Jonson company', 'testing address', 'testing city', 'testing state', 360001, 'https://web.whatsapp.com/', 'chatbot', '2024-07-30 02:30:13', '2024-08-12 13:38:38'),
(25, 2, 6, 8, 'Jonson company', 'sdfs', 'fdsf', 'dsf', 360005, 'dsfdsf', 'chatbot', '2024-07-30 03:12:27', '2024-08-12 13:38:38'),
(26, 2, 2, 18, 'Jonson company', 'dfdf', 'dfdfsdasd', 'dfdf', 360002, 'dfd', 'chatbot', '2024-07-30 06:20:42', '2024-08-12 13:38:38'),
(27, 97, 2, 2, 'testing', 'testing address', 'testing city', 'testing state', 360001, '', 'chatbot', '2024-08-14 08:22:27', '2024-08-14 08:22:27');

-- --------------------------------------------------------

--
-- Table structure for table `complain`
--

CREATE TABLE `complain` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `companyId` bigint(20) UNSIGNED NOT NULL,
  `assignedToUserId` bigint(20) UNSIGNED NOT NULL,
  `priority` enum('0','1','2') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '0-low,1-medium,2-high',
  `progressStatus` enum('new','inprogress','need_user_input','resolved') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'new',
  `complainTitle` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `complainDetail` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `complainStatus` enum('open','close') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'open',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `complain`
--

INSERT INTO `complain` (`id`, `companyId`, `assignedToUserId`, `priority`, `progressStatus`, `complainTitle`, `complainDetail`, `complainStatus`, `created_at`, `updated_at`) VALUES
(1, 1, 1, '0', 'new', 'Unable to load the server', 'Hi There,I have issue with my server. I am unable to load it.', 'close', '2023-10-31 10:35:15', '2024-02-27 06:39:48'),
(2, 1, 1, '2', 'new', 'Test', 'This is for testing!', 'close', '2023-11-03 21:23:29', '2024-08-13 08:55:44'),
(3, 1, 1, '1', 'new', 'Test', 'Test', 'close', '2023-11-03 21:24:17', '2024-02-27 06:40:02'),
(5, 1, 5, '1', 'new', 'test', 'test', 'open', '2024-02-27 06:07:41', '2024-03-21 05:27:40'),
(7, 1, 5, '', 'new', 'test', 'test', 'open', '2024-02-29 05:40:58', '2024-02-29 05:40:58'),
(8, 1, 5, '', 'new', 'test', 'test', 'open', '2024-03-01 08:42:49', '2024-03-01 08:42:49'),
(9, 1, 5, '1', 'new', 'test', 'test', 'open', '2024-03-21 05:26:30', '2024-03-21 05:26:30'),
(11, 1, 1, '1', 'new', 'test', 'test complain', 'close', '2024-03-21 14:55:10', '2024-03-21 15:58:03'),
(12, 1, 1, '1', 'new', 'test', 'test complain', 'close', '2024-03-22 12:22:41', '2024-03-22 12:23:30'),
(13, 13, 53, '1', 'new', 'testing complain', 'testing', 'open', '2024-04-01 19:36:56', '2024-04-01 19:36:56'),
(14, 13, 48, '1', 'new', 'test', 'testing', 'open', '2024-04-01 20:37:08', '2024-04-01 20:37:08'),
(15, 1, 12, '0', 'new', 'test 2024', 'tesdtsdfsdfsdfssd ccds csdc sd', 'open', '2024-07-03 07:12:53', '2024-07-03 07:52:29'),
(16, 11, 12, '2', 'new', 'tester', 'testerer', 'open', '2024-07-03 10:27:45', '2024-07-03 10:27:45'),
(17, 7, 12, '2', 'new', 'dfdfdfd', 'fdfdfdfdf', 'open', '2024-07-03 10:29:01', '2024-07-03 10:29:01'),
(18, 1, 48, '1', 'new', 'dsfgdg', 'dfgdg', 'open', '2024-07-26 04:08:48', '2024-07-26 04:08:48'),
(19, 7, 12, '1', 'new', 'dfdf', 'dfd', 'open', '2024-07-26 04:50:56', '2024-07-26 04:50:56'),
(20, 1, 48, '1', 'new', 'test 2024', '54', 'open', '2024-07-30 06:17:31', '2024-07-30 06:17:31'),
(21, 1, 6, '1', 'inprogress', 'This is for testing!', 'Hi There,I have issue with my server. I am unable to load it.', 'open', '2024-08-13 08:35:58', '2024-08-13 08:35:58'),
(22, 1, 6, '0', 'inprogress', 'i need help', 'i need help in authenticate user list to fetch', 'close', '2024-08-22 10:04:14', '2024-08-22 10:08:09'),
(23, 1, 6, '0', 'inprogress', 'i need help', 'i need help in authenticate user list to fetch', 'open', '2024-08-22 10:10:07', '2024-08-22 10:10:07');

-- --------------------------------------------------------

--
-- Table structure for table `complainmessage`
--

CREATE TABLE `complainmessage` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `complainId` bigint(20) UNSIGNED NOT NULL,
  `senderId` bigint(20) UNSIGNED NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `complainmessage`
--

INSERT INTO `complainmessage` (`id`, `complainId`, `senderId`, `content`, `created_at`, `updated_at`) VALUES
(1, 15, 2, 'hi', '2024-07-31 05:52:09', '2024-07-31 05:52:09'),
(2, 2, 1, 'your issue is solved', '2024-08-13 08:55:44', '2024-08-13 08:55:44'),
(3, 5, 5, 'testing', '2024-08-13 08:58:37', '2024-08-13 08:58:37'),
(4, 5, 5, 'testing', '2024-08-13 09:05:07', '2024-08-13 09:05:07'),
(5, 5, 5, 'testing', '2024-08-13 09:12:31', '2024-08-13 09:12:31'),
(6, 22, 6, 'problem solved', '2024-08-22 10:08:09', '2024-08-22 10:08:09'),
(7, 23, 6, 'Hi im here', '2024-08-22 10:10:15', '2024-08-22 10:10:15');

-- --------------------------------------------------------

--
-- Table structure for table `contactus`
--

CREATE TABLE `contactus` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `fullname` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `country`
--

CREATE TABLE `country` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(180) COLLATE utf8mb4_unicode_ci NOT NULL,
  `nicename` varchar(180) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `numcode` int(11) DEFAULT NULL,
  `phonecode` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `country`
--

INSERT INTO `country` (`id`, `name`, `nicename`, `numcode`, `phonecode`, `created_at`, `updated_at`) VALUES
(1, 'AFGHANISTAN', 'Afghanistan', 4, 93, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(2, 'ALBANIA', 'Albania', 8, 355, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(3, 'ALGERIA', 'Algeria', 12, 213, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(4, 'AMERICAN SAMOA', 'American Samoa', 16, 1684, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(5, 'ANDORRA', 'Andorra', 20, 376, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(6, 'ANGOLA', 'Angola', 24, 244, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(7, 'ANGUILLA', 'Anguilla', 660, 1264, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(8, 'ANTARCTICA', 'Antarctica', NULL, 0, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(9, 'ANTIGUA AND BARBUDA', 'Antigua and Barbuda', 28, 1268, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(10, 'ARGENTINA', 'Argentina', 32, 54, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(11, 'ARMENIA', 'Armenia', 51, 374, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(12, 'ARUBA', 'Aruba', 533, 297, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(13, 'AUSTRALIA', 'Australia', 36, 61, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(14, 'AUSTRIA', 'Austria', 40, 43, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(15, 'AZERBAIJAN', 'Azerbaijan', 31, 994, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(16, 'BAHAMAS', 'Bahamas', 44, 1242, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(17, 'BAHRAIN', 'Bahrain', 48, 973, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(18, 'BANGLADESH', 'Bangladesh', 50, 880, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(19, 'BARBADOS', 'Barbados', 52, 1246, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(20, 'BELARUS', 'Belarus', 112, 375, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(21, 'BELGIUM', 'Belgium', 56, 32, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(22, 'BELIZE', 'Belize', 84, 501, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(23, 'BENIN', 'Benin', 204, 229, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(24, 'BERMUDA', 'Bermuda', 60, 1441, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(25, 'BHUTAN', 'Bhutan', 64, 975, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(26, 'BOLIVIA', 'Bolivia', 68, 591, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(27, 'BOSNIA AND HERZEGOVINA', 'Bosnia and Herzegovina', 70, 387, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(28, 'BOTSWANA', 'Botswana', 72, 267, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(29, 'BOUVET ISLAND', 'Bouvet Island', NULL, 0, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(30, 'BRAZIL', 'Brazil', 76, 55, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(31, 'BRITISH INDIAN OCEAN TERRITORY', 'British Indian Ocean Territory', NULL, 246, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(32, 'BRUNEI DARUSSALAM', 'Brunei Darussalam', 96, 673, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(33, 'BULGARIA', 'Bulgaria', 100, 359, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(34, 'BURKINA FASO', 'Burkina Faso', 854, 226, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(35, 'BURUNDI', 'Burundi', 108, 257, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(36, 'CAMBODIA', 'Cambodia', 116, 855, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(37, 'CAMEROON', 'Cameroon', 120, 237, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(38, 'CANADA', 'Canada', 124, 1, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(39, 'CAPE VERDE', 'Cape Verde', 132, 238, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(40, 'CAYMAN ISLANDS', 'Cayman Islands', 136, 1345, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(41, 'CENTRAL AFRICAN REPUBLIC', 'Central African Republic', 140, 236, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(42, 'CHAD', 'Chad', 148, 235, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(43, 'CHILE', 'Chile', 152, 56, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(44, 'CHINA', 'China', 156, 86, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(45, 'CHRISTMAS ISLAND', 'Christmas Island', NULL, 61, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(46, 'COCOS (KEELING) ISLANDS', 'Cocos (Keeling) Islands', NULL, 672, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(47, 'COLOMBIA', 'Colombia', 170, 57, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(48, 'COMOROS', 'Comoros', 174, 269, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(49, 'CONGO', 'Congo', 178, 242, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(50, 'CONGO,THE DEMOCRATIC REPUBLIC OF THE', 'Congo,the Democratic Republic of the', 180, 242, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(51, 'COOK ISLANDS', 'Cook Islands', 184, 682, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(52, 'COSTA RICA', 'Costa Rica', 188, 506, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(53, 'COTE D\'IVOIRE', 'Cote D\'Ivoire', 384, 225, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(54, 'CROATIA', 'Croatia', 191, 385, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(55, 'CUBA', 'Cuba', 192, 53, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(56, 'CYPRUS', 'Cyprus', 196, 357, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(57, 'CZECH REPUBLIC', 'Czech Republic', 203, 420, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(58, 'DENMARK', 'Denmark', 208, 45, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(59, 'DJIBOUTI', 'Djibouti', 262, 253, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(60, 'DOMINICA', 'Dominica', 212, 1767, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(61, 'DOMINICAN REPUBLIC', 'Dominican Republic', 214, 1809, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(62, 'ECUADOR', 'Ecuador', 218, 593, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(63, 'EGYPT', 'Egypt', 818, 20, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(64, 'EL SALVADOR', 'El Salvador', 222, 503, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(65, 'EQUATORIAL GUINEA', 'Equatorial Guinea', 226, 240, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(66, 'ERITREA', 'Eritrea', 232, 291, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(67, 'ESTONIA', 'Estonia', 233, 372, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(68, 'ETHIOPIA', 'Ethiopia', 231, 251, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(69, 'FALKLAND ISLANDS (MALVINAS)', 'Falkland Islands (Malvinas)', 238, 500, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(70, 'FAROE ISLANDS', 'Faroe Islands', 234, 298, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(71, 'FIJI', 'Fiji', 242, 679, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(72, 'FINLAND', 'Finland', 246, 358, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(73, 'FRANCE', 'France', 250, 33, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(74, 'FRENCH GUIANA', 'French Guiana', 254, 594, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(75, 'FRENCH POLYNESIA', 'French Polynesia', 258, 689, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(76, 'FRENCH SOUTHERN TERRITORIES', 'French Southern Territories', NULL, 0, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(77, 'GABON', 'Gabon', 266, 241, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(78, 'GAMBIA', 'Gambia', 270, 220, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(79, 'GEORGIA', 'Georgia', 268, 995, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(80, 'GERMANY', 'Germany', 276, 49, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(81, 'GHANA', 'Ghana', 288, 233, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(82, 'GIBRALTAR', 'Gibraltar', 292, 350, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(83, 'GREECE', 'Greece', 300, 30, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(84, 'GREENLAND', 'Greenland', 304, 299, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(85, 'GRENADA', 'Grenada', 308, 1473, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(86, 'GUADELOUPE', 'Guadeloupe', 312, 590, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(87, 'GUAM', 'Guam', 316, 1671, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(88, 'GUATEMALA', 'Guatemala', 320, 502, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(89, 'GUINEA', 'Guinea', 324, 224, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(90, 'GUINEA-BISSAU', 'Guinea-Bissau', 624, 245, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(91, 'GUYANA', 'Guyana', 328, 592, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(92, 'HAITI', 'Haiti', 332, 509, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(93, 'HEARD ISLAND AND MCDONALD ISLANDS', 'Heard Island and Mcdonald Islands', NULL, 0, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(94, 'HOLY SEE (VATICAN CITY STATE)', 'Holy See (Vatican City State)', 336, 39, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(95, 'HONDURAS', 'Honduras', 340, 504, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(96, 'HONG KONG', 'Hong Kong', 344, 852, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(97, 'HUNGARY', 'Hungary', 348, 36, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(98, 'ICELAND', 'Iceland', 352, 354, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(99, 'INDIA', 'India', 356, 91, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(100, 'INDONESIA', 'Indonesia', 360, 62, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(101, 'IRAN,ISLAMIC REPUBLIC OF', 'Iran,Islamic Republic of', 364, 98, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(102, 'IRAQ', 'Iraq', 368, 964, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(103, 'IRELAND', 'Ireland', 372, 353, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(104, 'ISRAEL', 'Israel', 376, 972, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(105, 'ITALY', 'Italy', 380, 39, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(106, 'JAMAICA', 'Jamaica', 388, 1876, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(107, 'JAPAN', 'Japan', 392, 81, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(108, 'JORDAN', 'Jordan', 400, 962, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(109, 'KAZAKHSTAN', 'Kazakhstan', 398, 7, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(110, 'KENYA', 'Kenya', 404, 254, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(111, 'KIRIBATI', 'Kiribati', 296, 686, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(112, 'KOREA,DEMOCRATIC PEOPLE\'S REPUBLIC OF', 'Korea,Democratic People\'s Republic of', 408, 850, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(113, 'KOREA,REPUBLIC OF', 'Korea,Republic of', 410, 82, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(114, 'KUWAIT', 'Kuwait', 414, 965, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(115, 'KYRGYZSTAN', 'Kyrgyzstan', 417, 996, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(116, 'LAO PEOPLE\'S DEMOCRATIC REPUBLIC', 'Lao People\'s Democratic Republic', 418, 856, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(117, 'LATVIA', 'Latvia', 428, 371, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(118, 'LEBANON', 'Lebanon', 422, 961, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(119, 'LESOTHO', 'Lesotho', 426, 266, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(120, 'LIBERIA', 'Liberia', 430, 231, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(121, 'LIBYAN ARAB JAMAHIRIYA', 'Libyan Arab Jamahiriya', 434, 218, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(122, 'LIECHTENSTEIN', 'Liechtenstein', 438, 423, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(123, 'LITHUANIA', 'Lithuania', 440, 370, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(124, 'LUXEMBOURG', 'Luxembourg', 442, 352, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(125, 'MACAO', 'Macao', 446, 853, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(126, 'MACEDONIA,THE FORMER YUGOSLAV REPUBLIC OF', 'Macedonia,the Former Yugoslav Republic of', 807, 389, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(127, 'MADAGASCAR', 'Madagascar', 450, 261, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(128, 'MALAWI', 'Malawi', 454, 265, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(129, 'MALAYSIA', 'Malaysia', 458, 60, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(130, 'MALDIVES', 'Maldives', 462, 960, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(131, 'MALI', 'Mali', 466, 223, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(132, 'MALTA', 'Malta', 470, 356, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(133, 'MARSHALL ISLANDS', 'Marshall Islands', 584, 692, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(134, 'MARTINIQUE', 'Martinique', 474, 596, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(135, 'MAURITANIA', 'Mauritania', 478, 222, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(136, 'MAURITIUS', 'Mauritius', 480, 230, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(137, 'MAYOTTE', 'Mayotte', NULL, 269, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(138, 'MEXICO', 'Mexico', 484, 52, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(139, 'MICRONESIA,FEDERATED STATES OF', 'Micronesia,Federated States of', 583, 691, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(140, 'MOLDOVA,REPUBLIC OF', 'Moldova,Republic of', 498, 373, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(141, 'MONACO', 'Monaco', 492, 377, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(142, 'MONGOLIA', 'Mongolia', 496, 976, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(143, 'MONTSERRAT', 'Montserrat', 500, 1664, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(144, 'MOROCCO', 'Morocco', 504, 212, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(145, 'MOZAMBIQUE', 'Mozambique', 508, 258, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(146, 'MYANMAR', 'Myanmar', 104, 95, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(147, 'NAMIBIA', 'Namibia', 516, 264, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(148, 'NAURU', 'Nauru', 520, 674, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(149, 'NEPAL', 'Nepal', 524, 977, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(150, 'NETHERLANDS', 'Netherlands', 528, 31, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(151, 'NETHERLANDS ANTILLES', 'Netherlands Antilles', 530, 599, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(152, 'NEW CALEDONIA', 'New Caledonia', 540, 687, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(153, 'NEW ZEALAND', 'New Zealand', 554, 64, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(154, 'NICARAGUA', 'Nicaragua', 558, 505, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(155, 'NIGER', 'Niger', 562, 227, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(156, 'NIGERIA', 'Nigeria', 566, 234, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(157, 'NIUE', 'Niue', 570, 683, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(158, 'NORFOLK ISLAND', 'Norfolk Island', 574, 672, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(159, 'NORTHERN MARIANA ISLANDS', 'Northern Mariana Islands', 580, 1670, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(160, 'NORWAY', 'Norway', 578, 47, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(161, 'OMAN', 'Oman', 512, 968, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(162, 'PAKISTAN', 'Pakistan', 586, 92, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(163, 'PALAU', 'Palau', 585, 680, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(164, 'PALESTINIAN TERRITORY,OCCUPIED', 'Palestinian Territory,Occupied', NULL, 970, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(165, 'PANAMA', 'Panama', 591, 507, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(166, 'PAPUA NEW GUINEA', 'Papua New Guinea', 598, 675, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(167, 'PARAGUAY', 'Paraguay', 600, 595, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(168, 'PERU', 'Peru', 604, 51, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(169, 'PHILIPPINES', 'Philippines', 608, 63, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(170, 'PITCAIRN', 'Pitcairn', 612, 0, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(171, 'POLAND', 'Poland', 616, 48, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(172, 'PORTUGAL', 'Portugal', 620, 351, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(173, 'PUERTO RICO', 'Puerto Rico', 630, 1787, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(174, 'QATAR', 'Qatar', 634, 974, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(175, 'REUNION', 'Reunion', 638, 262, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(176, 'ROMANIA', 'Romania', 642, 40, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(177, 'RUSSIAN FEDERATION', 'Russian Federation', 643, 70, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(178, 'RWANDA', 'Rwanda', 646, 250, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(179, 'SAINT HELENA', 'Saint Helena', 654, 290, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(180, 'SAINT KITTS AND NEVIS', 'Saint Kitts and Nevis', 659, 1869, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(181, 'SAINT LUCIA', 'Saint Lucia', 662, 1758, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(182, 'SAINT PIERRE AND MIQUELON', 'Saint Pierre and Miquelon', 666, 508, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(183, 'SAINT VINCENT AND THE GRENADINES', 'Saint Vincent and the Grenadines', 670, 1784, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(184, 'SAMOA', 'Samoa', 882, 684, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(185, 'SAN MARINO', 'San Marino', 674, 378, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(186, 'SAO TOME AND PRINCIPE', 'Sao Tome and Principe', 678, 239, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(187, 'SAUDI ARABIA', 'Saudi Arabia', 682, 966, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(188, 'SENEGAL', 'Senegal', 686, 221, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(189, 'SERBIA AND MONTENEGRO', 'Serbia and Montenegro', NULL, 381, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(190, 'SEYCHELLES', 'Seychelles', 690, 248, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(191, 'SIERRA LEONE', 'Sierra Leone', 694, 232, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(192, 'SINGAPORE', 'Singapore', 702, 65, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(193, 'SLOVAKIA', 'Slovakia', 703, 421, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(194, 'SLOVENIA', 'Slovenia', 705, 386, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(195, 'SOLOMON ISLANDS', 'Solomon Islands', 90, 677, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(196, 'SOMALIA', 'Somalia', 706, 252, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(197, 'SOUTH AFRICA', 'South Africa', 710, 27, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(198, 'SOUTH GEORGIA AND THE SOUTH SANDWICH ISLANDS', 'South Georgia and the South Sandwich Islands', NULL, 0, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(199, 'SPAIN', 'Spain', 724, 34, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(200, 'SRI LANKA', 'Sri Lanka', 144, 94, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(201, 'SUDAN', 'Sudan', 736, 249, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(202, 'SURINAME', 'Suriname', 740, 597, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(203, 'SVALBARD AND JAN MAYEN', 'Svalbard and Jan Mayen', 744, 47, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(204, 'SWAZILAND', 'Swaziland', 748, 268, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(205, 'SWEDEN', 'Sweden', 752, 46, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(206, 'SWITZERLAND', 'Switzerland', 756, 41, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(207, 'SYRIAN ARAB REPUBLIC', 'Syrian Arab Republic', 760, 963, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(208, 'TAIWAN,PROVINCE OF CHINA', 'Taiwan,Province of China', 158, 886, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(209, 'TAJIKISTAN', 'Tajikistan', 762, 992, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(210, 'TANZANIA,UNITED REPUBLIC OF', 'Tanzania,United Republic of', 834, 255, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(211, 'THAILAND', 'Thailand', 764, 66, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(212, 'TIMOR-LESTE', 'Timor-Leste', NULL, 670, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(213, 'TOGO', 'Togo', 768, 228, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(214, 'TOKELAU', 'Tokelau', 772, 690, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(215, 'TONGA', 'Tonga', 776, 676, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(216, 'TRINIDAD AND TOBAGO', 'Trinidad and Tobago', 780, 1868, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(217, 'TUNISIA', 'Tunisia', 788, 216, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(218, 'TURKEY', 'Turkey', 792, 90, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(219, 'TURKMENISTAN', 'Turkmenistan', 795, 7370, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(220, 'TURKS AND CAICOS ISLANDS', 'Turks and Caicos Islands', 796, 1649, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(221, 'TUVALU', 'Tuvalu', 798, 688, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(222, 'UGANDA', 'Uganda', 800, 256, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(223, 'UKRAINE', 'Ukraine', 804, 380, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(224, 'UNITED ARAB EMIRATES', 'United Arab Emirates', 784, 971, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(225, 'UNITED KINGDOM', 'United Kingdom', 826, 44, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(226, 'UNITED STATES', 'United States', 840, 1, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(227, 'UNITED STATES MINOR OUTLYING ISLANDS', 'United States Minor Outlying Islands', NULL, 1, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(228, 'URUGUAY', 'Uruguay', 858, 598, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(229, 'UZBEKISTAN', 'Uzbekistan', 860, 998, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(230, 'VANUATU', 'Vanuatu', 548, 678, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(231, 'VENEZUELA', 'Venezuela', 862, 58, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(232, 'VIET NAM', 'Viet Nam', 704, 84, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(233, 'VIRGIN ISLANDS,BRITISH', 'Virgin Islands,British', 92, 1284, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(234, 'VIRGIN ISLANDS,U.S.', 'Virgin Islands,U.s.', 850, 1340, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(235, 'WALLIS AND FUTUNA', 'Wallis and Futuna', 876, 681, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(236, 'WESTERN SAHARA', 'Western Sahara', 732, 212, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(237, 'YEMEN', 'Yemen', 887, 967, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(238, 'ZAMBIA', 'Zambia', 894, 260, '2023-10-31 05:05:15', '2023-10-31 05:05:15'),
(239, 'ZIMBABWE', 'Zimbabwe', 716, 263, '2023-10-31 05:05:15', '2023-10-31 05:05:15');

-- --------------------------------------------------------

--
-- Table structure for table `deals`
--

CREATE TABLE `deals` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `companyId` bigint(20) UNSIGNED NOT NULL,
  `deal_file` longtext COLLATE utf8mb4_unicode_ci,
  `dealTitle` varchar(180) COLLATE utf8mb4_unicode_ci NOT NULL,
  `dealContent` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `dealStartOn` timestamp NULL DEFAULT NULL,
  `dealExpiresOn` timestamp NULL DEFAULT NULL,
  `deal_status` int(11) NOT NULL DEFAULT '0' COMMENT '0:default,1:start,2:end',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `deals`
--

INSERT INTO `deals` (`id`, `companyId`, `deal_file`, `dealTitle`, `dealContent`, `dealStartOn`, `dealExpiresOn`, `deal_status`, `created_at`, `updated_at`) VALUES
(1, 1, NULL, 'Macy\'s Last Act Sales', '75% off 1,000s of Items buy now, the deal is for a short period of time.', '2024-04-12 07:00:00', '2024-10-17 07:00:00', 0, '2023-10-31 10:35:15', '2024-07-26 07:38:11'),
(3, 13, NULL, 'Act Sales', '80% off 1,000s of Items buy now, the deal is for a short period of time.', '2024-04-13 07:00:00', '2024-04-17 07:00:00', 0, '2023-10-31 10:35:15', '2024-04-12 12:58:19');

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `failed_jobs`
--

INSERT INTO `failed_jobs` (`id`, `uuid`, `connection`, `queue`, `payload`, `exception`, `failed_at`) VALUES
(1, 'acacf405-3941-4a3d-8c0c-ee6024db7c8d', 'database', 'default', '{\"uuid\":\"acacf405-3941-4a3d-8c0c-ee6024db7c8d\",\"displayName\":\"App\\\\Events\\\\NotificationEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:28:\\\"App\\\\Events\\\\NotificationEvent\\\":1:{s:17:\\\"notificationsData\\\";a:1:{s:6:\\\"status\\\";i:1;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"}}', 'Illuminate\\Broadcasting\\BroadcastException: Pusher error: cURL error 28: Connection timed out after 10000 milliseconds (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://3.15.193.83:8001/apps/164294/events?auth_key=gkq017yvprodby4ndzv7&auth_timestamp=1722427226&auth_version=1.0&body_md5=2e2953de47344854baf08e2bddfe747f&auth_signature=0cf5dc8051313aa6bd1c93c865a450e380c52f7c0fb41054f8c4f05e7df723a2. in /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/Broadcasters/PusherBroadcaster.php:164\nStack trace:\n#0 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastEvent.php(92): Illuminate\\Broadcasting\\Broadcasters\\PusherBroadcaster->broadcast()\n#1 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Broadcasting\\BroadcastEvent->handle()\n#2 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#3 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#4 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#5 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#6 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#7 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#8 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#9 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#10 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#11 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#12 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#13 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Pipeline\\Pipeline->then()\n#14 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(71): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#15 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call()\n#16 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#17 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#18 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(333): Illuminate\\Queue\\Worker->runJob()\n#19 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(139): Illuminate\\Queue\\Worker->runNextJob()\n#20 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(122): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#21 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#22 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#23 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#24 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#25 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#26 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()\n#27 /www/wwwroot/*************/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()\n#28 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()\n#29 /www/wwwroot/*************/vendor/symfony/console/Application.php(1047): Illuminate\\Console\\Command->run()\n#30 /www/wwwroot/*************/vendor/symfony/console/Application.php(316): Symfony\\Component\\Console\\Application->doRunCommand()\n#31 /www/wwwroot/*************/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun()\n#32 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(196): Symfony\\Component\\Console\\Application->run()\n#33 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1203): Illuminate\\Foundation\\Console\\Kernel->handle()\n#34 /www/wwwroot/*************/artisan(13): Illuminate\\Foundation\\Application->handleCommand()\n#35 {main}', '2024-07-31 12:00:36'),
(2, 'b0825b42-6dda-4dac-8b78-95590276b6e7', 'database', 'default', '{\"uuid\":\"b0825b42-6dda-4dac-8b78-95590276b6e7\",\"displayName\":\"App\\\\Events\\\\NotificationEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:28:\\\"App\\\\Events\\\\NotificationEvent\\\":1:{s:17:\\\"notificationsData\\\";a:1:{s:6:\\\"status\\\";i:1;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"}}', 'Illuminate\\Broadcasting\\BroadcastException: Pusher error: cURL error 28: Connection timed out after 10001 milliseconds (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://3.15.193.83:8001/apps/164294/events?auth_key=gkq017yvprodby4ndzv7&auth_timestamp=1722427237&auth_version=1.0&body_md5=2e2953de47344854baf08e2bddfe747f&auth_signature=cd21619220b95d1e1d49d35a181d4e4595ccd0d69995ea3b242235cd27b685cf. in /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/Broadcasters/PusherBroadcaster.php:164\nStack trace:\n#0 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastEvent.php(92): Illuminate\\Broadcasting\\Broadcasters\\PusherBroadcaster->broadcast()\n#1 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Broadcasting\\BroadcastEvent->handle()\n#2 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#3 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#4 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#5 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#6 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#7 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#8 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#9 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#10 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#11 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#12 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#13 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Pipeline\\Pipeline->then()\n#14 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(71): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#15 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call()\n#16 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#17 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#18 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(333): Illuminate\\Queue\\Worker->runJob()\n#19 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(139): Illuminate\\Queue\\Worker->runNextJob()\n#20 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(122): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#21 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#22 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#23 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#24 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#25 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#26 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()\n#27 /www/wwwroot/*************/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()\n#28 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()\n#29 /www/wwwroot/*************/vendor/symfony/console/Application.php(1047): Illuminate\\Console\\Command->run()\n#30 /www/wwwroot/*************/vendor/symfony/console/Application.php(316): Symfony\\Component\\Console\\Application->doRunCommand()\n#31 /www/wwwroot/*************/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun()\n#32 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(196): Symfony\\Component\\Console\\Application->run()\n#33 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1203): Illuminate\\Foundation\\Console\\Kernel->handle()\n#34 /www/wwwroot/*************/artisan(13): Illuminate\\Foundation\\Application->handleCommand()\n#35 {main}', '2024-07-31 12:00:47'),
(3, '58185425-05e1-429f-bb51-3f89777641b5', 'database', 'default', '{\"uuid\":\"58185425-05e1-429f-bb51-3f89777641b5\",\"displayName\":\"App\\\\Events\\\\ChatAgentMessage\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\ChatAgentMessage\\\":1:{s:7:\\\"message\\\";a:1:{i:0;a:8:{s:11:\\\"chat_source\\\";s:16:\\\"Y2hhdGJvdC1kZHV0\\\";s:8:\\\"chat_uid\\\";s:4:\\\"MTA=\\\";s:11:\\\"bot_user_id\\\";i:2;s:7:\\\"user_id\\\";i:10;s:4:\\\"text\\\";s:4:\\\"yuyu\\\";s:6:\\\"sender\\\";s:6:\\\"server\\\";s:10:\\\"agent_type\\\";s:12:\\\"chatbot-ddut\\\";s:15:\\\"status_fallback\\\";i:0;}}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"}}', 'Illuminate\\Broadcasting\\BroadcastException: Pusher error: cURL error 28: Connection timed out after 10001 milliseconds (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://3.15.193.83:8001/apps/164294/events?auth_key=gkq017yvprodby4ndzv7&auth_timestamp=1722427264&auth_version=1.0&body_md5=0fea553b088ba43e3fcb63fc4d19ab7e&auth_signature=5c2672497f25a1aef91f5f551082aecef804e797353446d557d98d7e1cffb032. in /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/Broadcasters/PusherBroadcaster.php:164\nStack trace:\n#0 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastEvent.php(92): Illuminate\\Broadcasting\\Broadcasters\\PusherBroadcaster->broadcast()\n#1 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Broadcasting\\BroadcastEvent->handle()\n#2 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#3 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#4 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#5 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#6 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#7 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#8 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#9 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#10 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#11 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#12 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#13 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Pipeline\\Pipeline->then()\n#14 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(71): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#15 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call()\n#16 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#17 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#18 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(333): Illuminate\\Queue\\Worker->runJob()\n#19 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(139): Illuminate\\Queue\\Worker->runNextJob()\n#20 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(122): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#21 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#22 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#23 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#24 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#25 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#26 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()\n#27 /www/wwwroot/*************/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()\n#28 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()\n#29 /www/wwwroot/*************/vendor/symfony/console/Application.php(1047): Illuminate\\Console\\Command->run()\n#30 /www/wwwroot/*************/vendor/symfony/console/Application.php(316): Symfony\\Component\\Console\\Application->doRunCommand()\n#31 /www/wwwroot/*************/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun()\n#32 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(196): Symfony\\Component\\Console\\Application->run()\n#33 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1203): Illuminate\\Foundation\\Console\\Kernel->handle()\n#34 /www/wwwroot/*************/artisan(13): Illuminate\\Foundation\\Application->handleCommand()\n#35 {main}', '2024-07-31 12:01:14'),
(4, '20aaaa52-7605-4b71-bbd7-667f2d84fff5', 'database', 'default', '{\"uuid\":\"20aaaa52-7605-4b71-bbd7-667f2d84fff5\",\"displayName\":\"App\\\\Events\\\\ChatAgentMessage\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\ChatAgentMessage\\\":1:{s:7:\\\"message\\\";a:1:{i:0;a:8:{s:11:\\\"chat_source\\\";s:16:\\\"Y2hhdGJvdC1kZHV0\\\";s:8:\\\"chat_uid\\\";s:4:\\\"MTA=\\\";s:11:\\\"bot_user_id\\\";i:2;s:7:\\\"user_id\\\";i:93;s:4:\\\"text\\\";s:5:\\\"yuyu2\\\";s:6:\\\"sender\\\";s:4:\\\"user\\\";s:10:\\\"agent_type\\\";s:12:\\\"chatbot-ddut\\\";s:15:\\\"status_fallback\\\";i:0;}}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"}}', 'Illuminate\\Broadcasting\\BroadcastException: Pusher error: cURL error 28: Connection timed out after 10000 milliseconds (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://3.15.193.83:8001/apps/164294/events?auth_key=gkq017yvprodby4ndzv7&auth_timestamp=1722427295&auth_version=1.0&body_md5=3dddda8c99d53285d8765441535fbf02&auth_signature=a3bc35092724b229569e93e1e34426e0087f347d92698a6c3bc9a1279cb9d22e. in /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/Broadcasters/PusherBroadcaster.php:164\nStack trace:\n#0 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastEvent.php(92): Illuminate\\Broadcasting\\Broadcasters\\PusherBroadcaster->broadcast()\n#1 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Broadcasting\\BroadcastEvent->handle()\n#2 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#3 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#4 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#5 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#6 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#7 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#8 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#9 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#10 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#11 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#12 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#13 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Pipeline\\Pipeline->then()\n#14 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(71): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#15 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call()\n#16 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#17 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#18 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(333): Illuminate\\Queue\\Worker->runJob()\n#19 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(139): Illuminate\\Queue\\Worker->runNextJob()\n#20 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(122): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#21 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#22 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#23 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#24 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#25 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#26 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()\n#27 /www/wwwroot/*************/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()\n#28 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()\n#29 /www/wwwroot/*************/vendor/symfony/console/Application.php(1047): Illuminate\\Console\\Command->run()\n#30 /www/wwwroot/*************/vendor/symfony/console/Application.php(316): Symfony\\Component\\Console\\Application->doRunCommand()\n#31 /www/wwwroot/*************/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun()\n#32 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(196): Symfony\\Component\\Console\\Application->run()\n#33 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1203): Illuminate\\Foundation\\Console\\Kernel->handle()\n#34 /www/wwwroot/*************/artisan(13): Illuminate\\Foundation\\Application->handleCommand()\n#35 {main}', '2024-07-31 12:01:45'),
(5, '5d3d4a93-eb9f-40a1-88d1-4f5ccba87a9c', 'database', 'default', '{\"uuid\":\"5d3d4a93-eb9f-40a1-88d1-4f5ccba87a9c\",\"displayName\":\"App\\\\Events\\\\NotificationEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:28:\\\"App\\\\Events\\\\NotificationEvent\\\":1:{s:17:\\\"notificationsData\\\";a:1:{s:6:\\\"status\\\";i:1;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"}}', 'Illuminate\\Broadcasting\\BroadcastException: Pusher error: cURL error 28: Connection timed out after 10001 milliseconds (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://3.15.193.83:8001/apps/164294/events?auth_key=gkq017yvprodby4ndzv7&auth_timestamp=1722427309&auth_version=1.0&body_md5=2e2953de47344854baf08e2bddfe747f&auth_signature=fe035113a9fb987729caaa8a09a8e12e5d72244bb8b2e36832f71b8063b5c923. in /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/Broadcasters/PusherBroadcaster.php:164\nStack trace:\n#0 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastEvent.php(92): Illuminate\\Broadcasting\\Broadcasters\\PusherBroadcaster->broadcast()\n#1 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Broadcasting\\BroadcastEvent->handle()\n#2 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#3 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#4 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#5 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#6 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#7 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#8 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#9 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#10 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#11 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#12 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#13 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Pipeline\\Pipeline->then()\n#14 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(71): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#15 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call()\n#16 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#17 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#18 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(333): Illuminate\\Queue\\Worker->runJob()\n#19 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(139): Illuminate\\Queue\\Worker->runNextJob()\n#20 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(122): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#21 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#22 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#23 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#24 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#25 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#26 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()\n#27 /www/wwwroot/*************/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()\n#28 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()\n#29 /www/wwwroot/*************/vendor/symfony/console/Application.php(1047): Illuminate\\Console\\Command->run()\n#30 /www/wwwroot/*************/vendor/symfony/console/Application.php(316): Symfony\\Component\\Console\\Application->doRunCommand()\n#31 /www/wwwroot/*************/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun()\n#32 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(196): Symfony\\Component\\Console\\Application->run()\n#33 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1203): Illuminate\\Foundation\\Console\\Kernel->handle()\n#34 /www/wwwroot/*************/artisan(13): Illuminate\\Foundation\\Application->handleCommand()\n#35 {main}', '2024-07-31 12:01:59'),
(6, 'ae9b3f8e-c0de-471f-bb43-82035842afd0', 'database', 'default', '{\"uuid\":\"ae9b3f8e-c0de-471f-bb43-82035842afd0\",\"displayName\":\"App\\\\Events\\\\ChatAgentMessage\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\ChatAgentMessage\\\":1:{s:7:\\\"message\\\";a:1:{i:0;a:8:{s:11:\\\"chat_source\\\";s:16:\\\"Y2hhdGJvdC1kZHV0\\\";s:8:\\\"chat_uid\\\";s:4:\\\"MTA=\\\";s:11:\\\"bot_user_id\\\";i:2;s:7:\\\"user_id\\\";i:93;s:4:\\\"text\\\";s:5:\\\"cmd 2\\\";s:6:\\\"sender\\\";s:4:\\\"user\\\";s:10:\\\"agent_type\\\";s:12:\\\"chatbot-ddut\\\";s:15:\\\"status_fallback\\\";i:0;}}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"}}', 'Illuminate\\Broadcasting\\BroadcastException: Pusher error: cURL error 7: Failed to connect to 3.12.108.75 port 8001: Connection refused (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://3.12.108.75:8001/apps/242751/events?auth_key=pw5ovyjnmkbnc24siszf&auth_timestamp=1722428133&auth_version=1.0&body_md5=6f65d7e3014f13b80ef399a1f9a4d325&auth_signature=ff7cc74fcd8a94bdb8e598cc5ae423134fccfdcafb7dce6ef18f811e8f334d65. in /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/Broadcasters/PusherBroadcaster.php:164\nStack trace:\n#0 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastEvent.php(92): Illuminate\\Broadcasting\\Broadcasters\\PusherBroadcaster->broadcast()\n#1 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Broadcasting\\BroadcastEvent->handle()\n#2 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#3 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#4 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#5 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#6 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#7 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#8 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#9 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#10 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#11 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#12 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#13 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Pipeline\\Pipeline->then()\n#14 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(71): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#15 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call()\n#16 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#17 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#18 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(333): Illuminate\\Queue\\Worker->runJob()\n#19 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(139): Illuminate\\Queue\\Worker->runNextJob()\n#20 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(122): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#21 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#22 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#23 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#24 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#25 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#26 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()\n#27 /www/wwwroot/*************/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()\n#28 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()\n#29 /www/wwwroot/*************/vendor/symfony/console/Application.php(1047): Illuminate\\Console\\Command->run()\n#30 /www/wwwroot/*************/vendor/symfony/console/Application.php(316): Symfony\\Component\\Console\\Application->doRunCommand()\n#31 /www/wwwroot/*************/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun()\n#32 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(196): Symfony\\Component\\Console\\Application->run()\n#33 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1203): Illuminate\\Foundation\\Console\\Kernel->handle()\n#34 /www/wwwroot/*************/artisan(13): Illuminate\\Foundation\\Application->handleCommand()\n#35 {main}', '2024-07-31 12:15:33'),
(7, '5d75b908-003c-45e4-8ddb-636687c63599', 'database', 'default', '{\"uuid\":\"5d75b908-003c-45e4-8ddb-636687c63599\",\"displayName\":\"App\\\\Events\\\\NotificationEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:28:\\\"App\\\\Events\\\\NotificationEvent\\\":1:{s:17:\\\"notificationsData\\\";a:1:{s:6:\\\"status\\\";i:1;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"}}', 'Illuminate\\Broadcasting\\BroadcastException: Pusher error: cURL error 28: Connection timed out after 10000 milliseconds (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://3.12.108.75:8001/apps/242751/events?auth_key=pw5ovyjnmkbnc24siszf&auth_timestamp=1722430335&auth_version=1.0&body_md5=2e2953de47344854baf08e2bddfe747f&auth_signature=f455e7b2673fdd11c5177678a9e6902f44f3e8617b9e49b7f3c582f48fcef552. in /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/Broadcasters/PusherBroadcaster.php:164\nStack trace:\n#0 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastEvent.php(92): Illuminate\\Broadcasting\\Broadcasters\\PusherBroadcaster->broadcast()\n#1 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Broadcasting\\BroadcastEvent->handle()\n#2 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#3 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#4 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#5 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#6 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#7 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#8 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#9 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#10 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#11 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#12 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#13 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Pipeline\\Pipeline->then()\n#14 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(71): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#15 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call()\n#16 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#17 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#18 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(333): Illuminate\\Queue\\Worker->runJob()\n#19 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(139): Illuminate\\Queue\\Worker->runNextJob()\n#20 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(122): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#21 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#22 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#23 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#24 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#25 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#26 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()\n#27 /www/wwwroot/*************/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()\n#28 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()\n#29 /www/wwwroot/*************/vendor/symfony/console/Application.php(1047): Illuminate\\Console\\Command->run()\n#30 /www/wwwroot/*************/vendor/symfony/console/Application.php(316): Symfony\\Component\\Console\\Application->doRunCommand()\n#31 /www/wwwroot/*************/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun()\n#32 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(196): Symfony\\Component\\Console\\Application->run()\n#33 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1203): Illuminate\\Foundation\\Console\\Kernel->handle()\n#34 /www/wwwroot/*************/artisan(13): Illuminate\\Foundation\\Application->handleCommand()\n#35 {main}', '2024-07-31 12:52:25');
INSERT INTO `failed_jobs` (`id`, `uuid`, `connection`, `queue`, `payload`, `exception`, `failed_at`) VALUES
(8, '979fc44a-77b1-438c-bd1f-970a79b4ce5c', 'database', 'default', '{\"uuid\":\"979fc44a-77b1-438c-bd1f-970a79b4ce5c\",\"displayName\":\"App\\\\Events\\\\NotificationEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:28:\\\"App\\\\Events\\\\NotificationEvent\\\":1:{s:17:\\\"notificationsData\\\";a:1:{s:6:\\\"status\\\";i:1;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"}}', 'Illuminate\\Broadcasting\\BroadcastException: Pusher error: cURL error 28: Connection timed out after 10000 milliseconds (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://3.12.108.75:8001/apps/242751/events?auth_key=pw5ovyjnmkbnc24siszf&auth_timestamp=1722430346&auth_version=1.0&body_md5=2e2953de47344854baf08e2bddfe747f&auth_signature=e0d09a98d0a8f514b430f1aaeb4341b51350c74acc7c474bbd6cf30407f5956a. in /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/Broadcasters/PusherBroadcaster.php:164\nStack trace:\n#0 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastEvent.php(92): Illuminate\\Broadcasting\\Broadcasters\\PusherBroadcaster->broadcast()\n#1 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Broadcasting\\BroadcastEvent->handle()\n#2 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#3 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#4 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#5 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#6 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#7 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#8 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#9 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#10 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#11 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#12 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#13 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Pipeline\\Pipeline->then()\n#14 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(71): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#15 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call()\n#16 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#17 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#18 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(333): Illuminate\\Queue\\Worker->runJob()\n#19 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(139): Illuminate\\Queue\\Worker->runNextJob()\n#20 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(122): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#21 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#22 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#23 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#24 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#25 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#26 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()\n#27 /www/wwwroot/*************/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()\n#28 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()\n#29 /www/wwwroot/*************/vendor/symfony/console/Application.php(1047): Illuminate\\Console\\Command->run()\n#30 /www/wwwroot/*************/vendor/symfony/console/Application.php(316): Symfony\\Component\\Console\\Application->doRunCommand()\n#31 /www/wwwroot/*************/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun()\n#32 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(196): Symfony\\Component\\Console\\Application->run()\n#33 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1203): Illuminate\\Foundation\\Console\\Kernel->handle()\n#34 /www/wwwroot/*************/artisan(13): Illuminate\\Foundation\\Application->handleCommand()\n#35 {main}', '2024-07-31 12:52:36'),
(9, 'dd5da322-3c9b-4839-a135-0912c7dc10bc', 'database', 'default', '{\"uuid\":\"dd5da322-3c9b-4839-a135-0912c7dc10bc\",\"displayName\":\"App\\\\Events\\\\ChatAgentMessage\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\ChatAgentMessage\\\":1:{s:7:\\\"message\\\";a:1:{i:0;a:8:{s:11:\\\"chat_source\\\";s:16:\\\"Y2hhdGJvdC1kZHV0\\\";s:8:\\\"chat_uid\\\";s:4:\\\"MTA=\\\";s:11:\\\"bot_user_id\\\";i:2;s:7:\\\"user_id\\\";i:10;s:4:\\\"text\\\";s:5:\\\"cmd 4\\\";s:6:\\\"sender\\\";s:6:\\\"server\\\";s:10:\\\"agent_type\\\";s:12:\\\"chatbot-ddut\\\";s:15:\\\"status_fallback\\\";i:0;}}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"}}', 'Illuminate\\Broadcasting\\BroadcastException: Pusher error: cURL error 28: Connection timed out after 10001 milliseconds (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://3.12.108.75:8001/apps/242751/events?auth_key=pw5ovyjnmkbnc24siszf&auth_timestamp=1722430356&auth_version=1.0&body_md5=6408906d7a8230ca9b6fe474a82fdcde&auth_signature=43424d0f21417bbd9899788b9fd4df1a2cf16ff57687890fa4dce3bf878a3ba3. in /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/Broadcasters/PusherBroadcaster.php:164\nStack trace:\n#0 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastEvent.php(92): Illuminate\\Broadcasting\\Broadcasters\\PusherBroadcaster->broadcast()\n#1 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Broadcasting\\BroadcastEvent->handle()\n#2 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#3 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#4 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#5 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#6 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#7 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#8 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#9 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#10 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#11 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#12 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#13 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Pipeline\\Pipeline->then()\n#14 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(71): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#15 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call()\n#16 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#17 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#18 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(333): Illuminate\\Queue\\Worker->runJob()\n#19 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(139): Illuminate\\Queue\\Worker->runNextJob()\n#20 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(122): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#21 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#22 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#23 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#24 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#25 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#26 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()\n#27 /www/wwwroot/*************/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()\n#28 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()\n#29 /www/wwwroot/*************/vendor/symfony/console/Application.php(1047): Illuminate\\Console\\Command->run()\n#30 /www/wwwroot/*************/vendor/symfony/console/Application.php(316): Symfony\\Component\\Console\\Application->doRunCommand()\n#31 /www/wwwroot/*************/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun()\n#32 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(196): Symfony\\Component\\Console\\Application->run()\n#33 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1203): Illuminate\\Foundation\\Console\\Kernel->handle()\n#34 /www/wwwroot/*************/artisan(13): Illuminate\\Foundation\\Application->handleCommand()\n#35 {main}', '2024-07-31 12:52:46'),
(10, '96b542db-a981-48eb-956f-7c4996b11b8e', 'database', 'default', '{\"uuid\":\"96b542db-a981-48eb-956f-7c4996b11b8e\",\"displayName\":\"App\\\\Events\\\\NotificationEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:28:\\\"App\\\\Events\\\\NotificationEvent\\\":1:{s:17:\\\"notificationsData\\\";a:1:{s:6:\\\"status\\\";i:1;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"}}', 'Illuminate\\Broadcasting\\BroadcastException: Pusher error: cURL error 7: Failed to connect to 3.147.237.217 port 8001: Connection refused (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://3.147.237.217:8001/apps/242751/events?auth_key=pw5ovyjnmkbnc24siszf&auth_timestamp=1722434557&auth_version=1.0&body_md5=2e2953de47344854baf08e2bddfe747f&auth_signature=5698490ff6b908cb058742973bf504efe4cb716211f4f806b9951c798b927b30. in /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/Broadcasters/PusherBroadcaster.php:164\nStack trace:\n#0 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastEvent.php(92): Illuminate\\Broadcasting\\Broadcasters\\PusherBroadcaster->broadcast()\n#1 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Broadcasting\\BroadcastEvent->handle()\n#2 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#3 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#4 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#5 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#6 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#7 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#8 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#9 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#10 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#11 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#12 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#13 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Pipeline\\Pipeline->then()\n#14 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(71): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#15 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call()\n#16 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#17 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#18 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(333): Illuminate\\Queue\\Worker->runJob()\n#19 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(139): Illuminate\\Queue\\Worker->runNextJob()\n#20 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(122): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#21 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#22 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#23 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#24 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#25 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#26 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()\n#27 /www/wwwroot/*************/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()\n#28 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()\n#29 /www/wwwroot/*************/vendor/symfony/console/Application.php(1047): Illuminate\\Console\\Command->run()\n#30 /www/wwwroot/*************/vendor/symfony/console/Application.php(316): Symfony\\Component\\Console\\Application->doRunCommand()\n#31 /www/wwwroot/*************/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun()\n#32 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(196): Symfony\\Component\\Console\\Application->run()\n#33 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1203): Illuminate\\Foundation\\Console\\Kernel->handle()\n#34 /www/wwwroot/*************/artisan(13): Illuminate\\Foundation\\Application->handleCommand()\n#35 {main}', '2024-07-31 14:02:37'),
(11, '9b63c56d-e16e-4117-9dfd-f7e2a14341fd', 'database', 'default', '{\"uuid\":\"9b63c56d-e16e-4117-9dfd-f7e2a14341fd\",\"displayName\":\"App\\\\Events\\\\NotificationEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:28:\\\"App\\\\Events\\\\NotificationEvent\\\":1:{s:17:\\\"notificationsData\\\";a:1:{s:6:\\\"status\\\";i:1;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"}}', 'Illuminate\\Broadcasting\\BroadcastException: Pusher error: cURL error 28: Connection timed out after 10001 milliseconds (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://3.147.237.217:6001/apps/242751/events?auth_key=pw5ovyjnmkbnc24siszf&auth_timestamp=1722434693&auth_version=1.0&body_md5=2e2953de47344854baf08e2bddfe747f&auth_signature=f498fa2f6786ed3f44b6c41bc3b18e204178ab14aab1cc8798f0af04cd2018db. in /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/Broadcasters/PusherBroadcaster.php:164\nStack trace:\n#0 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastEvent.php(92): Illuminate\\Broadcasting\\Broadcasters\\PusherBroadcaster->broadcast()\n#1 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Broadcasting\\BroadcastEvent->handle()\n#2 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#3 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#4 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#5 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#6 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#7 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#8 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#9 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#10 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#11 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#12 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#13 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Pipeline\\Pipeline->then()\n#14 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(71): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#15 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call()\n#16 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#17 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#18 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(333): Illuminate\\Queue\\Worker->runJob()\n#19 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(139): Illuminate\\Queue\\Worker->runNextJob()\n#20 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(122): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#21 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#22 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#23 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#24 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#25 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#26 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()\n#27 /www/wwwroot/*************/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()\n#28 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()\n#29 /www/wwwroot/*************/vendor/symfony/console/Application.php(1047): Illuminate\\Console\\Command->run()\n#30 /www/wwwroot/*************/vendor/symfony/console/Application.php(316): Symfony\\Component\\Console\\Application->doRunCommand()\n#31 /www/wwwroot/*************/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun()\n#32 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(196): Symfony\\Component\\Console\\Application->run()\n#33 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1203): Illuminate\\Foundation\\Console\\Kernel->handle()\n#34 /www/wwwroot/*************/artisan(13): Illuminate\\Foundation\\Application->handleCommand()\n#35 {main}', '2024-07-31 14:05:03'),
(12, '192766e0-5362-4b2e-a1bb-fdb89b54af29', 'database', 'default', '{\"uuid\":\"192766e0-5362-4b2e-a1bb-fdb89b54af29\",\"displayName\":\"App\\\\Events\\\\NotificationEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:28:\\\"App\\\\Events\\\\NotificationEvent\\\":1:{s:17:\\\"notificationsData\\\";a:1:{s:6:\\\"status\\\";i:1;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"}}', 'Illuminate\\Broadcasting\\BroadcastException: Pusher error: cURL error 28: Connection timed out after 10001 milliseconds (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://3.147.237.217:6001/apps/242751/events?auth_key=pw5ovyjnmkbnc24siszf&auth_timestamp=1722434849&auth_version=1.0&body_md5=2e2953de47344854baf08e2bddfe747f&auth_signature=c37bc933fbc724a99c50ebe09929fd27acb13cacbe421d5d376e4972d7499638. in /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/Broadcasters/PusherBroadcaster.php:164\nStack trace:\n#0 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastEvent.php(92): Illuminate\\Broadcasting\\Broadcasters\\PusherBroadcaster->broadcast()\n#1 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Broadcasting\\BroadcastEvent->handle()\n#2 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#3 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#4 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#5 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#6 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#7 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#8 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#9 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#10 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#11 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#12 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#13 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Pipeline\\Pipeline->then()\n#14 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(71): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#15 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call()\n#16 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#17 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#18 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(333): Illuminate\\Queue\\Worker->runJob()\n#19 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(139): Illuminate\\Queue\\Worker->runNextJob()\n#20 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(122): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#21 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#22 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#23 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#24 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#25 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#26 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()\n#27 /www/wwwroot/*************/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()\n#28 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()\n#29 /www/wwwroot/*************/vendor/symfony/console/Application.php(1047): Illuminate\\Console\\Command->run()\n#30 /www/wwwroot/*************/vendor/symfony/console/Application.php(316): Symfony\\Component\\Console\\Application->doRunCommand()\n#31 /www/wwwroot/*************/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun()\n#32 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(196): Symfony\\Component\\Console\\Application->run()\n#33 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1203): Illuminate\\Foundation\\Console\\Kernel->handle()\n#34 /www/wwwroot/*************/artisan(13): Illuminate\\Foundation\\Application->handleCommand()\n#35 {main}', '2024-07-31 14:07:39'),
(13, 'ab197e3b-a850-4781-b15b-e915379baa4a', 'database', 'default', '{\"uuid\":\"ab197e3b-a850-4781-b15b-e915379baa4a\",\"displayName\":\"App\\\\Events\\\\NotificationEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:28:\\\"App\\\\Events\\\\NotificationEvent\\\":1:{s:17:\\\"notificationsData\\\";a:1:{s:6:\\\"status\\\";i:1;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"}}', 'Illuminate\\Broadcasting\\BroadcastException: Pusher error: cURL error 28: Connection timed out after 10000 milliseconds (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://3.147.237.217:6001/apps/242751/events?auth_key=pw5ovyjnmkbnc24siszf&auth_timestamp=1722434923&auth_version=1.0&body_md5=2e2953de47344854baf08e2bddfe747f&auth_signature=ca094f16bff3b8678940b6c4d2e63e0090d243066fca24080ec01ad3b69fa55e. in /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/Broadcasters/PusherBroadcaster.php:164\nStack trace:\n#0 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastEvent.php(92): Illuminate\\Broadcasting\\Broadcasters\\PusherBroadcaster->broadcast()\n#1 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Broadcasting\\BroadcastEvent->handle()\n#2 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#3 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#4 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#5 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#6 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#7 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#8 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#9 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#10 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#11 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#12 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#13 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Pipeline\\Pipeline->then()\n#14 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(71): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#15 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call()\n#16 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#17 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#18 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(333): Illuminate\\Queue\\Worker->runJob()\n#19 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(139): Illuminate\\Queue\\Worker->runNextJob()\n#20 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(122): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#21 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#22 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#23 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#24 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#25 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#26 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()\n#27 /www/wwwroot/*************/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()\n#28 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()\n#29 /www/wwwroot/*************/vendor/symfony/console/Application.php(1047): Illuminate\\Console\\Command->run()\n#30 /www/wwwroot/*************/vendor/symfony/console/Application.php(316): Symfony\\Component\\Console\\Application->doRunCommand()\n#31 /www/wwwroot/*************/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun()\n#32 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(196): Symfony\\Component\\Console\\Application->run()\n#33 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1203): Illuminate\\Foundation\\Console\\Kernel->handle()\n#34 /www/wwwroot/*************/artisan(13): Illuminate\\Foundation\\Application->handleCommand()\n#35 {main}', '2024-07-31 14:08:53'),
(14, '05f5231d-f8d6-454c-b180-7c66a0c389d1', 'database', 'default', '{\"uuid\":\"05f5231d-f8d6-454c-b180-7c66a0c389d1\",\"displayName\":\"App\\\\Events\\\\NotificationEvent\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:28:\\\"App\\\\Events\\\\NotificationEvent\\\":1:{s:17:\\\"notificationsData\\\";a:1:{s:6:\\\"status\\\";i:1;}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"}}', 'Illuminate\\Broadcasting\\BroadcastException: Pusher error: cURL error 28: Connection timed out after 10001 milliseconds (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://3.147.237.217:6001/apps/242751/events?auth_key=pw5ovyjnmkbnc24siszf&auth_timestamp=1722434991&auth_version=1.0&body_md5=2e2953de47344854baf08e2bddfe747f&auth_signature=6cd9baead50b31cb9c1a48dfda6e07b501cac86f09591b4ade1c6d2a10c07087. in /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/Broadcasters/PusherBroadcaster.php:164\nStack trace:\n#0 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastEvent.php(92): Illuminate\\Broadcasting\\Broadcasters\\PusherBroadcaster->broadcast()\n#1 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Broadcasting\\BroadcastEvent->handle()\n#2 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#3 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#4 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#5 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#6 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#7 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#8 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#9 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#10 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#11 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#12 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#13 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Pipeline\\Pipeline->then()\n#14 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(71): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#15 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call()\n#16 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#17 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#18 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(333): Illuminate\\Queue\\Worker->runJob()\n#19 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(139): Illuminate\\Queue\\Worker->runNextJob()\n#20 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(122): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#21 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#22 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#23 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#24 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#25 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#26 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()\n#27 /www/wwwroot/*************/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()\n#28 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()\n#29 /www/wwwroot/*************/vendor/symfony/console/Application.php(1047): Illuminate\\Console\\Command->run()\n#30 /www/wwwroot/*************/vendor/symfony/console/Application.php(316): Symfony\\Component\\Console\\Application->doRunCommand()\n#31 /www/wwwroot/*************/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun()\n#32 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(196): Symfony\\Component\\Console\\Application->run()\n#33 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1203): Illuminate\\Foundation\\Console\\Kernel->handle()\n#34 /www/wwwroot/*************/artisan(13): Illuminate\\Foundation\\Application->handleCommand()\n#35 {main}', '2024-07-31 14:10:01');
INSERT INTO `failed_jobs` (`id`, `uuid`, `connection`, `queue`, `payload`, `exception`, `failed_at`) VALUES
(15, 'df2e1dbc-f079-4fa5-af42-1c610dc6c8d7', 'database', 'default', '{\"uuid\":\"df2e1dbc-f079-4fa5-af42-1c610dc6c8d7\",\"displayName\":\"App\\\\Events\\\\ChatAgentMessage\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\",\"command\":\"O:38:\\\"Illuminate\\\\Broadcasting\\\\BroadcastEvent\\\":14:{s:5:\\\"event\\\";O:27:\\\"App\\\\Events\\\\ChatAgentMessage\\\":1:{s:7:\\\"message\\\";a:1:{i:0;a:8:{s:11:\\\"chat_source\\\";s:16:\\\"Y2hhdGJvdC1kZHV0\\\";s:8:\\\"chat_uid\\\";s:4:\\\"MTA=\\\";s:11:\\\"bot_user_id\\\";i:2;s:7:\\\"user_id\\\";i:10;s:4:\\\"text\\\";s:6:\\\"cmd 11\\\";s:6:\\\"sender\\\";s:6:\\\"server\\\";s:10:\\\"agent_type\\\";s:12:\\\"chatbot-ddut\\\";s:15:\\\"status_fallback\\\";i:0;}}}s:5:\\\"tries\\\";N;s:7:\\\"timeout\\\";N;s:7:\\\"backoff\\\";N;s:13:\\\"maxExceptions\\\";N;s:10:\\\"connection\\\";N;s:5:\\\"queue\\\";N;s:5:\\\"delay\\\";N;s:11:\\\"afterCommit\\\";N;s:10:\\\"middleware\\\";a:0:{}s:7:\\\"chained\\\";a:0:{}s:15:\\\"chainConnection\\\";N;s:10:\\\"chainQueue\\\";N;s:19:\\\"chainCatchCallbacks\\\";N;}\"}}', 'Illuminate\\Broadcasting\\BroadcastException: Pusher error: cURL error 28: Connection timed out after 10000 milliseconds (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://3.147.237.217:6001/apps/242751/events?auth_key=pw5ovyjnmkbnc24siszf&auth_timestamp=1722435005&auth_version=1.0&body_md5=277d693bb482fd48be2dd5c33c1456af&auth_signature=9ae5a930c2ba3e95d0092c29d5d2430d02924a8f8fca8c45f8d28cff37adec90. in /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/Broadcasters/PusherBroadcaster.php:164\nStack trace:\n#0 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastEvent.php(92): Illuminate\\Broadcasting\\Broadcasters\\PusherBroadcaster->broadcast()\n#1 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Broadcasting\\BroadcastEvent->handle()\n#2 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#3 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#4 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#5 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#6 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#7 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#8 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#9 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#10 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#11 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#12 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#13 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Pipeline\\Pipeline->then()\n#14 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(71): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#15 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call()\n#16 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#17 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#18 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(333): Illuminate\\Queue\\Worker->runJob()\n#19 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(139): Illuminate\\Queue\\Worker->runNextJob()\n#20 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(122): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#21 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#22 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#23 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#24 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#25 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Container/Container.php(690): Illuminate\\Container\\BoundMethod::call()\n#26 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()\n#27 /www/wwwroot/*************/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()\n#28 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()\n#29 /www/wwwroot/*************/vendor/symfony/console/Application.php(1047): Illuminate\\Console\\Command->run()\n#30 /www/wwwroot/*************/vendor/symfony/console/Application.php(316): Symfony\\Component\\Console\\Application->doRunCommand()\n#31 /www/wwwroot/*************/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun()\n#32 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(196): Symfony\\Component\\Console\\Application->run()\n#33 /www/wwwroot/*************/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1203): Illuminate\\Foundation\\Console\\Kernel->handle()\n#34 /www/wwwroot/*************/artisan(13): Illuminate\\Foundation\\Application->handleCommand()\n#35 {main}', '2024-07-31 14:10:15');

-- --------------------------------------------------------

--
-- Table structure for table `group`
--

CREATE TABLE `group` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `queue` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `job_batches`
--

CREATE TABLE `job_batches` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` mediumtext COLLATE utf8mb4_unicode_ci,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `lastmessage`
--

CREATE TABLE `lastmessage` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `groupId` bigint(20) UNSIGNED NOT NULL,
  `messageId` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `message`
--

CREATE TABLE `message` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `senderId` bigint(20) UNSIGNED NOT NULL,
  `groupId` bigint(20) UNSIGNED NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `messages`
--

CREATE TABLE `messages` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(6, '0001_01_01_000000_create_users_table', 1),
(7, '2014_10_12_100000_create_password_reset_tokens_table', 1),
(8, '2014_10_12_100000_create_password_resets_table', 1),
(9, '2019_08_19_000000_create_failed_jobs_table', 1),
(10, '2019_12_14_000001_create_personal_access_tokens_table', 1),
(11, '2023_10_31_054823_create_category_table', 1),
(12, '2023_10_31_055149_create_country_table', 1),
(13, '2023_10_31_055557_create_company_table', 1),
(14, '2023_10_31_060701_create_deals_table', 1),
(15, '2023_10_31_061632_create_complain_table', 1),
(16, '2023_10_31_063016_create_group_table', 1),
(17, '2023_10_31_063126_create_message_table', 1),
(18, '2023_10_31_063342_create_complainmessage_table', 1),
(19, '2023_10_31_063513_create_statushistory_table', 1),
(20, '2023_10_31_063752_create_lastmessage_table', 1),
(21, '2023_10_31_064608_create_person_table', 1),
(22, '2023_11_29_135427_add_lastmsg_to_users_table', 2),
(23, '2024_03_11_072253_create_bots_table', 2),
(24, '0000_00_00_000000_create_websockets_statistics_entries_table', 3),
(25, '2024_03_07_095405_create_agents_table', 3),
(26, '2024_04_08_071444_create_messages_table', 3),
(27, '0001_01_01_000001_create_cache_table', 1),
(28, '0001_01_01_000002_create_jobs_table', 1),
(29, '2024_06_06_133524_create_notifications_table', 4),
(30, '2024_06_12_172500_create_sessiondomain_table', 5),
(31, '2024_07_31_065938_create_contactus_table', 6);

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `receiver_id` int(11) NOT NULL,
  `message` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `route` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(180) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('seen','unseen') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'unseen',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `password_resets`
--

CREATE TABLE `password_resets` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `password_reset_tokens`
--

INSERT INTO `password_reset_tokens` (`email`, `token`, `created_at`) VALUES
('<EMAIL>', '$2y$12$RKAu0MsefIhXyXusbvdQL.xV.qpLWj6/YMyK3M7hzKbMR9r2tN/ye', '2024-05-28 02:48:22'),
('<EMAIL>', '$2y$12$abpQvo6Uwm3vuqbtxTVN6enu1H2kmy.zg2pVBaBChDVIASIq08km6', '2024-07-30 06:35:10'),
('<EMAIL>', '$2y$12$Sh9VqRbUnRSCjZ0vUae6Oe8lz9Fp/.M8frz5HDXGRrWHZqBvcEqla', '2024-07-26 04:21:02');

-- --------------------------------------------------------

--
-- Table structure for table `person`
--

CREATE TABLE `person` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `userId` bigint(20) UNSIGNED NOT NULL,
  `firstName` varchar(180) COLLATE utf8mb4_unicode_ci NOT NULL,
  `lastName` varchar(180) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `person`
--

INSERT INTO `person` (`id`, `userId`, `firstName`, `lastName`, `created_at`, `updated_at`) VALUES
(1, 1, 'John', 'Bruko', '2023-10-31 10:35:15', '2023-10-31 10:35:15'),
(2, 2, 'John', 'Doe', '2023-10-31 10:35:15', '2024-08-12 12:54:26'),
(5, 5, 'jonney', 'sojitra', '2024-02-27 05:32:35', '2024-02-27 07:44:10'),
(6, 6, 'john', 'doe', '2024-02-27 05:37:36', '2024-02-27 05:37:36'),
(10, 10, 'john1', 'doe1', '2024-02-28 06:55:53', '2024-02-28 06:55:53'),
(11, 11, 'john2', 'doe2', '2024-02-28 07:01:51', '2024-02-28 07:01:51'),
(12, 12, 'john', 'doe', '2024-02-29 05:39:19', '2024-02-29 05:39:19'),
(13, 13, 'john', 'doe', '2024-02-29 05:40:32', '2024-02-29 05:40:32'),
(18, 18, 'john', 'doe', '2024-03-01 09:24:14', '2024-03-01 09:24:14'),
(37, 42, 'john', 'doe', '2024-03-07 06:32:29', '2024-03-07 06:32:29'),
(38, 43, 'john', 'doe', '2024-03-11 07:58:12', '2024-03-11 07:58:12'),
(40, 56, 'test2024', '2024tester', '2024-05-06 13:20:22', '2024-05-06 13:39:50'),
(41, 94, 'jenish', 'sondi', '2024-08-12 13:04:22', '2024-08-12 13:04:22'),
(42, 95, 'jenish', 'sondi', '2024-08-12 13:05:11', '2024-08-12 13:05:11'),
(43, 96, 'jenish', 'sondi', '2024-08-12 13:10:22', '2024-08-12 13:10:22');

-- --------------------------------------------------------

--
-- Table structure for table `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `sessiondomain`
--

CREATE TABLE `sessiondomain` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `session_domain` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sessions`
--

INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES
('6UsMvXzvc3jFMqH1nfZb7eTCmad72exzJrviXLtf', NULL, '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YToyOntzOjY6Il90b2tlbiI7czo0MDoibVBFTmVGbksydjBlN1JyNjY2WlhsanduVWdmRllES1B5S2J1TVFDMyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1724320851),
('CMHZ41trGFtvqnaxv6izraU35ypGrTtJVRrXeEwY', NULL, '*************', '', '************************************************************************************************************************************************************************************************************************************************', 1724307080),
('d07B5i2hOXDUzHGozxMmIEmN5ESxGScw6B2a3wyd', 93, '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************', 1724319013),
('d0thahIjRCNQx3SYqj6unbSr9NbDShj6musEHoW7', NULL, '*************', '', '************************************************************************************************************************************************************************************************************************************************', 1724319987),
('hB673eHHES6g64pX8cvjUjl2x6niwwzpVWZKgXXs', NULL, '72.255.231.87', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.103 Safari/537.36', '************************************************************************************************************************************************************************************************************************************************', 1724307882),
('hT13xtbGDueTNq5G04mKHCebwyAUVMDYQDwFLrUm', NULL, '185.242.226.80', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.190 Safari/537.36', '************************************************************************************************************************************************************************************************************************************************', 1724313712),
('J7ZkcR0zuPCbv2CYC1meqaQTZjZPPwYaXPg48M7W', NULL, '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoicEh3Z3IwTXozMVZiOUZ1dnFsN3o5dzFNWkQ4eDBqdFNhejhtclUyTCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDc6Imh0dHA6Ly8xOC4yMjYuMTg3LjI1MS9nbG9iYWwtY2hhdGJvdC1zZXJ2aWNlLzExIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1724313214),
('l1LjFUid7qeWFSQNuiBIUpbJz3I2Ua7gjdLofJeh', NULL, '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiSkJJbzFPbVFRV2kydjlFOEI5aG5OYzdMSG1UbUc1S2luMVc0NG1TSiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MzE6Imh0dHA6Ly8xOC4yMjYuMTg3LjI1MS9jb21wYW5pZXMiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19', 1724304985),
('LIqXG67zxc6OEnCauhHBwfABXSOwLlf8CfVcvzbz', NULL, '************', 'HTTP Banner Detection (https://security.ipip.net)', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiZ2Y5a3dzN3BPYWV1UE9yNU14aGtHb05EaEVhSG5SY2tZQXJIOUFWQiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly8xOC4yMjYuMTg3LjI1MSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1724306049),
('o1qAIXwnLCidC8iSJST3ilJ1QaLH162tnP8bgsec', NULL, '**************', '', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiSnFkMlcxZXdiemZZRnZkSk5ZUUFZQjZramtZZ3dYNVJmdm5ua0VYNiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly8xOC4yMjYuMTg3LjI1MSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1724311208),
('Pe0rBST4Nx8TiC145HLUjOhHjVliUOCGCJCrzdMx', NULL, '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiN1dzdTFiRWZHNGxYTjdzdHgzcFkxcnZ1WUZKcnJ0N3R2N2lHTk8zRSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NTI6Imh0dHA6Ly8xOC4yMjYuMTg3LjI1MS8/WERFQlVHX1NFU1NJT05fU1RBUlQ9cGhwc3Rvcm0iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19', 1724319655),
('pVSz2Z3MmrAZ1gIhrDKbP6A29vMhVzMeHKZKSO4k', NULL, '*************', 'Mozilla/5.0 (compatible; Odin; https://docs.getodin.com/)', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiUW1KaG5KRnE5dENrMldSVGtoM2tjZVhraUwzZDJrdnJucm5ESmpzciI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly8xOC4yMjYuMTg3LjI1MSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1724311210),
('q9F9BVkBpHYsq8pHZC3xq2LMwP4K9Xup8F7JJVHe', NULL, '65.49.1.107', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoid1NCS2pkOU9CMzVQQU9ZM1RTS3paZzYxY3d4YmloeHZEcFNpZGhHQSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly8xOC4yMjYuMTg3LjI1MSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1724316016),
('Sl82frDyBiRkNuCKZUtwV6apSBCWSDv5YyOdiif0', NULL, '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiVmFFTjZNNVVNVlBHSmhHNDRJVGZGY0V2VXdpejRJZHNENE4wTWJFaiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MzE6Imh0dHA6Ly8xOC4yMjYuMTg3LjI1MS9jb21wYW5pZXMiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19', 1724311506),
('ZRtTtA1pYkOB0G2VqbjQjrWxJuM7ixXtlK3rgWOP', NULL, '95.214.55.138', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36 Edg/90.0.818.46', 'YToyOntzOjY6Il90b2tlbiI7czo0MDoiMk1HQUJHaEdmSDF2b2N5dVlxdmZIdVFIYUZaNVR6QTYydHJXVFJEOSI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1724318293);

-- --------------------------------------------------------

--
-- Table structure for table `statushistory`
--

CREATE TABLE `statushistory` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `complainId` bigint(20) UNSIGNED NOT NULL,
  `progressStatus` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `company_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(80) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `profileType` enum('user','company','admin','agent') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'user',
  `verificationStatus` enum('not_verified','verified') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'not_verified',
  `status` enum('active','inactive') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `profilePicture` text COLLATE utf8mb4_unicode_ci,
  `LastMessage` longtext COLLATE utf8mb4_unicode_ci,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `timeStamp` datetime DEFAULT CURRENT_TIMESTAMP,
  `otp` varchar(80) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `parent_id` int(11) NOT NULL DEFAULT '0',
  `vid` int(11) NOT NULL DEFAULT '0' COMMENT 'vendor_id',
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT 'userid',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `company_name`, `phone`, `email`, `email_verified_at`, `password`, `profileType`, `verificationStatus`, `status`, `profilePicture`, `LastMessage`, `remember_token`, `timeStamp`, `otp`, `parent_id`, `vid`, `uid`, `created_at`, `updated_at`) VALUES
(1, 'Admin', NULL, '9888555545', '<EMAIL>', NULL, '$2y$12$t08pctuQ8VkYO4rZwSSZdu2ecVK8fMkM6qsHWb/rNgdf218O9Bu1m', 'admin', 'verified', 'active', 'images/profile/sDwSwRHPsHZvsFjReEX2evQtHadqfY04Gtfnu0dV.jpg', NULL, '0TTkfZnZRr52LJTldQNYB0avNETEknzIGNWin4QRgWZsVg3Id2mLC3JmfFGa', '2024-02-26 22:17:13', NULL, 0, 0, 0, '2023-10-31 10:35:15', '2024-08-07 06:45:50'),
(2, 'John', 'Jonson company', '9888885558', '<EMAIL>', NULL, '$2y$12$M0Zsg3yDT2fdCYpZr246E.6wtZRTzpHDrcdEmSaluesXE4Kc4l3ra', 'company', 'verified', 'active', 'images/profile/6LpyG5ClYGTYRCB8ACqqkSParMadUQzMoBrv3R6w.jpg', 'testing', NULL, '2024-02-26 22:17:13', NULL, 0, 0, 0, '2023-10-31 10:35:15', '2024-08-14 06:28:58'),
(5, 'sdsd', NULL, 'sdsd', '<EMAIL>', NULL, '$2y$12$3HJ7JqW38/4QzPIHf.8nlOXn3udoy2T0ocxfu9i6HAG83gpIPX8uW', 'company', 'verified', 'active', 'images/profile/dY7ml5PoqIFocaPaddEm2UWcqTze22rw1ku4wRG4.png', 'this is last test message', NULL, '2024-02-27 00:00:00', NULL, 0, 0, 0, '2024-02-27 05:32:35', '2024-07-30 01:39:38'),
(6, 'karan', NULL, '0', '<EMAIL>', NULL, '$2y$12$nXu/g0agfLC8rlhaB0XN/uHCoxvatkDscbGSZ9dE2octw5V.WtYRm', 'user', 'verified', 'active', '', '[{\"cid\":\"36\",\"text\":\"Hi\",\"date\":\"2024-03-07T00:39:42.670Z\"},{\"cid\":37,\"text\":\"Heelo\",\"date\":\"2024-04-10T06:41:17.341Z\"}]', NULL, '2024-02-27 00:00:00', '', 0, 0, 0, '2024-02-27 05:37:36', '2024-08-22 09:55:30'),
(8, 'A', NULL, '0', '<EMAIL>', NULL, '$2y$10$GG2fzs4hPQlDW1mqPzgX6OHR021eAilJBZF4QHt3tgxd6rHgD1RYi', 'user', 'verified', 'active', '', NULL, NULL, '2024-02-28 00:00:00', NULL, 0, 0, 0, '2024-02-28 06:18:15', '2024-07-03 07:59:44'),
(9, 'A', NULL, '0', '<EMAIL>', NULL, '$2y$10$GG2fzs4hPQlDW1mqPzgX6OHR021eAilJBZF4QHt3tgxd6rHgD1RYi', 'user', 'verified', 'active', '', NULL, NULL, '2024-02-28 00:00:00', NULL, 0, 0, 0, '2024-02-28 06:22:05', '2024-07-03 07:59:46'),
(10, 'karan', NULL, '0', '<EMAIL>', NULL, '$2y$12$8DRO8Oy6GOtGrLBmy9g8jeIa2Kk/Jd23wwzqrE1.UsIhyz6ktN69u', 'user', 'verified', 'active', '', NULL, NULL, '2024-02-28 00:00:00', NULL, 0, 0, 0, '2024-02-28 06:55:53', '2024-07-03 07:59:49'),
(11, 'karan2', NULL, '0', '<EMAIL>', NULL, '$2y$12$aOu1pvyQm6MoKn/Bp8lgv.M5hkj1ccnVGHId4zQ6oqzZlmVLMI/oa', 'user', 'verified', 'active', '', NULL, NULL, '2024-02-28 00:00:00', NULL, 0, 0, 0, '2024-02-28 07:01:51', '2024-07-03 07:59:51'),
(12, 'john', NULL, '0', '<EMAIL>', NULL, '$2y$12$Ep3LkXaimLckF71PGi6cDe1tDmTRvUwQSi5qO0Pg3TQ97DkRrUQLO', 'user', 'verified', 'active', '', NULL, NULL, '2024-02-29 00:00:00', NULL, 0, 0, 0, '2024-02-29 05:39:19', '2024-07-03 07:13:38'),
(13, 'john', NULL, '0', '<EMAIL>', NULL, '$2y$10$GG2fzs4hPQlDW1mqPzgX6OHR021eAilJBZF4QHt3tgxd6rHgD1RYi', 'user', 'verified', 'active', '', NULL, NULL, '2024-02-29 00:00:00', NULL, 0, 0, 0, '2024-02-29 05:40:32', '2024-04-10 06:57:28'),
(14, '', NULL, '0', '<EMAIL>', NULL, '$2y$10$GG2fzs4hPQlDW1mqPzgX6OHR021eAilJBZF4QHt3tgxd6rHgD1RYi', 'user', 'verified', 'active', '', NULL, NULL, '2024-03-01 00:00:00', NULL, 0, 0, 0, '2024-03-01 08:46:46', '2024-04-10 06:57:31'),
(16, '', NULL, '0', '<EMAIL>', NULL, '$2y$10$GG2fzs4hPQlDW1mqPzgX6OHR021eAilJBZF4QHt3tgxd6rHgD1RYi', 'user', 'verified', 'active', '', NULL, NULL, '2024-03-01 00:00:00', NULL, 0, 0, 0, '2024-03-01 08:49:20', '2024-04-10 06:57:33'),
(18, 'john', NULL, '0', '<EMAIL>', NULL, '$2y$10$GG2fzs4hPQlDW1mqPzgX6OHR021eAilJBZF4QHt3tgxd6rHgD1RYi', 'user', 'verified', 'active', '', NULL, NULL, '2024-03-01 00:00:00', NULL, 0, 0, 0, '2024-03-01 09:24:14', '2024-04-10 06:57:36'),
(19, '', NULL, '0', '<EMAIL>', NULL, '$2y$10$GG2fzs4hPQlDW1mqPzgX6OHR021eAilJBZF4QHt3tgxd6rHgD1RYi', 'user', 'verified', 'active', '', NULL, NULL, '2024-03-01 00:00:00', '132514', 0, 0, 0, '2024-03-01 10:51:03', '2024-04-10 06:57:38'),
(37, 'Iveta bruco', NULL, '123', '<EMAIL>', NULL, '$2y$10$GG2fzs4hPQlDW1mqPzgX6OHR021eAilJBZF4QHt3tgxd6rHgD1RYi', 'company', 'verified', 'active', 'https://firebasestorage.googleapis.com/v0/b/digitalk-26324.appspot.com/o/9f82359c-f7a7-4f48-ac18-c8293bf0b0c6.png?alt=media&token=1b99e344-fc7f-4d11-b090-a1d887dc5a70', '[{\"cid\":\"44\",\"text\":\"Hello\",\"date\":\"2024-03-15T02:45:35.352Z\"},{\"cid\":\"6\",\"text\":\"Heelo\",\"date\":\"2024-04-10T06:41:17.341Z\"}]', NULL, '2024-03-06 00:00:00', '660715', 0, 0, 0, '2024-03-06 06:28:22', '2024-04-10 06:40:59'),
(38, 'test', NULL, '9888885555', '<EMAIL>', NULL, '$2y$10$GG2fzs4hPQlDW1mqPzgX6OHR021eAilJBZF4QHt3tgxd6rHgD1RYi', 'user', 'not_verified', 'active', 'https://firebasestorage.googleapis.com/v0/b/digitalk-26324.appspot.com/o/153A24CD-899F-4618-B552-A4E9994F293A.jpg?alt=media&token=1c251e42-bb85-4346-8651-3866ec500ff6', NULL, NULL, '2024-03-06 00:00:00', '', 0, 0, 0, '2024-03-06 06:34:04', '2024-04-10 06:57:40'),
(41, 'test', NULL, '9888885555', '<EMAIL>', NULL, '$2y$10$GG2fzs4hPQlDW1mqPzgX6OHR021eAilJBZF4QHt3tgxd6rHgD1RYi', 'company', 'not_verified', 'active', 'https://firebasestorage.googleapis.com/v0/b/digitalk-26324.appspot.com/o/153A24CD-899F-4618-B552-A4E9994F293A.jpg?alt=media&token=1c251e42-bb85-4346-8651-3866ec500ff6', NULL, NULL, '2024-03-07 00:00:00', '', 0, 0, 0, '2024-03-07 05:11:50', '2024-04-10 06:57:42'),
(42, 'test', NULL, '9888885555', '<EMAIL>', NULL, '$2y$10$GG2fzs4hPQlDW1mqPzgX6OHR021eAilJBZF4QHt3tgxd6rHgD1RYi', 'company', 'not_verified', 'active', 'https://firebasestorage.googleapis.com/v0/b/digitalk-26324.appspot.com/o/153A24CD-899F-4618-B552-A4E9994F293A.jpg?alt=media&token=1c251e42-bb85-4346-8651-3866ec500ff6', NULL, NULL, '2024-03-07 00:00:00', '', 0, 0, 0, '2024-03-07 06:32:29', '2024-04-10 06:57:45'),
(43, 'test', NULL, '9888885555', '<EMAIL>', NULL, '$2y$10$g7qRDxZJkMa53bhASmG4FeoRZODfTR0xxMyM20yqOFH/3Q3URSCyu', 'company', 'not_verified', 'active', 'https://firebasestorage.googleapis.com/v0/b/digitalk-26324.appspot.com/o/153A24CD-899F-4618-B552-A4E9994F293A.jpg?alt=media&token=1c251e42-bb85-4346-8651-3866ec500ff6', NULL, NULL, '2024-03-11 00:00:00', '', 0, 0, 0, '2024-03-11 07:58:12', '2024-03-11 08:07:58'),
(44, 'Dev', NULL, '', '<EMAIL>', NULL, '$2y$10$dGHYunauS0Hb.JTnnlAUJ.9GW76sWMaGRpfWoj6RXPSdN83OkoI9q', 'company', 'not_verified', 'active', 'https://firebasestorage.googleapis.com/v0/b/digitalk-26324.appspot.com/o/36d00857-62b6-4130-aa97-e8dda4ccc703.jpeg?alt=media&token=c9fa7bfe-d57a-403b-8fc4-f634b663629d', '[{\"cid\":\"44\",\"text\":\"Hello\",\"date\":\"2024-03-12T18:39:25.725Z\"},{\"cid\":\"5\",\"text\":\"How are you ?\",\"date\":\"2024-03-12T18:59:33.793Z\"},{\"cid\":\"37\",\"text\":\"Hello\",\"date\":\"2024-03-15T02:45:35.352Z\"},{\"cid\":\"49\",\"text\":\"Hello\",\"date\":\"2024-03-15T02:46:02.028Z\"},{\"cid\":\"51\",\"text\":\"Hello farhan\",\"date\":\"2024-03-16T17:45:33.496Z\"}]', NULL, '2024-03-12 00:00:00', '', 0, 0, 0, '2024-03-12 18:17:07', '2024-03-16 17:45:34'),
(45, 'johndoe', NULL, '34434343434', '<EMAIL>', NULL, '$2y$12$o6meEtTV835DYFSFgyqvR.Olp78lAASw.KGOD5hEr.4FMlK7ds6Bm', 'company', 'not_verified', 'active', NULL, '[{\"cid\":\"49\",\"text\":\"Hello john\",\"date\":\"2024-03-15T02:44:03.814Z\"}]', NULL, '2024-03-13 06:25:31', NULL, 0, 0, 0, '2024-03-13 20:25:31', '2024-03-27 14:02:01'),
(48, 'john', NULL, '', '<EMAIL>', NULL, '$2y$12$XwuutnwjxX4E7M9ApWrX5.kXKqYyW6lVsWPBu6Clh3r.CvvpG6KOu', 'user', 'verified', 'active', 'https://firebasestorage.googleapis.com/v0/b/digitalk-26324.appspot.com/o/8c599384-5e78-406c-a209-9e17c281eb71.jpeg?alt=media&token=be902c5e-aab2-4ccd-81d8-62f7929fa2db', NULL, NULL, '2024-03-13 06:32:25', '', 0, 2, 3221, '2024-03-13 20:32:25', '2024-06-18 12:46:45'),
(49, 'Farhan Devs', NULL, '', '<EMAIL>', NULL, '$2y$10$4CEWjQjXmQe.OhaQ//yTfuFh/cvSdXerm4rqbKpXM0sYcqO.T/ZSq', 'company', 'not_verified', 'active', 'https://firebasestorage.googleapis.com/v0/b/digitalk-26324.appspot.com/o/8c599384-5e78-406c-a209-9e17c281eb71.jpeg?alt=media&token=be902c5e-aab2-4ccd-81d8-62f7929fa2db', '[{\"cid\":\"44\",\"text\":\"Hello\",\"date\":\"2024-03-15T02:46:02.028Z\"},{\"cid\":\"45\",\"text\":\"Hello john\",\"date\":\"2024-03-15T02:44:03.814Z\"},{\"cid\":\"51\",\"text\":\"Hi\",\"date\":\"2024-03-16T17:46:42.702Z\"}]', NULL, '2024-03-15 00:00:00', '', 0, 0, 0, '2024-03-15 02:41:05', '2024-03-16 17:46:40'),
(50, 'pogila8450', NULL, '', '<EMAIL>', NULL, '$2y$10$Zg8PDqBGnsXVjlRrjTrAkOXHhpE.B4103PLYxVNC1HcBU7BXWPSlG', 'company', 'not_verified', 'active', 'https://firebasestorage.googleapis.com/v0/b/digitalk-26324.appspot.com/o/98e2aa94-bae5-43ef-bd29-b79c75f68926.jpeg?alt=media&token=050abdad-b615-47e8-9343-3dc034960c8b', '[{\"cid\":null,\"text\":\"Hello\",\"date\":\"2024-03-16T17:28:16.467Z\"},{\"cid\":\"51\",\"text\":\"Hello here\",\"date\":\"2024-03-16T17:30:11.660Z\"}]', NULL, '2024-03-16 00:00:00', '792414', 0, 0, 0, '2024-03-16 05:43:06', '2024-03-16 17:30:20'),
(52, 'Test User', NULL, '', '<EMAIL>', NULL, '$2y$12$wkpl3WfQpHJeR2EB9zZVp.R5vigIfAF2tXA6kThBLPN2M1Dvj.rI.', 'user', 'not_verified', 'active', NULL, NULL, NULL, '2024-03-25 23:29:27', NULL, 0, 0, 0, '2024-03-26 13:29:27', '2024-03-26 13:29:27'),
(53, 'roca disoja', NULL, '8555552255', '<EMAIL>', NULL, '$2y$12$EFQEd9WY/6YfmC0ncUJXA.Ni1mwFSbrmjyu.a4jBjwQcDPUc5YajG', 'user', 'not_verified', 'active', NULL, NULL, NULL, '2024-04-01 05:36:56', NULL, 0, 0, 0, '2024-04-01 19:36:56', '2024-04-01 19:36:56'),
(56, 'test2024', NULL, '0', '<EMAIL>', NULL, '$2y$12$u4SkmLQUiO7dzhhnGpo3/u49EMQALjtXfNcXuy7fGxnEGbF2DSWay', 'user', 'not_verified', 'active', 'images/profile/I7qQf1rxls3k2vrixuGY0KkkdaAI0sFLqagGTfce.png', NULL, NULL, '2024-05-05 23:20:22', '', 0, 0, 0, '2024-05-06 13:20:22', '2024-05-06 14:05:02'),
(57, 'test', NULL, '9888555525', '<EMAIL>', NULL, '$2y$12$L.vnTyDoYcVeBl7ioKgEHO.tEgMMbG9Nilk6w6nWHtiXsyCqByQ3C', 'company', 'not_verified', 'active', NULL, NULL, NULL, '2024-05-06 02:39:39', NULL, 0, 0, 0, '2024-05-06 16:39:39', '2024-05-06 16:41:01'),
(93, 'Agent 1', NULL, '4444555556', '<EMAIL>', NULL, '$2y$12$7de6ABVB3RZM3KN8besM3.d/1OGN9BVTJ8x7TBx.GLLWjv4DXAcEi', 'agent', 'verified', 'active', NULL, NULL, NULL, '2024-05-28 13:20:14', NULL, 2, 0, 0, '2024-05-28 02:20:14', '2024-05-28 09:32:53'),
(94, 'jenish', NULL, '', '<EMAIL>', NULL, '$2y$12$XK0mOAin2aZS/Brxfkco5OkFYuw2PyJ.8vRI1hNKj5INAwSJVTCOG', 'user', 'not_verified', 'active', '', NULL, NULL, '2024-08-12 13:04:22', '', 0, 0, 0, '2024-08-12 13:04:22', '2024-08-12 13:04:22'),
(95, 'jenish', NULL, '', '<EMAIL>', NULL, '$2y$12$e/F/Zc2C8Hm12jHYNg3/Ye.OrUvtjEX1hyvbKfRLCrPg7boFlia0C', 'user', 'not_verified', 'active', '', NULL, NULL, '2024-08-12 13:05:11', '', 0, 0, 0, '2024-08-12 13:05:11', '2024-08-12 13:05:11'),
(96, 'jenish', NULL, '', '<EMAIL>', NULL, '$2y$12$ex2ATmo086wkvSSO2Ui72OXuIY5phfa79PC0kXdPFxzzXddkRGXK2', 'user', 'not_verified', 'active', '', NULL, NULL, '2024-08-12 13:10:22', '', 0, 0, 0, '2024-08-12 13:10:22', '2024-08-12 13:10:22'),
(97, 'testing', NULL, '8555555558', '<EMAIL>', NULL, '$2y$12$W88R7G5scFKgUtjq7MkF.Om52eQJ/BRMmP97WJL3.nw498vhvEmDa', 'company', 'not_verified', 'active', 'images/profile/PcyEaTb3bWmP2vosH4EILff4D8xSUAyaLhf5Gqcb.png', NULL, NULL, '2024-08-14 08:22:27', NULL, 0, 0, 0, '2024-08-14 08:22:27', '2024-08-14 08:29:29');

-- --------------------------------------------------------

--
-- Table structure for table `websockets_statistics_entries`
--

CREATE TABLE `websockets_statistics_entries` (
  `id` int(10) UNSIGNED NOT NULL,
  `app_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `peak_connection_count` int(11) NOT NULL,
  `websocket_message_count` int(11) NOT NULL,
  `api_message_count` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `botconversation`
--
ALTER TABLE `botconversation`
  ADD PRIMARY KEY (`id`),
  ADD KEY `botconversation_user_id_foreign` (`user_id`),
  ADD KEY `botconversation_bot_id_foreign` (`bot_id`);

--
-- Indexes for table `bots`
--
ALTER TABLE `bots`
  ADD PRIMARY KEY (`id`),
  ADD KEY `bots_user_id_foreign` (`user_id`);

--
-- Indexes for table `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `category`
--
ALTER TABLE `category`
  ADD PRIMARY KEY (`catId`);

--
-- Indexes for table `company`
--
ALTER TABLE `company`
  ADD PRIMARY KEY (`id`),
  ADD KEY `company_user_id_foreign` (`userId`),
  ADD KEY `company_catid_foreign` (`catId`),
  ADD KEY `company_countryid_foreign` (`countryId`);

--
-- Indexes for table `complain`
--
ALTER TABLE `complain`
  ADD PRIMARY KEY (`id`),
  ADD KEY `complain_companyid_foreign` (`companyId`),
  ADD KEY `complain_assignedtouserid_foreign` (`assignedToUserId`);

--
-- Indexes for table `complainmessage`
--
ALTER TABLE `complainmessage`
  ADD PRIMARY KEY (`id`),
  ADD KEY `complainmessage_complainid_foreign` (`complainId`),
  ADD KEY `complainmessage_senderid_foreign` (`senderId`);

--
-- Indexes for table `contactus`
--
ALTER TABLE `contactus`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `country`
--
ALTER TABLE `country`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `deals`
--
ALTER TABLE `deals`
  ADD PRIMARY KEY (`id`),
  ADD KEY `deals_companyid_foreign` (`companyId`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `group`
--
ALTER TABLE `group`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indexes for table `job_batches`
--
ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `lastmessage`
--
ALTER TABLE `lastmessage`
  ADD PRIMARY KEY (`id`),
  ADD KEY `lastmessage_groupid_foreign` (`groupId`),
  ADD KEY `lastmessage_messageid_foreign` (`messageId`);

--
-- Indexes for table `message`
--
ALTER TABLE `message`
  ADD PRIMARY KEY (`id`),
  ADD KEY `message_senderid_foreign` (`senderId`),
  ADD KEY `message_groupid_foreign` (`groupId`);

--
-- Indexes for table `messages`
--
ALTER TABLE `messages`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notifications_user_id_foreign` (`user_id`);

--
-- Indexes for table `password_resets`
--
ALTER TABLE `password_resets`
  ADD KEY `password_resets_email_index` (`email`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `person`
--
ALTER TABLE `person`
  ADD PRIMARY KEY (`id`),
  ADD KEY `person_userid_foreign` (`userId`);

--
-- Indexes for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indexes for table `sessiondomain`
--
ALTER TABLE `sessiondomain`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indexes for table `statushistory`
--
ALTER TABLE `statushistory`
  ADD PRIMARY KEY (`id`),
  ADD KEY `statushistory_complainid_foreign` (`complainId`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

--
-- Indexes for table `websockets_statistics_entries`
--
ALTER TABLE `websockets_statistics_entries`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `botconversation`
--
ALTER TABLE `botconversation`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `bots`
--
ALTER TABLE `bots`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `category`
--
ALTER TABLE `category`
  MODIFY `catId` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `company`
--
ALTER TABLE `company`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `complain`
--
ALTER TABLE `complain`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT for table `complainmessage`
--
ALTER TABLE `complainmessage`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `contactus`
--
ALTER TABLE `contactus`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `country`
--
ALTER TABLE `country`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=240;

--
-- AUTO_INCREMENT for table `deals`
--
ALTER TABLE `deals`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `group`
--
ALTER TABLE `group`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=55;

--
-- AUTO_INCREMENT for table `lastmessage`
--
ALTER TABLE `lastmessage`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `message`
--
ALTER TABLE `message`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `messages`
--
ALTER TABLE `messages`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `person`
--
ALTER TABLE `person`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=44;

--
-- AUTO_INCREMENT for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `sessiondomain`
--
ALTER TABLE `sessiondomain`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `statushistory`
--
ALTER TABLE `statushistory`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=98;

--
-- AUTO_INCREMENT for table `websockets_statistics_entries`
--
ALTER TABLE `websockets_statistics_entries`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `bots`
--
ALTER TABLE `bots`
  ADD CONSTRAINT `bots_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `company`
--
ALTER TABLE `company`
  ADD CONSTRAINT `company_catid_foreign` FOREIGN KEY (`catId`) REFERENCES `category` (`catId`),
  ADD CONSTRAINT `company_countryid_foreign` FOREIGN KEY (`countryId`) REFERENCES `country` (`id`),
  ADD CONSTRAINT `company_user_id_foreign` FOREIGN KEY (`userId`) REFERENCES `users` (`id`);

--
-- Constraints for table `complain`
--
ALTER TABLE `complain`
  ADD CONSTRAINT `complain_assignedtouserid_foreign` FOREIGN KEY (`assignedToUserId`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `complain_companyid_foreign` FOREIGN KEY (`companyId`) REFERENCES `company` (`id`);

--
-- Constraints for table `complainmessage`
--
ALTER TABLE `complainmessage`
  ADD CONSTRAINT `complainmessage_complainid_foreign` FOREIGN KEY (`complainId`) REFERENCES `complain` (`id`),
  ADD CONSTRAINT `complainmessage_senderid_foreign` FOREIGN KEY (`senderId`) REFERENCES `users` (`id`);

--
-- Constraints for table `deals`
--
ALTER TABLE `deals`
  ADD CONSTRAINT `deals_companyid_foreign` FOREIGN KEY (`companyId`) REFERENCES `company` (`id`);

--
-- Constraints for table `lastmessage`
--
ALTER TABLE `lastmessage`
  ADD CONSTRAINT `lastmessage_groupid_foreign` FOREIGN KEY (`groupId`) REFERENCES `group` (`id`),
  ADD CONSTRAINT `lastmessage_messageid_foreign` FOREIGN KEY (`messageId`) REFERENCES `message` (`id`);

--
-- Constraints for table `message`
--
ALTER TABLE `message`
  ADD CONSTRAINT `message_groupid_foreign` FOREIGN KEY (`groupId`) REFERENCES `group` (`id`),
  ADD CONSTRAINT `message_senderid_foreign` FOREIGN KEY (`senderId`) REFERENCES `users` (`id`);

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `person`
--
ALTER TABLE `person`
  ADD CONSTRAINT `person_userid_foreign` FOREIGN KEY (`userId`) REFERENCES `users` (`id`);

--
-- Constraints for table `statushistory`
--
ALTER TABLE `statushistory`
  ADD CONSTRAINT `statushistory_complainid_foreign` FOREIGN KEY (`complainId`) REFERENCES `complain` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
