import React, { useState } from "react";
import DashboardLayout from "@/Layouts/DashboardLayout";
import { Input } from "@/Components/FormElements";
import Button from "@/Components/Button";
import { Card, CardContent, PageHeader } from "@/Components/UI";
import { useForm } from "@inertiajs/react";
import toast from "react-hot-toast";

export default function Edit({ complaint, companies = [], users = [] }) {
    const [searchUser, setSearchUser] = useState("");

    const { data, setData, put, processing, errors } = useForm({
        title: complaint.complainTitle || complaint.title || "",
        description: complaint.complainDetail || complaint.description || "",
        priority: complaint.priority?.toString() || "0",
        company_id: complaint.companyId || complaint.company_id || "",
        assigned_user_id:
            complaint.assignedToUserId || complaint.assigned_user_id || "",
        status: complaint.progressStatus || complaint.status || "new",
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        put(route("complains.update", complaint.cmp_id || complaint.id), {
            onSuccess: () => {
                toast.success("Complaint updated successfully.");
            },
            onError: () => {
                toast.error("Failed to update complaint.");
            },
        });
    };

    const filteredUsers = users.filter(
        (user) =>
            user.name.toLowerCase().includes(searchUser.toLowerCase()) ||
            user.email.toLowerCase().includes(searchUser.toLowerCase())
    );

    return (
        <DashboardLayout title="Edit Complaint">
            <title>Edit Complaint</title>

            <div className="space-y-6">
                <PageHeader
                    title="Edit Complaint"
                    subtitle="Update complaint details and status"
                />

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Left Column - Complaint Form */}
                    <div className="lg:col-span-2">
                        <Card>
                            <CardContent>
                                <form
                                    onSubmit={handleSubmit}
                                    className="space-y-8"
                                >
                                    {/* Complaint Details Section */}
                                    <div className="space-y-6">
                                        <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                                            Complaint Details
                                        </h3>

                                        <div className="space-y-6">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Complaint Title *
                                                </label>
                                                <Input
                                                    name="title"
                                                    value={data.title}
                                                    onChange={(e) =>
                                                        setData(
                                                            "title",
                                                            e.target.value
                                                        )
                                                    }
                                                    placeholder="Enter complaint title"
                                                    className="w-full"
                                                    error={errors.title}
                                                />
                                                {errors.title && (
                                                    <p className="text-red-500 text-sm mt-1">
                                                        {errors.title}
                                                    </p>
                                                )}
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Company *
                                                </label>
                                                <select
                                                    value={data.company_id}
                                                    onChange={(e) =>
                                                        setData(
                                                            "company_id",
                                                            e.target.value
                                                        )
                                                    }
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-cf-primary focus:border-transparent"
                                                >
                                                    <option value="">
                                                        Select a company
                                                    </option>
                                                    {companies.map(
                                                        (company) => (
                                                            <option
                                                                key={company.id}
                                                                value={
                                                                    company.id
                                                                }
                                                            >
                                                                {
                                                                    company.companyName
                                                                }
                                                            </option>
                                                        )
                                                    )}
                                                </select>
                                                {errors.company_id && (
                                                    <p className="text-red-500 text-sm mt-1">
                                                        {errors.company_id}
                                                    </p>
                                                )}
                                            </div>

                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                                        Priority Level *
                                                    </label>
                                                    <div className="grid grid-cols-3 gap-2">
                                                        {[
                                                            {
                                                                value: "0",
                                                                label: "Low",
                                                            },
                                                            {
                                                                value: "1",
                                                                label: "Medium",
                                                            },
                                                            {
                                                                value: "2",
                                                                label: "High",
                                                            },
                                                        ].map((level) => (
                                                            <button
                                                                type="button"
                                                                key={
                                                                    level.value
                                                                }
                                                                className={`px-4 py-3 rounded-lg border text-sm font-medium transition-all ${
                                                                    data.priority ===
                                                                    level.value
                                                                        ? "bg-cf-primary text-white border-cf-primary shadow-sm"
                                                                        : "bg-white border-gray-300 text-gray-700 hover:border-cf-primary-300 hover:bg-cf-primary-50"
                                                                }`}
                                                                onClick={() =>
                                                                    setData(
                                                                        "priority",
                                                                        level.value
                                                                    )
                                                                }
                                                            >
                                                                {level.label}
                                                            </button>
                                                        ))}
                                                    </div>
                                                    {errors.priority && (
                                                        <p className="text-red-500 text-sm mt-1">
                                                            {errors.priority}
                                                        </p>
                                                    )}
                                                </div>

                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                                        Status *
                                                    </label>
                                                    <select
                                                        value={data.status}
                                                        onChange={(e) =>
                                                            setData(
                                                                "status",
                                                                e.target.value
                                                            )
                                                        }
                                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-cf-primary focus:border-transparent"
                                                    >
                                                        <option value="new">
                                                            New
                                                        </option>
                                                        <option value="inprogress">
                                                            In Progress
                                                        </option>
                                                        <option value="need_user_input">
                                                            Need User Input
                                                        </option>
                                                        <option value="resolved">
                                                            Resolved
                                                        </option>
                                                    </select>
                                                    {errors.status && (
                                                        <p className="text-red-500 text-sm mt-1">
                                                            {errors.status}
                                                        </p>
                                                    )}
                                                </div>
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Description *
                                                </label>
                                                <Input
                                                    name="description"
                                                    value={data.description}
                                                    onChange={(e) =>
                                                        setData(
                                                            "description",
                                                            e.target.value
                                                        )
                                                    }
                                                    as="textarea"
                                                    rows={6}
                                                    placeholder="Describe the complaint in detail..."
                                                    className="w-full"
                                                    error={errors.description}
                                                />
                                                {errors.description && (
                                                    <p className="text-red-500 text-sm mt-1">
                                                        {errors.description}
                                                    </p>
                                                )}
                                            </div>
                                        </div>
                                    </div>

                                    {/* Action Buttons */}
                                    <div className="flex items-center justify-end gap-4 pt-6 border-t border-gray-200">
                                        <Button
                                            type="button"
                                            variant="dark"
                                            onClick={() =>
                                                window.history.back()
                                            }
                                        >
                                            Cancel
                                        </Button>
                                        <Button
                                            type="submit"
                                            variant="default"
                                            disabled={
                                                processing ||
                                                !data.assigned_user_id
                                            }
                                        >
                                            {processing
                                                ? "Updating..."
                                                : "Update Complaint"}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Right Column - Assign to User Section */}
                    <div className="lg:col-span-1">
                        <Card>
                            <CardContent>
                                <div className="space-y-6">
                                    <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                                        Assign to User
                                    </h3>

                                    {/* Search Users */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Search Users
                                        </label>
                                        <Input
                                            value={searchUser}
                                            onChange={(e) =>
                                                setSearchUser(e.target.value)
                                            }
                                            placeholder="Search by name or email..."
                                            className="w-full"
                                        />
                                    </div>

                                    {/* User Selection */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Select User *
                                        </label>
                                        <div className="max-h-48 overflow-y-auto border border-gray-300 rounded-md">
                                            {filteredUsers.length > 0 ? (
                                                filteredUsers.map((user) => (
                                                    <div
                                                        key={user.id}
                                                        className={`p-3 border-b border-gray-200 cursor-pointer hover:bg-gray-50 ${
                                                            data.assigned_user_id ==
                                                            user.id
                                                                ? "bg-cf-primary-50 border-cf-primary"
                                                                : ""
                                                        }`}
                                                        onClick={() =>
                                                            setData(
                                                                "assigned_user_id",
                                                                user.id
                                                            )
                                                        }
                                                    >
                                                        <div className="font-medium text-gray-900">
                                                            {user.name}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            {user.email}
                                                        </div>
                                                    </div>
                                                ))
                                            ) : (
                                                <div className="p-3 text-gray-500 text-center">
                                                    No users found
                                                </div>
                                            )}
                                        </div>
                                        {errors.assigned_user_id && (
                                            <p className="text-red-500 text-sm mt-1">
                                                {errors.assigned_user_id}
                                            </p>
                                        )}
                                    </div>

                                    {/* Selected User Display */}
                                    {data.assigned_user_id && (
                                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                            <div className="text-sm font-medium text-blue-800">
                                                Currently Assigned:
                                            </div>
                                            <div className="text-sm text-blue-700">
                                                {users.find(
                                                    (u) =>
                                                        u.id ==
                                                        data.assigned_user_id
                                                )?.name || "Unknown User"}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    );
}
