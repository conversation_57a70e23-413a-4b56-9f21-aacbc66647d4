<script setup>
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import { ref, computed } from 'vue';
import { useForm, usePage } from '@inertiajs/vue3';
import { RefreshCw, Clipboard, Check } from 'lucide-vue-next';

const { oauthDetail } = usePage().props;
var baseurl = window.location.origin;

const generateRandomString = () => {
    return [...Array(40)].map(() => Math.random().toString(36).charAt(2)).join('');
};

const form = useForm({
    name: oauthDetail?.name || '',
    client_id: oauthDetail?.client_id || generateRandomString(),
    client_secret: oauthDetail?.client_secret || generateRandomString(),
    redirect_uri: oauthDetail?.redirect_uri || '',
});

const copied = ref(false);

const generateClientId = () => {
    form.client_id = generateRandomString();
};

const generateClientSecret = () => {
    form.client_secret = generateRandomString();
};

const oauthScript = computed(() => {
    return `<button data-oauth-login
    data-client-id="${form.client_id}"
    data-client-secret="${form.client_secret}"
    data-redirect-uri="${form.redirect_uri}"
    data-auth-url="${baseurl}/oauth/login"
    data-token-url="${baseurl}/api/oauth/token"
    data-popup-options="width=500,height=600"
    data-user-id=""
    data-icon="${baseurl}/favicon.ico">
    Login with Chatfil
</button>
<script src="${baseurl}/oauth-login.js"><\/script>`;
});

// Only show the script and copy button if all fields are filled
const isScriptVisible = computed(() => {
    return (
        form.client_id.trim() !== '' &&
        form.client_secret.trim() !== '' &&
        form.redirect_uri.trim() !== ''
    );
});

const copyToClipboard = async () => {
    try {
        await navigator.clipboard.writeText(oauthScript.value);
        copied.value = true;
        setTimeout(() => copied.value = false, 2000);
    } catch (err) {
        console.error('Failed to copy:', err);
    }
};
</script>

<template>
    <section>
        <header>
            <h2 class="font-medium text-gray-900 text-lg">OAuth Details</h2>
            <p class="mt-1 text-gray-600 text-sm">
                Save your OAuth details to configure login with Chatfil.
            </p>
        </header>

        <form @submit.prevent="form.post(route('company.oauth.update'))" class="space-y-6 mt-6">
            <!-- <div>
                <InputLabel for="oauthname" value="Name" />
                <TextInput id="oauthname" type="text" class="block mt-1 w-full" placeholder="Name" v-model="form.name" required />
                <InputError class="mt-2" :message="form.errors.name" />
            </div> -->

            <div>
                <InputLabel for="oauthclient_id" value="Client ID" />
                <div class="flex items-center gap-2">
                    <TextInput id="oauthclient_id" type="text" class="block mt-1 w-full" placeholder="Client ID" v-model="form.client_id" required />
                    <button type="button" @click="generateClientId" class="p-2 bg-gray-200 rounded-md hover:bg-gray-300">
                        <RefreshCw class="w-5 h-5 text-gray-600" />
                    </button>
                </div>
                <InputError class="mt-2" :message="form.errors.client_id" />
            </div>

            <div>
                <InputLabel for="oauthclient_secret" value="Secret ID" />
                <div class="flex items-center gap-2">
                    <TextInput id="oauthclient_secret" type="text" class="block mt-1 w-full" placeholder="Secret ID" v-model="form.client_secret" required />
                    <button type="button" @click="generateClientSecret" class="p-2 bg-gray-200 rounded-md hover:bg-gray-300">
                        <RefreshCw class="w-5 h-5 text-gray-600" />
                    </button>
                </div>
                <InputError class="mt-2" :message="form.errors.client_secret" />
            </div>

            <div>
                <InputLabel for="oauthredirect_uri" value="Redirect URL" />
                <TextInput id="oauthredirect_uri" type="text" class="block mt-1 w-full" placeholder="Redirect URL" v-model="form.redirect_uri" required />
                <InputError class="mt-2" :message="form.errors.redirect_uri" />
            </div>

            <div class="flex items-center gap-4">
                <PrimaryButton :disabled="form.processing">Save</PrimaryButton>
            </div>
        </form>

        <div v-if="isScriptVisible" class="mt-6">
            <h3 class="font-medium text-gray-900 text-lg">OAuth Login Script</h3>
            <p class="mt-1 text-gray-600 text-sm">Copy and use the following script for OAuth login:</p>
            <div class="relative">
            <pre class="bg-gray-100 p-4 pr-2 mt-2 rounded-md text-sm overflow-auto relative">{{ oauthScript }}</pre>
            <button
                @click="copyToClipboard"
                class="absolute top-2 right-2 px-2 py-1 bg-gray-200 text-gray-700 text-xs rounded-md hover:bg-gray-300 transition"
            >
                <template v-if="copied">Copied</template>
                <Clipboard v-else class="w-4 h-4" />
            </button>
    </div>
        </div>
    </section>
</template>