<!-- eslint-disable no-unused-vars -->
<script setup>
import { onMounted } from 'vue';
import { Link, router } from '@inertiajs/vue3';

const props = defineProps(['deals', 'searchField']);
let displayedDeals = props.deals;
let searchField = props.searchField;
var baseurl = window.location.origin;

const formatDate = (dateString) => {
  const date = new Date(dateString);
  const formattedDate = `${date.getFullYear()}-${('0' + (date.getMonth() + 1)).slice(-2)}-${('0' + date.getDate()).slice(-2)} ${('0' + date.getHours()).slice(-2)}:${('0' + date.getMinutes()).slice(-2)}`;
  return formattedDate;
}



const searchCompanydeals = (event) => {

  const searchTerm = event.target.value;
  if (!searchTerm) {
    let url = baseurl + '/deals';
    router.visit(url);
    return;
  }

  fetch(`/search-deals?search=${searchTerm}`)
    .then(response => response.json())
    .then(data => {
      displayedDeals.data = data.deals;
      document.getElementsByClassName('paginationdefault')[0].style.display = 'none';
    })
    .catch(error => {
      console.error(error);
    });


};

onMounted(() => {
  //const inputField = document.getElementById('fieldAutofocus');
  //inputField.focus();
});

const getFacebookShareLink = (dealId) => {
  const url = encodeURIComponent(`${baseurl}/deal/${dealId}`);
  return `https://www.facebook.com/sharer/sharer.php?u=${url}`;
};

const getWhatsAppShareLink = (dealId) => {
  const url = encodeURIComponent(`${baseurl}/deal/${dealId}`);
  return `https://api.whatsapp.com/send?text=${url}`;
};

const getTwitterShareLink = (dealId) => {
  const url = encodeURIComponent(`${baseurl}/deal/${dealId}`);
  return `https://twitter.com/intent/tweet?url=${url}`;
};

</script>

<template>

  <div v-if="displayedDeals.data.length > 0" class="relative xl:mx-auto">
    <!-- Search box -->
    <div class="text-center">
      <input type="text" placeholder="Search deals..." v-model="searchField" id="fieldAutofocus"
        @keyup="searchCompanydeals"
        class="border-gray-200 focus:border-cfp-500 mt-3 mb-8 px-4 py-3 border rounded-md focus:ring-cfp-500/50 w-full sm:w-4/12">
    </div>

    <!-- Grid -->
    <div v-if="displayedDeals.data.length > 0" class="gap-8 lg:gap-8 grid grid-cols-1 lg:grid-cols-1">
      <div v-for="(deal, index) in displayedDeals.data" :key="index"
        class="relative border-gray-300 border rounded-md overflow-hidden">

        <div class="block sm:flex items-start">
          <!-- Deal image -->


          <img v-if="deal.deal_file" :src="baseurl + '/storage/' + deal.deal_file" :alt="deal.dealTitle"
            class="mr-6 w-full sm:w-1/6 h-auto sm:h-46 object-cover" />
          <img v-else-if="deal.profilePicture && deal.profilePicture.startsWith('http')" :src="deal.profilePicture"
            :alt="deal.dealTitle" class="mr-6 w-full sm:w-1/6 h-auto sm:h-46 object-cover" />
          <img v-else-if="deal.profilePicture" :src="baseurl + '/storage/' + deal.profilePicture" :alt="deal.dealTitle"
            class="mr-6 w-full sm:w-1/6 h-auto sm:h-46 object-cover" />
          <img v-else src="@/assets/images/user/user-profile.jpeg" :alt="deal.dealTitle"
            class="mr-6 w-full sm:w-1/6 h-auto sm:h-46 object-cover" />

          <!-- Card -->
          <div class="relative z-10 items-center p-4 text-left align-middle self-center">
            <div class="mb-2">
              <a :href="`${deal.deal_link}`" class="mb-2 font-semibold text-2xl text-slate-800 hover:text-cfp-500"
                target="_blank">{{
                  deal.dealTitle
                }}</a>
            </div>
            <div class="py-1 text-slate-800 text-sm">{{ deal.dealContent }}</div>
            <div class="py-1 text-slate-800 text-sm"> <span class="text-slate-800">Date:</span> {{
              formatDate(deal.dealStartOn) }} <span class="italic">to</span> {{ formatDate(deal.dealExpiresOn) }}</div>

          </div>
          <!-- End Card -->
        </div>
        <!-- Social Button Start  -->
        <div
          class="max-md:relative right-0 bottom-0 absolute flex max-md:justify-center space-x-4 mt-3 max-md:mt-0 max-md:mb-4">
          <!-- Facebook -->
          <a :href="getFacebookShareLink(deal.id)" target="_blank"
            class="bg-gray-50 hover:bg-gray-200 shadow-sm px-2 py-2 rounded-md text-gray-400 hover:text-cfp-500 duration-500 cursor-pointer">
            <svg width="18px" height="18px" viewBox="-5 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink">
              <title>facebook [#176]</title>
              <desc>Created with Sketch.</desc>
              <defs>
              </defs>
              <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <g id="Dribbble-Light-Preview" transform="translate(-385.000000, -7399.000000)" fill="#000000">
                  <g id="icons" transform="translate(56.000000, 160.000000)">
                    <path
                      d="M335.821282,7259 L335.821282,7250 L338.553693,7250 L339,7246 L335.821282,7246 L335.821282,7244.052 C335.821282,7243.022 335.847593,7242 337.286884,7242 L338.744689,7242 L338.744689,7239.14 C338.744689,7239.097 337.492497,7239 336.225687,7239 C333.580004,7239 331.923407,7240.657 331.923407,7243.7 L331.923407,7246 L329,7246 L329,7250 L331.923407,7250 L331.923407,7259 L335.821282,7259 Z"
                      id="facebook-[#176]">
                    </path>
                  </g>
                </g>
              </g>
            </svg>
          </a>
          <!-- WhatsApp -->
          <a :href="getWhatsAppShareLink(deal.id)" target="_blank"
            class="bg-gray-50 hover:bg-gray-200 shadow-sm px-2 py-2 rounded-md text-gray-400 hover:text-cfp-500 duration-500 cursor-pointer">
            <svg fill="#000000" width="18px" height="18px" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M11.42 9.49c-.19-.09-1.1-.54-1.27-.61s-.29-.09-.42.1-.48.6-.59.73-.21.14-.4 0a5.13 5.13 0 0 1-1.49-.92 5.25 5.25 0 0 1-1-1.29c-.11-.18 0-.28.08-.38s.18-.21.28-.32a1.39 1.39 0 0 0 .18-.31.38.38 0 0 0 0-.33c0-.09-.42-1-.58-1.37s-.3-.32-.41-.32h-.4a.72.72 0 0 0-.5.23 2.1 2.1 0 0 0-.65 1.55A3.59 3.59 0 0 0 5 8.2 8.32 8.32 0 0 0 8.19 11c.44.19.78.3 1.05.39a2.53 2.53 0 0 0 1.17.07 1.93 1.93 0 0 0 1.26-.88 1.67 1.67 0 0 0 .11-.88c-.05-.07-.17-.12-.36-.21z" />
              <path
                d="M13.29 2.68A7.36 7.36 0 0 0 8 .5a7.44 7.44 0 0 0-6.41 11.15l-1 3.85 3.94-1a7.4 7.4 0 0 0 3.55.9H8a7.44 7.44 0 0 0 5.29-12.72zM8 14.12a6.12 6.12 0 0 1-3.15-.87l-.22-.13-2.34.61.62-2.28-.14-.23a6.18 6.18 0 0 1 9.6-7.65 6.12 6.12 0 0 1 1.81 4.37A6.19 6.19 0 0 1 8 14.12z" />
            </svg>
          </a>
          <!-- Twitter -->
          <a :href="getTwitterShareLink(deal.id)" target="_blank"
            class="bg-gray-50 hover:bg-gray-200 shadow-sm px-2 py-2 rounded-md text-gray-400 hover:text-cfp-500 duration-500 cursor-pointer">
            <svg width="18px" height="18px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd"
                d="M19.7828 3.91825C20.1313 3.83565 20.3743 3.75444 20.5734 3.66915C20.8524 3.54961 21.0837 3.40641 21.4492 3.16524C21.7563 2.96255 22.1499 2.9449 22.4739 3.11928C22.7979 3.29366 23 3.6319 23 3.99986C23 5.08079 22.8653 5.96673 22.5535 6.7464C22.2911 7.40221 21.9225 7.93487 21.4816 8.41968C21.2954 11.7828 20.3219 14.4239 18.8336 16.4248C17.291 18.4987 15.2386 19.8268 13.0751 20.5706C10.9179 21.3121 8.63863 21.4778 6.5967 21.2267C4.56816 20.9773 2.69304 20.3057 1.38605 19.2892C1.02813 19.0108 0.902313 18.5264 1.07951 18.109C1.25671 17.6916 1.69256 17.4457 2.14144 17.5099C3.42741 17.6936 4.6653 17.4012 5.6832 16.9832C5.48282 16.8742 5.29389 16.7562 5.11828 16.6346C4.19075 15.9925 3.4424 15.1208 3.10557 14.4471C2.96618 14.1684 2.96474 13.8405 3.10168 13.5606C3.17232 13.4161 3.27562 13.293 3.40104 13.1991C2.04677 12.0814 1.49999 10.5355 1.49999 9.49986C1.49999 9.19192 1.64187 8.90115 1.88459 8.71165C1.98665 8.63197 2.10175 8.57392 2.22308 8.53896C2.12174 8.24222 2.0431 7.94241 1.98316 7.65216C1.71739 6.3653 1.74098 4.91284 2.02985 3.75733C2.1287 3.36191 2.45764 3.06606 2.86129 3.00952C3.26493 2.95299 3.6625 3.14709 3.86618 3.50014C4.94369 5.36782 6.93116 6.50943 8.78086 7.18568C9.6505 7.50362 10.4559 7.70622 11.0596 7.83078C11.1899 6.61019 11.5307 5.6036 12.0538 4.80411C12.7439 3.74932 13.7064 3.12525 14.74 2.84698C16.5227 2.36708 18.5008 2.91382 19.7828 3.91825ZM10.7484 9.80845C10.0633 9.67087 9.12171 9.43976 8.09412 9.06408C6.7369 8.56789 5.16088 7.79418 3.84072 6.59571C3.86435 6.81625 3.89789 7.03492 3.94183 7.24766C4.16308 8.31899 4.5742 8.91899 4.94721 9.10549C5.40342 9.3336 5.61484 9.8685 5.43787 10.3469C5.19827 10.9946 4.56809 11.0477 3.99551 10.9046C4.45603 11.595 5.28377 12.2834 6.66439 12.5135C7.14057 12.5929 7.49208 13.0011 7.49986 13.4838C7.50765 13.9665 7.16949 14.3858 6.69611 14.4805L5.82565 14.6546C5.95881 14.7703 6.103 14.8838 6.2567 14.9902C6.95362 15.4727 7.65336 15.6808 8.25746 15.5298C8.70991 15.4167 9.18047 15.6313 9.39163 16.0472C9.60278 16.463 9.49846 16.9696 9.14018 17.2681C8.49626 17.8041 7.74425 18.2342 6.99057 18.5911C6.63675 18.7587 6.24134 18.9241 5.8119 19.0697C6.14218 19.1402 6.48586 19.198 6.84078 19.2417C8.61136 19.4594 10.5821 19.3126 12.4249 18.6792C14.2614 18.0479 15.9589 16.9385 17.2289 15.2312C18.497 13.5262 19.382 11.1667 19.5007 7.96291C19.51 7.71067 19.6144 7.47129 19.7929 7.29281C20.2425 6.84316 20.6141 6.32777 20.7969 5.7143C20.477 5.81403 20.1168 5.90035 19.6878 5.98237C19.3623 6.04459 19.0272 5.94156 18.7929 5.70727C18.0284 4.94274 16.5164 4.43998 15.2599 4.77822C14.6686 4.93741 14.1311 5.28203 13.7274 5.89906C13.3153 6.52904 13 7.51045 13 8.9999C13 9.28288 12.8801 9.5526 12.6701 9.74221C12.1721 10.1917 11.334 9.92603 10.7484 9.80845Z"
                fill="#0F0F0F" />
            </svg>
          </a>
        </div>
        <!-- End Social Button   -->
        <!-- Status badge -->
        <div v-if="deal.deal_status == 0"
          class="top-0 right-0 absolute bg-cfp-500 px-2 py-1 rounded-tr-md rounded-bl-md text-sm text-white">Pending
        </div>
        <div v-if="deal.deal_status == 1"
          class="top-0 right-0 absolute bg-cfp px-2 py-1 rounded-tr-md rounded-bl-md text-sm text-white">Start
        </div>
        <div v-if="deal.deal_status == 2"
          class="top-0 right-0 absolute bg-red-500 px-2 py-1 rounded-tr-md rounded-bl-md text-sm text-white">End</div>
      </div>
    </div>
    <!-- End Grid -->
    <!-- Pagination -->
    <div v-if="displayedDeals.links.length > 1" class="justify-center grid w-full paginationdefault">
      <div class="dt-table-pagi">
        <div class="row-start-2 w-full text-center">
          <div class="pagination-links">
            <ul class="flex justify-center">
              <li v-for="page in displayedDeals.links" :key="page.url">
                <button @click="$inertia.visit(page.url)" :disabled="!page.url"
                  :class="{ 'bg-gray-100': page.active, 'border-gray-300 hover:bg-gray-50 focus:outline-none focus:bg-gray-100    focus:text-slate-800  active:text-slate-800': !page.active }"
                  class="mr-2 px-3 py-1 border rounded-md" v-html="page.label"></button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-else>
    <h1>No deals found</h1>
  </div>
</template>

<style scoped>
/* Add any custom styling here */
</style>
