<?php

namespace App\Http\Controllers;

use App\Models\Company;
use App\Models\Complaint;
use App\Models\Deals;
use Illuminate\Http\Request;
use Inertia\Inertia;

class FinanceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {

        $companyIds = Company::pluck('id')->toArray();
        $TotalDeals = Deals::whereIn('companyId', $companyIds)->count();

        $resolvedComplains = Complaint::where('complainStatus', 'close')->whereIn('companyId', $companyIds)->count();

        $totalComplains = Complaint::whereIn('companyId', $companyIds)->count();

        $allCompanies = Company::join('category', 'category.catId', '=', 'company.catId')
            ->join('country', 'country.id', '=', 'company.countryId')
            ->join('users', 'users.id', '=', 'company.userId')
            ->where('users.profileType', 'company')
            ->select('company.*', 'country.name', 'category.catName', 'users.profilePicture')
            ->paginate(env('PAGE_LIMIT'));

        return Inertia::render('Finance', [
            'TotalDeals'        => $TotalDeals,
            'resolvedComplains' => $resolvedComplains,
            'totalComplains'    => $totalComplains,
            'allCompanies'      => $allCompanies,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
