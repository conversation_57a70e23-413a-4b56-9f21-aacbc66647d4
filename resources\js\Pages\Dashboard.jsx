import React from 'react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { Card, CardContent, PageHeader } from '@/Components/UI';

const stats = [
    {
        label: 'Users Contacted',
        value: 11,
        icon: (
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-lg">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2m5-8a3 3 0 11-6 0 3 3 0 016 0zm6 2a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
            </div>
        ),
        trend: '+11',
        trendColor: 'text-green-600',
        change: 'from last month',
    },
    {
        label: 'Total Complaints',
        value: 53,
        icon: (
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-red-500 to-red-600 text-white shadow-lg">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
            </div>
        ),
        trend: '+53',
        trendColor: 'text-red-600',
        change: 'total received',
    },
    {
        label: 'Resolved Issues',
        value: 14,
        icon: (
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-green-500 to-green-600 text-white shadow-lg">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
        ),
        trend: '+14',
        trendColor: 'text-green-600',
        change: 'this month',
    },
    {
        label: 'Active Deals',
        value: 1,
        icon: (
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 text-white shadow-lg">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
        ),
        trend: '+1',
        trendColor: 'text-purple-600',
        change: 'currently active',
    },
];

export default function Dashboard() {
    return (
        <DashboardLayout title="Dashboard">
            <title>Dashboard</title>

            <div className="space-y-8">
                {/* Welcome Section */}
                <div className="bg-gradient-to-r from-cf-primary-600 to-blue-600 rounded-2xl p-8 text-white relative overflow-hidden">
                    <div className="absolute inset-0 opacity-10">
                        <img
                            src="https://images.unsplash.com/photo-1551434678-e076c223a692?w=800&h=400&fit=crop&crop=center"
                            alt="Dashboard analytics"
                            className="w-full h-full object-cover"
                        />
                    </div>
                    <div className="relative grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                        <div>
                            <h1 className="text-3xl md:text-4xl font-bold mb-4">
                                Welcome back! 👋
                            </h1>
                            <p className="text-xl opacity-90 mb-6">
                                Here's what's happening with your ChatHi account today.
                            </p>
                            <div className="flex flex-wrap gap-4">
                                <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2">
                                    <div className="text-sm opacity-80">Active Users</div>
                                    <div className="text-2xl font-bold">2,847</div>
                                </div>
                                <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2">
                                    <div className="text-sm opacity-80">Messages Today</div>
                                    <div className="text-2xl font-bold">1,234</div>
                                </div>
                                <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2">
                                    <div className="text-sm opacity-80">Response Rate</div>
                                    <div className="text-2xl font-bold">98.5%</div>
                                </div>
                            </div>
                        </div>
                        <div className="hidden lg:block">
                            <div className="relative">
                                <img
                                    src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop&crop=center"
                                    alt="Analytics dashboard"
                                    className="w-full h-64 object-cover rounded-xl shadow-lg"
                                />
                                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl"></div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Page Header */}
                <PageHeader
                    title="Dashboard Overview"
                    subtitle="Monitor your system performance and key metrics"
                />

                {/* Stats Grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                    {stats.map((stat, i) => (
                        <Card key={i} className="hover:shadow-lg transition-all duration-200 border-0 shadow-md">
                            <CardContent className="p-6">
                                <div className="flex items-start justify-between">
                                    <div className="flex-1">
                                        <div className="flex items-center gap-4 mb-4">
                                            {stat.icon}
                                            <div>
                                                <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                                                <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                                            </div>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span className="text-xs text-gray-500">{stat.change}</span>
                                            <div className={`flex items-center gap-1 text-sm font-semibold ${stat.trendColor}`}>
                                                {stat.trend}
                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" d="M5 10l7-7 7 7" />
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* Additional Dashboard Content */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card>
                        <CardContent className="p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                            <div className="space-y-3">
                                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <span className="text-sm text-gray-700">New complaint resolved</span>
                                    <span className="text-xs text-gray-500 ml-auto">2 min ago</span>
                                </div>
                                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                    <span className="text-sm text-gray-700">User contacted via chat</span>
                                    <span className="text-xs text-gray-500 ml-auto">5 min ago</span>
                                </div>
                                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                    <span className="text-sm text-gray-700">New deal created</span>
                                    <span className="text-xs text-gray-500 ml-auto">10 min ago</span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                            <div className="grid grid-cols-2 gap-3">
                                <button className="p-4 bg-cf-primary-50 hover:bg-cf-primary-100 rounded-lg transition-colors text-left">
                                    <div className="text-cf-primary-600 font-medium text-sm">View Complaints</div>
                                    <div className="text-xs text-gray-600 mt-1">Manage customer issues</div>
                                </button>
                                <button className="p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors text-left">
                                    <div className="text-blue-600 font-medium text-sm">Start Chat</div>
                                    <div className="text-xs text-gray-600 mt-1">Connect with users</div>
                                </button>
                                <button className="p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors text-left">
                                    <div className="text-green-600 font-medium text-sm">Create Deal</div>
                                    <div className="text-xs text-gray-600 mt-1">Add new offers</div>
                                </button>
                                <button className="p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors text-left">
                                    <div className="text-purple-600 font-medium text-sm">Manage APIs</div>
                                    <div className="text-xs text-gray-600 mt-1">Configure integrations</div>
                                </button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </DashboardLayout>
    );
}