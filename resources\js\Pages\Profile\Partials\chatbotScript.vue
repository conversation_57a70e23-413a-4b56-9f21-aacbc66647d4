<script setup>
import { ref, computed } from 'vue';
import { useForm, usePage } from '@inertiajs/vue3';
import { Clipboard } from 'lucide-vue-next';

const { companyDetail, userDetail } = usePage().props;

let message = 'hi';
let companyuiD = userDetail.id;
let botUrl = import.meta.env.VITE_BOTBASE_URL+'?userId=0&companyId='+companyuiD+'&text='+message;
const form = useForm({
    name: userDetail.name,
    email: userDetail.email,
    phone: userDetail.phone,
    websiteUrl: (companyDetail) ? companyDetail.websiteUrl : '', // Added the second expression ''
    city: (companyDetail) ? companyDetail.city : '',
    state: (companyDetail) ? companyDetail.state : '',
    zipCode: (companyDetail) ? companyDetail.zipCode : '',
    countryId: (companyDetail) ? companyDetail.countryId : '',
    chatSupport: (companyDetail) ? companyDetail.chatSupport : '',
    catId: (companyDetail) ? companyDetail.catId : '',
    companyAdd: (companyDetail) ? companyDetail.companyAdd : '',
    company_name: userDetail?.company_name || '',
    chatbaseurl: botUrl,
    profilePicture: null,
    companyProfile: userDetail.profilePicture
});

var baseurl = window.location.origin;

const copied = ref(false);

const oauthScript = computed(() => {
    return `<script src="${baseurl}/dialogflow-chat.js"><\/script>
    <script>document.addEventListener("DOMContentLoaded", function() {
        const chat = new DialogflowChat({
            baseUrl: '${baseurl}',
            userID: '${userDetail.id}',
            companyName: '${form.company_name}',
            primaryColor: '#337E81',
            botAvatar: '/images/chat-icon.png',
            endpoint: '${form.chatbaseurl}'
        });
});<\/script>`;
});

// Only show the script and copy button if all fields are filled
const isScriptVisible = computed(() => {
    return (
        form.company_name.trim() !== '' &&
        form.chatbaseurl.trim() !== ''
    );
});

const copyToClipboard = async () => {
    try {
        await navigator.clipboard.writeText(oauthScript.value);
        copied.value = true;
        setTimeout(() => copied.value = false, 2000);
    } catch (err) {
        console.error('Failed to copy:', err);
    }
};
</script>

<template>
    <section>

        <div v-if="isScriptVisible" class="mt-6">
            <h3 class="font-medium text-gray-900 text-lg">Chatbot Script</h3>
            <p class="mt-1 text-gray-600 text-sm">Copy and use the following script for Chatbot Messanger:</p>
            <div class="relative">
                <pre class="bg-gray-100 p-4 pr-2 mt-2 rounded-md text-sm overflow-auto relative">{{ oauthScript }}</pre>
                <button
                    @click="copyToClipboard"
                    class="absolute top-2 right-2 px-2 py-1 bg-gray-200 text-gray-700 text-xs rounded-md hover:bg-gray-300 transition"
                >
                    <template v-if="copied">Copied</template>
                    <Clipboard v-else class="w-4 h-4" />
                </button>
            </div>
        </div>
    </section>
</template>
