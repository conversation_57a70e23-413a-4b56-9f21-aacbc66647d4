<?php

namespace App\Http\Controllers\Front;

use App\Http\Controllers\Controller;
use App\Mail\TestEmail;
use App\Models\Category;
use App\Models\Company;
use App\Models\Country;
use App\Models\Deals;
use App\Models\Person;
use App\Models\SessionDomain;
use Illuminate\Support\Str;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use PHPOpenSourceSaver\JWTAuth\Exceptions\JWTException;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Inertia\Inertia;

class FrontHomecontroller extends Controller
{
    // public function getSessionDomain()
    // {

    //     if (isset($_REQUEST['domainid'])) {
    //     } else {
    //         return response()->json(['status' => false], 200, [], JSO<PERSON>_PRETTY_PRINT);
    //     }
    //     $domainid        = $_REQUEST['domainid'];
    //     $domainStatusQry = SessionDomain::where('session_domain', $domainid)->first();
    //     if ($domainStatusQry) {

    //         $userToken = $domainStatusQry->token_id;

    //         $useremail     = '';
    //         $userid        = '';
    //         $token         = $userToken;
    //         $domain_status = 0;

    //         $curl = curl_init();

    //         curl_setopt_array($curl, [
    //             CURLOPT_URL            => 'http://bots.eruditetechnology.com/api/get-user-token-detail/'.$userToken,
    //             CURLOPT_RETURNTRANSFER => true,
    //             CURLOPT_ENCODING       => '',
    //             CURLOPT_MAXREDIRS      => 10,
    //             CURLOPT_TIMEOUT        => 0,
    //             CURLOPT_FOLLOWLOCATION => true,
    //             CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
    //             CURLOPT_CUSTOMREQUEST  => 'GET',
    //         ]);

    //         $response = curl_exec($curl);

    //         curl_close($curl);

    //         // Decode the JSON response into a PHP array
    //         $data = json_decode($response, true);

    //         // Check if decoding was successful
    //         if (json_last_error() === JSON_ERROR_NONE) {
    //             // Access the data
    //             $useremail = $data['useremail'];
    //             $userName  = $data['name'];
    //             $userPhone = $data['phone'];
    //             $userid    = $data['userid'];
    //             $token     = $data['token'];

    //             if ($token == $userToken) {
    //                 $domainStatusQry->status = 1;
    //                 $domainStatusQry->save();
    //                 $domain_status = 1;

    //                 $checkUser = User::where('email', $useremail)->first();
    //                 if ($checkUser) {
    //                     $saveUser                     = User::find($checkUser->id);
    //                     $saveUser->name               = $userName;
    //                     $saveUser->phone              = $userPhone;
    //                     $saveUser->verificationStatus = 'verified';
    //                     $saveUser->vendor_domain      = $domainid;
    //                     $saveUser->save();
    //                     Auth::login($saveUser);

    //                 } else {

    //                     $randomPass                   = rand(1111, 2222);
    //                     $saveUser                     = new User;
    //                     $saveUser->name               = $userName;
    //                     $saveUser->email              = $useremail;
    //                     $saveUser->phone              = $userPhone;
    //                     $saveUser->verificationStatus = 'verified';
    //                     $saveUser->vendor_domain      = $domainid;
    //                     $saveUser->password           = Hash::make($randomPass);
    //                     $saveUser->save();
    //                     Auth::login($saveUser);
    //                 }
    //             }

    //         }

    //         return response()->json([
    //             'status'        => true,
    //             'domain_status' => $domain_status,
    //             'useremail'     => $useremail,
    //             'userName'      => $userName,
    //             'userPhone'     => $userPhone,
    //             'userid'        => $userid,
    //             'userToken'     => $token,
    //         ], 200, [], JSON_PRETTY_PRINT);

    //     } else {
    //         return response()->json(['status' => false], 200, [], JSON_PRETTY_PRINT);

    //     }

    // }

    //     public function generateQRcode()
    //     {

    //         if (Auth::check()) {

    //             $url      = url('chatbot');
    //             $htmlCode = '<div class="flex justify-center items-center h-screen">';
    //             $htmlCode .= '<div class="qrcode-container">';
    //             $htmlCode .= '<div class="d-block w-full" id="ancherlink"><a class="w-full cursor-pointer rounded-md border border-cfp-500 bg-cfp-500 p-4 font-medium text-white transition hover:bg-opacity-90" href="'.$url.'" target="_top">Continue to Dititalk</a></div>';
    //             $htmlCode .= '</div></div>';
    //             $htmlCode .= '<style>a.w-full.cursor-pointer.rounded-md.border.border-cfp-500.bg-cfp-500.p-4.font-medium.text-white.transition.hover\:bg-opacity-90 {
    //     padding: 10px 20px;
    //     border: 2px solid #ccc;
    //     background: blue;
    //     color: #fff;
    // } .d-block.w-full {
    //     display: revert;
    //     clear: both;
    //     width: 100%;
    //     padding: 1rem;
    // }
    // .qrcode-container {display: flex;justify-content: center;align-items: center;height: 100%;}</style>';

    //             echo $htmlCode;
    //             exit;

    //         } else {
    //             //SessionDomain::truncate();
    //             // Default URL for login
    //             $url = route('login');

    //             // Start the HTML code for the page
    //             $htmlCode = '<div class="flex justify-center items-center h-screen">';
    //             $htmlCode .= '<div class="qrcode-container">';
    //             $htmlCode .= '<div class="d-block w-full" id="qrcode"></div>';
    //             $htmlCode .= '<div class="d-block w-full" id="ancherlink"><a target="_blank" class="w-full cursor-pointer rounded-md border border-cfp-500 bg-cfp-500 p-4 font-medium text-white transition hover:bg-opacity-90" href="'.$url.'">Login</a></div>';
    //             $htmlCode .= '</div></div>';
    //             $htmlCode .= '<style>a.w-full.cursor-pointer.rounded-md.border.border-cfp-500.bg-cfp-500.p-4.font-medium.text-white.transition.hover\:bg-opacity-90 {
    //         padding: 10px 20px;
    //         border: 2px solid #ccc;
    //         background: blue;
    //         color: #fff;
    //     } .d-block.w-full {
    //         display: revert;
    //         clear: both;
    //         width: 100%;
    //         padding: 1rem;
    //     }
    //         .qrcode-container {display: flex;justify-content: center;align-items: center;height: 100%;}</style>';
    //             $htmlCode .= '<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>';
    //             $htmlCode .= '<script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>';
    //             $htmlCode .= '<script>
    //                             // Helper function to get a cookie by name
    //                             function getCookie(name) {
    //                                 let value = "; " + document.cookie;
    //                                 let parts = value.split("; " + name + "=");
    //                                 if (parts.length == 2) return parts.pop().split(";").shift();
    //                             }

    //                             // Helper function to set a cookie
    //                             function setCookie(name, value, minutes) {
    //                                 let expires = "";
    //                                 if (minutes) {
    //                                     let date = new Date();
    //                                     date.setTime(date.getTime() + (minutes * 60 * 1000));
    //                                     expires = "; expires=" + date.toUTCString();
    //                                 }
    //                                 document.cookie = name + "=" + (value || "") + expires + "; path=/";
    //                             }

    //                             // Helper function to delete a cookie
    //                             function deleteCookie(name) {
    //                                 document.cookie = name + "=; Max-Age=-99999999;";
    //                             }

    //                             // Extract the URL parameters
    //                             var urlParams = new URLSearchParams(window.location.search);
    //                             var parentDomain = urlParams.get("parentDomain");
    //                             var tokenVendor = urlParams.get("randomToken");
    //                             var currentDomain = window.location.origin;

    //                             // Periodically check the current domain
    //                             setInterval(function(){
    //                                 // Fetch the domain data from the server
    //                                 $.ajax({
    //                                     url: "'.route('get-domain-data').'?domainid="+parentDomain,
    //                                     method: "GET",
    //                                     success: function(data) {

    //                                         if(data.status == false){

    //                                         }else if(data.status == true){

    //                                             var targetUrl = currentDomain + "/chatbot";
    //                                             window.top.location.href = targetUrl;

    //                                             document.getElementById("ancherlink").remove();

    //                                             if(data.domain_status === 0){
    //                                                 document.getElementById("qrcode").innerHTML = "<span style=\'color:green; font-size:20px;\'>Verification in process... <br> Please wait for Verification</span>";
    //                                             }

    //                                             if(data.domain_status === 1){
    //                                                 document.getElementById("qrcode").innerHTML = "<span style=\'color:green; font-size:20px;\'>Verification Success</span><div>User ID: " + data.userid + "<br> User Email: " + data.useremail + "</div>";
    //                                             }

    //                                         }

    //                                         // Get the current domain

    //                                         //console.log("Current domain:", currentDomain);
    //                                         //console.log("Third party domain:", parentDomain);
    //                                        // console.log("Third party token:", tokenVendor);
    //                                     },
    //                                     error: function(err) {
    //                                         console.error("Error fetching domain data:", err);
    //                                     }
    //                                 });
    //                             }, 2000); // Check every 5 seconds

    //                             // Generate the QR code with the appropriate URL
    //                             var url = "'.$url.'?triggerDomain=" + encodeURIComponent(parentDomain || "") + "&randomToken=" + encodeURIComponent(tokenVendor || "");
    //                             new QRCode(document.getElementById("qrcode"), {
    //                                 text: url,
    //                                 width: 300,
    //                                 height: 300
    //                             });
    //                         </script>';
    //             echo $htmlCode;
    //             exit;
    //         }

    //     }

    public function index()
    {

        return Inertia::render('Front/views/Home', []);

    }

    public function sendMailWithAttachment(Request $request)
    {
        $mailData = [
            'title' => 'This is Test Mail',
        ];

        Mail::to('<EMAIL>')->send(new TestEmail($mailData));

        echo 'Mail send successfully !!';

    }

    public function Deals()
    {

        $companyIds = Company::pluck('id')->toArray();
        $deals      = Deals::join('company', 'company.id', '=', 'deals.companyId')
            ->where('deals.deal_status', 1)
            ->join('users', 'users.id', '=', 'company.userId')
            ->select('users.profilePicture', 'deals.*', 'company.websiteUrl')
            ->whereIn('deals.companyId', $companyIds)
            ->paginate(env('PAGE_LIMIT'));

        return Inertia::render('Front/views/Deals', ['deals' => $deals]);

    }

    public function SearchDeals()
    {

        $searchField = (isset($_REQUEST['search']) && trim($_REQUEST['search']) != '') ? $_REQUEST['search'] : '';

        $companyIds = Company::pluck('id')->toArray();
        $Alldeals   = Deals::join('company', 'company.id', '=', 'deals.companyId')
            ->join('users', 'users.id', '=', 'company.userId')
            ->whereIn('deals.companyId', $companyIds)
            ->where(function ($query) use ($searchField) {
                $query->where('users.phone', 'LIKE', '%'.$searchField.'%');
                $query->orWhere('deals.dealTitle', 'LIKE', '%'.$searchField.'%');
                $query->orWhere('deals.dealContent', 'LIKE', '%'.$searchField.'%');
                $query->orWhere('company.companyName', 'LIKE', '%'.$searchField.'%');
                $query->orWhere('users.name', 'LIKE', '%'.$searchField.'%');
            })
            ->select('users.profilePicture', 'deals.*', 'company.websiteUrl')
            ->get();

        if (count($Alldeals) > 0) {
            return response()->json(['deals' => $Alldeals]);
        } else {
            return response()->json(['deals' => []]);
        }

    }

    public function SearchCompanies()
    {

        $searchField = (isset($_REQUEST['search']) && trim($_REQUEST['search']) != '') ? $_REQUEST['search'] : '';

        $allCompanies = Company::join('category', 'category.catId', '=', 'company.catId')
            ->leftJoin('bots', 'bots.company_id', '=', 'company.id')
            ->join('country', 'country.id', '=', 'company.countryId')
            ->join('users', 'users.id', '=', 'company.userId')
            ->where('users.profileType', 'company')
            ->where(function ($query) use ($searchField) {
                $query->where('country.name', 'LIKE', '%'.$searchField.'%');
                $query->orWhere('category.catName', 'LIKE', '%'.$searchField.'%');
                $query->orWhere('users.email', 'LIKE', '%'.$searchField.'%');
                $query->orWhere('users.phone', 'LIKE', '%'.$searchField.'%');
                $query->orWhere('company.companyName', 'LIKE', '%'.$searchField.'%');
                $query->orWhere('users.name', 'LIKE', '%'.$searchField.'%');
            })
            ->select('company.*', 'bots.chatbot_name', 'bots.chatbot_project_id', 'bots.chatbot_private_key_id',
                'bots.chatbot_client_email', 'bots.chatbot_client_id', 'bots.chatbot_private_key', 'country.name', 'category.catName', 'users.profilePicture', 'users.phone', 'users.email')
            ->orderBy('company.id', 'desc')
            ->get();

        if (count($allCompanies) > 0) {
            return response()->json(['company' => $allCompanies]);
        } else {
            return response()->json(['company' => []]);
        }

    }

    public function CompaniesBotsGlobal($companyID)
    {

        $searchField = (isset($_REQUEST['search']) && trim($_REQUEST['search']) != '') ? $_REQUEST['search'] : '';

        $singleCompany = Company::join('category', 'category.catId', '=', 'company.catId')
            ->join('bots', 'bots.company_id', '=', 'company.id')
            ->join('country', 'country.id', '=', 'company.countryId')
            ->join('users', 'users.id', '=', 'company.userId')
            ->where('users.profileType', 'company')
            ->where('company.id', $companyID)
            ->select('company.*', 'bots.chatbot_name', 'bots.chatbot_project_id', 'bots.chatbot_private_key_id',
                'bots.chatbot_client_email', 'bots.chatbot_client_id', 'bots.chatbot_private_key', 'country.name', 'category.catName', 'users.profilePicture', 'users.phone', 'users.email')
            ->orderBy('company.id', 'desc')->first();

        return Inertia::render('Front/views/ChatBotsGlobal', ['singleCompany' => $singleCompany, 'searchField' => $searchField]);

    }

    // public function Companies()
    // {

    //     $searchField = (isset($_REQUEST['search']) && trim($_REQUEST['search']) != '') ? $_REQUEST['search'] : '';

    //     $allCompanies = Company::join('category', 'category.catId', '=', 'company.catId')
    //         ->join('bots', 'bots.company_id', '=', 'company.id')
    //         ->join('country', 'country.id', '=', 'company.countryId')
    //         ->join('users', 'users.id', '=', 'company.userId')
    //         ->where('users.profileType', 'company')
    //         ->where(function ($query) use ($searchField) {
    //             $query->where('country.name', 'LIKE', '%'.$searchField.'%');
    //             $query->orWhere('category.catName', 'LIKE', '%'.$searchField.'%');
    //             $query->orWhere('users.email', 'LIKE', '%'.$searchField.'%');
    //             $query->orWhere('users.phone', 'LIKE', '%'.$searchField.'%');
    //             $query->orWhere('company.companyName', 'LIKE', '%'.$searchField.'%');
    //             $query->orWhere('users.name', 'LIKE', '%'.$searchField.'%');
    //         })
    //         ->select('company.*', 'bots.chatbot_name', 'bots.chatbot_project_id', 'bots.chatbot_private_key_id',
    //             'bots.chatbot_client_email', 'bots.chatbot_client_id', 'bots.chatbot_private_key', 'country.name', 'category.catName', 'users.profilePicture', 'users.phone', 'users.email')
    //         ->orderBy('company.id', 'desc')
    //         ->paginate(env('PAGE_LIMIT'));

    //     return Inertia::render('Front/views/Companies', ['allCompanies' => $allCompanies, 'searchField' => $searchField]);

    // }

    public function Companies()
    {

        $searchField = (isset($_REQUEST['search']) && trim($_REQUEST['search']) != '') ? $_REQUEST['search'] : '';

        $allCompanies = Company::join('category', 'category.catId', '=', 'company.catId')
            ->leftJoin('bots', 'bots.company_id', '=', 'company.id')
            ->join('country', 'country.id', '=', 'company.countryId')
            ->join('users', 'users.id', '=', 'company.userId')
            ->where('users.profileType', 'company')
            ->where('users.verificationStatus', 'verified')
            ->where('users.status', 'active')
            ->where('company.chatSupport', '<>', '')
            ->where(function ($query) use ($searchField) {
                $query->where('country.name', 'LIKE', '%'.$searchField.'%');
                $query->orWhere('category.catName', 'LIKE', '%'.$searchField.'%');
                $query->orWhere('users.email', 'LIKE', '%'.$searchField.'%');
                $query->orWhere('users.phone', 'LIKE', '%'.$searchField.'%');
                $query->orWhere('company.companyName', 'LIKE', '%'.$searchField.'%');
                $query->orWhere('users.name', 'LIKE', '%'.$searchField.'%');
            })
            ->select('company.*', 'bots.chatbot_name', 'bots.chatbot_project_id', 'bots.chatbot_private_key_id',
                'bots.chatbot_client_email', 'bots.chatbot_client_id', 'bots.chatbot_private_key', 'country.name', 'category.catName', 'users.profilePicture', 'users.phone', 'users.email')
            ->orderBy('company.id', 'desc')
            ->paginate(env('PAGE_LIMIT'));

        return Inertia::render('Front/views/Companies', ['allCompanies' => $allCompanies, 'searchField' => $searchField]);

    }

    public function Contact()
    {

        return Inertia::render('Front/views/Contact', []);

    }

    public function DealDetail($id)
    {

        $dealsQry = Deals::join('company', 'company.id', '=', 'deals.companyId')
            ->join('users', 'users.id', '=', 'company.userId')
            ->select('users.profilePicture', 'deals.*', 'company.websiteUrl', 'users.phone')
            ->where('deals.id', $id)
            ->first();

        if ($dealsQry) {
            return Inertia::render('Front/views/SingleDeal', ['dealsQry' => $dealsQry]);
        } else {
            return redirect()->back();
        }

    }

    public function ComplanyDetail($id)
    {

        $companyQry = Company::join('category', 'category.catId', '=', 'company.catId')
            ->join('country', 'country.id', '=', 'company.countryId')
            ->join('users', 'users.id', '=', 'company.userId')
            ->where('users.profileType', 'company')
            ->where('company.id', $id)
            ->select('company.*', 'country.name', 'category.catName', 'users.profilePicture', 'users.phone')
            ->first();
        if ($companyQry) {
            $totalActiveDeal = Deals::where('companyId', $companyQry->id)->where('deal_status', 1)->count();

            return Inertia::render('Front/views/SingleCompany', ['companyQry' => $companyQry, 'totalActiveDeal' => $totalActiveDeal]);
        } else {
            return redirect()->back();
        }

    }

    public function Login()
    {

        return Inertia::render('Front/views/Login', []);

    }

    public function LoginAuth(Request $request)
    {
        if ($request->client_id && $request->redirect_uri && $request->vuserid) {
            return Inertia::render('Front/views/LoginAuth', ['client_id' => $request->client_id, 'redirect_uri' => $request->redirect_uri, 'vuserid' => $request->vuserid]);
        } else {
            abort(404);
        }

    }


    public function webGoogleLogin(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id_token' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'errors' => $validator->errors(),
                ], 400);
            }

            // Manually verify the Google ID token
            $client = new \Google_Client(['client_id' => config('services.google.client_id')]);
            $payload = $client->verifyIdToken($request->id_token);
            
            if (!$payload) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid Google token'
                ], 401);
            }
            
            // Extract user information from the payload
            $email = $payload['email'];
            $name = $payload['name'] ?? ($payload['given_name'] . ' ' . $payload['family_name']);
            
            // Check if user exists
            $user = User::where('email', $email)->first();
            
            if (!$user) {
                // Create new user
                $user = new User();
                $user->name = $name;
                $user->email = $email;
                $user->password = Hash::make(Str::random(16));
                $user->profileType = 'user';
                $user->verificationStatus = 'verified'; // Auto-verify Google users
                $user->status = 'active';
                $user->save();
                
                // Create person record
                $nameParts = explode(' ', $name, 2);
                $firstName = $nameParts[0];
                $lastName = isset($nameParts[1]) ? $nameParts[1] : '';
                
                $person = new Person();
                $person->userId = $user->id;
                $person->firstName = $firstName;
                $person->lastName = $lastName;
                $person->save();
            }
            
            // Login the user with Laravel's authentication
            Auth::login($user);
            
            // Generate JWT token for API access if needed
            try {
                $token = JWTAuth::fromUser($user);
            } catch (JWTException $e) {
                $token = null;
            }
            
            return response()->json([
                'status' => true,
                'message' => 'User logged in successfully with Google',
                'access_token' => $token,
            ]);
            
        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function Signup()
    {

        return Inertia::render('Front/views/Signup', []);

    }

    public function SignupBusiness()
    {

        $categories = Category::all();

        if ($categories->isNotEmpty()) {
            // Add URL prefix to category pictures if needed
            $categories->map(function ($category) {
                $category->catPicture = $category->catPicture ? url('/storage/'.$category->catPicture) : '';

                return $category;
            });
        }

        $countries = Country::all();

        return Inertia::render('Front/views/SignupBusiness', ['Categories' => $categories, 'Countries' => $countries]);

    }
}

