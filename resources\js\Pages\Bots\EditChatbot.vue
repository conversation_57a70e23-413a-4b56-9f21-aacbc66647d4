<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import ApplicationLogo from '@/Components/ApplicationLogo.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import InputError from '@/Components/InputError.vue';
import SelectInput from '@/Components/SelectInput.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import axios from 'axios';
import { ref } from 'vue';
import { Head, usePage, useForm } from '@inertiajs/vue3';
import BreadcrumbDefault from '@/Components/Breadcrumbs/BreadcrumbDefault.vue';
import PageHeading from '@/Components/Global/PageHeading.vue';

const pageTitle = ref('Update Bot Detail');

const { agentDetail, allCompny } = usePage().props;



const form = useForm({
    chatbot_id: agentDetail.id || '',
    company_id: agentDetail.company_id || '',
    chatbot_name: agentDetail.chatbot_name || '',
    chatbot_project_id: agentDetail.chatbot_project_id || '',
    chatbot_private_key_id: agentDetail.chatbot_private_key_id || '',
    chatbot_client_email: agentDetail.chatbot_client_email || '',
    chatbot_client_id: agentDetail.chatbot_client_id || '',
    chatbot_private_key: agentDetail.chatbot_private_key || '',
});

</script>

<template>

    <Head :title="pageTitle" />

    <AuthenticatedLayout>

        <PageHeading :title="pageTitle"></PageHeading>



        <div class="flex">

            <div class="flex-1">

                <form @submit.prevent="form.post(route('chatbots.update'))" class="form-main-body">
                    <div class="border-stroke p-6 border rounded-md">



                        <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
                            leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
                            <p v-if="form.recentlySuccessful" enter-active-class="transition ease-in-out"
                                enter-from-class="opacity-0" leave-active-class="transition ease-in-out"
                                leave-to-class="opacity-0" :class="$page.props.flash?.class">
                                {{ $page.props.flash.message }}</p>
                        </Transition>

                        <input type="hidden" id="chatbot_id" name="chatbot_id" v-model="form.chatbot_id" />

                        <div class="form-ttl-gap">
                            <InputLabel for="chatbot_name" value="Chatbot Name" />
                            <TextInput id="chatbot_name" name="chatbot_name" type="text" class="block mt-1 w-full"
                                v-model="form.chatbot_name" />
                            <InputError :message="form.errors.chatbot_name" class="mt-2" />
                        </div>

                        <div class="form-ttl-gap">
                            <InputLabel for="company_id" value="Select Company Address" />
                            <SelectInput id="company_id" class="block mt-1 w-full" v-model="form.company_id" required
                                autocomplete="company_id" name="company_id">
                                <option v-for="(option, index) in allCompny" :key="index" :value="option.id">
                                    {{ option.companyAdd }}, {{ option.city }}, {{ option.state }}, {{
                                        option.zipCode }}
                                </option>
                            </SelectInput>
                            <InputError class="mt-2" :message="form.errors.company_id" />
                        </div>


                        <div class="form-ttl-gap">
                            <InputLabel for="chatbot_project_id" value="Project ID" />
                            <TextInput id="chatbot_project_id" name="chatbot_project_id" type="text"
                                class="block mt-1 w-full" v-model="form.chatbot_project_id" />
                            <InputError :message="form.errors.chatbot_project_id" class="mt-2" />
                        </div>

                        <div class="form-ttl-gap">

                            <InputLabel for="chatbot_private_key_id" value="Private Key ID" />
                            <TextInput id="chatbot_private_key_id" name="chatbot_private_key_id" type="text"
                                class="block mt-1 w-full" v-model="form.chatbot_private_key_id" />
                            <InputError :message="form.errors.chatbot_private_key_id" class="mt-2" />
                        </div>

                        <div class="form-ttl-gap">

                            <InputLabel for="chatbot_client_email" value="Client Email" />
                            <TextInput id="chatbot_client_email" name="chatbot_client_email" type="text"
                                class="block mt-1 w-full" v-model="form.chatbot_client_email" />
                            <InputError :message="form.errors.chatbot_client_email" class="mt-2" />
                        </div>

                        <div class="form-ttl-gap">

                            <InputLabel for="chatbot_client_id" value="Client ID" />
                            <TextInput id="chatbot_client_id" name="chatbot_client_id" type="text"
                                class="block mt-1 w-full" v-model="form.chatbot_client_id" />
                            <InputError :message="form.errors.chatbot_client_id" class="mt-2" />
                        </div>

                        <div class="form-ttl-gap">
                            <InputLabel for="chatbot_private_key" value="Private Key" />
                            <TextInput id="chatbot_private_key" name="chatbot_private_key"
                                v-model="form.chatbot_private_key"
                                class="block border-gray-300 focus:border-indigo-500 shadow-sm mt-1 rounded-md focus:ring-indigo-500 w-full" />

                            <InputError :message="form.errors.chatbot_private_key" class="mt-2" />
                        </div>



                        <div class="flex items-center gap-0">
                            <PrimaryButton :disabled="form.processing">
                                <template #default>
                                    <span v-if="form.processing">Loading...</span>
                                    <span v-else>Save</span>
                                </template>
                            </PrimaryButton>

                            <div class="relative">
                                <ResponsiveNavLink :href="route('chatbots.setting')" class="dk-cancle-btn">
                                    Cancel
                                </ResponsiveNavLink>

                            </div>


                        </div>

                    </div>
                </form>

            </div>
        </div>
    </AuthenticatedLayout>
</template>
