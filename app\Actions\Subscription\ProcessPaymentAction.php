<?php

namespace App\Actions\Subscription;

use App\Models\Invoice;
use App\Models\PaymentGateway;
use App\Models\PaymentTransaction;
use App\Models\Subscription;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Srmklive\PayPal\Services\PayPal as PayPalClient;

class ProcessPaymentAction
{
    /**
     * Process a payment for a subscription.
     */
    public function handle(Subscription $subscription, Invoice $invoice): PaymentTransaction
    {
        return DB::transaction(function () use ($subscription, $invoice) {
            // Validate that the invoice belongs to the subscription
            if ($invoice->subscription_id !== $subscription->id) {
                throw new \Exception('Invoice does not belong to this subscription.');
            }

            if ($invoice->isPaid()) {
                throw new \Exception('Invoice is already paid.');
            }

            // Get payment gateway
            $gateway = PaymentGateway::where('name', $subscription->payment_method)
                ->where('is_active', true)
                ->first();

            if (!$gateway || !$gateway->isConfigured()) {
                throw new \Exception("Payment gateway {$subscription->payment_method} is not available.");
            }

            // Create payment transaction record
            $transaction = PaymentTransaction::create([
                'invoice_id' => $invoice->id,
                'subscription_id' => $subscription->id,
                'user_id' => $subscription->user_id,
                'transaction_id' => 'pending_' . uniqid(),
                'gateway' => $subscription->payment_method,
                'type' => 'payment',
                'status' => 'pending',
                'amount' => $invoice->amount_due,
                'currency' => $invoice->currency,
            ]);

            try {
                // Process payment through gateway
                $gatewayResponse = $this->processGatewayPayment($subscription, $invoice, $gateway);

                // Update transaction with gateway response
                $transaction->update([
                    'transaction_id' => $gatewayResponse['transaction_id'],
                    'status' => $gatewayResponse['status'],
                    'gateway_response' => $gatewayResponse,
                    'processed_at' => now(),
                ]);

                // If payment successful, mark invoice as paid
                if ($gatewayResponse['status'] === 'succeeded') {
                    $invoice->markAsPaid($invoice->amount_due);

                    // Update subscription status if it was past due
                    if ($subscription->status === 'past_due') {
                        $subscription->update(['status' => 'active']);
                    }
                }

                return $transaction;
            } catch (\Exception $e) {
                // Mark transaction as failed
                $transaction->update([
                    'status' => 'failed',
                    'gateway_response' => ['error' => $e->getMessage()],
                    'processed_at' => now(),
                ]);

                // Mark subscription as past due if payment failed
                if ($subscription->status === 'active') {
                    $subscription->update(['status' => 'past_due']);
                }

                throw $e;
            }
        });
    }

    /**
     * Process payment through the payment gateway.
     */
    private function processGatewayPayment(Subscription $subscription, Invoice $invoice, PaymentGateway $gateway): array
    {
        if ($gateway->name === 'stripe') {
            return $this->processStripePayment($subscription, $invoice, $gateway);
        } elseif ($gateway->name === 'paypal') {
            return $this->processPayPalPayment($subscription, $invoice, $gateway);
        }

        throw new \Exception('Unsupported payment gateway: ' . $gateway->name);
    }

    /**
     * Process Stripe payment.
     */
    private function processStripePayment(Subscription $subscription, Invoice $invoice, PaymentGateway $gateway): array
    {
        $config = $gateway->getCurrentConfig();
        \Stripe\Stripe::setApiKey($config['secret_key']);

        try {
            // For subscription payments, we typically use the invoice payment intent
            if ($invoice->stripe_invoice_id) {
                $stripeInvoice = \Stripe\Invoice::retrieve($invoice->stripe_invoice_id);
                $paymentIntent = \Stripe\PaymentIntent::retrieve($stripeInvoice->payment_intent);

                return [
                    'transaction_id' => $paymentIntent->id,
                    'status' => $paymentIntent->status === 'succeeded' ? 'succeeded' : 'failed',
                    'amount' => $paymentIntent->amount,
                    'currency' => $paymentIntent->currency,
                    'payment_method' => $paymentIntent->payment_method,
                ];
            }

            // Fallback: create a payment intent manually
            $paymentIntent = \Stripe\PaymentIntent::create([
                'amount' => $invoice->amount_due,
                'currency' => strtolower($invoice->currency),
                'customer' => $this->getStripeCustomerId($subscription),
                'metadata' => [
                    'subscription_id' => $subscription->id,
                    'invoice_id' => $invoice->id,
                ],
            ]);

            return [
                'transaction_id' => $paymentIntent->id,
                'status' => $paymentIntent->status === 'succeeded' ? 'succeeded' : 'pending',
                'amount' => $paymentIntent->amount,
                'currency' => $paymentIntent->currency,
                'client_secret' => $paymentIntent->client_secret,
            ];
        } catch (\Stripe\Exception\ApiErrorException $e) {
            Log::error('Stripe payment failed', [
                'subscription_id' => $subscription->id,
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('Payment processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Process PayPal payment.
     */
    private function processPayPalPayment(Subscription $subscription, Invoice $invoice, PaymentGateway $gateway): array
    {
        try {
            $config = $gateway->getCurrentConfig();

            $paypal = new PayPalClient();
            $paypal->setApiCredentials([
                'mode' => $gateway->test_mode ? 'sandbox' : 'live',
                'sandbox' => [
                    'client_id' => $config['client_id'] ?? '',
                    'client_secret' => $config['client_secret'] ?? '',
                ],
                'live' => [
                    'client_id' => $config['client_id'] ?? '',
                    'client_secret' => $config['client_secret'] ?? '',
                ],
            ]);

            $accessToken = $paypal->getAccessToken();

            if (!$accessToken) {
                throw new \Exception('Failed to get PayPal access token');
            }

            // Create payment request
            $paymentData = [
                'intent' => 'CAPTURE',
                'purchase_units' => [
                    [
                        'amount' => [
                            'currency_code' => $invoice->currency,
                            'value' => number_format($invoice->amount_due, 2, '.', ''),
                        ],
                        'description' => "Subscription payment for plan: {$subscription->subscriptionPlan->name}",
                        'custom_id' => $subscription->id,
                    ]
                ],
                'application_context' => [
                    'return_url' => route('company.subscription.dashboard'),
                    'cancel_url' => route('company.subscription.dashboard'),
                ]
            ];

            $order = $paypal->createOrder($paymentData);

            if (isset($order['id'])) {
                return [
                    'transaction_id' => $order['id'],
                    'status' => 'pending',
                    'amount' => $invoice->amount_due,
                    'currency' => $invoice->currency,
                    'approval_url' => $order['links'][1]['href'] ?? null,
                ];
            }

            throw new \Exception('Failed to create PayPal order');

        } catch (\Exception $e) {
            Log::error('PayPal payment failed', [
                'subscription_id' => $subscription->id,
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('PayPal payment processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Get Stripe customer ID for the subscription.
     */
    private function getStripeCustomerId(Subscription $subscription): ?string
    {
        // This would typically be stored on the user model or retrieved from Stripe
        // For now, we'll extract it from the subscription's payment method details

        $paymentDetails = $subscription->payment_method_details;
        return $paymentDetails['stripe_customer_id'] ?? null;
    }
}
