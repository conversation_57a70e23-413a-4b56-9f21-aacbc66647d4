<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('botconversation', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users');
            $table->foreignId('bot_id')->constrained('bots');
            $table->integer('company_user_id');
            $table->text('lastmessage');
            $table->text('notification_msg')->nullable();
            $table->integer('status')->default(0)->comment('0:new,1:ready,2:solved');
            $table->integer('agent_id')->default(0);
            $table->enum('last_msg_type', ['agent', 'user', 'bot'])->default('user');
            $table->integer('status_ticket')->default(0)->comment('0:default,1:fallback for ticket,2:ticket sent');
            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('botconversation');
    }
};
