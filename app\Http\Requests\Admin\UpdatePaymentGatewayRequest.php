<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePaymentGatewayRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() && $this->user()->hasRole('admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $gateway = $this->route('paymentGateway');

        $rules = [
            'is_active' => 'boolean',
            'test_mode' => 'boolean',
            'webhook_secret' => 'nullable|string|max:255',
        ];

        // Add specific validation rules based on gateway type
        if ($gateway && $gateway->name === 'stripe') {
            $rules['test_config.publishable_key'] = 'nullable|string|starts_with:pk_test_';
            $rules['test_config.secret_key'] = 'nullable|string|starts_with:sk_test_';
            $rules['test_config.webhook_endpoint_secret'] = 'nullable|string|starts_with:whsec_';
            $rules['live_config.publishable_key'] = 'nullable|string|starts_with:pk_live_';
            $rules['live_config.secret_key'] = 'nullable|string|starts_with:sk_live_';
            $rules['live_config.webhook_endpoint_secret'] = 'nullable|string|starts_with:whsec_';
        } elseif ($gateway && $gateway->name === 'paypal') {
            $rules['test_config.client_id'] = 'nullable|string';
            $rules['test_config.client_secret'] = 'nullable|string';
            $rules['test_config.webhook_id'] = 'nullable|string';
            $rules['live_config.client_id'] = 'nullable|string';
            $rules['live_config.client_secret'] = 'nullable|string';
            $rules['live_config.webhook_id'] = 'nullable|string';
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'test_config.publishable_key.starts_with' => 'Test publishable key must start with pk_test_',
            'test_config.secret_key.starts_with' => 'Test secret key must start with sk_test_',
            'test_config.webhook_endpoint_secret.starts_with' => 'Test webhook secret must start with whsec_',
            'live_config.publishable_key.starts_with' => 'Live publishable key must start with pk_live_',
            'live_config.secret_key.starts_with' => 'Live secret key must start with sk_live_',
            'live_config.webhook_endpoint_secret.starts_with' => 'Live webhook secret must start with whsec_',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'test_config.publishable_key' => 'test publishable key',
            'test_config.secret_key' => 'test secret key',
            'test_config.webhook_endpoint_secret' => 'test webhook secret',
            'live_config.publishable_key' => 'live publishable key',
            'live_config.secret_key' => 'live secret key',
            'live_config.webhook_endpoint_secret' => 'live webhook secret',
            'test_config.client_id' => 'test client ID',
            'test_config.client_secret' => 'test client secret',
            'test_config.webhook_id' => 'test webhook ID',
            'live_config.client_id' => 'live client ID',
            'live_config.client_secret' => 'live client secret',
            'live_config.webhook_id' => 'live webhook ID',
        ];
    }
}
