import React from 'react';
import { Link } from '@inertiajs/react';
import { PlusIcon } from '@/Components/UI';

/**
 * A reusable header component with statistics display
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - The main title to display
 * @param {string} props.subtitle - The subtitle text
 * @param {string} props.backgroundImage - URL for the background image
 * @param {string} props.buttonText - Text for the action button
 * @param {string} props.buttonLink - Link for the action button
 * @param {Array} props.stats - Array of statistics to display
 * @param {string} props.stats[].value - The value of the statistic
 * @param {string} props.stats[].label - The label for the statistic
 * @param {string} props.gradientFrom - Starting color for gradient (default: cf-primary-600)
 * @param {string} props.gradientTo - Ending color for gradient (default: blue-600)
 */
export default function HeaderStats({
    title,
    subtitle,
    backgroundImage = "https://images.unsplash.com/photo-1600880292203-757bb62b4baf?w=800&h=400&fit=crop&crop=center",
    buttonText,
    buttonLink,
    stats = [],
    gradientFrom = "cf-primary-600",
    gradientTo = "blue-600"
}) {
    return (
        <div className={`bg-gradient-to-r from-${gradientFrom} to-${gradientTo} rounded-2xl p-8 text-white relative overflow-hidden`}>
            <div className="absolute inset-0 opacity-10">
                <img
                    src={backgroundImage}
                    alt={title || "Header background"}
                    className="w-full h-full object-cover"
                />
            </div>
            <div className="relative grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <div>
                    {title && (
                        <h1 className="text-3xl md:text-4xl font-bold mb-4">
                            {title}
                        </h1>
                    )}
                    {subtitle && (
                        <p className="text-xl opacity-90 mb-6">
                            {subtitle}
                        </p>
                    )}
                    {buttonText && buttonLink && (
                        <Link
                            href={buttonLink}
                            className="inline-flex items-center bg-white text-cf-primary-600 hover:bg-gray-100 px-6 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105"
                        >
                            <PlusIcon className="w-5 h-5 mr-2" />
                            {buttonText}
                        </Link>
                    )}
                </div>
                {stats.length > 0 && (
                    <div className="grid grid-cols-2 gap-4">
                        {stats.map((stat, index) => (
                            <div key={index} className="bg-white/20 backdrop-blur-sm rounded-xl p-4 text-center">
                                <div className="text-3xl font-bold">{stat.value}</div>
                                <div className="text-sm opacity-80">{stat.label}</div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
}