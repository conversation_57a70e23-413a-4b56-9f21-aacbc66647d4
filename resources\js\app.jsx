import "./bootstrap";
import { createInertiaApp } from "@inertiajs/react";
import { createRoot } from "react-dom/client";
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { route } from './utils/route';
import "@resources/css/app.css";

// Make route function globally available
window.route = route;

createInertiaApp({
    resolve: (name) => resolvePageComponent(`./Pages/${name}.jsx`, import.meta.glob('./Pages/**/*.jsx')),

    setup({ el, App, props }) {
        createRoot(el).render(<App {...props} />);
    },
});
