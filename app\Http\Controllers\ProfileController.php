<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProfileUpdateRequest;
use App\Models\Category;
use App\Models\Company;
use App\Models\Country;
use App\Models\OAuthClient;
use App\Models\User;
use Exception;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response;

class ProfileController extends Controller
{
    /**
     * Display the user's profile form.
     */
    public function edit(Request $request): Response
    {

        $user        = Auth::user();
        $profileType = $user->profileType;
        if ($profileType == 'agent') {

            $companyDetail = Company::where('userId', Auth::user()->parent_id)->first();

        } else {
            $companyDetail = Company::where('userId', Auth::id())->first();
        }

        $categoryList = Category::all();
        $countryList  = Country::all();
        $userDetail   = User::find(Auth::id());

        $oauthDetail = OAuthClient::where('userId', Auth::id())->first();
        $title       = 'Edit Profile';

        return Inertia::render('Profile/Edit', [
            'userDetail'      => $userDetail,
            'companyDetail'   => $companyDetail,
            'categoryList'    => $categoryList,
            'countryList'     => $countryList,
            'oauthDetail'     => $oauthDetail,
            'mustVerifyEmail' => $request->user() instanceof MustVerifyEmail,
            'status'          => session('status'),
            'title'           => $title,
        ]);
    }

    /**
     * Update the user's profile information.
     */
    public function update(ProfileUpdateRequest $request): RedirectResponse
    {
        $request->user()->fill($request->validated());

        if ($request->user()->isDirty('email')) {
            $request->user()->email_verified_at = null;
        }

        $request->user()->save();

        $currentUser = User::find(Auth::id());

        if (Auth::user()->profileType == 'company') {

            $currentUser->company_name = $request->company_name;
            $currentUser->profileType  = 'company';
            $currentUser->save();

            $saveCompany = Company::where('userId', Auth::id())->update(['companyName' => $request->company_name]);

        }

        if ($request->hasFile('profilePicture')) {
            try {
                $image       = $request->file('profilePicture');
                $folder_name = 'images/profile';
                $profileUrl  = $image->store($folder_name, 'public');
                if ($currentUser->profilePicture != '') {
                    Storage::disk('public')->delete($currentUser->profilePicture);
                }

            } catch (Exception $ex) {

                return Redirect::back()->with([
                    'status'  => false,
                    'message' => $ex->getMessage(),
                    'class'   => 'flex items-center p-2 mb-4 text-white rounded-md bg-red-500',
                ]);
            }
        } else {
            $profileUrl = $currentUser->profilePicture;
        }

        $currentUser->profilePicture = $profileUrl;
        $currentUser->save();

        return Redirect::back()->with([
            'status'  => true,
            'message' => 'Profile saved successfully',
            'class'   => 'flex items-center p-2 mb-4 text-green-700 rounded-md bg-green-100',
        ]);

        // return Redirect::route('profile.edit');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $request->validate([
            'password' => ['required', 'current_password'],
        ]);

        $user = $request->user();
        User::deleteUserRelationTable($user->id);

        Auth::logout();

        $user->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return Redirect::to('/');
    }
}
