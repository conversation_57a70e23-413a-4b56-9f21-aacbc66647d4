<script setup>
import { ref } from 'vue'
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, usePage, Link } from '@inertiajs/vue3';
import DtIconGlobal from '@/Components/DtIcon/DtIconGlobal.vue';
import axios from 'axios';


const { usersData, title, currentRole, roles } = usePage().props;

const pageTitle = ref(title);
var baseurl = window.location.origin;

const formatDate = (dateString) => {
  const date = new Date(dateString);
  const formattedDate = `${date.getFullYear()}-${('0' + (date.getMonth() + 1)).slice(-2)}-${('0' + date.getDate()).slice(-2)} ${('0' + date.getHours()).slice(-2)}:${('0' + date.getMinutes()).slice(-2)}`;
  return formattedDate;
}

const deleteItem = async (item) => {
    try {
        const confirmation = await Swal.fire({
            title: 'Are you sure?',
            text: 'You are about to delete this user. This action cannot be undone. Are you sure you want to proceed?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel',
            customClass: {
                confirmButton: 'dk-update-btn',
                cancelButton: 'dk-cancle-btn',
            },
            buttonsStyling: false,
        });

        if (confirmation.isConfirmed) {
            const response = await axios.delete(route('manage-users.delete', { id: item.id }));
            if (response.data.success) {
                // Create a new array without the deleted item
                const updatedUsersData = { ...usersData }; // Create a shallow copy
                updatedUsersData.data = usersData.data.filter((user) => user.id !== item.id);
                usersData.data.splice(0, usersData.data.length, ...updatedUsersData.data)

                //Swal.fire('Deleted!', 'The user has been deleted.', 'success');
            } else {
                throw new Error('Failed to delete user');
            }
        }
    } catch (error) {
        console.error(error);
        Swal.fire('Error', 'An error occurred while deleting the user.', 'error');
    }
};

const formatVerificationStatus = (status) => {
  if (!status) {
    return ''; // Handle null or undefined status
  }
  const words = status.split('_');
  const capitalizedWords = words.map(word => word.charAt(0).toUpperCase() + word.slice(1));
  return capitalizedWords.join(' ');
};

const selectedRole = ref(currentRole);

const filterByRole = () => {
    window.location.href = route('manage-users', { 
        utype: selectedRole.value 
    });
};

</script>

<template>

  <Head :title="pageTitle" />

  <AuthenticatedLayout>

    <div class="flex">


      <div class="flex-1">
        <div class="border-stroke overflow-x-auto w-full bg-white p-6 max-md:p-4 border rounded-md ">
            <div class="max-w-full">
            <div class="flex justify-between items-center mb-6">
                <div class="flex items-center">
                    <label for="roleFilter" class="mr-2">Filter by Role:</label>
                    <select 
                        id="roleFilter" 
                        v-model="selectedRole"
                        @change="filterByRole"
                        class="border-gray-300 focus:border-cfp-500 focus:ring-cfp-500 rounded-md shadow-sm"
                    >
                        <option v-for="role in roles" :key="role" :value="role">
                            {{ role.charAt(0).toUpperCase() + role.slice(1) }}
                        </option>
                    </select>
                </div>
            </div>
            <div class="dt-users-wrapper">
                <table class="dt-users-table w-full">
                <thead>
                    <tr>
                    <th scope="col" class="dt-deals-th">User Details</th>
                    <th scope="col" class="dt-deals-th">Status</th>
                    <th scope="col" class="dt-deals-th">Verification Status</th>
                    <th scope="col" class="dt-deals-th">Created at</th>
                    <th scope="col" >Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(item, index) in usersData.data" :key="index" class="dt-users-tr">
                        <td class="dt-deals-td align-middle">
                            <div class="flex items-center"> 
                                <div class="mr-4" v-if="item.profilePicture">
                                    <img
                                        :key="item.profilePicture"
                                        :src="baseurl + '/storage/' + item.profilePicture"
                                        alt="Profile"
                                        class="rounded-full w-15 h-15"
                                    />
                                </div>

                                <div class="">
                                    <div v-if="item.profileType === 'company'">
                                        <span class="">Name:</span> {{ item.company_name }}
                                    </div>
                                    <div v-if="item.name">
                                        <span class="">Name:</span> {{ item.name }}
                                    </div>
                                    <div v-if="item.email">
                                        <span class="">Email:</span> {{ item.email }}
                                    </div>
                                    <div v-if="item.phone">
                                        <span class="">Phone:</span> {{ item.phone }}
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="dt-deals-td">
                            <div class="dt-deals-title-wrap">
                                <div class="">
                                    <span :class="item.status === 'active' ? 'text-green-500' : 'text-red-500'">{{ formatVerificationStatus(item.status) }}</span>
                                </div>
                                
                            </div>
                        </td>
                        <td class="dt-deals-td">
                            <div class="dt-deals-title-wrap">
                                <span :class="item.verificationStatus === 'verified' ? 'text-green-500' : 'text-red-500'">{{ formatVerificationStatus(item.verificationStatus) }}</span>
                            </div>
                        </td>
                        <td class="dt-deals-td">
                            <div class="dt-deals-title-wrap">
                                <span>{{ formatDate(item.created_at) }}</span>
                            </div>
                        </td>
                        <td class="dt-deals-td">
                            <div class="dt-deals-actions">
                                <Link :href="route('manage-users.edit', item.id)" class="dt-deals-action-btn">

                                    <DtIconGlobal :type="'edit'" />

                                    </Link>
                                    <!-- delete button  -->
                                    <button @click="deleteItem(item)" class="dt-deals-action-btn dt-deals-delete-btn">
                                    <DtIconGlobal :type="'delete'" />

                                    </button>

                                    <Link :href="route('manage-users.access-portal', item.id)" class="inline-flex items-center bg-cfp-500 hover:bg-cfp-500/85 px-6 max-md:px-4 py-2.5 border rounded-md font-medium text-sm text-white capitalize leading-normal transition duration-150 ease-in-out">
                                        Access Portal
                                    </Link>
                            </div>
                        </td>
                    </tr>
                </tbody>
                </table>
            </div>

            <div v-if="usersData.data.length > 0" class="dt-table-pagi">
                <div class="content-center row-span-2 row-start-2">
                    Showing <b>{{ (usersData.current_page - 1) * usersData.per_page + 1 }}</b>-
                    <b>{{ Math.min(usersData.current_page * usersData.per_page, usersData.total) }}</b>
                    from <b>{{ usersData.total }}</b> data
                </div>

                <div class="row-start-2 row-end-4 text-end">
                    <div class="pagination-links">
                        <ul class="flex justify-items-end place-content-end">
                            <li v-for="page in usersData.links" :key="page.url">
                                <button 
                                    @click="$inertia.visit(page.url ? `${page.url}${page.url.includes('?') ? '&' : '?'}utype=${selectedRole}` : '#')"
                                    :class="{ 'bg-cfp-500 text-white': page.active, 'hover:bg-cfp-500 hover:text-white': !page.active }"
                                    class="px-3 py-1 rounded-full focus:outline-none mx-1" 
                                    v-html="page.label"
                                ></button>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>


            </div>
        </div>

      </div>

    </div>

  </AuthenticatedLayout>

</template>