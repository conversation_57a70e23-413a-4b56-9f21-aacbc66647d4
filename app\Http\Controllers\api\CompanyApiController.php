<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class CompanyApiController extends Controller
{
    public function SaveCompany(Request $request)
    {
        // Define validation rules
        $validator = Validator::make($request->all(), [
            'country'  => 'required|exists:country,id',
            'email'    => 'required|email',
            'name'     => 'required|string|max:255',
            'password' => 'required|min:6',
            'phone'    => 'required|string|max:20',
            'address'  => 'required|string|max:255',
            'city'     => 'required|string|max:255',
            'state'    => 'required|string|max:255',
            'zip'      => 'required|string|max:20',
            'url'      => 'nullable|url',
            'profile'  => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
        ]);

        // Check if validation fails
        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'errors' => $validator->errors(),
            ], 400);
        }

        try {
            // Retrieve or create the user based on email
            $user = User::firstOrNew(['email' => $request->email]);

            // Update user details
            $user->name        = $request->name;
            $user->password    = Hash::make($request->password);
            $user->phone       = $request->phone;
            $user->profileType = 'company';

            // Handle profile picture upload if provided
            if ($request->hasFile('profile')) {
                $profilePicture       = $request->file('profile');
                $folder_name          = 'images/profile';
                $profileUrl           = $profilePicture->store($folder_name, 'public');
                $user->profilePicture = $profileUrl;
            }

            // Save the user
            $user->save();

            // Retrieve or create the company linked to the user
            $company = Company::firstOrNew(['userId' => $user->id]);

            // Update company details
            $company->companyName = $request->name;
            $company->companyAdd  = $request->address;
            $company->city        = $request->city;
            $company->state       = $request->state;
            $company->countryId   = $request->country;
            $company->zipCode     = $request->zip;
            $company->websiteUrl  = ($request->url != '') ? $request->url : '';
            $company->chatSupport = 'chatbot';

            // Save the company
            $company->save();
            $companyData = Company::companyDetails($company->id);

            // Return success response with company details
            return response()->json([
                'status'     => true,
                'message'    => 'Company saved successfully.',
                'data'       => $companyData,
                'company_id' => $company->id,
            ], 201);

        } catch (Exception $e) {
            // Handle any errors and return error response
            return response()->json([
                'status'  => false,
                'message' => 'An error occurred while saving the company.',
                'error'   => $e->getMessage(),
            ], 500);
        }
    }

    public function LoadBusiness(Request $request)
    {

        try {
            $companies = Company::select('company.id', 'company.userId as id', 'companyName as UserName',
                'users.profilePicture', 'companyAdd',
                'city', 'state', 'countryId', 'zipCode',
                'websiteUrl', 'chatSupport', 'users.last_activity', 'users.updated_at as last_update_date', 'users.last_seen'
            )
                ->join('users', 'company.userId', '=', 'users.id')
                ->where('users.profileType', 'company')
                ->where('users.verificationStatus', 'verified')
                ->get();
            // Append full URL to profilePicture
            $companies->transform(function ($company) {

                $lastActivity       = $company->last_activity ?? $company->last_update_date;
                $lastSeen           = $company->last_seen     ?? $company->created_at;
                $onlineThreshold    = now()->subMinutes(5); // Set your online threshold here
                $isOnline           = Carbon::parse($lastActivity)->greaterThan($onlineThreshold);
                $company->is_online = $isOnline;
                $company->last_seen = Carbon::parse($lastSeen)->diffForHumans();

                $company->profilePicture = url('/storage/'.$company->profilePicture);

                return $company;
            });

            if ($companies->isNotEmpty()) {
                return response()->json(['status' => true, 'data' => $companies]);
            } else {
                return response()->json(['status' => false, 'message' => 'no record found']);
            }
        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }

    }

    // public function LoadBusiness(Request $request)
    // {

    //     try {
    //         $companies = Company::select('company.id', 'company.userId as id', 'companyName as UserName', 'users.profilePicture', 'companyAdd', 'city', 'state', 'countryId', 'zipCode', 'websiteUrl', 'chatSupport', 'bots.chatbot_project_id as botID')
    //             ->join('users', 'company.userId', '=', 'users.id')
    //             ->leftJoin('bots', 'bots.company_id', '=', 'company.id')
    //             ->get();

    //         // Append full URL to profilePicture
    //         $companies->transform(function ($company) {
    //             $company->profilePicture = url('/storage/'.$company->profilePicture);
    //             $company->botID          = base64_encode($company->botID);

    //             return $company;
    //         });

    //         if ($companies->isNotEmpty()) {
    //             return response()->json(['status' => true, 'data' => $companies]);
    //         } else {
    //             return response()->json(['status' => false, 'message' => 'no record found']);
    //         }
    //     } catch (Exception $e) {
    //         return response()->json(['status' => false, 'message' => $e->getMessage()]);
    //     }

    // }

    // public function ShowBusiness(Request $request)
    // {

    //     try {
    //         $companies = Company::select('company.id', 'company.userId', 'company.catId', 'category.catName', 'company.countryId', 'company.companyName', 'company.companyAdd', 'company.city', 'company.state', 'company.zipCode', 'company.websiteUrl', 'company.chatSupport', 'company.created_at', 'company.updated_at')
    //             ->join('category', 'company.catId', '=', 'category.catId')
    //             ->get();

    //         if ($companies->isNotEmpty()) {
    //             return response()->json(['status' => true, 'data' => $companies]);
    //         } else {
    //             return response()->json(['status' => false, 'message' => 'no record found']);
    //         }
    //     } catch (Exception $e) {
    //         return response()->json(['status' => false, 'message' => $e->getMessage()]);
    //     }

    // }

    public function ShowBusiness(Request $request)
    {

        $validator = Validator::make($request->all(), []);
        $validator->addRules([
            'company_id' => [
                'required',
                'exists:company,id',
            ],
        ]);
        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $companies = Company::select('company.id', 'company.userId as id', 'companyName as UserName',
                'users.profilePicture', 'companyAdd',
                'city', 'state', 'countryId', 'zipCode',
                'websiteUrl', 'chatSupport', 'users.last_activity', 'users.updated_at as last_update_date', 'users.last_seen'
            )
                ->join('users', 'company.userId', '=', 'users.id')
                ->where('users.profileType', 'company')
                ->where('users.verificationStatus', 'verified')
                ->where('company.id', $request->company_id)
                ->get();
            // Append full URL to profilePicture
            $companies->transform(function ($company) {

                $lastActivity       = $company->last_activity ?? $company->last_update_date;
                $lastSeen           = $company->last_seen     ?? $company->created_at;
                $onlineThreshold    = now()->subMinutes(5); // Set your online threshold here
                $isOnline           = Carbon::parse($lastActivity)->greaterThan($onlineThreshold);
                $company->is_online = $isOnline;
                $company->last_seen = Carbon::parse($lastSeen)->diffForHumans();

                $company->profilePicture = url('/storage/'.$company->profilePicture);

                return $company;
            });

            if ($companies->isNotEmpty()) {
                return response()->json(['status' => true, 'data' => $companies]);
            } else {
                return response()->json(['status' => false, 'message' => 'no record found']);
            }
        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }

    }

    public function ShowBusinessCategoryBased(Request $request)
    {

        // Define validation rules and custom messages
        $validator = Validator::make($request->all(), [
            'catname' => 'required',
        ], [
            'id.required' => 'The category name is required.',
        ]);

        // Check if validation fails
        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'errors' => $validator->errors(),
            ], 400);
        }

        try {
            $catname = $request->input('catname');

            if (empty(trim($catname))) {
                return response()->json(['status' => 1, 'data' => []]);
            }

            $companies = DB::table('company as c')
                ->select('c.id', 'c.userId', 'c.catId', 'ca.catName', 'c.countryId', 'c.companyName', 'c.companyAdd', 'c.city', 'users.profilePicture', 'c.state', 'c.zipCode', 'c.websiteUrl', 'c.chatSupport', 'c.created_at', 'c.updated_at', 'bots.chatbot_project_id as botID')
                ->join('category as ca', 'c.catId', '=', 'ca.catId')
                ->join('users', 'c.userId', '=', 'users.id')
                ->leftJoin('bots', 'bots.company_id', '=', 'c.id')
                ->where('ca.catName', $catname)
                ->get();

            $companies->transform(function ($company) {
                $company->profilePicture = url('/storage/'.$company->profilePicture);
                $company->botID          = base64_encode($company->botID);

                return $company;
            });

            if ($companies->isNotEmpty()) {
                return response()->json(['status' => true, 'data' => $companies]);
            } else {
                return response()->json(['status' => false, 'message' => 'no record found']);
            }
        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }
    }
}
