<?php

namespace App\Models;

use App\Events\NotificationEvent;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notifications extends Model
{
    use HasFactory;

    protected $table = 'notifications';

    public static function saveNotifications($sender_id, $receiver_id, $message, $route, $slug)
    {
        $exists = Notifications::where('receiver_id', $receiver_id)
        ->where('slug', $slug)
        ->where('message', $message)
        ->where('route', $route)
        ->exists();

        if (!$exists) {
            $saveQry              = new Notifications;
            $saveQry->user_id     = $sender_id;
            $saveQry->receiver_id = $receiver_id;
            $saveQry->message     = $message;
            $saveQry->route       = $route;
            $saveQry->slug        = $slug;
            $saveQry->save();
        }
    }

    public static function updateStatus($slug)
    {
        // Notifications::where('slug',$slug)->update(['status'=>'seen']);
        Notifications::where('slug', $slug)->delete();
        $totalCount = Notifications::count();
        if ($totalCount == 0) {
            Notifications::truncate();
        }
        $allBrodcast = ['status' => 1];
        broadcast(new NotificationEvent($allBrodcast))->toOthers();
    }

    public static function removeNotification($route, $slug)
    {
        // Update status to 'seen' instead of deleting
        Notifications::where('slug', $slug)
            ->where('route', $route)
            ->update(['status' => 'seen']);

        // Broadcast the notification update event
        $allBrodcast = ['status' => 1];
        broadcast(new NotificationEvent($allBrodcast))->toOthers();
    }

}
