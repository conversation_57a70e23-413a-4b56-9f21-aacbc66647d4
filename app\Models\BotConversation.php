<?php

namespace App\Models;

use App\Events\NotificationEvent;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class BotConversation extends Model
{
    use HasFactory;

    protected $table = 'botconversation';

    protected $fillable = [
        'user_id',
        'bot_id',
        'company_user_id',
        'lastmessage',
        'status',
        'agent_id',
        'last_msg_type',
        'notification_msg',
        'status_ticket',
    ];

    //    public static function checkNotification($userid)
    //    {
    //        $lastMesage = BotConversation::join('users', 'users.id', 'botconversation.user_id')
    //            ->where('botconversation.company_user_id', $userid)
    //            ->where('botconversation.user_id', Auth::id())
    //            ->where('botconversation.status', 0)
    //            ->select('botconversation.*', 'users.email', 'users.name', 'users.profilePicture')
    //            ->orderBy('botconversation.id', 'desc')
    //            ->first();
    //
    //        if ($lastMesage) {
    //
    //            $botId  = $lastMesage->bot_id;
    //            $botQry = Bots::find($botId);
    //            if ($botQry) {
    //
    //                $message = '<strong>'.ucfirst(Auth::user()->name).'</strong> sent you message';
    //                $source  = base64_encode($botQry->chatbot_project_id);
    //                $encodId = base64_encode(Auth::id());
    //                $route   = 'chats?source='.$source.'&tab=active&umid='.$encodId;
    //                $route   = 'chats';
    //
    //                $allAgentList = User::where('parent_id', $userid)->get();
    //                if (count($allAgentList) > 0) {
    //                    foreach ($allAgentList as $agnetID) {
    //                        Notifications::saveNotifications(Auth::id(), $agnetID->id, $message, $route, 'chats');
    //                    }
    //                }
    //
    //                $allBrodcast = ['status' => 1];
    //                broadcast(new NotificationEvent($allBrodcast))->toOthers();
    //
    //            }
    //
    //        }
    //
    //    }

    public static function checkNotification($userid)
    {
        $lastMesage = BotConversation::join('users', 'users.id', 'botconversation.user_id')
            ->join('company', 'company.userId', '=', 'botconversation.company_user_id')
            ->where('botconversation.company_user_id', $userid)
            ->where('botconversation.user_id', Auth::id())
            ->where('botconversation.status', 0)
            ->select('botconversation.*', 'users.email', 'users.name', 'users.profilePicture', 'company.id as company_id')
            ->orderBy('botconversation.id', 'desc')
            ->first();

        if ($lastMesage) {

            $message = '<strong>'.ucfirst(Auth::user()->name).'</strong> sent you message';
            $source  = $lastMesage->company_user_id.'_'.$lastMesage->company_id;
            $encodId = base64_encode(Auth::id());
            $route   = 'chats?source='.$source.'&tab=active&umid='.$encodId;
            $route   = 'chats';

            $allAgentList = User::where('parent_id', $userid)->get();
            if (count($allAgentList) > 0) {
                foreach ($allAgentList as $agnetID) {
                    Notifications::saveNotifications(Auth::id(), $agnetID->id, $message, $route, 'chats');
                }
            }

            $allBrodcast = ['status' => 1];
            broadcast(new NotificationEvent($allBrodcast))->toOthers();

        }

    }

    public function getDialogflowlastMsg($tableName, $database, $agentType, $userid)
    {

        $path         = $tableName.'/user_id_'.$userid.'/agent_'.$agentType;
        $userChatsRef = $database->getReference($path);
        $chats        = $userChatsRef->getValue();
        $lastMessage  = '';

        if ($chats) {
            $chats = array_filter($chats, function ($chat) {
                return is_array($chat) && isset($chat['timestamp']);
            });

            // Get the last message
            $lastMessage = end($chats);
        }

        return $lastMessage;

    }

    public static function getBotConversaction($ownerID)
    {

        return BotConversation::where('botconversation.company_user_id', $ownerID)
            ->join('users', 'users.id', '=', 'botconversation.user_id')
            ->join('company', 'company.userId', '=', 'botconversation.company_user_id')
            ->leftJoin('users as agent', 'agent.id', '=', 'botconversation.agent_id')
            ->select('botconversation.status', 'botconversation.created_at', 'botconversation.updated_at',
                'botconversation.last_msg_type', 'botconversation.user_id',
                'botconversation.lastmessage', 'botconversation.agent_id',
                'users.name', 'users.profilePicture', 'users.phone',
                'agent.name as agent_name', 'users.email', 'company.id as company_id', 'botconversation.company_user_id','botconversation.notification_msg')
            ->orderBy('updated_at', 'desc')
            ->get();

    }

    //    public static function getBotConversaction($ownerID)
    //    {
    //
    //         return BotConversation::where('botconversation.company_user_id', $ownerID)
    //             ->join('users', 'users.id', '=', 'botconversation.user_id')
    //             ->join('bots', 'bots.id', '=', 'botconversation.bot_id')
    //             ->leftJoin('users as agent', 'agent.id', '=', 'botconversation.agent_id')
    //             ->select('botconversation.status', 'botconversation.bot_id',
    //                 'botconversation.created_at', 'botconversation.updated_at',
    //                 'botconversation.last_msg_type', 'botconversation.user_id',
    //                 'botconversation.lastmessage', 'botconversation.agent_id',
    //                 'users.name', 'users.profilePicture', 'users.phone',
    //                 'agent.name as agent_name',
    //                 'users.email', 'bots.chatbot_name', 'bots.chatbot_project_id')
    //             ->orderBy('updated_at', 'desc')
    //             ->get();
    //    }

    public static function getBotConversactionUserSide($userID)
    {

        return BotConversation::where('botconversation.user_id', $userID)
            ->join('users', 'users.id', '=', 'botconversation.user_id')
            ->join('company', 'company.userId', '=', 'botconversation.company_user_id')
            ->leftJoin('users as agent', 'agent.id', '=', 'botconversation.agent_id')
            ->select('botconversation.status', 'botconversation.bot_id',
                'botconversation.created_at', 'botconversation.updated_at',
                'botconversation.last_msg_type', 'botconversation.user_id',
                'botconversation.lastmessage', 'botconversation.agent_id',
                'users.name', 'users.profilePicture', 'users.phone',
                'agent.name as agent_name', 'users.email', 'company.id as company_id', 'botconversation.company_user_id')
            ->orderBy('updated_at', 'desc')
            ->get();

        // return BotConversation::where('botconversation.user_id', $userID)
        //     ->join('users', 'users.id', '=', 'botconversation.user_id')
        //     ->leftJoin('bots', 'bots.id', '=', 'botconversation.bot_id')
        //     ->leftJoin('users as agent', 'agent.id', '=', 'botconversation.agent_id')
        //     ->select('botconversation.status', 'botconversation.bot_id',
        //         'botconversation.created_at', 'botconversation.updated_at',
        //         'botconversation.last_msg_type', 'botconversation.user_id',
        //         'botconversation.lastmessage', 'botconversation.agent_id',
        //         'users.name', 'users.profilePicture', 'users.phone',
        //         'agent.name as agent_name',
        //         'users.email', 'bots.chatbot_name', 'bots.chatbot_project_id','botconversation.company_user_id')
        //     ->orderBy('updated_at', 'desc')
        //     ->get();
    }
}
