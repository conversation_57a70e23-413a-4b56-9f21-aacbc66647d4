<?php

namespace App\Actions;

use App\Models\Complaint;

class UpdateComplaintAction
{
    public function handle(Complaint $complaint, array $data): Complaint
    {
        $complaint->update([
            'complainTitle' => $data['title'],
            'complainDetail' => $data['description'],
            'priority' => $data['priority'],
            'companyId' => $data['company_id'],
            'assignedToUserId' => $data['assigned_user_id'],
            'progressStatus' => $data['status'] ?? $complaint->progressStatus,
            'complainStatus' => ($data['status'] == 'resolved') ? 'close' : 'open',
        ]);

        return $complaint;
    }
}
