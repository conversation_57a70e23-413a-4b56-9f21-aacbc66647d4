<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CbVendorApi extends Model
{
    use SoftDeletes;

    protected $table = 'cb_vendor_api';

    protected $fillable = ['user_id', 'name', 'url', 'method', 'request_parameters', 'response_parameters', 'description'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public static function checkorCreateDefaultApi($userID)
    {

        $checkData = CbVendorApi::where('user_id', $userID)->where('name', '@apiauth-get_api_token')->first();
        if (! $checkData) {
            $saveData = [
                'user_id'             => $userID,
                'name'                => '@apiauth-get_api_token',
                'url'                 => '',
                'method'              => 'POST',
                'request_parameters'  => '[]',
                'response_parameters' => '[]',
                'description'         => '',
            ];
            CbVendorApi::create($saveData);
        }

    }
}
