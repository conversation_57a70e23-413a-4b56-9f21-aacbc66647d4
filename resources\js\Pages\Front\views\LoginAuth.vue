<script setup>
import { ref } from "vue";
import { Head, usePage } from "@inertiajs/vue3";

const pageTitle = ref("Login");
const page = usePage();

// Reactive form data
const email = ref("");
const password = ref("");
const client_id = ref(page.props.client_id || "");
const redirect_uri = ref(page.props.redirect_uri || "");
const vuseridData = ref(page.props.vuserid || "");
const errorMessage = ref("");
const isLoading = ref(false);

async function submitLogin() {
    isLoading.value = true;
    try {
        const response = await fetch("/api/oauth/login", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
                email: email.value,
                password: password.value,
                client_id: client_id.value,
                redirect_uri: redirect_uri.value,
                vuserid:vuseridData.value
            }),
        });

        const data = await response.json();

        if (data.code) {
            // Send auth code back to parent window (client site)
            window.opener.postMessage({ code: data.code, client_id: client_id.value }, redirect_uri.value);
            window.close();
        } else {
            errorMessage.value = data.error || "Invalid login.";
        }
    } catch (error) {
        console.error("Login error:", error);
        errorMessage.value = "Something went wrong. Please try again.";
    } finally {
        isLoading.value = false;
    }
}
</script>

<template>
    <Head :title="pageTitle" />

    <div class="flex items-center justify-center min-h-screen bg-gray-100">
        <div class="bg-white p-6 rounded-lg shadow-lg w-96">
            <h2 class="text-2xl font-bold text-center text-gray-700 mb-4">Login to Chatfil</h2>

            <form @submit.prevent="submitLogin" class="space-y-4">
                <input type="hidden" v-model="client_id">
                <input type="hidden" v-model="redirect_uri">

                <div>
                    <label class="block text-gray-600 font-medium">Email:</label>
                    <input
                        type="email"
                        v-model="email"
                        required
                        class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                </div>

                <div>
                    <label class="block text-gray-600 font-medium">Password:</label>
                    <input
                        type="password"
                        v-model="password"
                        required
                        class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                </div>

                <button 
                    type="submit" 
                    class="w-full max-md:w-auto dk-update-btn" 
                    :disabled="isLoading"
                >
                    {{ isLoading ? 'Loading...' : 'Login' }}
                </button>

                <p v-if="errorMessage" class="text-red-500 text-center">{{ errorMessage }}</p>
            </form>
        </div>
    </div>
</template>
