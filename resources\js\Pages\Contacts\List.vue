<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, usePage } from '@inertiajs/vue3';
import { ref } from 'vue';
import ContactsTable from '@/Components/Tables/ContactsTable.vue';
import BreadcrumbDefault from '@/Components/Breadcrumbs/BreadcrumbDefault.vue'
const pageTitle = ref('Contact Inquiry List');

const { allContacts } = usePage().props;
</script>

<template>

  <Head :title="pageTitle" />

  <AuthenticatedLayout>

    <ContactsTable :ContactsList="allContacts" />

  </AuthenticatedLayout>
</template>
