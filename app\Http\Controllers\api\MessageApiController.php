<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class MessageApiController extends Controller
{
    public function loadLastMessages(Request $request)
    {
        try {

            $userID = Auth::id();
            $user   = User::where('id', $userID)->first();

            if ($user) {

                $userdata = User::getUserDetail($user);

                return response()->json([
                    'status'  => true,
                    'data'    => $userdata,
                    'message' => 'Last messages retrieved successfully.',
                ]);
            } else {
                return response()->json([
                    'status'  => false,
                    'message' => 'User not found or no messages available.',
                ]);
            }
        } catch (Exception $e) {
            return response()->json([
                'status'  => false,
                'message' => 'An error occurred while retrieving messages.',
                'error'   => $e->getMessage(),
            ]);
        }
    }

    public function saveLastMessage(Request $request)
    {
        // Custom validation messages
        $messages = [
            'message.required' => 'The message field is required.',
            'message.string'   => 'The message must be a string.',
            'message.max'      => 'The message may not be greater than 255 characters.',
        ];

        // Validation rules
        $validator = Validator::make($request->all(), [
            'message' => 'required|string|max:255',
        ], $messages);

        // Check if validation fails
        if ($validator->fails()) {
            return response()->json([
                'status'  => false,
                'message' => 'Validation failed.',
                'errors'  => $validator->errors(),
            ], 422);
        }

        try {
            // Get the authenticated user's ID
            $userID = Auth::id();
            $user   = User::where('id', $userID)->first();

            if ($user) {
                // Save the last message
                $user->LastMessage = $request->message;
                $user->save();

                return response()->json([
                    'status'  => true,
                    'message' => 'Last message saved successfully.',
                ]);
            } else {
                return response()->json([
                    'status'  => false,
                    'message' => 'User not found. Message not saved.',
                ]);
            }
        } catch (Exception $e) {
            return response()->json([
                'status'  => false,
                'message' => 'An error occurred while saving the message.',
                'error'   => $e->getMessage(),
            ]);
        }
    }
}
