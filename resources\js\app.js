import "./bootstrap";
import "../css/app.css";

import { createApp, h } from "vue";
import { createInertiaApp } from "@inertiajs/vue3";
import { resolvePageComponent } from "laravel-vite-plugin/inertia-helpers";
import { ZiggyVue } from "../../vendor/tightenco/ziggy";

const appName = import.meta.env.VITE_APP_NAME || "Laravel";

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) =>
        resolvePageComponent(
            `./Pages/${name}.vue`,
            import.meta.glob("./Pages/**/*.vue")
        ),
    setup({ el, App, props, plugin }) {
        return createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(ZiggyVue)
            .mount(el);
    },
    progress: {
        color: "#4B5563",
    },
});

const appTheme = localStorage.getItem("theme");
// Check what is the active theme and change theme when user clicks on the theme button in header.
if (appTheme === "dark") {
    document.querySelector("body").classList.add("bg-cfp-500-dark");
    document.querySelector("body").classList.add("dark");
    document.querySelector("body").classList.remove("bg-secondary-light");
} else {
    document.querySelector("body").classList.remove("dark");
    document.querySelector("body").classList.add("bg-secondary-light");
    document.querySelector("body").classList.remove("bg-cfp-500-dark");
}
