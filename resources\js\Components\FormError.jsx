import React from 'react';

export default function FormError({ error }) {
    if (!error) return null;

    if (Array.isArray(error)) {
        return (
            <div className='bg-red-100 p-2'>
                <ul className="text-red-600 text-sm list-disc pl-5 rounded">
                    {error.map((e, i) => <li key={i}>{e}</li>)}
                </ul>
            </div>
        );
    }
    return <div className="bg-red-100 text-red-500 text-sm p-2 rounded">{error}</div>;
} 