---
type: "always_apply"
---

# Subscription Plan Module

1. I want to integrate subsciption module with following plans and include 7 days trial in all plans:
    - Monthly 10 USD Subscription
    - Quartly with 10% discount
    - Half Quartly plan with 20% discount
    - Yearly plan with 30% discount

2. All plans and trial can be managed from Admin panel and admin can manage pricing and it's features and number of days for trial from admin area.

3. I have create some database schema as per my undestanding please review them and if needed please update schema and make it scalable. If Needed create new table(s) as well.
    1. `subscription_plans` Table:
        - `id`
        - `name` 
        - `price` 
        - `billing_interval` 
        - `features` (json data of plan details which we can show in pricing table what includes)
        - `is_active` 
        - `mode` (enum: test/live)

    2. Create `subscriptions` Table:
        - `id`
        - `user_id`
        - `subscription_plan_id`
        - `subscription_id`
        - `status` 
        - `ends_at` 
        - `payment_method_json`

4. Update required relationships for each tables. Where need please do the needful changes.

5. For now, I want to use Stripe and Paypal as payment methods for my subscriptions management, I may add other payment gateway as needed. Please create payment Admin settings page to intigrate Stripe and Paypal gateways i.e. Add Test and Live keys, webhook urls if there is any. What I am thinking to inculde here:
    - Create admin settings with all required inputs.
    - Add/edit keys for both test and live mode
    - Switch between test/production mode
    - Restrict access to admin only

6. Create pricing frontend page for Company role, once signup process is completed they need to signup for a plan to access the dashboard and start using the app.

7. Create payments and invoice related pages i.e. Payment History, Invoice listing with print and download invoice option, and subscription informations. Create other frontend and admin necessary pages to manage all SaaS features.

8. Create reports page for admin users. Add all reports with graphs where needed.

9. Make frontend changes to align all these features.

10. Platform is free for end-users.
