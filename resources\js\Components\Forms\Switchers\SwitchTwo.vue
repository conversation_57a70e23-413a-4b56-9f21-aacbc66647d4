<script setup lang="ts">
import { ref } from 'vue'

const switcherToggle = ref<boolean>(false)
</script>

<template>
  <div>
    <label for="toggle2" class="flex items-center cursor-pointer select-none">
      <div class="relative">
        <input id="toggle2" type="checkbox" class="sr-only" @change="switcherToggle = !switcherToggle" />
        <div class="bg-meta-9 shadow-inner rounded-full w-14 h-5"></div>
        <div :class="switcherToggle && '!right-0 !translate-x-full !bg-cfp-500 '"
          class="-top-1 left-0 absolute bg-white shadow-switch-1 rounded-full w-7 h-7 transition dot"></div>
      </div>
    </label>
  </div>
</template>
