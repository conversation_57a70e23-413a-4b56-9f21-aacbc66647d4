<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\Complaint;
use App\Models\ComplaintMessage;
use App\Models\User;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('Creating users...');

        // Create 100 users
        $users = User::factory(100)->create();

        // Create a test admin user
        $adminUser = User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'profileType' => 'admin',
            'verificationStatus' => true,
            'status' => true,
        ]);

        $this->command->info('Created ' . ($users->count() + 1) . ' users');

        $this->command->info('Creating companies...');

        // Create 100 companies, each assigned to a random user
        $companies = Company::factory(100)->create([
            'userId' => function () use ($users, $adminUser) {
                return collect($users)->push($adminUser)->random()->id;
            }
        ]);

        $this->command->info('Created ' . $companies->count() . ' companies');

        $this->command->info('Creating complaints...');

        // Create 1000 complaints
        $complaints = Complaint::factory(1000)->create([
            'companyId' => function () use ($companies) {
                return $companies->random()->id;
            },
            'assignedToUserId' => function () use ($users, $adminUser) {
                return collect($users)->push($adminUser)->random()->id;
            }
        ]);

        // Generate complaint IDs for each complaint
        $complaints->each(function ($complaint) {
            $currentYear = date('Y');
            $complaint->cmp_id = 'CP' . $currentYear . $complaint->id;
            $complaint->save();
        });

        $this->command->info('Created ' . $complaints->count() . ' complaints');

        $this->command->info('Creating complaint messages...');

        // Create 2-5 messages for each complaint
        $complaints->each(function ($complaint) use ($users, $adminUser) {
            $messageCount = rand(2, 5);
            ComplaintMessage::factory($messageCount)->create([
                'complainId' => $complaint->id,
                'senderId' => function () use ($users, $adminUser) {
                    return collect($users)->push($adminUser)->random()->id;
                }
            ]);
        });

        $totalMessages = ComplaintMessage::count();
        $this->command->info('Created ' . $totalMessages . ' complaint messages');

        $this->command->info('Creating subscription plans...');
        $this->call(SubscriptionPlanSeeder::class);

        $this->command->info('Creating payment gateways...');
        $this->call(PaymentGatewaySeeder::class);

        $this->command->info('Database seeding completed successfully!');
        $this->command->info('Summary:');
        $this->command->info('- Users: ' . User::count());
        $this->command->info('- Companies: ' . Company::count());
        $this->command->info('- Complaints: ' . Complaint::count());
        $this->command->info('- Messages: ' . ComplaintMessage::count());
    }
}
