<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Mail\SendMailData;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Laravel\Sanctum\HasApiTokens;
use PHPOpenSourceSaver\JWTAuth\Contracts\JWTSubject;

class User extends Authenticatable implements JWTSubject
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'company_name',
        'phone',
        'email',
        'password',
        'profileType',
        'verificationStatus',
        'status',
        'profilePicture',
        'LastMessage',
        'timeStamp',
        'otp',
        'parent_id',
        'vid',
        'uid',
        'last_activity',
        'last_seen',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password'          => 'hashed',
        'last_seen'         => 'datetime',
        'last_activity'     => 'datetime',
    ];

    public function messages()
    {
        return $this->hasMany(Message::class);
    }

    public function company()
    {
        return $this->hasOne(Company::class, 'userId', 'id');
    }

    // User.php
    public function complaints()
    {
        return $this->hasMany(Complaint::class, 'assignedToUserId');
    }

    public static function deleteRelationtoCompany($companyID)
    {

        $botIds = Bots::where('company_id', $companyID)->pluck('id')->toArray();

        // Retrieve all bot IDs associated with the company
        $botIds = Bots::where('company_id', $companyID)->pluck('id')->toArray();

        // Delete all bot conversations related to the retrieved bot IDs
        BotConversation::whereIn('bot_id', $botIds)->delete();

        // Delete all bots related to the company
        Bots::where('company_id', $companyID)->delete();

        // Finally, delete the company
        Company::where('id', $companyID)->delete();

    }

    public static function deleteUserRelationTable($userID)
    {

        $companyIds = Company::where('userId', $userID)->pluck('id');

        if ($companyIds->isNotEmpty()) {

            $complaintIds = Complaint::whereIn('companyId', $companyIds)->pluck('id');
            ComplaintMessages::whereIn('complainId', $complaintIds)->delete();
            Complaint::whereIn('id', $complaintIds)->delete();
        }

        $complaintIds = Complaint::where('assignedToUserId', $userID)->pluck('id');

        if ($complaintIds->isNotEmpty()) {
            ComplaintMessages::whereIn('complainId', $complaintIds)->delete();
            Complaint::whereIn('id', $complaintIds)
                ->where('assignedToUserId', $userID)
                ->delete();
        }

        $dealsData = Deals::join('company', 'company.id', '=', 'deals.companyId')->where('company.userId', $userID)->select('deals.id', 'deals.deal_file')->get();
        if (count($dealsData) > 0) {
            foreach ($dealsData as $dealDetail) {
                $dealID = $dealDetail->id;
                if ($dealDetail->deal_file != '') {
                    Storage::disk('public')->delete($dealDetail->deal_file);
                }
                Deals::where('id', $dealID)->delete();
            }
        }

        Company::where('userId', $userID)->delete();
        Bots::where('user_id', $userID)->delete();
        BotConversation::where('user_id', $userID)->delete();
        ComplaintMessages::where('senderId', $userID)->delete();
        Complaint::where('assignedToUserId', $userID)->delete();
        DB::table('message')->where('senderId', $userID)->delete();
        DB::table('person')->where('userId', $userID)->delete();
        $userDetail = User::find($userID);
        if ($userDetail->profilePicture != '') {
            Storage::disk('public')->delete($userDetail->profilePicture);
        }

    }

    public static function sendActiveAgentMail($token, $email)
    {

        $activeUrl = url('agent-account/?emailAddress='.urlencode($email).'&identity='.urlencode($token));

        $mailData = [
            'activeUrl' => $activeUrl,
            'subject'   => 'Activate Your Account',
        ];

        Mail::to($email)->send(new SendMailData($mailData));

    }

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key-value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

    public static function getUserDetail($user)
    {

        $profileType = strtolower($user->profileType);

        $first_Name  = '';
        $last_name   = '';
        $companyName = '';

        $profile = Person::where('userId', $user->id)->first();
        if ($profile) {
            $first_Name = $profile->firstName;
            $last_name  = $profile->lastName;
        }

        $companyDetail = Company::where('userId', $user->id)->first();
        if ($companyDetail) {
            $companyName = $companyDetail->companyName;
        } else {

            $companyName = ($user->company_name != '') ? $user->company_name : '';
        }

        $onlineThreshold = now()->subMinutes(5); // Set your online threshold here
        $isOnline        = $user->last_activity && Carbon::parse($user->last_activity)->greaterThan($onlineThreshold);

        $responceData = [
            'id'                 => $user->id,
            'name'               => $user->name,
            'firstName'          => ($first_Name != '') ? $first_Name : $user->name,
            'lastName'           => $last_name,
            'company_name'       => ($companyName != '') ? $companyName : '',
            'phone'              => $user->phone,
            'email'              => $user->email,
            'verificationStatus' => $user->verificationStatus,
            'status'             => ($user->status != '') ? $user->status : '',
            'profilePicture'     => ($user->profilePicture != '') ? url('/storage/'.$user->profilePicture) : '',
            'parent_id'          => ($user->parent_id != '') ? $user->parent_id : '',
            'role'               => $profileType,
            'is_online'          => $isOnline,
            'last_seen'          => $user->last_seen ? $user->last_seen->diffForHumans() : 'never',
        ];

        return $responceData;

    }

    public static function checkRolePermission($uid, $requiredRole)
    {
        $userDetail = User::find($uid);

        if ($userDetail) {
            if ($userDetail->profileType == $requiredRole) {
                return true;
            } else {
                return response()->json([
                    'status'  => false,
                    'message' => 'Invalid role authentication',
                ], 403); // 403 Forbidden
            }
        } else {
            return response()->json([
                'status'  => false,
                'message' => 'Invalid authentication',
            ], 401); // 401 Unauthorized
        }
    }

    public static function getUserStatus($id)
    {

        $user = User::find($id);

        if (! $user) {
            return response()->json([
                'status'  => false,
                'message' => 'User not found',
            ]);
        }

        // Determine if the user is online
        $onlineThreshold = now()->subMinutes(5); // Set your online threshold here
        $isOnline        = $user->last_activity && Carbon::parse($user->last_activity)->greaterThan($onlineThreshold);

        return response()->json([
            'status'    => true,
            'user_id'   => $user->id,
            'is_online' => $isOnline,
            'last_seen' => $user->last_seen ? $user->last_seen->diffForHumans() : 'never',
        ]);

    }

    public function getProfilePictureUrlAttribute()
    {
        return url('/storage/'.$this->profilePicture);
    }
}
