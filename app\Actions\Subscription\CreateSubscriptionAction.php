<?php

namespace App\Actions\Subscription;

use App\Models\Invoice;
use App\Models\PaymentGateway;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Srmklive\PayPal\Services\PayPal as PayPalClient;

class CreateSubscriptionAction
{
    /**
     * Create a new subscription for a user.
     */
    public function handle(
        User $user,
        SubscriptionPlan $plan,
        string $paymentMethod,
        array $paymentMethodDetails = []
    ): Subscription {
        return DB::transaction(function () use ($user, $plan, $paymentMethod, $paymentMethodDetails) {
            // Check if user already has an active subscription
            $existingSubscription = $user->subscriptions()
                ->whereIn('status', ['active', 'trial'])
                ->first();

            if ($existingSubscription) {
                throw new \Exception('User already has an active subscription.');
            }

            // Validate payment gateway
            $gateway = PaymentGateway::where('name', $paymentMethod)
                ->where('is_active', true)
                ->first();

            if (!$gateway || !$gateway->isConfigured()) {
                throw new \Exception("Payment gateway {$paymentMethod} is not available.");
            }

            // Calculate trial and billing dates
            $trialEndsAt = now()->addDays($plan->trial_days);
            $currentPeriodStart = $trialEndsAt;
            $currentPeriodEnd = $this->calculatePeriodEnd($currentPeriodStart, $plan);

            // Create subscription
            $subscription = Subscription::create([
                'user_id' => $user->id,
                'subscription_plan_id' => $plan->id,
                'status' => 'trial',
                'trial_ends_at' => $trialEndsAt,
                'current_period_start' => $currentPeriodStart,
                'current_period_end' => $currentPeriodEnd,
                'payment_method' => $paymentMethod,
                'payment_method_details' => $paymentMethodDetails,
            ]);

            // Create gateway subscription based on payment method
            $this->createGatewaySubscription($subscription, $gateway);

            // Create initial invoice for the subscription
            $this->createInitialInvoice($subscription);

            return $subscription;
        });
    }

    /**
     * Calculate the end date for a billing period.
     */
    private function calculatePeriodEnd(Carbon $start, SubscriptionPlan $plan): Carbon
    {
        return match ($plan->billing_interval) {
            'monthly' => $start->copy()->addMonths($plan->billing_interval_count),
            'quarterly' => $start->copy()->addMonths(3 * $plan->billing_interval_count),
            'semi_annual' => $start->copy()->addMonths(6 * $plan->billing_interval_count),
            'annual' => $start->copy()->addYears($plan->billing_interval_count),
            default => $start->copy()->addMonth(),
        };
    }

    /**
     * Create subscription in the payment gateway.
     */
    private function createGatewaySubscription(Subscription $subscription, PaymentGateway $gateway): void
    {
        try {
            if ($gateway->name === 'stripe') {
                $this->createStripeSubscription($subscription, $gateway);
            } elseif ($gateway->name === 'paypal') {
                $this->createPayPalSubscription($subscription, $gateway);
            }
        } catch (\Exception $e) {
            // Log the error but don't fail the subscription creation
            // The subscription can be manually activated later
            Log::error('Failed to create gateway subscription', [
                'subscription_id' => $subscription->id,
                'gateway' => $gateway->name,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Create Stripe subscription.
     */
    private function createStripeSubscription(Subscription $subscription, PaymentGateway $gateway): void
    {
        $config = $gateway->getCurrentConfig();
        \Stripe\Stripe::setApiKey($config['secret_key']);

        $stripeSubscription = \Stripe\Subscription::create([
            'customer' => $this->getOrCreateStripeCustomer($subscription->user, $config),
            'items' => [
                ['price' => $subscription->plan->stripe_price_id],
            ],
            'trial_end' => $subscription->trial_ends_at->timestamp,
            'metadata' => [
                'subscription_id' => $subscription->id,
                'user_id' => $subscription->user_id,
            ],
        ]);

        $subscription->update([
            'stripe_subscription_id' => $stripeSubscription->id,
        ]);
    }

    /**
     * Create PayPal subscription.
     */
    private function createPayPalSubscription(Subscription $subscription, PaymentGateway $gateway): void
    {
        try {
            $config = $gateway->getCurrentConfig();

            $paypal = new PayPalClient();
            $paypal->setApiCredentials([
                'mode' => $gateway->test_mode ? 'sandbox' : 'live',
                'sandbox' => [
                    'client_id' => $config['client_id'] ?? '',
                    'client_secret' => $config['client_secret'] ?? '',
                ],
                'live' => [
                    'client_id' => $config['client_id'] ?? '',
                    'client_secret' => $config['client_secret'] ?? '',
                ],
            ]);

            $accessToken = $paypal->getAccessToken();

            if (!$accessToken) {
                throw new \Exception('Failed to get PayPal access token');
            }

            // Create subscription plan in PayPal if it doesn't exist
            $planId = $this->getOrCreatePayPalPlan($subscription->subscriptionPlan, $paypal);

            // Create subscription
            $subscriptionData = [
                'plan_id' => $planId,
                'custom_id' => $subscription->id,
                'application_context' => [
                    'brand_name' => config('app.name'),
                    'return_url' => route('company.subscription.dashboard'),
                    'cancel_url' => route('company.subscription.dashboard'),
                ],
                'subscriber' => [
                    'email_address' => $subscription->user->email,
                    'name' => [
                        'given_name' => explode(' ', $subscription->user->name)[0] ?? '',
                        'surname' => explode(' ', $subscription->user->name)[1] ?? '',
                    ],
                ],
            ];

            $paypalSubscription = $paypal->createSubscription($subscriptionData);

            if (isset($paypalSubscription['id'])) {
                $subscription->update([
                    'paypal_subscription_id' => $paypalSubscription['id'],
                    'status' => 'pending',
                ]);
            } else {
                throw new \Exception('Failed to create PayPal subscription');
            }

        } catch (\Exception $e) {
            Log::error('PayPal subscription creation failed', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Get or create PayPal plan.
     */
    private function getOrCreatePayPalPlan(SubscriptionPlan $plan, PayPalClient $paypal): string
    {
        // Check if plan already has PayPal plan ID
        if ($plan->paypal_plan_id) {
            return $plan->paypal_plan_id;
        }

        // Create new PayPal plan
        $planData = [
            'product_id' => $this->getOrCreatePayPalProduct($plan, $paypal),
            'name' => $plan->name,
            'description' => $plan->description ?? "Subscription plan: {$plan->name}",
            'status' => 'ACTIVE',
            'billing_cycles' => [
                [
                    'frequency' => [
                        'interval_unit' => strtoupper($plan->billing_interval),
                        'interval_count' => 1,
                    ],
                    'tenure_type' => 'REGULAR',
                    'sequence' => 1,
                    'total_cycles' => 0, // Infinite
                    'pricing_scheme' => [
                        'fixed_price' => [
                            'value' => number_format($plan->price, 2, '.', ''),
                            'currency_code' => 'USD',
                        ],
                    ],
                ],
            ],
            'payment_preferences' => [
                'auto_bill_outstanding' => true,
                'setup_fee_failure_action' => 'CONTINUE',
                'payment_failure_threshold' => 3,
            ],
        ];

        $paypalPlan = $paypal->createPlan($planData);

        if (isset($paypalPlan['id'])) {
            $plan->update(['paypal_plan_id' => $paypalPlan['id']]);
            return $paypalPlan['id'];
        }

        throw new \Exception('Failed to create PayPal plan');
    }

    /**
     * Get or create PayPal product.
     */
    private function getOrCreatePayPalProduct(SubscriptionPlan $plan, PayPalClient $paypal): string
    {
        // Check if plan already has PayPal product ID
        if ($plan->paypal_product_id) {
            return $plan->paypal_product_id;
        }

        // Create new PayPal product
        $productData = [
            'name' => $plan->name,
            'description' => $plan->description ?? "Product for subscription plan: {$plan->name}",
            'type' => 'SERVICE',
            'category' => 'SOFTWARE',
        ];

        $paypalProduct = $paypal->createProduct($productData);

        if (isset($paypalProduct['id'])) {
            $plan->update(['paypal_product_id' => $paypalProduct['id']]);
            return $paypalProduct['id'];
        }

        throw new \Exception('Failed to create PayPal product');
    }

    /**
     * Get or create Stripe customer.
     */
    private function getOrCreateStripeCustomer(User $user, array $config): string
    {
        // This would typically check if the user already has a Stripe customer ID
        // and create one if they don't

        try {
            $customer = \Stripe\Customer::create([
                'email' => $user->email,
                'name' => $user->name,
                'metadata' => [
                    'user_id' => $user->id,
                ],
            ]);

            return $customer->id;
        } catch (\Exception $e) {
            throw new \Exception('Failed to create Stripe customer: ' . $e->getMessage());
        }
    }

    /**
     * Create initial invoice for the subscription.
     */
    private function createInitialInvoice(Subscription $subscription): void
    {
        Invoice::create([
            'subscription_id' => $subscription->id,
            'user_id' => $subscription->user_id,
            'invoice_number' => Invoice::generateInvoiceNumber(),
            'status' => 'draft',
            'amount_due' => $subscription->plan->price,
            'amount_paid' => 0,
            'tax_amount' => 0,
            'currency' => 'USD',
            'billing_reason' => 'subscription_create',
            'due_date' => $subscription->trial_ends_at,
        ]);
    }
}
