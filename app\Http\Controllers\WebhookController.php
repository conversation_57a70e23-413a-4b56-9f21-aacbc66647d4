<?php

namespace App\Http\Controllers;

use App\Models\PaymentGateway;
use App\Models\Subscription;
use App\Models\Invoice;
use App\Models\PaymentTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Stripe\Webhook;
use Srmklive\PayPal\Services\PayPal as PayPalClient;

class WebhookController extends Controller
{
    /**
     * Handle Stripe webhook events.
     */
    public function stripe(Request $request)
    {
        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');

        try {
            // Get Stripe gateway configuration
            $stripeGateway = PaymentGateway::where('name', 'stripe')->first();
            if (!$stripeGateway) {
                Log::error('Stripe gateway not found for webhook');
                return response('Gateway not found', 400);
            }

            $config = $stripeGateway->getCurrentConfig();
            $webhookSecret = $config['webhook_secret'] ?? null;

            if (!$webhookSecret) {
                Log::error('Stripe webhook secret not configured');
                return response('Webhook secret not configured', 400);
            }

            // Verify webhook signature
            $event = Webhook::constructEvent($payload, $sigHeader, $webhookSecret);

        } catch (\UnexpectedValueException $e) {
            Log::error('Invalid Stripe webhook payload: ' . $e->getMessage());
            return response('Invalid payload', 400);
        } catch (\Stripe\Exception\SignatureVerificationException $e) {
            Log::error('Invalid Stripe webhook signature: ' . $e->getMessage());
            return response('Invalid signature', 400);
        }

        // Handle the event
        try {
            switch ($event['type']) {
                case 'customer.subscription.created':
                    $this->handleSubscriptionCreated($event['data']['object']);
                    break;

                case 'customer.subscription.updated':
                    $this->handleSubscriptionUpdated($event['data']['object']);
                    break;

                case 'customer.subscription.deleted':
                    $this->handleSubscriptionDeleted($event['data']['object']);
                    break;

                case 'invoice.payment_succeeded':
                    $this->handleInvoicePaymentSucceeded($event['data']['object']);
                    break;

                case 'invoice.payment_failed':
                    $this->handleInvoicePaymentFailed($event['data']['object']);
                    break;

                case 'payment_intent.succeeded':
                    $this->handlePaymentIntentSucceeded($event['data']['object']);
                    break;

                case 'payment_intent.payment_failed':
                    $this->handlePaymentIntentFailed($event['data']['object']);
                    break;

                default:
                    Log::info('Unhandled Stripe webhook event: ' . $event['type']);
            }

            return response('Webhook handled', 200);

        } catch (\Exception $e) {
            Log::error('Error handling Stripe webhook: ' . $e->getMessage(), [
                'event_type' => $event['type'],
                'event_id' => $event['id'],
            ]);
            return response('Error handling webhook', 500);
        }
    }

    /**
     * Handle PayPal webhook events.
     */
    public function paypal(Request $request)
    {
        try {
            // Get PayPal gateway configuration
            $paypalGateway = PaymentGateway::where('name', 'paypal')->first();
            if (!$paypalGateway) {
                Log::error('PayPal gateway not found for webhook');
                return response('Gateway not found', 400);
            }

            // Verify webhook signature
            if (!$this->verifyPayPalWebhook($request, $paypalGateway)) {
                Log::error('PayPal webhook signature verification failed');
                return response('Invalid signature', 400);
            }

            $payload = $request->all();
            $eventType = $payload['event_type'] ?? null;

            if (!$eventType) {
                Log::error('PayPal webhook missing event_type');
                return response('Missing event type', 400);
            }

            // Handle the event
            switch ($eventType) {
                case 'BILLING.SUBSCRIPTION.ACTIVATED':
                    $this->handlePayPalSubscriptionActivated($payload);
                    break;

                case 'BILLING.SUBSCRIPTION.CANCELLED':
                    $this->handlePayPalSubscriptionCancelled($payload);
                    break;

                case 'BILLING.SUBSCRIPTION.SUSPENDED':
                    $this->handlePayPalSubscriptionSuspended($payload);
                    break;

                case 'PAYMENT.SALE.COMPLETED':
                    $this->handlePayPalPaymentCompleted($payload);
                    break;

                case 'PAYMENT.SALE.DENIED':
                    $this->handlePayPalPaymentDenied($payload);
                    break;

                default:
                    Log::info('Unhandled PayPal webhook event: ' . $eventType);
            }

            return response('Webhook handled', 200);

        } catch (\Exception $e) {
            Log::error('Error handling PayPal webhook: ' . $e->getMessage(), [
                'payload' => $request->all(),
            ]);
            return response('Error handling webhook', 500);
        }
    }

    /**
     * Handle subscription created event.
     */
    private function handleSubscriptionCreated($stripeSubscription)
    {
        $subscriptionId = $stripeSubscription['metadata']['subscription_id'] ?? null;
        
        if ($subscriptionId) {
            $subscription = Subscription::find($subscriptionId);
            if ($subscription) {
                $subscription->update([
                    'stripe_subscription_id' => $stripeSubscription['id'],
                    'status' => $this->mapStripeStatus($stripeSubscription['status']),
                ]);
            }
        }
    }

    /**
     * Handle subscription updated event.
     */
    private function handleSubscriptionUpdated($stripeSubscription)
    {
        $subscription = Subscription::where('stripe_subscription_id', $stripeSubscription['id'])->first();
        
        if ($subscription) {
            $subscription->update([
                'status' => $this->mapStripeStatus($stripeSubscription['status']),
                'ends_at' => $stripeSubscription['current_period_end'] ? 
                    \Carbon\Carbon::createFromTimestamp($stripeSubscription['current_period_end']) : null,
            ]);
        }
    }

    /**
     * Handle subscription deleted event.
     */
    private function handleSubscriptionDeleted($stripeSubscription)
    {
        $subscription = Subscription::where('stripe_subscription_id', $stripeSubscription['id'])->first();
        
        if ($subscription) {
            $subscription->update(['status' => 'cancelled']);
        }
    }

    /**
     * Handle invoice payment succeeded event.
     */
    private function handleInvoicePaymentSucceeded($stripeInvoice)
    {
        $subscription = Subscription::where('stripe_subscription_id', $stripeInvoice['subscription'])->first();
        
        if ($subscription) {
            // Create or update invoice record
            $invoice = Invoice::updateOrCreate(
                ['stripe_invoice_id' => $stripeInvoice['id']],
                [
                    'subscription_id' => $subscription->id,
                    'amount' => $stripeInvoice['amount_paid'] / 100, // Convert from cents
                    'currency' => $stripeInvoice['currency'],
                    'status' => 'paid',
                    'paid_at' => now(),
                    'invoice_data' => $stripeInvoice,
                ]
            );

            // Create payment transaction
            PaymentTransaction::create([
                'user_id' => $subscription->user_id,
                'subscription_id' => $subscription->id,
                'invoice_id' => $invoice->id,
                'amount' => $stripeInvoice['amount_paid'] / 100,
                'currency' => $stripeInvoice['currency'],
                'payment_method' => 'stripe',
                'transaction_id' => $stripeInvoice['payment_intent'],
                'status' => 'completed',
                'transaction_data' => $stripeInvoice,
            ]);
        }
    }

    /**
     * Handle invoice payment failed event.
     */
    private function handleInvoicePaymentFailed($stripeInvoice)
    {
        $subscription = Subscription::where('stripe_subscription_id', $stripeInvoice['subscription'])->first();
        
        if ($subscription) {
            // Update or create invoice record
            Invoice::updateOrCreate(
                ['stripe_invoice_id' => $stripeInvoice['id']],
                [
                    'subscription_id' => $subscription->id,
                    'amount' => $stripeInvoice['amount_due'] / 100,
                    'currency' => $stripeInvoice['currency'],
                    'status' => 'failed',
                    'invoice_data' => $stripeInvoice,
                ]
            );

            // Update subscription status if needed
            if ($subscription->status === 'active') {
                $subscription->update(['status' => 'past_due']);
            }
        }
    }

    /**
     * Handle payment intent succeeded event.
     */
    private function handlePaymentIntentSucceeded($paymentIntent)
    {
        // Handle one-time payments if needed
        Log::info('Payment intent succeeded', ['payment_intent_id' => $paymentIntent['id']]);
    }

    /**
     * Handle payment intent failed event.
     */
    private function handlePaymentIntentFailed($paymentIntent)
    {
        // Handle failed payments if needed
        Log::warning('Payment intent failed', ['payment_intent_id' => $paymentIntent['id']]);
    }

    /**
     * Map Stripe subscription status to our internal status.
     */
    private function mapStripeStatus($stripeStatus): string
    {
        return match ($stripeStatus) {
            'active' => 'active',
            'trialing' => 'trial',
            'past_due' => 'past_due',
            'canceled' => 'cancelled',
            'unpaid' => 'past_due',
            'incomplete' => 'pending',
            'incomplete_expired' => 'cancelled',
            default => 'pending',
        };
    }

    /**
     * Verify PayPal webhook signature.
     */
    private function verifyPayPalWebhook(Request $request, PaymentGateway $gateway): bool
    {
        try {
            $config = $gateway->getCurrentConfig();
            $webhookId = $config['webhook_id'] ?? null;

            if (!$webhookId) {
                Log::warning('PayPal webhook ID not configured');
                return true; // Allow for now, but should be configured in production
            }

            // PayPal webhook verification would go here
            // For now, we'll return true but this should be implemented with proper PayPal SDK
            return true;

        } catch (\Exception $e) {
            Log::error('PayPal webhook verification failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Handle PayPal subscription activated event.
     */
    private function handlePayPalSubscriptionActivated($payload)
    {
        $subscriptionId = $payload['resource']['id'] ?? null;
        $customId = $payload['resource']['custom_id'] ?? null;

        if ($customId) {
            $subscription = Subscription::find($customId);
            if ($subscription) {
                $subscription->update([
                    'paypal_subscription_id' => $subscriptionId,
                    'status' => 'active',
                ]);
            }
        }
    }

    /**
     * Handle PayPal subscription cancelled event.
     */
    private function handlePayPalSubscriptionCancelled($payload)
    {
        $subscriptionId = $payload['resource']['id'] ?? null;

        if ($subscriptionId) {
            $subscription = Subscription::where('paypal_subscription_id', $subscriptionId)->first();
            if ($subscription) {
                $subscription->update(['status' => 'cancelled']);
            }
        }
    }

    /**
     * Handle PayPal subscription suspended event.
     */
    private function handlePayPalSubscriptionSuspended($payload)
    {
        $subscriptionId = $payload['resource']['id'] ?? null;

        if ($subscriptionId) {
            $subscription = Subscription::where('paypal_subscription_id', $subscriptionId)->first();
            if ($subscription) {
                $subscription->update(['status' => 'suspended']);
            }
        }
    }

    /**
     * Handle PayPal payment completed event.
     */
    private function handlePayPalPaymentCompleted($payload)
    {
        $saleId = $payload['resource']['id'] ?? null;
        $amount = $payload['resource']['amount']['total'] ?? 0;
        $currency = $payload['resource']['amount']['currency'] ?? 'USD';
        $subscriptionId = $payload['resource']['billing_agreement_id'] ?? null;

        if ($subscriptionId) {
            $subscription = Subscription::where('paypal_subscription_id', $subscriptionId)->first();

            if ($subscription) {
                // Create invoice record
                $invoice = Invoice::create([
                    'subscription_id' => $subscription->id,
                    'amount' => $amount,
                    'currency' => $currency,
                    'status' => 'paid',
                    'paid_at' => now(),
                    'paypal_sale_id' => $saleId,
                    'invoice_data' => $payload,
                ]);

                // Create payment transaction
                PaymentTransaction::create([
                    'user_id' => $subscription->user_id,
                    'subscription_id' => $subscription->id,
                    'invoice_id' => $invoice->id,
                    'amount' => $amount,
                    'currency' => $currency,
                    'payment_method' => 'paypal',
                    'transaction_id' => $saleId,
                    'status' => 'completed',
                    'transaction_data' => $payload,
                ]);
            }
        }
    }

    /**
     * Handle PayPal payment denied event.
     */
    private function handlePayPalPaymentDenied($payload)
    {
        $saleId = $payload['resource']['id'] ?? null;
        $subscriptionId = $payload['resource']['billing_agreement_id'] ?? null;

        if ($subscriptionId) {
            $subscription = Subscription::where('paypal_subscription_id', $subscriptionId)->first();

            if ($subscription) {
                // Update subscription status
                $subscription->update(['status' => 'past_due']);

                // Log the failed payment
                Log::warning('PayPal payment denied', [
                    'subscription_id' => $subscription->id,
                    'sale_id' => $saleId,
                    'payload' => $payload,
                ]);
            }
        }
    }
}
