<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Mail\SendOTP;
use App\Models\ApiAccessToken;
use App\Models\BotConversation;
use App\Models\Bots;
use App\Models\Company;
use App\Models\FirebaseFunction;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Kreait\Firebase\Contract\Database;
use PHPOpenSourceSaver\JWTAuth\Exceptions\JWTException;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;

class Apicontroller extends Controller
{
    protected $database;

    protected $tableName;

    public function __construct(Database $database)
    {
        $this->database  = $database;
        $this->tableName = 'chats';
    }

    public function getAPIAccessToken(Request $request)
    {

        // Use Validator to validate the request
        $validator = Validator::make($request->all(), [
            'username' => 'required|email',
            'password' => 'required',
        ]);

        // If validation fails, return a JSON response
        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        // Find the user by email
        $user = ApiAccessToken::where('api_email', $request->username)->first();

        if (! $user || $request->password !== $user->api_password) {
            return response()->json(['message' => 'Invalid credentials'], 401);
        }

        //        if (! $user || ! Hash::check($request->password, $user->api_password)) {
        //            return response()->json(['message' => 'Invalid credentials'], 401);
        //        }

        $token = Str::random(60);

        // Mark old tokens as expired
        ApiAccessToken::where('expires_at', '<', now())
            ->update(['is_expired' => true]);

        // Check if a token already exists
        $Data = ApiAccessToken::first();

        if ($Data) {

            $checkExpired = ApiAccessToken::where('expires_at', '<', now())->first();
            if ($checkExpired) {

                // Update the existing token
                $Data->update([
                    'token'      => $token,
                    'expires_at' => now()->addDay(),
                    'is_expired' => false,
                ]);

            }

        } else {
            // Create a new token entry if none exists
            $Data = ApiAccessToken::create([
                'user_id'      => 1,
                'api_email'    => '<EMAIL>',
                'api_password' => 123123123,
                'token'        => $token,
                'expires_at'   => now()->addDay(),
                'is_expired'   => false,
            ]);
        }

        $responseData = [
            'status' => true,
            'token'  => $Data->token,
        ];

        return response()->json($responseData);
    }

    public function getallUserEnquery(Request $request)
    {

        $authDetail  = Auth::user();
        $profileType = $authDetail->profileType;
        if (! in_array($profileType, ['agent', 'company'])) {
            return response()->json([
                'status'  => false,
                'message' => 'Invalid role authentication',
            ], 403); // 403 Forbidden
        }

        if ($profileType == 'company') {
            $ownerID = $authDetail->id;
        } else {
            $ownerID = $authDetail->parent_id;
        }

        $botConverList = BotConversation::getBotConversaction($ownerID);

        // Transform the result
        $transformedBotConverList = $botConverList->map(function ($item) {
            // Update profilePicture to full path
            $item->profilePicture = ($item->profilePicture != '') ? url('/storage/'.$item->profilePicture) : '';

            if (isset($item->chatbot_project_id)) {
                unset($item->chatbot_project_id);
            }

            if (isset($item->bot_id)) {
                unset($item->bot_id);
            }

            $item->agent_name = ($item->agent_name == null) ? '' : $item->agent_name;

            // $user = User::find($item->user_id);
            // $userDetail = User::getUserDetail($user);
            // $item->user_detail = $userDetail;

            return $item;
        });

        $responseData['status'] = true;
        $responseData['data']   = $botConverList;

        return response()->json($responseData);

    }

    public function loadCompany(Request $request)
    {
        try {

            $authDetail  = Auth::user();
            $profileType = $authDetail->profileType;
            $searchfield = $request->search;

            if ($profileType == 'user') {

                if (isset($request->all) && $request->all == 1) {

                    try {
                        $companies = Company::select(
                            'company.id',
                            'company.userId as company_user_id',
                            'catId',
                            'companyName as UserName',
                            'users.profilePicture',
                            'companyAdd',
                            'city',
                            'state',
                            'countryId',
                            'zipCode',
                            'websiteUrl',
                            'chatSupport',
                        )->join('users', 'company.userId', '=', 'users.id')
                            ->where('users.profileType', 'company')
                            ->where('users.verificationStatus', 'verified')
                            ->where('company.companyName', 'LIKE', '%'.$searchfield.'%')
                            ->get();

                        // Append full URL to profilePicture
                        $companies->transform(function ($company) {

                            $conUserDetail      = User::find($company->company_user_id);
                            $onlineThreshold    = now()->subMinutes(5); // Set your online threshold here
                            $isOnline           = $conUserDetail->last_activity && Carbon::parse($conUserDetail->last_activity)->greaterThan($onlineThreshold);
                            $company->is_online = $isOnline;
                            $company->last_seen = $conUserDetail->last_seen ? $conUserDetail->last_seen->diffForHumans() : 'never';

                            $company->profilePicture = url('/storage/'.$company->profilePicture);

                            return $company;
                        });

                        if ($companies->isNotEmpty()) {
                            return response()->json(['status' => true, 'data' => $companies]);
                        } else {
                            return response()->json(['status' => false, 'message' => 'no record found']);
                        }
                    } catch (Exception $e) {
                        return response()->json(['status' => false, 'message' => $e->getMessage()]);
                    }

                }

                if (isset($request->all) && $request->all == 0) {

                    $companyUserIds = BotConversation::where('user_id', Auth::id())->pluck('company_user_id')->toArray();

                    try {
                        $companies = Company::select(
                            'company.id',
                            'company.userId as company_user_id',
                            'botconversation.agent_id as conversation_user_id',
                            'companyName as UserName',
                            'users.profilePicture',
                            'companyAdd',
                            'city',
                            'state',
                            'countryId',
                            'zipCode',
                            'websiteUrl',
                            'chatSupport',
                        )->join('users', 'company.userId', '=', 'users.id')
                            ->join('botconversation', 'botconversation.company_user_id', '=', 'users.id')
                            ->where('users.profileType', 'company')
                            ->whereIn('company.userId', $companyUserIds)
                            ->where('users.verificationStatus', 'verified')
                            ->where('company.companyName', 'LIKE', '%'.$searchfield.'%')
                            ->get();

                        // Append full URL to profilePicture
                        $companies->transform(function ($company) {

                            if ($company->conversation_user_id == 0) {
                                $conUserDetail = User::find($company->company_user_id);
                            } else {
                                $conUserDetail = User::find($company->conversation_user_id);
                            }
                            $onlineThreshold    = now()->subMinutes(5); // Set your online threshold here
                            $isOnline           = $conUserDetail->last_activity && Carbon::parse($conUserDetail->last_activity)->greaterThan($onlineThreshold);
                            $company->is_online = $isOnline;
                            $company->last_seen = $conUserDetail->last_seen ? $conUserDetail->last_seen->diffForHumans() : 'never';

                            $company->profilePicture = url('/storage/'.$company->profilePicture);

                            return $company;
                        });

                        if ($companies->isNotEmpty()) {
                            return response()->json(['status' => true, 'data' => $companies]);
                        } else {
                            return response()->json(['status' => false, 'message' => 'no record found']);
                        }
                    } catch (Exception $e) {
                        return response()->json(['status' => false, 'message' => $e->getMessage()]);
                    }

                }

            }

            if ($profileType == 'company' || $profileType == 'agent') {

                $searchfield = $request->search;

                if ($profileType == 'agent') {
                    $useriD = Auth::user()->parent_id;
                } else {
                    $useriD = Auth::id();
                }

                return response()->json(['status' => false, 'message' => 'no record found']);

            }

            return response()->json(['status' => false, 'message' => 'no record found']);

        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }
    }

    // public function loadCompany(Request $request)
    // {
    //     try {

    //         $authDetail  = Auth::user();
    //         $profileType = $authDetail->profileType;
    //         $searchfield = $request->search;

    //         if ($profileType == 'user') {

    //             if (isset($request->all) && $request->all == 1) {

    //                 try {
    //                     $companies = Company::select('company.id', 'company.userId as id', 'companyName as UserName', 'users.profilePicture', 'companyAdd', 'city', 'state', 'countryId', 'zipCode', 'websiteUrl', 'chatSupport', 'bots.chatbot_project_id as botID')
    //                         ->join('users', 'company.userId', '=', 'users.id')
    //                         ->leftJoin('bots', 'bots.company_id', '=', 'company.id')
    //                         ->where('company.companyName', 'LIKE', '%'.$searchfield.'%')
    //                         ->get();

    //                     // Append full URL to profilePicture
    //                     $companies->transform(function ($company) {
    //                         $company->profilePicture = url('/storage/'.$company->profilePicture);
    //                         $company->botID          = base64_encode($company->botID);

    //                         return $company;
    //                     });

    //                     if ($companies->isNotEmpty()) {
    //                         return response()->json(['status' => true, 'data' => $companies]);
    //                     } else {
    //                         return response()->json(['status' => false, 'message' => 'no record found']);
    //                     }
    //                 } catch (Exception $e) {
    //                     return response()->json(['status' => false, 'message' => $e->getMessage()]);
    //                 }

    //             }

    //             $companies = Company::select(
    //                 'company.id',
    //                 'company.userId as company_user_id',
    //                 'botconversation.agent_id as conversation_user_id',
    //                 'catId',
    //                 'companyName as UserName',
    //                 'users.profilePicture',
    //                 'companyAdd',
    //                 'city',
    //                 'state',
    //                 'countryId',
    //                 'zipCode',
    //                 'websiteUrl',
    //                 'chatSupport',
    //                 'bots.chatbot_project_id as botID'
    //             )->join('users', 'company.userId', '=', 'users.id')
    //                 ->leftJoin('bots', 'bots.company_id', '=', 'company.id')
    //                 ->leftJoin('botconversation', 'botconversation.bot_id', '=', 'bots.id')
    //                 ->where('users.profileType', 'company')
    //                 ->where('botconversation.user_id', Auth::id())
    //                 ->where('users.verificationStatus', 'verified')
    //                 ->where('company.companyName', 'LIKE', '%'.$searchfield.'%')
    //                 ->get();

    //             // Append full URL to profilePicture
    //             $companies->transform(function ($company) {

    //                 if (trim($company->profilePicture) != '') {
    //                     $company->profilePicture = url('/storage/'.$company->profilePicture);

    //                 } else {
    //                     $company->profilePicture = '';
    //                 }

    //                 if ($company->conversation_user_id == 0) {
    //                     $conUserDetail = User::find($company->company_user_id);
    //                 } else {
    //                     $conUserDetail = User::find($company->conversation_user_id);
    //                 }

    //                 $company->botID  = base64_encode($company->botID);
    //                 $onlineThreshold = now()->subMinutes(5); // Set your online threshold here
    //                 $isOnline        = $conUserDetail->last_activity && Carbon::parse($conUserDetail->last_activity)->greaterThan($onlineThreshold);

    //                 $company->is_online   = $isOnline;
    //                 $company->last_seen   = $conUserDetail->last_seen ? $conUserDetail->last_seen->diffForHumans() : 'never';
    //                 $getMessage           = new BotConversation;
    //                 $lastMessage          = $getMessage->getDialogflowlastMsg($this->tableName, $this->database, base64_decode($company->botID), Auth::id());
    //                 $company->lastmessage = $lastMessage;

    //                 return $company;
    //             });

    //             if ($companies->isNotEmpty()) {
    //                 return response()->json(['status' => true, 'data' => $companies]);
    //             } else {
    //                 return response()->json(['status' => false, 'message' => 'no record found']);
    //             }

    //         }

    //         if ($profileType == 'company' || $profileType == 'agent') {

    //             $searchfield = $request->search;

    //             if ($profileType == 'agent') {
    //                 $useriD = Auth::user()->parent_id;
    //             } else {
    //                 $useriD = Auth::id();
    //             }

    //             $companies = BotConversation::select(
    //                 'users.id', 'users.name', 'botconversation.user_id',
    //                 'users.email',
    //                 'users.phone',
    //                 'users.profilePicture',
    //                 'company.chatSupport',
    //                 'bots.chatbot_project_id as botID'
    //             )->where('botconversation.company_user_id', $useriD)
    //                 ->leftJoin('bots', 'bots.id', '=', 'botconversation.bot_id')
    //                 ->join('users', 'botconversation.user_id', '=', 'users.id')
    //                 ->join('company', 'company.id', '=', 'bots.company_id')
    //                 ->where(function ($query) use ($searchfield) {
    //                     $query->where('users.name', 'LIKE', '%'.$searchfield.'%')
    //                         ->orWhere('users.phone', 'LIKE', '%'.$searchfield.'%')
    //                         ->orWhere('users.email', 'LIKE', '%'.$searchfield.'%');
    //                 })
    //                 ->get();

    //             // Append full URL to profilePicture
    //             $companies->transform(function ($company) {

    //                 if (trim($company->profilePicture) != '') {
    //                     $company->profilePicture = url('/storage/'.$company->profilePicture);

    //                 } else {
    //                     $company->profilePicture = '';
    //                 }

    //                 $conUserDetail = User::find($company->user_id);

    //                 $company->botID  = base64_encode($company->botID);
    //                 $onlineThreshold = now()->subMinutes(5); // Set your online threshold here
    //                 $isOnline        = $conUserDetail->last_activity && Carbon::parse($conUserDetail->last_activity)->greaterThan($onlineThreshold);

    //                 $company->is_online   = $isOnline;
    //                 $company->last_seen   = $conUserDetail->last_seen ? $conUserDetail->last_seen->diffForHumans() : 'never';
    //                 $getMessage           = new BotConversation;
    //                 $lastMessage          = $getMessage->getDialogflowlastMsg($this->tableName, $this->database, base64_decode($company->botID), $company->user_id);
    //                 $company->lastmessage = $lastMessage;

    //                 return $company;
    //             });

    //             if ($companies->isNotEmpty()) {
    //                 return response()->json(['status' => true, 'data' => $companies]);
    //             } else {
    //                 return response()->json(['status' => false, 'message' => 'no record found']);
    //             }

    //         }

    //     } catch (Exception $e) {
    //         return response()->json(['status' => false, 'message' => $e->getMessage()]);
    //     }
    // }

    public function sendOtp(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'email'  => 'required|email',
            'action' => 'required|in:0,1',
        ]);

        // Check if validation fails
        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'errors' => $validator->errors(),
            ], 400);
        }

        // Reset API logic
        $responseData = [];

        $otpReal = rand(111111, 999999);

        $checkEmail = User::where('email', $request->email)->first();

        if ($checkEmail) {

            if ($request->action == 1) {

                // if ($checkEmail->verificationStatus == 'verified') {
                //     $responseData['status']  = false;
                //     $responseData['message'] = 'This Email already registered';

                //     return response()->json($responseData);
                // }

                User::where('email', $request->email)->update(['otp' => $otpReal, 'verificationStatus' => 'not_verified']);

            } else {

                User::where('email', $request->email)->update(['otp' => $otpReal]);

            }

            $setmailids = $request->email;

            $mailData = [
                'otp'     => $otpReal,
                'subject' => 'Please verify your OTP',
            ];

            try {

                Mail::to($setmailids)->send(new SendOTP($mailData));
                $responseData['status']  = true;
                $responseData['message'] = 'OTP sent successfully';

            } catch (Exception $ex) {

                $responseData['status']  = true;
                $responseData['message'] = 'OTP sent successfully';

            }

        } else {

            $responseData['status']  = false;
            $responseData['message'] = 'invalid email request';

            return response()->json($responseData);

        }

        // Return JSON response
        return response()->json($responseData);
    }

    public function verifyMailOTP(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'code'  => 'required',
        ]);

        // Check if validation fails
        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'errors' => $validator->errors(),
            ], 400);
        }

        // Reset API logic
        $responseData = [];

        $otpReal = $request->code;

        $checkEmail = User::where('email', $request->email)->where('otp', $otpReal)->first();

        if ($checkEmail) {

            User::where('email', $request->email)->update(['otp' => '', 'verificationStatus' => 'verified']);

            try {
                $token = JWTAuth::fromUser($checkEmail);
                if (! $token) {
                    return response()->json(['status' => false, 'message' => 'Unauthorized'], 401);
                }
            } catch (JWTException $e) {
                return response()->json(['status' => false, 'message' => 'Could not create token'], 500);
            }

            // $responceData = User::getUserDetail($checkEmail);

            $responseData['status']  = true;
            $responseData['message'] = 'OTP verified successfully';
            $responseData['token']   = $token;

            return response()->json($responseData);

        } else {
            $responseData['status']  = false;
            $responseData['message'] = 'OTP verification failed';

            return response()->json($responseData);
        }

    }

    // public function verifyMail(Request $request){
    //     // Reset API logic
    //     $responseData = [];

    //     if(isset($request->email)){

    //         if(trim($request->email) == ''){
    //             $responseData['status'] = false;
    //             $responseData['message'] = 'email field is required';
    //             return response()->json($responseData);
    //         }

    //     }else{
    //         $responseData['status'] = false;
    //         $responseData['message'] = 'email field is required';
    //         return response()->json($responseData);
    //     }

    //     if(isset($request->code)){

    //         if(trim($request->code) == ''){
    //             $responseData['status'] = false;
    //             $responseData['message'] = 'code field is required';
    //             return response()->json($responseData);
    //         }

    //     }else{
    //         $responseData['status'] = false;
    //         $responseData['message'] = 'code field is required';
    //         return response()->json($responseData);
    //     }

    //     $otpReal = $request->code;

    //     $checkEmail = User::where('email',$request->email)->where('otp',$otpReal)->first();

    //     if($checkEmail){

    //         User::where('email',$request->email)->update(['otp'=>'','verificationStatus'=>'verified']);
    //         $responceData = User::getUserDetail($checkEmail);

    //         $responseData['status'] = true;
    //         $responseData['message'] = 'verification success';
    //         $responseData['data'] = $responceData;
    //         return response()->json($responseData);

    //     }else{
    //         $responseData['status'] = false;
    //         $responseData['message'] = 'verification failed';
    //          return response()->json($responseData);
    //     }

    // }

    // public function sendmail(Request $request){
    //     // Reset API logic
    //     $responseData = [];

    //     if(isset($request->email)){

    //         if(trim($request->email) == ''){
    //             $responseData['status'] = false;
    //             $responseData['message'] = 'email field is required';
    //             return response()->json($responseData);
    //         }

    //     }else{
    //         $responseData['status'] = false;
    //         $responseData['message'] = 'email field is required';
    //         return response()->json($responseData);
    //     }

    //     $otpReal = rand(111111,999999);

    //     $checkEmail = User::where('email',$request->email)->first();

    //     if($checkEmail){

    //         if($checkEmail->verificationStatus == 'verified'){
    //             $responseData['status'] = false;
    //             $responseData['message'] = 'This Email already registered';
    //             return response()->json($responseData);
    //         }

    //         User::where('email',$request->email)->update(['otp'=>$otpReal]);

    //     }

    //     $setmailids = $request->email;

    //     $mailData = [
    //         'otp' => $otpReal,
    //         'subject' => 'Please verify your OTP',
    //     ];

    //     try{

    //         Mail::to($setmailids)->send(new SendOTP($mailData));
    //         $responseData['status'] = true;
    //         $responseData['message'] = 'OTP sent successfully';

    //     }catch(Exception $ex){

    //         $responseData['status'] = true;
    //         $responseData['message'] = 'OTP sent successfully';

    //     }

    //     // Return JSON response
    //     return response()->json($responseData);
    // }

    public function sendCompanyResponseMessage(Request $request): JsonResponse
    {
        $authUser = Auth::user();
        if (! in_array($authUser->profileType, ['agent', 'company'])) {
            return response()->json([
                'status'  => false,
                'message' => 'Invalid role authentication',
            ], 403);
        }
        $ownerID         = $authUser->parent_id ?: $authUser->id;
        $companyDetail   = Company::where('userId', $ownerID)->first();
        $types           = $companyDetail->userId.'_'.$companyDetail->id;
        $twoTypes        = explode('_', $types);
        $company_user_id = (isset($twoTypes[0])) ? $twoTypes[0] : '';
        if ($company_user_id != '') {
            $checkBotconver = BotConversation::where('company_user_id', $company_user_id)->where('user_id', $request->userid)->first();
            if ($checkBotconver) {
                $currenetUserID = Auth::id();
                $fireBaeFun     = new FirebaseFunction($this->database);
                $result         = $fireBaeFun->agentChatbotWorkLive($request->message, $request->userid, $checkBotconver, $types, $currenetUserID);

                $chatData  = [];
                $agentType = $types;
                $userId    = $request->userid;
                $botResult = new FirebaseFunction($this->database);
                $chatData  = $botResult->getBotConversation($userId, $agentType);

                return response()->json(['status' => true, 'message' => 'Message sent successfully', 'data' => $chatData]);
            }
        }

        return response()->json(['status' => false, 'message' => 'Invalid request'], 500);

    }

    public function getUserChatInboxList(Request $request): JsonResponse
    {
        $authUser = Auth::user();

        if (! in_array($authUser->profileType, ['agent', 'company'])) {
            return response()->json([
                'status'  => false,
                'message' => 'Invalid role authentication',
            ], 403);
        }

        $ownerID       = $authUser->parent_id ?: $authUser->id;
        $botConverList = BotConversation::getBotConversaction($ownerID);

        $responseData = $botConverList->map(function ($chat) use ($ownerID) {
            return [
                'tab_status'      => $this->determineTabStatus($chat, $ownerID),
                'lastmessage'     => $chat->lastmessage,
                'user_name'       => $chat->name,
                'user_phone'      => $chat->phone,
                'profilePicture'  => $chat->profilePicture,
                'email'           => $chat->email,
                'agent_name'      => $chat->agent_name,
                'company_id'      => $chat->company_id,
                'company_user_id' => $chat->company_user_id,
            ];
        });

        return response()->json(['status' => true, 'data' => $responseData], 200, [], JSON_PRETTY_PRINT);
    }

    private function determineTabStatus($chat, $ownerID): string
    {
        if ($chat->status == 0 || ($chat->agent_id == $ownerID && $chat->company_user_id == $ownerID)) {
            return 'active';
        }
        if ($chat->status == 1 && $chat->agent_id == $ownerID && $chat->company_user_id != $ownerID) {
            return 'active';
        }
        if ($chat->status == 1 && $chat->agent_id != $ownerID) {
            return 'inactive';
        }

        return $chat->status == 2 ? 'closed' : 'unknown';
    }

    public function chatbotList(Request $request)
    {

        $userDetail = User::find(Auth::id());
        if ($userDetail->profileType == 'agent' || $userDetail->profileType == 'user') {
        } else {
            return response()->json([
                'status'  => false,
                'message' => 'Invalid role authentication',
            ], 403); // 403 Forbidden
        }

        $dataList = Bots::select('chatbot_name', 'chatbot_project_id', 'user_id')->get()->toArray();

        $allAgents = [];
        foreach ($dataList as $bot) {
            $slug       = base64_encode($bot['chatbot_project_id']);
            $companyQry = Company::select('company.id', 'company.userId as id', 'catId', 'companyName as UserName', 'users.profilePicture', 'companyAdd', 'city', 'state', 'countryId', 'zipCode', 'websiteUrl', 'chatSupport')
                ->join('users', 'company.userId', '=', 'users.id')
                ->where('users.profileType', 'company')
                ->where('users.verificationStatus', 'verified')
                ->where('company.userId', $bot['user_id'])
                ->first();

            $response = [];

            if ($companyQry) {

                $res['name']                   = $bot['chatbot_name'];
                $res['bot_id']                 = $slug;
                $res['user_id']                = $bot['user_id'];
                $res['company_id']             = $companyQry->id;
                $res['company_catId']          = $companyQry->catId;
                $res['company_UserName']       = $companyQry->UserName;
                $res['company_profilePicture'] = url('/storage/'.$companyQry->profilePicture);
                $res['company_companyAdd']     = $companyQry->companyAdd;
                $res['company_city']           = $companyQry->city;
                $res['company_state']          = $companyQry->state;
                $res['company_countryId']      = $companyQry->countryId;
                $res['company_zipCode']        = $companyQry->zipCode;
                $res['company_websiteUrl']     = $companyQry->websiteUrl;
                $res['company_chatSupport']    = $companyQry->chatSupport;

                array_push($allAgents, $res);
            }

        }

        if (count($allAgents) > 0) {
            return response()->json(['status' => true, 'data' => $allAgents], 200, [], JSON_PRETTY_PRINT);
        } else {
            return response()->json(['status' => false, 'data' => $allAgents], 200, [], JSON_PRETTY_PRINT);
        }

    }

    public function sendChatmessageByAgent(Request $request)
    {

        $currentUser = User::find(Auth::id());

        // Check if the current user's profile type is valid
        if (! in_array($currentUser->profileType, ['agent', 'company'])) {
            return response()->json([
                'status'  => false,
                'message' => 'Invalid role authentication',
            ], 403); // 403 Forbidden
        }

        $expectedProfileType = 'user';

        // Validation rules
        $validator = Validator::make($request->all(), [
            'bot_id' => 'required|string',
            'userid' => [
                'required',
                'exists:users,id',
                function ($attribute, $value, $fail) use ($expectedProfileType) {
                    $user = User::find($value);
                    if (! $user) {
                        $fail('The selected userid is invalid.');
                    } elseif ($user->profileType !== $expectedProfileType) {
                        $fail('The userid must have a profileType of "'.$expectedProfileType.'".');
                    }
                },
            ],
            'message' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $types       = base64_decode($request->bot_id);
        $agentDetail = Bots::where('chatbot_project_id', $types)->first();
        if ($agentDetail) {
        } else {
            return response()->json(['status' => false, 'message' => 'Invalid project id'], 500);
        }

        $fireBaeFun = new FirebaseFunction($this->database);
        $resultdata = $fireBaeFun->agentChatbotWork($request, $agentDetail, $types);

        $resultdata = $resultdata->getData(true);
        if (! $resultdata['status']) {

            $errorMessage = $resultdata['message'];

            return response()->json(['status' => false, 'message' => $errorMessage, 'data' => []]);

        } else {

            $botResult = new FirebaseFunction($this->database);
            $chatData  = $botResult->getBotConversation($request->userid, $types);

            return response()->json(['status' => true, 'message' => 'Message sent successfully', 'data' => $chatData]);

        }

    }

    public function getSingleMessageData(Request $request): JsonResponse
    {

        $currentUser = User::find(Auth::id());

        if (in_array($currentUser->profileType, ['agent', 'company'])) {

            $authUser      = Auth::user();
            $ownerID       = $authUser->parent_id ?: $authUser->id;
            $companyDetail = Company::where('userId', $ownerID)->first();
            $types         = $companyDetail->userId.'_'.$companyDetail->id;

            $botResult = new FirebaseFunction($this->database);
            $chatData  = $botResult->getBotConversation($request->userid, $types);

            return response()->json(['status' => true, 'data' => $chatData]);

        }
        if (in_array($currentUser->profileType, ['user'])) {

            $userID        = Auth::id();
            $companyDetail = Company::where('userId', $request->company_user_id)->first();
            $types         = $companyDetail->userId.'_'.$companyDetail->id;

            $botResult = new FirebaseFunction($this->database);
            $chatData  = $botResult->getBotConversation($userID, $types);

            return response()->json(['status' => true, 'data' => $chatData]);
        }

        return response()->json(['status' => false, 'message' => 'Invalid request'], 500);

    }

    public function companyConversationList(Request $request)
    {

        $currentUser = User::find(Auth::id());

        // Check if the current user's profile type is valid
        if (! in_array($currentUser->profileType, ['user'])) {
            return response()->json([
                'status'  => false,
                'message' => 'Invalid role authentication',
            ], 403); // 403 Forbidden
        }

        $chatData  = [];
        $agentType = '';

        $dataList = Company::join('users', 'users.id', '=', 'company.userId')
            ->select(
                'company.id as company_id',
                'company.userId',
                'company.companyName',
                'company.chatSupport',
                'users.profilePicture'
            )
            ->get();

        $allAgents      = [];
        $selecteduserid = Auth::id(); // Moved outside loop since it's constant

        foreach ($dataList as $detail) {
            $selecedagentType = $detail->userId.'_'.$detail->company_id;

            $resultData = $this->getLastMessage($selecteduserid, $selecedagentType);

            $lastMessage   = '';
            $timestampReal = '';

            if ($resultData->status() === 200) {
                $data        = $resultData->getData(true);
                $lastMessage = $data['message'] ?? '';

                if (! empty($data['timestamp'])) {
                    $timestamp     = Carbon::createFromTimestamp($data['timestamp']);
                    $timestampReal = match (true) {
                        $timestamp->isToday()     => 'Today',
                        $timestamp->isYesterday() => 'Yesterday',
                        default                   => $timestamp->format('Y-m-d H:i')
                    };
                }

                $allAgents[] = [
                    'userid'         => $detail->userId,
                    'name'           => $detail->companyName,
                    'company_name'   => $detail->companyName,
                    'chat_support'   => $detail->chatSupport,
                    'profilePicture' => $detail->profilePicture,
                    'last_message'   => $lastMessage,
                    'last_msg_time'  => $timestampReal,
                    'slug'           => $selecedagentType,
                ];
            }

        }

        return response()->json([
            'status'  => true,
            'message' => 'Data retrieved successfully',
            'data'    => $allAgents,
        ], 200);

    }

    public function getLastMessage($userId, $agentType)
    {

        $chatData     = [];
        $path         = $this->tableName.'/user_id_'.$userId.'/agent_'.$agentType;
        $userChatsRef = $this->database->getReference($path);
        $chats        = $userChatsRef->getValue();

        if ($chats) {
            $chats = array_filter($chats, function ($chat) {
                return is_array($chat) && isset($chat['timestamp']);
            });

            // Get the last message
            $lastMessage = end($chats);
            if ($lastMessage) {
                return response()->json([
                    'id'        => $lastMessage['id'],
                    'message'   => $lastMessage['message'],
                    'timestamp' => $lastMessage['timestamp'],
                ]);
            }
        }

        return response()->json(['message' => 'No chats found'], 404);
    }

    public function sendChatmessage(Request $request)
    {

        $currentUser = User::find(Auth::id());

        // Check if the current user's profile type is valid
        if (! in_array($currentUser->profileType, ['user'])) {
            return response()->json([
                'status'  => false,
                'message' => 'Invalid role authentication',
            ], 403); // 403 Forbidden
        }

        // Validation rules
        $validator = Validator::make($request->all(), [
            'company_id' => 'required|string',
            'message'    => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $companyDetail = Company::where('id', $request->company_id)->first();

        if ($companyDetail) {
            $companyUserID = $companyDetail->userId;
        } else {
            return response()->json(['status' => false, 'message' => 'Invalid company id'], 500);
        }

        $types = $companyUserID.'_'.$request->company_id;

        $checkExistingConversation = BotConversation::where('user_id', Auth::id())
            ->where('company_user_id', $companyUserID)->where('status', '<>', 2)->first();
        if ($checkExistingConversation) {

            $fireBaeFun = new FirebaseFunction($this->database);
            $result     = $fireBaeFun->liveagentChat($request->message, Auth::id(), $companyUserID, 0);

            $botResult = new FirebaseFunction($this->database);
            $chatData  = $botResult->getBotConversation(Auth::id(), $types);

            return response()->json(['status' => true, 'message' => 'Message sent successfully', 'data' => $chatData]);

        } else {
            $checkCompany = Company::where('userId', $companyUserID)->first();
            if ($checkCompany) {

                if ($checkCompany->chatSupport == 'both' || $checkCompany->chatSupport == 'chatbot') {

                    $fireBaeFun     = new FirebaseFunction($this->database);
                    $result         = $fireBaeFun->javaChatAgent($request->message, Auth::id(), $companyUserID, 0);
                    $statusResponse = $result->getData();

                    $botResult = new FirebaseFunction($this->database);
                    $chatData  = $botResult->getBotConversation(Auth::id(), $types);

                    return response()->json(['status' => true, 'message' => 'Message sent successfully', 'data' => $chatData]);

                } elseif ($checkCompany->chatSupport == 'livechat') {

                    $fireBaeFun = new FirebaseFunction($this->database);
                    $result     = $fireBaeFun->liveagentChat($request->message, Auth::id(), $companyUserID, 0);

                    $botResult = new FirebaseFunction($this->database);
                    $chatData  = $botResult->getBotConversation(Auth::id(), $types);

                    return response()->json(['status' => true, 'message' => 'Message sent successfully', 'data' => $chatData]);

                }

            } else {
                return json_encode(['status' => false, 'message' => 'Invalid request'], 500);
            }
        }

    }

    //    public function sendChatmessage(Request $request)
    //    {
    //
    //        $currentUser = User::find(Auth::id());
    //
    //        // Check if the current user's profile type is valid
    //        if (! in_array($currentUser->profileType, ['user'])) {
    //            return response()->json([
    //                'status'  => false,
    //                'message' => 'Invalid role authentication',
    //            ], 403); // 403 Forbidden
    //        }
    //
    //        // Validation rules
    //        $validator = Validator::make($request->all(), [
    //            'bot_id'  => 'required|string',
    //            'message' => 'required|string|max:1000',
    //        ]);
    //
    //        if ($validator->fails()) {
    //            return response()->json([
    //                'status' => false,
    //                'errors' => $validator->errors(),
    //            ], 422);
    //        }
    //
    //        $types       = base64_decode($request->bot_id);
    //        $agentDetail = Bots::where('chatbot_project_id', $types)->first();
    //        if ($agentDetail) {
    //        } else {
    //            return response()->json(['status' => false, 'message' => 'Invalid project id'], 500);
    //        }
    //
    //        $fireBaeFun = new FirebaseFunction($this->database);
    //        $result     = $fireBaeFun->chatbotWork($request, $agentDetail, $types);
    //
    //        $botResult = new FirebaseFunction($this->database);
    //        $chatData  = $botResult->getBotConversation(Auth::id(), $types);
    //
    //        return response()->json(['status' => true, 'message' => 'Message sent successfully', 'data' => $chatData]);
    //
    //    }

    public function chatbotJson(Request $request)
    {

        $currentUser = User::find(Auth::id());

        // Check if the current user's profile type is valid
        if (! in_array($currentUser->profileType, ['agent', 'user', 'company'])) {
            return response()->json([
                'status'  => false,
                'message' => 'Invalid role authentication',
            ], 403); // 403 Forbidden
        }

        // Determine the opposite profile type
        $userProfileType = $currentUser->profileType;

        $validator = Validator::make($request->all(), []);

        // Dynamically add the 'user_id' validation rule if the profile type is 'agent'
        if ($userProfileType == 'agent' || $userProfileType == 'company') {
            $validator->addRules([
                'user_id' => [
                    'required',  // Required only if $userProfileType is 'agent'
                    'exists:users,id',
                ],
            ]);
        }

        if ($userProfileType == 'user') {
            $validator->addRules([
                'company_id' => [
                    'required',
                    'exists:company,id',
                ],
            ]);
        }

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $chatData = [];

        if ($userProfileType == 'agent' || $userProfileType == 'company') {
            $userId     = $request->user_id;
            $authDetail = Auth::user();
            $ownerID    = ($userProfileType == 'agent') ? $authDetail->parent_id : Auth::id();

            $companyDetail = Company::where('userId', $ownerID)->first();

        } else {
            $userId        = Auth::id();
            $companyDetail = Company::find($request->company_id);
        }

        if (! $companyDetail) {
            return response()->json(['status' => false, 'message' => 'Invalid company id'], 500);
        }

        $agentType = $companyDetail->userId.'_'.$companyDetail->id;

        $botResult = new FirebaseFunction($this->database);
        $chatData  = $botResult->getBotConversation($userId, $agentType);

        return response()->json(['status' => true, 'data' => $chatData]);

    }

    public function retriveLastMessage(Request $request)
    {

        $currentUser = User::find(Auth::id());

        // Check if the current user's profile type is valid
        if (! in_array($currentUser->profileType, ['agent', 'user', 'company'])) {
            return response()->json([
                'status'  => false,
                'message' => 'Invalid role authentication',
            ], 403); // 403 Forbidden
        }

        // Determine the opposite profile type
        $userProfileType = $currentUser->profileType;

        $chatData      = [];
        $botConverList = [];

        if ($userProfileType == 'agent' || $userProfileType == 'company') {

            $authDetail    = Auth::user();
            $ownerID       = ($userProfileType == 'agent') ? $authDetail->parent_id : Auth::id();
            $botConverList = BotConversation::getBotConversaction($ownerID);

        } else {
            $userId        = Auth::id();
            $botConverList = BotConversation::getBotConversactionUserSide($userId);

        }

        if (count($botConverList) > 0) {

            foreach ($botConverList as $botdeTails) {
                $agentType    = $botdeTails->company_user_id.'_'.$botdeTails->company_id;
                $userId       = $botdeTails->user_id;
                $path         = $this->tableName.'/user_id_'.$userId.'/agent_'.$agentType;
                $userChatsRef = $this->database->getReference($path);
                $chats        = $userChatsRef->getValue();

                if ($chats) {
                    $chats = array_filter($chats, function ($chat) {
                        return is_array($chat) && isset($chat['timestamp']);
                    });

                    // Get the last message
                    $lastMessage = end($chats);
                    if ($lastMessage) {

                        $companyUid         = $botdeTails->company_user_id;
                        $companyOwnerDetail = User::find($companyUid);

                        $companyDetail = Company::find($botdeTails->company_id);
                        if ($companyDetail) {
                        } else {
                            return response()->json([
                                'status'  => false,
                                'message' => 'Company not found',
                            ]);
                        }
                        $companyName = $companyDetail->companyName;

                        $userProfileDetail = User::find($userId);

                        $res['company_name']    = $companyName;
                        $res['message']         = $lastMessage['message'];
                        $res['timestamp']       = $lastMessage['timestamp'];
                        $res['company_id']      = $botdeTails->company_id;
                        $res['chatSupport']     = $companyDetail->chatSupport;
                        $res['company_user_id'] = $companyUid;
                        $res['company_profile'] = ($companyOwnerDetail->profilePicture != '') ? url('/storage/'.$companyOwnerDetail->profilePicture) : '';
                        $res['user_id']         = $userProfileDetail->id;
                        $res['user_name']       = $userProfileDetail->name;
                        $res['user_profile']    = ($userProfileDetail->profilePicture != '') ? url('/storage/'.$userProfileDetail->profilePicture) : '';
                        array_push($chatData, $res);

                    }
                }

            }

        }

        return response()->json([
            'status'  => true,
            'message' => 'Data retrieved successfully',
            'data'    => $chatData,
        ], 200);

    }
}
