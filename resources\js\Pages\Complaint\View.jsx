import React, { useState } from "react";
import DashboardLayout from "@/Layouts/DashboardLayout";
import { Input } from "@/Components/FormElements";
import Button from "@/Components/Button";
import { <PERSON>, CardContent, PageHeader, StatusBadge } from "@/Components/UI";
import { Link, useForm, router } from "@inertiajs/react";
import { FiEdit2, FiArrowLeft, FiMessageSquare, FiCheck } from "react-icons/fi";

export default function View({ complaint }) {
    const { data, setData, post, processing, reset } = useForm({
        message: "",
    });

    const { patch, processing: updatingStatus } = useForm();

    const handleMessageSubmit = (e) => {
        e.preventDefault();
        post(
            route("complains.messages.store", complaint.cmp_id || complaint.id),
            {
                onSuccess: () => {
                    message.value = "";
                    reset();
                },
            }
        );
    };

    const handleMarkAsResolved = () => {
        if (
            confirm("Are you sure you want to mark this complaint as resolved?")
        ) {
            router.patch(
                route("complains.update", complaint.cmp_id || complaint.id),
                {
                    title: complaint.title,
                    description: complaint.description,
                    priority: complaint.priority_key,
                    company_id: complaint.company.id,
                    assigned_user_id: complaint.assignedUser.id,
                    status: "resolved",
                },
                {
                    onSuccess: () => {
                        // Optionally redirect or show success message
                    },
                }
            );
        }
    };

    const getStatusVariant = (status) => {
        switch (status.toLowerCase()) {
            case "resolved":
                return "success";
            case "in progress":
                return "warning";
            case "new":
                return "secondary";
            case "need user input":
                return "warning";
            default:
                return "secondary";
        }
    };

    const getPriorityVariant = (priority) => {
        switch (priority.toLowerCase()) {
            case "high":
                return "danger";
            case "medium":
                return "warning";
            case "low":
                return "secondary";
            default:
                return "secondary";
        }
    };

    return (
        <DashboardLayout title="View Complaint">
            <title>View Complaint - {complaint.title}</title>

            <div className="space-y-6">
                <PageHeader
                    title={`Complaint: ${
                        complaint.cmp_id || `#${complaint.id}`
                    }`}
                    subtitle="View complaint details and conversation history"
                >
                    <div className="flex gap-2">
                        {complaint.status !== "Resolved" && (
                            <>
                                <Button
                                    variant="default"
                                    onClick={handleMarkAsResolved}
                                    disabled={
                                        updatingStatus ||
                                        complaint.progressStatus === "resolved"
                                    }
                                >
                                    {updatingStatus
                                        ? "Updating..."
                                        : complaint.progressStatus ===
                                          "resolved"
                                        ? "Already Resolved"
                                        : "Mark as Resolved"}
                                </Button>
                                <Link
                                    href={route(
                                        "complains.edit",
                                        complaint.cmp_id || complaint.id
                                    )}
                                >
                                    <Button variant="default">
                                        Edit Complaint
                                    </Button>
                                </Link>
                            </>
                        )}

                        <Link href={route("complains.index")}>
                            <Button variant="default">Back to List</Button>
                        </Link>
                    </div>
                </PageHeader>

                <div className="flex gap-10">
                    <div className="space-y-8 flex-1">
                        <Card>
                            <CardContent>
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                    Complaint Details
                                </h3>

                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Title
                                        </label>
                                        <p className="text-gray-900 font-medium">
                                            {complaint.title}
                                        </p>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Description
                                        </label>
                                        <p className="text-gray-700 whitespace-pre-wrap">
                                            {complaint.description}
                                        </p>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Priority
                                            </label>
                                            <StatusBadge
                                                variant={getPriorityVariant(
                                                    complaint.priority
                                                )}
                                            >
                                                {complaint.priority}
                                            </StatusBadge>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Status
                                            </label>
                                            <StatusBadge
                                                variant={getStatusVariant(
                                                    complaint.status
                                                )}
                                            >
                                                {complaint.status}
                                            </StatusBadge>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Company
                                            </label>
                                            <p className="text-gray-900">
                                                {complaint.company
                                                    ?.companyName || "N/A"}
                                            </p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Assigned User
                                            </label>
                                            <p className="text-gray-900">
                                                {complaint.assignedUser?.name ||
                                                    "N/A"}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Created
                                            </label>
                                            <p className="text-gray-700">
                                                {new Date(
                                                    complaint.created_at
                                                ).toLocaleString()}
                                            </p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Last Updated
                                            </label>
                                            <p className="text-gray-700">
                                                {new Date(
                                                    complaint.updated_at
                                                ).toLocaleString()}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Message Form */}
                        {complaint.status !== "Resolved" && (
                            <Card>
                                <CardContent>
                                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                        Add Message
                                    </h3>
                                    <form
                                        onSubmit={handleMessageSubmit}
                                        className="space-y-4"
                                    >
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Message
                                            </label>
                                            <Input
                                                name="message"
                                                onChange={(e) =>
                                                    setData(
                                                        "message",
                                                        e.target.value
                                                    )
                                                }
                                                as="textarea"
                                                rows={3}
                                                placeholder="Enter your message..."
                                                className="w-full"
                                            />
                                        </div>
                                        <div className="flex gap-2">
                                            <Button
                                                type="submit"
                                                className="bg-cf-primary hover:bg-cf-primary-600 text-white"
                                                disabled={processing}
                                            >
                                                {processing
                                                    ? "Sending..."
                                                    : "Send Message"}
                                            </Button>
                                        </div>
                                    </form>
                                </CardContent>
                            </Card>
                        )}
                    </div>

                    <div className="flex-1">
                        {/* Messages/Conversation */}
                        <Card>
                            <CardContent>
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                    Conversation History
                                </h3>

                                {complaint.messages &&
                                complaint.messages.length > 0 ? (
                                    <div className="space-y-4 overflow-y-auto h-[500px]">
                                        {complaint.messages.map((message) => (
                                            <div
                                                key={message.id}
                                                className="border-l-4 border-cf-primary pl-4 py-2"
                                            >
                                                <div className="flex justify-between items-start mb-2">
                                                    <span className="font-medium text-gray-900">
                                                        {message.sender?.name ||
                                                            "Unknown User"}
                                                    </span>
                                                    <span className="text-sm text-gray-500">
                                                        {new Date(
                                                            message.created_at
                                                        ).toLocaleString()}
                                                    </span>
                                                </div>
                                                <p className="text-gray-700 whitespace-pre-wrap">
                                                    {message.content}
                                                </p>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="text-center py-8">
                                        <p className="text-gray-500">
                                            No messages yet. Be the first to add
                                            a message!
                                        </p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    );
}
