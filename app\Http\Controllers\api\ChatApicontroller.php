<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Models\Bots;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Kreait\Firebase\Contract\Database;

class ChatApicontroller extends Controller
{
    protected $database;

    protected $tableName;

    public function __construct(Database $database)
    {
        $this->database  = $database;
        $this->tableName = 'live_agent_chats';
    }

    public function sendChatmessage(Request $request)
    {

        $checkUser = User::find($request->sender_id);
        if ($checkUser) {
        } else {
            return response()->json(['status' => false, 'message' => 'Invalid authentication'], 500);
        }

        $checkUser = User::find($request->receiver_id);
        if ($checkUser) {
        } else {
            return response()->json(['status' => false, 'message' => 'Invalid authentication'], 500);
        }

        $types = base64_decode($request->bot_id);

        $agentDetail = Bots::where('chatbot_project_id', $types)->first();

        if ($agentDetail) {

            $userInput = $request->message;
            $agneId    = $agentDetail->user_id;

            if ($agneId == $request->sender_id) {

                $userId    = $request->receiver_id;
                $timestemp = time();
                $postData  = [
                    'user_id'    => $request->sender_id,
                    'message'    => $request->message,
                    'agent_type' => $types,
                    'timestamp'  => $timestemp,
                ];

            } else {

                $userId    = $request->sender_id;
                $timestemp = time();
                $postData  = [
                    'user_id'    => $request->sender_id,
                    'message'    => $request->message,
                    'agent_type' => $types,
                    'timestamp'  => $timestemp,
                ];

            }

            try {

                // Concatenate the agent type after user_id
                $path = $this->tableName.'/user_id_'.$userId.'/agent_'.$types;

                $postRef = $this->database->getReference($path)->push($postData);

                return response()->json(['status' => true, 'data' => 'Message sent']);

            } catch (Exception $ex) {
                return response()->json(['status' => false, 'message' => $ex->getMessage()], 500);
            }
        } else {
            return response()->json(['status' => false, 'message' => 'Invalid project id'], 500);
        }

    }

    public function livechatJson(Request $request)
    {

        $checkUser = User::find($request->sender_id);
        if ($checkUser) {
        } else {
            return response()->json(['status' => false, 'message' => 'Invalid authentication'], 500);
        }

        $checkUser = User::find($request->receiver_id);
        if ($checkUser) {
        } else {
            return response()->json(['status' => false, 'message' => 'Invalid authentication'], 500);
        }

        $chatData = [];

        $types = base64_decode($request->bot_id);

        $agentDetail = Bots::where('chatbot_project_id', $types)->first();
        if ($agentDetail) {

            if ($agentDetail->user_id == $request->sender_id) {
                $userId = $request->receiver_id;
            } else {
                $userId = $request->sender_id;
            }

            $agentType = $types;

            $path = $this->tableName.'/user_id_'.$userId.'/agent_'.$agentType;

            $userChatsRef = $this->database->getReference($path);
            $chats        = $userChatsRef->getValue();
            if ($chats) {

                foreach ($chats as $chatId => $chat) {

                    $res['user_id']    = $chat['user_id'];
                    $res['message']    = $chat['message'];
                    $res['agent_type'] = $types;
                    $res['timestamp']  = (isset($chat['timestamp'])) ? $chat['timestamp'] : '';
                    $chatData[]        = $res;
                }

            }

            return response()->json(['status' => true, 'data' => $chatData]);

        } else {
            return response()->json(['status' => false, 'message' => 'Invalid project id'], 500);
        }

    }

    public function chatUserList(Request $request)
    {

        $currentUser = User::find(Auth::id());

        // Check if the current user's profile type is valid
        if (! in_array($currentUser->profileType, ['agent', 'user'])) {
            return response()->json([
                'status'  => false,
                'message' => 'Invalid role authentication',
            ], 403); // 403 Forbidden
        }

        try {
            // Fetch the data from the Firebase table
            $snapshot = $this->database->getReference($this->tableName)->getSnapshot();
            $data     = $snapshot->getValue();

            $checkUser = User::find($request->user_id);
            if ($checkUser) {
            } else {
                return response()->json(['status' => false, 'message' => 'Invalid authentication'], 500);
            }

            $checkBotAgent = Bots::where('user_id', $request->user_id)->first();

            if ($checkBotAgent) {

                $agent_types = Bots::where('user_id', $request->user_id)
                    ->select('chatbot_project_id')
                    ->get()
                    ->pluck('chatbot_project_id')
                    ->toArray();

                $chatData = [];

                // Iterate through the live_agent_chats data
                foreach ($data as $user_id => $chats) {

                    // Iterate through each agent type under the user_id
                    foreach ($chats as $agent_type => $agent_chats) {
                        $agent_type_key = str_replace('agent_', '', $agent_type);
                        // Check if the agent_type matches any in the provided array
                        if (in_array($agent_type_key, $agent_types)) {
                            $userid            = str_replace('user_id_', '', $user_id);
                            $bot_id            = base64_encode($agent_type_key);
                            $res['user_id']    = $userid;
                            $res['bot_id']     = $bot_id;
                            $res['agent_type'] = $agent_type_key;
                            $chatData[]        = $res;
                            break;
                        }
                    }
                }
                if (count($chatData) > 0) {
                    return response()->json(['status' => true, 'data' => $chatData], 200, [], JSON_PRETTY_PRINT);
                } else {
                    return response()->json(['status' => false, 'data' => []], 200, [], JSON_PRETTY_PRINT);
                }

            } else {

                $chatData      = [];
                $AgentchatData = [];

                // Iterate through the live_agent_chats data
                foreach ($data as $user_id => $chats) {

                    if ($user_id === 'user_id_'.$request->user_id) {

                        foreach ($chats as $agent_type_key => $agent_chats) {
                            $agent_type = str_replace('agent_', '', $agent_type_key);
                            if (! in_array($agent_type, $chatData)) {
                                $AgentchatData[] = $agent_type;
                                $bot_id          = base64_encode($agent_type);
                                $checkBotAgent   = Bots::where('chatbot_project_id', $agent_type)->first();
                                if ($checkBotAgent) {
                                    $res['user_id']    = $checkBotAgent->user_id;
                                    $res['bot_id']     = $bot_id;
                                    $res['agent_type'] = $agent_type;
                                    $chatData[]        = $res;
                                }

                            }
                        }

                        break;
                    }

                }
                if (count($chatData) > 0) {
                    return response()->json(['status' => true, 'data' => $chatData], 200, [], JSON_PRETTY_PRINT);
                } else {
                    return response()->json(['status' => false, 'data' => []], 200, [], JSON_PRETTY_PRINT);
                }

            }

        } catch (Exception $ex) {
            return response()->json(['status' => false, 'message' => $ex->getMessage()], 500);
        }
    }
}
