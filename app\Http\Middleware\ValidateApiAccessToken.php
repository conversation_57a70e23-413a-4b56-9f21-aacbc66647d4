<?php

namespace App\Http\Middleware;

use App\Models\ApiAccessToken;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ValidateApiAccessToken
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $token = $request->header('ApiKey');

        if (
            ! $token || ! ApiAccessToken::where('token', $token)
                ->where('is_expired', false)
                ->where('expires_at', '>', now())
                ->exists()
        ) {

            return response()->json(['status' => false, 'message' => 'Unauthorized or token expired'],401);
        }

        return $next($request);
    }
}
