<?php

namespace App\Http\Controllers\Company;

use App\Actions\Subscription\CreateSubscriptionAction;
use App\Http\Controllers\Controller;
use App\Models\PaymentGateway;
use App\Models\SubscriptionPlan;
use App\Models\Subscription;
use App\Models\Invoice;
use App\Models\PaymentTransaction;
use App\Services\InvoicePdfService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class SubscriptionController extends Controller
{
    /**
     * Show the pricing page.
     */
    public function pricing(): Response
    {
        $plans = SubscriptionPlan::active()->ordered()->get();
        $user = Auth::user();
        $currentSubscription = $user->currentSubscription();

        return Inertia::render('Company/Subscription/Pricing', [
            'plans' => $plans->map(function ($plan) {
                return [
                    'id' => $plan->id,
                    'name' => $plan->name,
                    'slug' => $plan->slug,
                    'description' => $plan->description,
                    'formatted_price' => $plan->formatted_price,
                    'monthly_price' => $plan->monthly_price,
                    'billing_interval' => $plan->billing_interval,
                    'trial_days' => $plan->trial_days,
                    'features' => $plan->features,
                    'limits' => $plan->limits,
                    'is_popular' => $plan->is_popular,
                    'discount_percentage' => $plan->discount_percentage,
                ];
            }),
            'currentSubscription' => $currentSubscription ? [
                'id' => $currentSubscription->id,
                'plan' => [
                    'id' => $currentSubscription->plan->id,
                    'name' => $currentSubscription->plan->name,
                ],
                'status' => $currentSubscription->status,
                'trial_ends_at' => $currentSubscription->trial_ends_at,
                'current_period_end' => $currentSubscription->current_period_end,
                'is_active' => $currentSubscription->isActive(),
                'on_trial' => $currentSubscription->onTrial(),
                'trial_days_remaining' => $currentSubscription->trialDaysRemaining(),
            ] : null,
            'paymentGateways' => PaymentGateway::active()->get(['id', 'name', 'display_name']),
        ]);
    }

    /**
     * Show the subscription dashboard.
     */
    public function dashboard()
    {
        $user = Auth::user();
        $subscription = $user->currentSubscription();

        if (!$subscription) {
            return redirect()->route('company.subscription.pricing');
        }

        $subscription->load(['plan', 'invoices' => function ($query) {
            $query->latest()->limit(5);
        }]);

        return Inertia::render('Company/Subscription/Dashboard', [
            'subscription' => [
                'id' => $subscription->id,
                'plan' => [
                    'id' => $subscription->plan->id,
                    'name' => $subscription->plan->name,
                    'description' => $subscription->plan->description,
                    'formatted_price' => $subscription->plan->price,
                    'billing_interval' => $subscription->plan->billing_interval,
                    'features' => $subscription->plan->features,
                    'limits' => $subscription->plan->limits,
                ],
                'status' => $subscription->status,
                'payment_method' => $subscription->payment_method,
                'trial_ends_at' => $subscription->trial_ends_at,
                'current_period_start' => $subscription->current_period_start,
                'current_period_end' => $subscription->current_period_end,
                'ends_at' => $subscription->ends_at,
                'canceled_at' => $subscription->canceled_at,
                'created_at' => $subscription->created_at,
                'is_active' => $subscription->isActive(),
                'on_trial' => $subscription->onTrial(),
                'is_canceled' => $subscription->isCanceled(),
                'trial_days_remaining' => $subscription->trialDaysRemaining(),
                'days_remaining_in_period' => $subscription->daysRemainingInPeriod(),
            ],
            'recentInvoices' => $subscription->invoices->map(function ($invoice) {
                return [
                    'id' => $invoice->id,
                    'invoice_number' => $invoice->invoice_number,
                    'status' => $invoice->status,
                    'formatted_amount_due' => $invoice->formatted_amount_due,
                    'due_date' => $invoice->due_date,
                    'paid_at' => $invoice->paid_at,
                    'created_at' => $invoice->created_at,
                    'is_paid' => $invoice->isPaid(),
                    'is_overdue' => $invoice->isOverdue(),
                ];
            }),
            'availablePlans' => SubscriptionPlan::active()
                ->where('id', '!=', $subscription->subscription_plan_id)
                ->ordered()
                ->get(['id', 'name', 'price', 'billing_interval']),
        ]);
    }

    /**
     * Create a new subscription.
     */
    public function subscribe(Request $request, CreateSubscriptionAction $action)
    {
        $validated = $request->validate([
            'plan_id' => 'required|exists:subscription_plans,id',
            'payment_method' => 'required|in:stripe,paypal',
            'payment_method_details' => 'nullable|array',
        ]);

        $user = Auth::user();
        $plan = SubscriptionPlan::findOrFail($validated['plan_id']);

        // Check if user is a company
        if ($user->profileType !== 'company') {
            return back()->withErrors(['subscription' => 'Only companies can subscribe to plans.']);
        }

        try {
            $subscription = $action->handle(
                $user,
                $plan,
                $validated['payment_method'],
                $validated['payment_method_details'] ?? []
            );

            return redirect()->route('company.subscription.dashboard')
                            ->with('success', 'Subscription created successfully! Your trial period has started.');
        } catch (\Exception $e) {
            return back()->withErrors(['subscription' => $e->getMessage()]);
        }
    }

    /**
     * Show invoices.
     */
    public function invoices(): Response
    {
        $user = Auth::user();
        $subscription = $user->currentSubscription();

        if (!$subscription) {
            return redirect()->route('company.subscription.pricing');
        }

        $invoices = $subscription->invoices()->latest()->paginate(20);

        return Inertia::render('Company/Subscription/Invoices', [
            'invoices' => $invoices->through(function ($invoice) {
                return [
                    'id' => $invoice->id,
                    'invoice_number' => $invoice->invoice_number,
                    'status' => $invoice->status,
                    'formatted_amount_due' => $invoice->formatted_amount_due,
                    'formatted_amount_paid' => $invoice->formatted_amount_paid,
                    'billing_reason' => $invoice->billing_reason,
                    'due_date' => $invoice->due_date,
                    'paid_at' => $invoice->paid_at,
                    'created_at' => $invoice->created_at,
                    'is_paid' => $invoice->isPaid(),
                    'is_overdue' => $invoice->isOverdue(),
                ];
            }),
            'subscription' => [
                'id' => $subscription->id,
                'plan' => [
                    'name' => $subscription->plan->name,
                ],
                'status' => $subscription->status,
            ],
        ]);
    }

    /**
     * Download invoice PDF.
     */
    public function downloadInvoice($invoiceId, InvoicePdfService $pdfService)
    {
        $user = Auth::user();
        $subscription = $user->currentSubscription();

        if (!$subscription) {
            return redirect()->route('company.subscription.pricing')
                ->with('error', 'No active subscription found.');
        }

        $invoice = Invoice::where('id', $invoiceId)
            ->where('subscription_id', $subscription->id)
            ->firstOrFail();


        return $pdfService->downloadPdf($invoice);
    }

    /**
     * Show payment history.
     */
    public function paymentHistory(Request $request)
    {
        $user = Auth::user();
        $subscription = $user->currentSubscription();

        if (!$subscription) {
            return redirect()->route('company.subscription.pricing')
                ->with('error', 'No active subscription found.');
        }

        $transactions = PaymentTransaction::where('user_id', $user->id)
            ->with(['invoice', 'subscription.subscriptionPlan'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return Inertia::render('Company/Subscription/PaymentHistory', [
            'subscription' => $subscription->load('subscriptionPlan'),
            'transactions' => $transactions,
        ]);
    }

    /**
     * Cancel subscription.
     */
    public function cancel(Request $request)
    {
        $user = Auth::user();
        $subscription = $user->currentSubscription();

        if (!$subscription || $subscription->isCanceled()) {
            return back()->withErrors(['subscription' => 'No active subscription to cancel.']);
        }

        $validated = $request->validate([
            'immediately' => 'boolean',
        ]);

        try {
            $subscription->cancel();

            $message = $validated['immediately'] ?? false
                ? 'Subscription canceled immediately.'
                : 'Subscription will be canceled at the end of the current billing period.';

            return redirect()->route('company.subscription.dashboard')
                            ->with('success', $message);
        } catch (\Exception $e) {
            return back()->withErrors(['subscription' => $e->getMessage()]);
        }
    }
}
