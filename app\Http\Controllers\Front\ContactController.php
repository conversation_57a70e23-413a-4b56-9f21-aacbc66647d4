<?php

namespace App\Http\Controllers\Front;

use App\Http\Controllers\Controller;
use App\Http\Requests\ContactFormRequest;
use App\Models\Contact;
use Inertia\Inertia;

class ContactController extends Controller
{
    public function sendQuery(ContactFormRequest $request)
    {
        $validated = $request->validated();

        Contact::create([
            'fullname' => $validated['fullname'],
            'email'    => $validated['email'],
            'subject'  => $validated['subject'],
            'message'  => $validated['message'],
        ]);

        return redirect()->back()->with('success', 'Your message has been sent successfully!');
    }

    public function index()
    {

        $allContacts = Contact::orderBy('contactus.id', 'desc')->paginate(env('PAGE_LIMIT'));
        $title       = 'Inquiry';

        return Inertia::render('Contacts/List', ['allContacts' => $allContacts, 'title' => $title]);

    }

    public function destroy(string $id)
    {
        $contactDetail = Contact::find($id);
        if ($contactDetail) {

            $contactDetail->delete();

            return response()->json(['success' => true]);

        } else {
            return response()->json(['success' => true]);
        }
    }
}
