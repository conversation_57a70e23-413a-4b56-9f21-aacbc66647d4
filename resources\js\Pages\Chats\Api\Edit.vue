<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head } from '@inertiajs/vue3';
import { ref, onMounted, watch, nextTick } from 'vue';
import { usePage, useForm } from '@inertiajs/vue3';
import PageHeading from '@/Components/Global/PageHeading.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import SelectInput from '@/Components/SelectInput.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';

const { api,apiAccess,flash } = usePage().props;
const pageTitle = ref('Edit API');

const requestParams = ref([]);
const responseParams = ref([]);
const showSuccess = ref(false);

const newRequestParam = ref({
    label: '',
    param: '',
    datatype: 'string'
});

const newResponseParam = ref({
    label: '',
    param: '',
    datatype: 'string'
});

const datatypes = ['string', 'number', 'date'];

const form = useForm({
    name: api.name || '',
    url: api.url || '',
    method: api.method || 'GET',
    request_parameters: api.request_parameters || '',
    response_parameters: api.response_parameters || '',
    description: api.description || '',
    username: api.username || '',
    username_param: api.username_param || '',
    password: api.password || '',
    password_param: api.password_param || ''
});

// Watch for changes in the name field
watch(() => form.name, (newValue) => {
    if (newValue) {
        // Remove spaces and replace with underscores
        const formattedName = newValue.toLowerCase().replace(/\s+/g, '_');
        // Add @api- prefix if it doesn't exist
        const prefixedName = formattedName.startsWith('@api-') ? formattedName : '@api-' + formattedName;
        form.name = prefixedName;
    }
});

const methods = ['GET', 'POST'];

onMounted(() => {
    // Initialize request parameters
    if (api.request_parameters) {
        try {
            const params = JSON.parse(api.request_parameters);
            requestParams.value = params;
            let table = document.getElementById("paramsTable");
            let tbody = table.querySelector('tbody');
            
            // Clear existing tbody content
            tbody.innerHTML = '';
            
            // Add existing parameters
            params.forEach(param => {
                let newRow = tbody.insertRow();
                newRow.classList.add("new-row-cls");
                newRow.innerHTML = `
                    <td class="p-1"><input type="text" class="w-full p-2 border border-gray-300 rounded-md" value="${param.label}"></td>
                    <td class="p-1"><input type="text" class="w-full p-2 border border-gray-300 rounded-md" value="${param.param}"></td>
                    <td class="p-1">
                        <select class="w-full p-2 border border-gray-300 rounded-md">
                            <option value="string" ${param.datatype === 'string' ? 'selected' : ''}>String</option>
                            <option value="number" ${param.datatype === 'number' ? 'selected' : ''}>Number</option>
                            <option value="date" ${param.datatype === 'date' ? 'selected' : ''}>Date</option>
                        </select>
                    </td>
                    <td class="p-1 text-center">
                        <button class="px-2 py-1 hover:text-red-500 rounded-md delete-btn">
                            <svg class="dt-deals-icon" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
                        </button>
                    </td>
                `;

                nextTick(() => {
                    const deleteButton = newRow.querySelector(".delete-btn");
                    deleteButton.addEventListener("click", () => {
                        deleteRow(deleteButton);
                        updateRequestParameters();
                    });
                });
            });

            // Add empty row at the bottom
            let emptyRow = tbody.insertRow();
            emptyRow.classList.add("empty-row");
            emptyRow.innerHTML = `
                <td class="p-1">
                    <input type="text" class="w-full p-2 border border-gray-300 rounded-md" @input="addRow">
                </td>
                <td class="p-1">
                    <input type="text" class="w-full p-2 border border-gray-300 rounded-md">
                </td>
                <td class="p-1">
                    <select class="w-full p-2 border border-gray-300 rounded-md">
                        <option value="string">String</option>
                        <option value="number">Number</option>
                        <option value="date">Date</option>
                    </select>
                </td>
                <td></td>
            `;

            nextTick(() => {
                const labelInput = emptyRow.querySelector('input');
                labelInput.addEventListener('input', addRow);
            });
        } catch (e) {
            console.error('Error parsing request parameters:', e);
        }
    }

    // Initialize response parameters
    if (api.response_parameters) {
        try {
            const params = JSON.parse(api.response_parameters);
            responseParams.value = params;
            let table = document.getElementById("responseParamsTable");
            let tbody = table.querySelector('tbody');
            
            // Clear existing tbody content
            tbody.innerHTML = '';
            
            // Add existing parameters
            params.forEach(param => {
                let newRow = tbody.insertRow();
                newRow.classList.add("new-row-cls");
                newRow.innerHTML = `
                    <td class="p-1"><input type="text" class="w-full p-2 border border-gray-300 rounded-md" value="${param.label}"></td>
                    <td class="p-1"><input type="text" class="w-full p-2 border border-gray-300 rounded-md" value="${param.param}"></td>
                    <td class="p-1">
                        <select class="w-full p-2 border border-gray-300 rounded-md">
                            <option value="string" ${param.datatype === 'string' ? 'selected' : ''}>String</option>
                            <option value="number" ${param.datatype === 'number' ? 'selected' : ''}>Number</option>
                            <option value="date" ${param.datatype === 'date' ? 'selected' : ''}>Date</option>
                        </select>
                    </td>
                    <td class="p-1 text-center">
                        <button class="px-2 py-1 hover:text-red-500 rounded-md delete-btn">
                            <svg class="dt-deals-icon" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
                        </button>
                    </td>
                `;

                nextTick(() => {
                    const deleteButton = newRow.querySelector(".delete-btn");
                    deleteButton.addEventListener("click", () => {
                        deleteRow(deleteButton);
                        updateResponseParameters();
                    });
                });
            });

            // Add empty row at the bottom
            let emptyRow = tbody.insertRow();
            emptyRow.classList.add("empty-row");
            emptyRow.innerHTML = `
                <td class="p-1">
                    <input type="text" class="w-full p-2 border border-gray-300 rounded-md" @input="addResponseRow">
                </td>
                <td class="p-1">
                    <input type="text" class="w-full p-2 border border-gray-300 rounded-md">
                </td>
                <td class="p-1">
                    <select class="w-full p-2 border border-gray-300 rounded-md">
                        <option value="string">String</option>
                        <option value="number">Number</option>
                        <option value="date">Date</option>
                    </select>
                </td>
                <td></td>
            `;

            nextTick(() => {
                const labelInput = emptyRow.querySelector('input');
                labelInput.addEventListener('input', addResponseRow);
            });
        } catch (e) {
            console.error('Error parsing response parameters:', e);
        }
    }
});

const addRequestParam = () => {
    if (newRequestParam.value.label && newRequestParam.value.param) {
        requestParams.value.push({...newRequestParam.value});
        form.request_parameters = JSON.stringify(requestParams.value);
        newRequestParam.value = {
            label: '',
            param: '',
            datatype: 'string'
        };
    }
};

const removeRequestParam = (index) => {
    requestParams.value.splice(index, 1);
    form.request_parameters = JSON.stringify(requestParams.value);
};

const addResponseParam = () => {
    if (newResponseParam.value.label && newResponseParam.value.param) {
        responseParams.value.push({...newResponseParam.value});
        form.response_parameters = JSON.stringify(responseParams.value);
        newResponseParam.value = {
            label: '',
            param: '',
            datatype: 'string'
        };
    }
};

const removeResponseParam = (index) => {
    responseParams.value.splice(index, 1);
    form.response_parameters = JSON.stringify(responseParams.value);
};

function addRow(event) {
    let table = document.getElementById("paramsTable");
    let lastRow = table.rows[table.rows.length - 1];
    let labelInput = lastRow?.cells[0]?.querySelector('input');

    if (labelInput && labelInput.value.trim() !== "") {
        let newRow = table.insertRow();
        newRow.classList.add("new-row-cls");
        newRow.innerHTML = `
            <td class="p-1"><input type="text" class="w-full p-2 border border-gray-300 rounded-md"></td>
            <td class="p-1"><input type="text" class="w-full p-2 border border-gray-300 rounded-md"></td>
            <td class="p-1">
                <select class="w-full p-2 border border-gray-300 rounded-md">
                    <option value="string">String</option>
                    <option value="number">Number</option>
                    <option value="date">Date</option>
                </select>
            </td>
            <td class="p-1 text-center">
                <button type="button" class="px-2 py-1 hover:text-red-500 rounded-md delete-btn">
                    <svg class="dt-deals-icon" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
                </button>
            </td>
        `;

        nextTick(() => {
            const newLabelInput = newRow.cells[0].querySelector('input');
            newLabelInput.addEventListener('input', addRow);
            const deleteButton = newRow.querySelector(".delete-btn");
            deleteButton.addEventListener("click", () => {
                deleteRow(deleteButton);
                updateRequestParameters();
            });
        });
    }
}

function addResponseRow(event) {
    let table = document.getElementById("responseParamsTable");
    let lastRow = table.rows[table.rows.length - 1];
    let labelInput = lastRow?.cells[0]?.querySelector('input');

    if (labelInput && labelInput.value.trim() !== "") {
        let newRow = table.insertRow();
        newRow.classList.add("new-row-cls");
        newRow.innerHTML = `
            <td class="p-1"><input type="text" class="w-full p-2 border border-gray-300 rounded-md"></td>
            <td class="p-1"><input type="text" class="w-full p-2 border border-gray-300 rounded-md"></td>
            <td class="p-1">
                <select class="w-full p-2 border border-gray-300 rounded-md">
                    <option value="string">String</option>
                    <option value="number">Number</option>
                    <option value="date">Date</option>
                </select>
            </td>
            <td class="p-1 text-center">
                <button type="button" class="px-2 py-1 hover:text-red-500 rounded-md delete-btn">
                    <svg class="dt-deals-icon" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
                </button>
            </td>
        `;

        nextTick(() => {
            const newLabelInput = newRow.cells[0].querySelector('input');
            newLabelInput.addEventListener('input', addResponseRow);
            const deleteButton = newRow.querySelector(".delete-btn");
            deleteButton.addEventListener("click", () => {
                deleteRow(deleteButton);
                updateResponseParameters();
            });
        });
    }
}

function updateRequestParameters() {
    const requestTable = document.getElementById('paramsTable');
    const params = [];
    
    for (let i = 1; i < requestTable.rows.length; i++) {
        const row = requestTable.rows[i];
        const label = row.cells[0]?.querySelector('input')?.value;
        const param = row.cells[1]?.querySelector('input')?.value;
        const datatype = row.cells[2]?.querySelector('select')?.value;
        
        if (label && param) {
            params.push({ label, param, datatype: datatype || 'string' });
        }
    }
    
    form.request_parameters = JSON.stringify(params);
}

function updateResponseParameters() {
    const responseTable = document.getElementById('responseParamsTable');
    const params = [];
    
    for (let i = 1; i < responseTable.rows.length; i++) {
        const row = responseTable.rows[i];
        const label = row.cells[0]?.querySelector('input')?.value;
        const param = row.cells[1]?.querySelector('input')?.value;
        const datatype = row.cells[2]?.querySelector('select')?.value;
        
        if (label && param) {
            params.push({ label, param, datatype: datatype || 'string' });
        }
    }
    
    form.response_parameters = JSON.stringify(params);
}

function deleteRow(button) {
    const row = button.closest('tr');
    row.remove();
}

const handleSubmit = () => {
    if (form.name === '@apiauth-get_api_token') {
        // For authentication API, collect only response parameters
        const responseTable = document.getElementById('responseParamsTable');
        const responseParams = [];

        // Collect response parameters
        for (let i = 1; i < responseTable.rows.length; i++) {
            const row = responseTable.rows[i];
            const label = row.cells[0]?.querySelector('input')?.value;
            const param = row.cells[1]?.querySelector('input')?.value;
            const datatype = row.cells[2]?.querySelector('select')?.value;
            
            if (label && param) {
                responseParams.push({ label, param, datatype: datatype || 'string' });
            }
        }

        form.response_parameters = JSON.stringify(responseParams);
        form.patch(route('vendor-apis.update', api.id));
        return;
    }

    // For non-auth APIs, collect both request and response parameters
    const requestTable = document.getElementById('paramsTable');
    const responseTable = document.getElementById('responseParamsTable');
    
    const requestParams = [];
    const responseParams = [];

    // Collect request parameters
    for (let i = 1; i < requestTable.rows.length; i++) {
        const row = requestTable.rows[i];
        const label = row.cells[0]?.querySelector('input')?.value;
        const param = row.cells[1]?.querySelector('input')?.value;
        const datatype = row.cells[2]?.querySelector('select')?.value;
        
        if (label && param) {
            requestParams.push({ label, param, datatype: datatype || 'string' });
        }
    }

    // Collect response parameters
    for (let i = 1; i < responseTable.rows.length; i++) {
        const row = responseTable.rows[i];
        const label = row.cells[0]?.querySelector('input')?.value;
        const param = row.cells[1]?.querySelector('input')?.value;
        const datatype = row.cells[2]?.querySelector('select')?.value;
        
        if (label && param) {
            responseParams.push({ label, param, datatype: datatype || 'string' });
        }
    }

    form.request_parameters = JSON.stringify(requestParams);
    form.response_parameters = JSON.stringify(responseParams);

    // Submit the form
    form.patch(route('vendor-apis.update', api.id));
};


// Add this watch effect to handle flash messages
watch(() => usePage().props.flash, (newFlash) => {
    if (newFlash?.message) {
        showSuccess.value = true;
        setTimeout(() => {
            showSuccess.value = false;
        }, 3000);
    }
}, { immediate: true });

</script>

<template>
    <Head :title="pageTitle" />

    <AuthenticatedLayout>
        <!-- <PageHeading :title="pageTitle"></PageHeading> -->

        <div class="flex">
            <div class="flex-1">
                <div class="form-main-body">
                    <div class="border-stroke border rounded-md">
                        <div class="p-6 text-gray-900">

                            

                            <form @submit.prevent="handleSubmit" class="space-y-6">

                                 <!-- Success Message -->
                                 <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
                                    leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
                                    <p v-if="showSuccess" enter-active-class="transition ease-in-out"
                                        enter-from-class="opacity-0" leave-active-class="transition ease-in-out"
                                        leave-to-class="opacity-0" :class="$page.props.flash?.class">
                                        {{ $page.props.flash.message }}</p>
                                </Transition>



                                <!-- Basic Fields -->
                                <div>
                                    <InputLabel for="name" value="API Name" />
                                    <TextInput id="name" type="text" class="mt-1 block w-full" v-model="form.name" required :readonly="form.name === '@apiauth-get_api_token'" :class="{'!bg-gray-100': form.name === '@apiauth-get_api_token'}" />
                                    <InputError :message="form.errors.name" class="mt-2" />
                                </div>

                                <div>
                                    <InputLabel for="url" value="API URL" />
                                    <TextInput id="url" type="text" class="mt-1 block w-full" v-model="form.url" required />
                                    <InputError :message="form.errors.url" class="mt-2" />
                                </div>

                                <div>
                                    <InputLabel for="method" value="HTTP Method" />
                                    <SelectInput id="method" class="mt-1 block w-full" v-model="form.method" required>
                                        <option v-for="method in methods" :key="method" :value="method">
                                            {{ method }}
                                        </option>
                                    </SelectInput>
                                    <InputError :message="form.errors.method" class="mt-2" />
                                </div>

                                <!-- Request Parameters Table -->
                                <div v-if="form.name !== '@apiauth-get_api_token'" class="mt-4 mb-4 border-b pb-2">
                                    <h3 class="font-medium mb-2">Request Parameters</h3>
                                    <table class="w-full border-collapse" id="paramsTable">
                                        <thead>
                                            <tr>
                                                <th class="p-1 text-left font-medium">Label</th>
                                                <th class="p-1 text-left font-medium">Parameter</th>
                                                <th class="p-1 text-left font-medium">Type</th>
                                                <th class="p-1 text-left font-medium"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="p-1">
                                                    <TextInput type="text" class="w-full p-2 border border-gray-300 rounded-md"
                                                        @input="addRow" />
                                                </td>
                                                <td class="p-1">
                                                    <TextInput type="text" class="w-full p-2 border border-gray-300 rounded-md" />
                                                </td>
                                                <td class="p-1">
                                                    <select class="w-full p-2 border border-gray-300 rounded-md">
                                                        <option v-for="type in datatypes" :key="type" :value="type">
                                                            {{ type }}
                                                        </option>
                                                    </select>
                                                </td>
                                                <td></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Authentication Details (shown only for @apiauth-get_api_token) -->
                                <div v-if="form.name === '@apiauth-get_api_token'" class="space-y-4">
                                    <h3 class="font-semibold">Authentication Detail</h3>
                                    
                                    <div class="flex gap-2">
                                        <div class="flex-1">
                                            <TextInput 
                                                type="text" 
                                                placeholder="Parameter name for username"
                                                v-model="form.username_param"
                                            />
                                            <InputError :message="form.errors.username_param" class="mt-2" />
                                        </div>
                                        <div class="flex-1">
                                            <TextInput 
                                                type="text" 
                                                placeholder="Username" 
                                                v-model="form.username" 
                                            />
                                            <InputError :message="form.errors.username" class="mt-2" />
                                        </div>
                                    </div>
                                    <div class="flex gap-2">
                                        <div class="flex-1">
                                            <TextInput 
                                                type="text" 
                                                placeholder="Parameter name for password"
                                                v-model="form.password_param"
                                            />
                                            <InputError :message="form.errors.password_param" class="mt-2" />
                                        </div>
                                        <div class="flex-1">
                                            <TextInput 
                                                type="text" 
                                                placeholder="Password" 
                                                v-model="form.password" 
                                            />
                                            <InputError :message="form.errors.password" class="mt-2" />
                                        </div>
                                    </div>
                                </div>

                                <!-- Response Parameters Table -->
                                <div class="mt-4 mb-4 border-b pb-2">
                                    <h3 class="font-medium mb-2">Response Parameters</h3>
                                    <table class="w-full border-collapse" id="responseParamsTable">
                                        <thead>
                                            <tr>
                                                <th class="p-1 text-left font-medium">Label</th>
                                                <th class="p-1 text-left font-medium">Parameter</th>
                                                <th class="p-1 text-left font-medium">Type</th>
                                                <th class="p-1 text-left font-medium"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="p-1">
                                                    <TextInput type="text" class="w-full p-2 border border-gray-300 rounded-md"
                                                        @input="addResponseRow" />
                                                </td>
                                                <td class="p-1">
                                                    <TextInput type="text" class="w-full p-2 border border-gray-300 rounded-md" />
                                                </td>
                                                <td class="p-1">
                                                    <select class="w-full p-2 border border-gray-300 rounded-md">
                                                        <option v-for="type in datatypes" :key="type" :value="type">
                                                            {{ type }}
                                                        </option>
                                                    </select>
                                                </td>
                                                <td></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <div>
                                    <InputLabel for="description" value="Description" />
                                    <TextInput id="description" type="text" class="mt-1 block w-full" v-model="form.description" />
                                    <InputError :message="form.errors.description" class="mt-2" />
                                </div>

                                <div class="flex items-center gap-4">
                                    <button type="submit" class="dk-update-btn">Update</button>
                                    <button type="button" class="dk-cancle-btn" @click="$inertia.visit(route('vendor-apis.index'))">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
