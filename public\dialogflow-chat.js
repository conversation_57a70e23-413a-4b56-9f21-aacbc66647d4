(function(window, document) {


    const DEFAULT_CONFIG = {
        companyName: 'Your Company Name',
        primaryColor: '#337E81',
        botAvatar: '/images/chat-icon.png',
        welcomeMessage: 'Hello! How can I help you today?',
        endpoint: 'https://your-backend-api.com/chat'
    };

    const style = document.createElement('style');
    style.textContent = `
        .chat-box-main .chat-icon {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            z-index: 9998;
            transition: transform 0.3s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .chat-box-main .send-btn svg {
            width: 20px;
            height: 20px;
        }

        .chat-box-main .chat-icon svg {
            width: 32px;
            height: 32px;
            transition: transform 0.3s ease-in-out;
        }

        .chat-box-main .message-avatar {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            border-radius: 50%;
            overflow: hidden;
        }

        .chat-box-main .message-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .chat-box-main .chat-icon:hover {
            transform: scale(1.05);
        }

        .chat-box-main .chat-icon:hover svg {
            transform: scale(0.95) rotate(5deg);
        }

        .chat-box-main .chat-icon::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: var(--primary-color);
            opacity: 0.4;
            transform: scale(1);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 0.4; }
            50% { transform: scale(1.2); opacity: 0; }
            100% { transform: scale(1); opacity: 0; }
        }

        .chat-box-main .chat-wrapper {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 90%;
            max-width: 400px;
            max-height: 550px;
            min-height: 300px;
            border-radius: 16px;
            background-color: #fff;
            box-shadow: rgba(0, 0, 0, 0.05) 0px 0.48px 2.41px -0.38px, 
                        rgba(0, 0, 0, 0.17) 0px 4px 20px -0.75px;
            z-index: 9999;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-box-main .chat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: white;
        }

        .chat-box-main .chat-messages {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            background-color: #f8f8f8;
            font-size: 14px;
            line-height: normal;
        }

        .chat-box-main .message {
            margin: 15px 0;
            display: flex;
            align-items: flex-start;
        }

        .chat-box-main .user-message {
            justify-content: flex-end;
        }

        .chat-box-main .bot-message {
            justify-content: flex-start;
        }

        .chat-box-main .message-content {
            max-width: 70%;
            padding: 10px;
            border-radius: 15px;
            background: #FFF;
        }

        .chat-box-main .user-message .message-content {
            background-color: var(--primary-color);
            color: white;
        }

        .chat-box-main .bot-message .message-content {
            border-radius: 10px;
            color: #000;
            background-color: #FFF;
            box-shadow: rgba(0, 0, 0, 0.15) 0px 0.6px 0.54px -1.33px,
                        rgba(0, 0, 0, 0.13) 0px 2.29px 2.06px -2.67px,
                        rgba(0, 0, 0, 0.04) 0px 10px 9px -4px;
        }

        .chat-box-main .chat-input {
            padding: 15px;
            border-top: 1px solid #dee2e6;
            position: relative;
        }

        .chat-box-main .input-group {
            display: flex;
            align-items: center;
            padding: 4px 15px;
            border: 1px solid #dee2e6;
            border-radius: 16px;
            background-color: #fff;
            overflow: hidden;
        }

        .chat-box-main .chat-input input {
            flex: 1;
            border: none;
            outline: none;
            padding: 8px;
            font-size: 14px;
            background: transparent;
        }

        .chat-box-main .send-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            background: none;
            border: none;
            padding: 5px;
            cursor: pointer;
            color: var(--primary-color);
            transition: transform 0.2s ease;
        }

        .chat-box-main .send-btn:hover {
            transform: scale(1.1);
        }

        .chat-box-main .hidden {
            display: none;
        }
    `;

    class DialogflowChat {
        constructor(config = {}) {
            this.config = { ...DEFAULT_CONFIG, ...config };
            this.init();
        }

        init() {
            document.head.appendChild(style);
            document.documentElement.style.setProperty('--primary-color', this.config.primaryColor);
            
            const container = document.createElement('div');
            container.className = 'chat-box-main';
            container.innerHTML = this.getChatHTML();
            document.body.appendChild(container);

            this.initializeElements(container);
            this.addEventListeners();
            this.addMessage(this.config.welcomeMessage, 'bot');
        }

        getChatHTML() {
            return `
                <div id="chatbot-icon" class="chat-icon">
                    <svg class="default-icon" viewBox="0 0 32 32">
                        <path fill="#FFFFFF" d="M12.63,26.46H8.83a6.61,6.61,0,0,1-6.65-6.07,89.05,89.05,0,0,1,0-11.2A6.5,6.5,0,0,1,8.23,3.25a121.62,121.62,0,0,1,15.51,0A6.51,6.51,0,0,1,29.8,9.19a77.53,77.53,0,0,1,0,11.2,6.61,6.61,0,0,1-6.66,6.07H19.48L12.63,31V26.46"></path>
                    </svg>
                </div>
                <div id="custom-chatbot-wrapper" class="chat-wrapper hidden">
                    <div class="chat-header">
                        <div class="company-name">${this.config.companyName}</div>
                        <div class="minimize-btn">
                            <svg width="24" height="24" viewBox="0 0 32 32" fill="currentColor">
                                <path d="M24.18,21h-16.36c-0.45,0-0.82,0.45-0.82,1s0.37,1,0.82,1h16.36c0.45,0,0.82-0.45,0.82-1s-0.37-1-0.82-1z"></path>
                            </svg>
                        </div>
                    </div>
                    <div id="custom-chatbot-messages" class="chat-messages"></div>
                    <div class="chat-input">
                        <div class="input-group">
                            <input type="text" name="message" placeholder="Type your message...">
                            <button class="send-btn">
                                <svg viewBox="0 0 32 32" fill="currentColor">
                        <path
                            d="M9.05674797,7.10056554 L9.13703813,7.13553157 L25.4390381,15.1015316 L25.5284558,15.1506535 L25.6286153,15.2222405 C25.7452987,15.313793 25.8339182,15.4266828 25.895416,15.5505399 L25.9423517,15.6622033 L25.9751927,15.7773803 L25.9891204,15.8509608 L25.998657,15.9475578 L25.9972397,16.0748669 L25.9800642,16.201216 L25.9701282,16.2435678 C25.9550365,16.3071288 25.9331784,16.3694784 25.9050831,16.4294253 L25.8937351,16.4490792 C25.8488724,16.5422577 25.7878083,16.6290528 25.7112518,16.7055442 L25.609137,16.7931281 L25.539527,16.8424479 L25.4390381,16.8984684 L9.05674797,24.8994345 C8.4880852,25.1179893 7.84373932,24.9716543 7.42618713,24.5298922 C7.02348961,24.1049956 6.89354829,23.48994 7.08502271,22.9526995 L9.44381329,15.9994998 L7.08997091,9.06153122 C6.90991684,8.5560159 7.00409914,7.99707209 7.33051276,7.58090053 L7.4252609,7.47108641 C7.84373932,7.02834566 8.4880852,6.8820107 9.05674797,7.10056554 Z">
                        </path>
                    </svg>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        initializeElements(container) {
            this.chatIcon = container.querySelector('#chatbot-icon');
            this.chatWrapper = container.querySelector('#custom-chatbot-wrapper');
            this.messagesContainer = container.querySelector('#custom-chatbot-messages');
            this.input = container.querySelector('.chat-input input');
            this.sendButton = container.querySelector('.send-btn');
            this.minimizeBtn = container.querySelector('.minimize-btn');
        }

        addEventListeners() {
            this.chatIcon.addEventListener('click', () => this.toggleChat(true));
            this.minimizeBtn.addEventListener('click', () => this.toggleChat(false));
            this.sendButton.addEventListener('click', () => this.sendMessage());
            this.input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.sendMessage();
            });
        }

        toggleChat(show) {
            if (show) {
                this.chatWrapper.classList.remove('hidden');
                this.chatIcon.classList.add('hidden');
            } else {
                this.chatWrapper.classList.add('hidden');
                this.chatIcon.classList.remove('hidden');
            }
        }

        addMessage(text, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            
            let html = '<div class="message-content">';
            if (type === 'bot') {
                html = `
                    <div class="message-avatar">
                        <img src="${this.config.baseUrl}${this.config.botAvatar}" alt="Bot Avatar">
                    </div>
                    ${html}
                `;
            }
            html += `${text}</div>`;
            messageDiv.innerHTML = html;
            
            this.messagesContainer.appendChild(messageDiv);
            this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        }

        async sendMessage() {
            const text = this.input.value.trim();
            if (!text) return;
            

            this.addMessage(text, 'user');
            this.input.value = '';

            try {
                const url = this.config.endpoint.replace(/([?&])text=[^&]*/, `$1text=${encodeURIComponent(text)}`);
                console.log(url);
                const response = await fetch(url, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: text })
                });
                const data = await response.json();
                this.addMessage(data.response, 'bot');
            } catch (error) {
                console.error('Error sending message:', error);
                this.addMessage('Sorry, I encountered an error. Please try again later.', 'bot');
            }
        }
    }

    window.DialogflowChat = DialogflowChat;
})(window, document);