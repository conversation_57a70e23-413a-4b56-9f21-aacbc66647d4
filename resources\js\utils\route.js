// Simple route helper for Inertia.js with React
export function route(name, params = {}) {
    const routes = {
        // Complaint routes (Resource routes) - Using cmp_id for SEO-friendly URLs
        'complains.index': '/complains',
        'complains.create': '/complains/create',
        'complains.store': '/complains',
        'complains.show': '/complains/{cmp_id}',
        'complains.edit': '/complains/{cmp_id}/edit',
        'complains.update': '/complains/{cmp_id}',
        'complains.destroy': '/complains/{cmp_id}',
        'complains.messages.store': '/complains/{cmp_id}/messages',
        // Backward compatibility
        'complains': '/complains',

        // Auth routes
        'login': '/login',
        'register': '/register',
        'logout': '/logout',
        'dashboard': '/dashboard',

        // Other routes
        'profile': '/profile',
        'apis.index': '/apis',
        'apis.create': '/apis/create',
        'apis.edit': '/apis/{id}/edit',
        'agents.index': '/agents',
        'agents.create': '/agents/create',
        'agents.edit': '/agents/{id}/edit',
        'chats': '/chats',
        'intents': '/intents',
        'intents.create': '/intents/create',
        'deals.index': '/deals',
        'deals.create': '/deals/create',

        // Public routes
        'companies': '/companies',
        'deals.public': '/today-deals',
        'contact': '/contact',

        // Admin routes
        'admin.payment-gateways.index': '/admin/payment-gateways',
        'admin.payment-gateways.edit': '/admin/payment-gateways/{paymentGateway}/edit',
        'admin.payment-gateways.update': '/admin/payment-gateways/{paymentGateway}',
        'admin.payment-gateways.toggle-status': '/admin/payment-gateways/{paymentGateway}/toggle-status',
        'admin.payment-gateways.toggle-test-mode': '/admin/payment-gateways/{paymentGateway}/toggle-test-mode',
        'admin.payment-gateways.test-connection': '/admin/payment-gateways/{paymentGateway}/test-connection',

        // Stripe Connect routes
        'admin.stripe.connect': '/admin/stripe/connect',
        'admin.stripe.callback': '/admin/stripe/callback',
        'admin.stripe.disconnect': '/admin/stripe/disconnect',

        // Webhook routes
        'webhooks.stripe': '/webhooks/stripe',
        'webhooks.paypal': '/webhooks/paypal',

        'admin.subscription-plans.index': '/admin/subscription-plans',
        'admin.subscription-plans.create': '/admin/subscription-plans/create',
        'admin.subscription-plans.store': '/admin/subscription-plans',
        'admin.subscription-plans.show': '/admin/subscription-plans/{subscriptionPlan}',
        'admin.subscription-plans.edit': '/admin/subscription-plans/{subscriptionPlan}/edit',
        'admin.subscription-plans.update': '/admin/subscription-plans/{subscriptionPlan}',
        'admin.subscription-plans.destroy': '/admin/subscription-plans/{subscriptionPlan}',
        'admin.subscription-plans.toggle-status': '/admin/subscription-plans/{subscriptionPlan}/toggle-status',

        'admin.subscriptions.index': '/admin/subscriptions',
        'admin.subscriptions.show': '/admin/subscriptions/{subscription}',
        'admin.subscriptions.cancel': '/admin/subscriptions/{subscription}/cancel',
        'admin.subscriptions.resume': '/admin/subscriptions/{subscription}/resume',

        // Admin reports routes
        'admin.reports.index': '/admin/reports',
        'admin.reports.subscription-metrics': '/admin/reports/subscription-metrics',
        'admin.reports.revenue-metrics': '/admin/reports/revenue-metrics',
        'admin.reports.churn-analysis': '/admin/reports/churn-analysis',

        // Company subscription routes
        'company.subscription.pricing': '/company/subscription/pricing',
        'company.subscription.dashboard': '/company/subscription/dashboard',
        'company.subscription.subscribe': '/company/subscription/subscribe',
        'company.subscription.invoices': '/company/subscription/invoices',
        'company.subscription.invoices.download': '/company/subscription/invoices/{invoice}/download',
        'company.subscription.payment-history': '/company/subscription/payment-history',
        'company.subscription.cancel': '/company/subscription/cancel',
    };

    let routePattern = routes[name];

    if (!routePattern) {
        console.warn(`Route "${name}" not found`);
        return '#';
    }

    // Handle parameters
    if (typeof params === 'object' && params !== null) {
        // If params is a single value (like an ID), treat it as the first parameter
        if (typeof params === 'string' || typeof params === 'number') {
            routePattern = routePattern.replace(/\{[^}]+\}/, params);
        } else {
            // Replace named parameters
            Object.keys(params).forEach(key => {
                routePattern = routePattern.replace(`{${key}}`, params[key]);
            });

            // Replace unnamed parameters in order
            const values = Object.values(params);
            let index = 0;
            routePattern = routePattern.replace(/\{[^}]+\}/g, () => {
                return values[index++] || '';
            });
        }
    } else if (params) {
        // Single parameter (ID or cmp_id)
        routePattern = routePattern.replace(/\{[^}]+\}/, params);
    }

    return routePattern;
}

// Make route function globally available
if (typeof window !== 'undefined') {
    window.route = route;
}
