import React from "react";
import { Link, usePage } from "@inertiajs/react";
import clsx from "clsx";
import { BrandLogo } from "@/Components/Icons";
import { FiX } from "react-icons/fi";
import Button from "./Button";
import {
    FiAnchor,
    FiFileText,
    FiGift,
    FiGrid,
    FiInfo,
    FiMessageCircle,
    FiUsers,
    FiCreditCard,
    FiDollarSign,
    FiBarChart,
} from "react-icons/fi";

// Base navigation links for all users
const baseNavLinks = [
    {
        name: "Dashboard",
        href: "/dashboard",
        icon: <FiGrid />,
        roles: ["admin", "company", "agent", "user"],
    },
    {
        name: "Cha<PERSON>",
        href: "/chats",
        icon: <FiMessageCircle />,
        roles: ["company", "agent", "user"],
    },
    {
        name: "Complains",
        href: "/complains",
        icon: <FiFileText />,
        roles: ["admin", "company", "agent", "user"],
    },
    {
        name: "Deals",
        href: "/deals",
        icon: <FiGift />,
        roles: ["company"],
    },
    {
        name: "Intents",
        href: "/intents",
        icon: <FiInfo />,
        roles: ["company"],
    },
    {
        name: "Apis",
        href: "/apis",
        icon: <FiAnchor />,
        roles: ["company"],
    },
    {
        name: "Agents",
        href: "/agents",
        icon: <FiUsers />,
        roles: ["company"],
    },
];

// Admin-specific navigation links
const adminNavLinks = [
    {
        name: "Payment Gateways",
        href: "/admin/payment-gateways",
        icon: <FiCreditCard />,
        roles: ["admin"],
    },
    {
        name: "Subscription Plans",
        href: "/admin/subscription-plans",
        icon: <FiDollarSign />,
        roles: ["admin"],
    },
    {
        name: "Subscriptions",
        href: "/admin/subscriptions",
        icon: <FiUsers />,
        roles: ["admin"],
    },
    {
        name: "Reports",
        href: "/admin/reports",
        icon: <FiBarChart />,
        roles: ["admin"],
    },
];

// Company subscription links
const companySubscriptionLinks = [
    {
        name: "Subscription",
        href: "/company/subscription/dashboard",
        icon: <FiCreditCard />,
        roles: ["company"],
    },
];

export default function Sidebar({ mobileMenuOpen, setMobileMenuOpen }) {
    const { url } = usePage();
    const { auth } = usePage().props;
    const user = auth?.user;
    const userRole = user?.profileType;



    return (
        <>
            <aside
                className={clsx(
                    "max-md:fixed max-md:z-40 md:flex md:flex-col bg-white transform transition-transform duration-200 w-64 max-md:h-full",
                    { "translate-x-0": mobileMenuOpen },
                    { "-translate-x-full": !mobileMenuOpen },
                    "md:static md:shadow-lg md:flex md:translate-x-0 border-r border-gray-100"
                )}
            >
                <div className="flex items-center p-6 gap-4">
                    <BrandLogo className="w-40" />
                    <Button
                        className="md:hidden"
                        variant="secondary"
                        onClick={() => setMobileMenuOpen(false)}
                        aria-label="Close sidebar"
                    >
                        <FiX />
                    </Button>
                </div>
                <nav className="flex-1 px-4 py-6 space-y-1">
                    {/* Regular Navigation Links */}
                    {userRole && baseNavLinks.filter(link => link.roles.includes(userRole)).map((link) => (
                        <Link
                            key={link.name}
                            href={link.href}
                            className={clsx(
                              "flex items-center gap-3 px-4 py-2 rounded-lg text-gray-700 hover:bg-cf-primary/20 hover:text-black transition",
                              url && url.includes(link.href) && "bg-cf-primary/20 text-black font-semibold"
                            )}
                            onClick={() => setMobileMenuOpen(false)}
                        >
                            {link.icon}
                            {link.name}
                        </Link>
                    ))}

                    {/* Admin Section */}
                    {userRole === 'admin' && (
                        <>
                            <div className="pt-4 pb-2">
                                <div className="px-4">
                                    <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                                        Administration
                                    </h3>
                                </div>
                            </div>
                            {adminNavLinks.map((link) => (
                                <Link
                                    key={link.name}
                                    href={link.href}
                                    className={clsx(
                                      "flex items-center gap-3 px-4 py-2 rounded-lg text-gray-700 hover:bg-cf-primary/20 hover:text-black transition",
                                      url && url.includes(link.href) && "bg-cf-primary/20 text-black font-semibold"
                                    )}
                                    onClick={() => setMobileMenuOpen(false)}
                                >
                                    {link.icon}
                                    {link.name}
                                </Link>
                            ))}
                        </>
                    )}

                    {/* Company Subscription Section */}
                    {userRole === 'company' && (
                        <>
                            <div className="pt-4 pb-2">
                                <div className="px-4">
                                    <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                                        Billing
                                    </h3>
                                </div>
                            </div>
                            {companySubscriptionLinks.map((link) => (
                                <Link
                                    key={link.name}
                                    href={link.href}
                                    className={clsx(
                                      "flex items-center gap-3 px-4 py-2 rounded-lg text-gray-700 hover:bg-cf-primary/20 hover:text-black transition",
                                      url && url.includes(link.href) && "bg-cf-primary/20 text-black font-semibold"
                                    )}
                                    onClick={() => setMobileMenuOpen(false)}
                                >
                                    {link.icon}
                                    {link.name}
                                </Link>
                            ))}
                        </>
                    )}
                </nav>
            </aside>
        </>
    );
}
