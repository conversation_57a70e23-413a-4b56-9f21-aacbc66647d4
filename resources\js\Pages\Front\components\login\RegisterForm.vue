<script setup>
import { ref } from 'vue';
import Button from '../reusable/Button.vue';
import { useForm } from '@inertiajs/vue3';
import FormInput from '../reusable/FormInput.vue';
import InputError from '@/Components/InputError.vue';
import ResponsiveNavLink from '../ResponsiveNavLink.vue';

const registrationType = ref('user');

const userFields = [
  { label: 'Full Name', inputIdentifier: 'name', model: 'name' },
  { label: 'Email', inputIdentifier: 'email', inputType: 'email', model: 'email' },
  { label: 'Password', inputIdentifier: 'password', inputType: 'password', model: 'password' },
  { label: 'Confirm Password', inputIdentifier: 'password_confirmation', inputType: 'password', model: 'password_confirmation' }
];

const companyFields = [
  { label: 'Company Name', inputIdentifier: 'companyname', model: 'companyname' },
  { label: 'Email', inputIdentifier: 'email', inputType: 'email', model: 'email' },
  { label: 'Password', inputIdentifier: 'password', inputType: 'password', model: 'password' },
  { label: 'Confirm Password', inputIdentifier: 'password_confirmation', inputType: 'password', model: 'password_confirmation' }
];



const form = useForm({
  companyname: '',
  name: '',
  email: '',
  password: '',
  password_confirmation: '',
});

const switchRegistrationType = (type) => {
  form.reset('companyname', 'name'),
    registrationType.value = type;
};

const submit = () => {
  form.post(route('register-user'), {
    onFinish: () => form.reset('password', 'password_confirmation'),
  });
};

</script>

<template>
  <div class="mx-auto w-full md:w-1/2">
    <div class="login-body-cls">
      <p class="mb-4 font-general-medium text-2xl text-center text-cfp-500-dark">
        Signup
      </p>
      <!-- Registration type options -->
      <!-- <div class="flex justify-center mb-6">
        <button @click="switchRegistrationType('user')" :class="{ 'bg-gray-500 text-white': registrationType === 'user' }" class="bg-gray-200 hover:bg-gray-300 px-4 py-2.5 rounded-l-lg focus:ring-2 focus:ring-indigo-500 font-medium text-slate-800 text-sm focus:outline-none">
          Register as User
        </button>
        <button @click="switchRegistrationType('company')" :class="{ 'bg-gray-500 text-white': registrationType === 'company' }" class="bg-gray-200 hover:bg-gray-300 px-4 py-2.5 rounded-r-lg focus:ring-2 focus:ring-indigo-500 font-medium text-slate-800 text-sm focus:outline-none">
          Register as Company
        </button>
      </div> -->

      <!-- Registration form -->
      <form @submit.prevent="submit" class="space-y-4 pt-4 border-t font-general-regular">

        <template v-for="(field, index) in registrationType === 'user' ? userFields : companyFields">
          <FormInput v-model="form[field.model]" :label="field.label" :inputIdentifier="field.inputIdentifier"
            :inputType="field.inputType" :name="field.inputIdentifier" />
          <InputError class="" :message="form.errors[field.inputIdentifier]" />
        </template>

        <div class="text-center">
          <Button title="Submit" class="w-60 max-md:w-auto dk-update-btn" type="submit" aria-label="Submit" />
        </div>


      </form>
    </div>
  </div>
</template>
