<script setup>
import { ref } from 'vue'
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, usePage } from '@inertiajs/vue3';
import TableDeals from '@/Components/Tables/TableDeals.vue'
import BreadcrumbDefault from '@/Components/Breadcrumbs/BreadcrumbDefault.vue'
const pageTitle = ref('Deals')

const { deals } = usePage().props;

</script>

<template>

  <Head :title="pageTitle" />

  <AuthenticatedLayout>

    <div class="flex">


      <div class="flex-1">

        <TableDeals :deals="deals" />

      </div>

    </div>

  </AuthenticatedLayout>
</template>
