<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, usePage } from '@inertiajs/vue3';
import { ref } from 'vue';
import BotChatCard from '@/Components/BotsChat/BotChatCard.vue';
import UserBotChat from '@/Components/BotsChat/UserBotChat.vue';
import BreadcrumbDefault from '@/Components/Breadcrumbs/BreadcrumbDefault.vue'
import PageHeading from '@/Components/Global/PageHeading.vue';
const pageTitle = ref('Chats')

const { botConverList, authDetail, chatData, sourceID, umid, userData, activeTab } = usePage().props;

const currentChatData = ref({});
const currentSourceID = ref('');
const currentUmid = ref('');
const currentUserDetail = ref('');

currentChatData.value = chatData;
currentSourceID.value = sourceID;
currentUmid.value = umid;
currentUserDetail.value = userData;

const handleChatSelected = (chatData) => {
  currentChatData.value = chatData.messages;
  currentSourceID.value = chatData.selectedSource;
  currentUmid.value = chatData.selectedUser;
  currentUserDetail.value = chatData.userData;
};



</script>

<template>

  <Head :title="pageTitle" />

  <AuthenticatedLayout>
    <!-- <PageHeading :title="pageTitle"></PageHeading> -->
    <div class="flex">
      <div class="flex-1">
        <div class="gap-4 md:gap-6 2xl:gap-7.5 grid grid-cols-12 min-h-[calc(100vh-115px)]">
          <div class="col-span-12 xl:col-span-4 rounded-md">
            <BotChatCard :botConverList="botConverList" :authDetail="authDetail" :activeTab="activeTab"
              :sourceID="sourceID" :umid="umid" @chatSelected="handleChatSelected" />
          </div>
          <div
            class="border-stroke col-span-12 xl:col-span-8 bg-white px-5 sm:px-7.5 pt-6 pb-2.5 xl:pb-1 border rounded-md w-full h-[88vh]">
            <UserBotChat :authDetail="authDetail" :chatData="currentChatData" :sourceID="currentSourceID"
              :umid="currentUmid" :userData="currentUserDetail" />
          </div>
        </div>
      </div>
    </div>

  </AuthenticatedLayout>
</template>
