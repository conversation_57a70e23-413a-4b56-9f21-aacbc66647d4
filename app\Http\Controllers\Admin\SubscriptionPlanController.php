<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

class SubscriptionPlanController extends Controller
{
    /**
     * Display a listing of the subscription plans.
     */
    public function index(): Response
    {
        $plans = SubscriptionPlan::ordered()->get();

        return Inertia::render('Admin/SubscriptionPlans/Index', [
            'plans' => $plans->map(function ($plan) {
                return [
                    'id' => $plan->id,
                    'name' => $plan->name,
                    'slug' => $plan->slug,
                    'description' => $plan->description,
                    'formatted_price' => $plan->formatted_price,
                    'billing_interval' => $plan->billing_interval,
                    'trial_days' => $plan->trial_days,
                    'is_active' => $plan->is_active,
                    'is_popular' => $plan->is_popular,
                    'sort_order' => $plan->sort_order,
                    'features' => $plan->features,
                    'limits' => $plan->limits,
                    'subscriptions_count' => $plan->subscriptions()->count(),
                    'active_subscriptions_count' => $plan->activeSubscriptions()->count(),
                ];
            }),
        ]);
    }

    /**
     * Show the form for creating a new subscription plan.
     */
    public function create(): Response
    {
        return Inertia::render('Admin/SubscriptionPlans/Create');
    }

    /**
     * Store a newly created subscription plan.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_interval' => 'required|in:monthly,quarterly,semi_annual,annual',
            'billing_interval_count' => 'required|integer|min:1',
            'trial_days' => 'required|integer|min:0',
            'features' => 'nullable|array',
            'limits' => 'nullable|array',
            'is_active' => 'boolean',
            'is_popular' => 'boolean',
            'sort_order' => 'required|integer|min:0',
        ]);

        // Convert price to cents
        $validated['price'] = (int) ($validated['price'] * 100);

        // Generate slug from name
        $validated['slug'] = Str::slug($validated['name']);

        // Ensure slug is unique
        $originalSlug = $validated['slug'];
        $counter = 1;
        while (SubscriptionPlan::where('slug', $validated['slug'])->exists()) {
            $validated['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        SubscriptionPlan::create($validated);

        return redirect()->route('admin.subscription-plans.index')
                        ->with('success', 'Subscription plan created successfully.');
    }

    /**
     * Display the specified subscription plan.
     */
    public function show(SubscriptionPlan $subscriptionPlan): Response
    {
        $subscriptionPlan->load(['subscriptions.user']);

        return Inertia::render('Admin/SubscriptionPlans/Show', [
            'plan' => [
                'id' => $subscriptionPlan->id,
                'name' => $subscriptionPlan->name,
                'slug' => $subscriptionPlan->slug,
                'description' => $subscriptionPlan->description,
                'formatted_price' => $subscriptionPlan->formatted_price,
                'price' => $subscriptionPlan->price / 100, // Convert back to dollars
                'billing_interval' => $subscriptionPlan->billing_interval,
                'billing_interval_count' => $subscriptionPlan->billing_interval_count,
                'trial_days' => $subscriptionPlan->trial_days,
                'features' => $subscriptionPlan->features,
                'limits' => $subscriptionPlan->limits,
                'is_active' => $subscriptionPlan->is_active,
                'is_popular' => $subscriptionPlan->is_popular,
                'sort_order' => $subscriptionPlan->sort_order,
                'created_at' => $subscriptionPlan->created_at,
                'updated_at' => $subscriptionPlan->updated_at,
            ],
            'subscriptions' => $subscriptionPlan->subscriptions->map(function ($subscription) {
                return [
                    'id' => $subscription->id,
                    'user' => [
                        'id' => $subscription->user->id,
                        'name' => $subscription->user->name,
                        'email' => $subscription->user->email,
                    ],
                    'status' => $subscription->status,
                    'trial_ends_at' => $subscription->trial_ends_at,
                    'current_period_end' => $subscription->current_period_end,
                    'created_at' => $subscription->created_at,
                ];
            }),
        ]);
    }

    /**
     * Show the form for editing the specified subscription plan.
     */
    public function edit(SubscriptionPlan $subscriptionPlan): Response
    {
        return Inertia::render('Admin/SubscriptionPlans/Edit', [
            'plan' => [
                'id' => $subscriptionPlan->id,
                'name' => $subscriptionPlan->name,
                'slug' => $subscriptionPlan->slug,
                'description' => $subscriptionPlan->description,
                'price' => $subscriptionPlan->price / 100, // Convert to dollars
                'billing_interval' => $subscriptionPlan->billing_interval,
                'billing_interval_count' => $subscriptionPlan->billing_interval_count,
                'trial_days' => $subscriptionPlan->trial_days,
                'features' => $subscriptionPlan->features ?? [],
                'limits' => $subscriptionPlan->limits ?? [],
                'is_active' => $subscriptionPlan->is_active,
                'is_popular' => $subscriptionPlan->is_popular,
                'sort_order' => $subscriptionPlan->sort_order,
            ],
        ]);
    }

    /**
     * Update the specified subscription plan.
     */
    public function update(Request $request, SubscriptionPlan $subscriptionPlan)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_interval' => 'required|in:monthly,quarterly,semi_annual,annual',
            'billing_interval_count' => 'required|integer|min:1',
            'trial_days' => 'required|integer|min:0',
            'features' => 'nullable|array',
            'limits' => 'nullable|array',
            'is_active' => 'boolean',
            'is_popular' => 'boolean',
            'sort_order' => 'required|integer|min:0',
        ]);

        // Convert price to cents
        $validated['price'] = (int) ($validated['price'] * 100);

        // Update slug if name changed
        if ($validated['name'] !== $subscriptionPlan->name) {
            $validated['slug'] = Str::slug($validated['name']);

            // Ensure slug is unique (excluding current plan)
            $originalSlug = $validated['slug'];
            $counter = 1;
            while (SubscriptionPlan::where('slug', $validated['slug'])
                                  ->where('id', '!=', $subscriptionPlan->id)
                                  ->exists()) {
                $validated['slug'] = $originalSlug . '-' . $counter;
                $counter++;
            }
        }

        $subscriptionPlan->update($validated);

        return redirect()->route('admin.subscription-plans.index')
                        ->with('success', 'Subscription plan updated successfully.');
    }

    /**
     * Remove the specified subscription plan.
     */
    public function destroy(SubscriptionPlan $subscriptionPlan)
    {
        // Check if plan has active subscriptions
        if ($subscriptionPlan->activeSubscriptions()->exists()) {
            return back()->withErrors([
                'plan' => 'Cannot delete plan with active subscriptions.'
            ]);
        }

        $subscriptionPlan->delete();

        return redirect()->route('admin.subscription-plans.index')
                        ->with('success', 'Subscription plan deleted successfully.');
    }

    /**
     * Toggle the active status of a subscription plan.
     */
    public function toggleStatus(SubscriptionPlan $subscriptionPlan)
    {
        $subscriptionPlan->update(['is_active' => !$subscriptionPlan->is_active]);

        $status = $subscriptionPlan->is_active ? 'activated' : 'deactivated';

        return redirect()->route('admin.subscription-plans.index')
                        ->with('success', "Subscription plan {$status} successfully.");
    }
}
