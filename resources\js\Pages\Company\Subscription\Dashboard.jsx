import React from 'react';
import { Head, Link, router } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import Button from '@/Components/Button';

export default function Dashboard({ subscription, recentInvoices, availablePlans }) {
    const handleCancelSubscription = () => {
        if (confirm('Are you sure you want to cancel your subscription? It will remain active until the end of your current billing period.')) {
            router.post(route('company.subscription.cancel'), {
                immediately: false
            });
        }
    };

    const getStatusBadge = (status) => {
        const statusColors = {
            trial: 'bg-blue-100 text-blue-800',
            active: 'bg-green-100 text-green-800',
            past_due: 'bg-yellow-100 text-yellow-800',
            canceled: 'bg-red-100 text-red-800',
        };

        const colorClass = statusColors[status] || 'bg-gray-100 text-gray-800';

        return (
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${colorClass}`}>
                {status.replace('_', ' ').toUpperCase()}
            </span>
        );
    };

    const formatDate = (date) => {
        if (!date) return 'N/A';
        return new Date(date).toLocaleDateString();
    };

    const getBillingIntervalLabel = (interval) => {
        const labels = {
            monthly: 'Monthly',
            quarterly: 'Quarterly',
            semi_annual: 'Semi-Annual',
            annual: 'Annual'
        };
        return labels[interval] || interval;
    };

    return (
        <DashboardLayout>
            <Head title="Subscription Dashboard" />
            
            <div className="py-6">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="md:flex md:items-center md:justify-between">
                        <div className="flex-1 min-w-0">
                            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                                Subscription Dashboard
                            </h2>
                            <p className="mt-1 text-sm text-gray-500">
                                Manage your subscription and billing information.
                            </p>
                        </div>
                        <div className="mt-4 flex md:mt-0 md:ml-4">
                            <Link
                                href={route('company.subscription.invoices')}
                                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                View All Invoices
                            </Link>
                        </div>
                    </div>

                    {/* Subscription Overview */}
                    <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
                        <div className="px-4 py-5 sm:p-6">
                            <div className="sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                                        Current Plan: {subscription.plan.name}
                                    </h3>
                                    <div className="mt-2 max-w-xl text-sm text-gray-500">
                                        <p>{subscription.plan.description}</p>
                                    </div>
                                    <div className="mt-3 flex items-center space-x-4">
                                        <div className="flex items-center">
                                            <span className="text-sm font-medium text-gray-900">Status:</span>
                                            <div className="ml-2">
                                                {getStatusBadge(subscription.status)}
                                            </div>
                                        </div>
                                        <div className="flex items-center">
                                            <span className="text-sm font-medium text-gray-900">
                                                {subscription.plan.formatted_price} / {getBillingIntervalLabel(subscription.plan.billing_interval)}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div className="mt-5 sm:mt-0 sm:ml-6 sm:flex-shrink-0 sm:flex sm:items-center">
                                    {!subscription.is_canceled && (
                                        <Button
                                            variant="danger"
                                            onClick={handleCancelSubscription}
                                        >
                                            Cancel Subscription
                                        </Button>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Subscription Details */}
                    <div className="mt-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
                        {/* Trial Information */}
                        {subscription.on_trial && (
                            <div className="bg-blue-50 overflow-hidden shadow rounded-lg">
                                <div className="p-5">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0">
                                            <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div className="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt className="text-sm font-medium text-blue-900 truncate">Trial Period</dt>
                                                <dd className="text-lg font-medium text-blue-900">
                                                    {subscription.trial_days_remaining} days remaining
                                                </dd>
                                                <dd className="text-sm text-blue-700">
                                                    Ends on {formatDate(subscription.trial_ends_at)}
                                                </dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Next Billing */}
                        {subscription.is_active && (
                            <div className="bg-white overflow-hidden shadow rounded-lg">
                                <div className="p-5">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0">
                                            <svg className="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                            </svg>
                                        </div>
                                        <div className="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt className="text-sm font-medium text-gray-500 truncate">Next Billing</dt>
                                                <dd className="text-lg font-medium text-gray-900">
                                                    {formatDate(subscription.current_period_end)}
                                                </dd>
                                                <dd className="text-sm text-gray-500">
                                                    {subscription.days_remaining_in_period} days remaining
                                                </dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Payment Method */}
                        <div className="bg-white overflow-hidden shadow rounded-lg">
                            <div className="p-5">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0">
                                        <svg className="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                                        </svg>
                                    </div>
                                    <div className="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt className="text-sm font-medium text-gray-500 truncate">Payment Method</dt>
                                            <dd className="text-lg font-medium text-gray-900 capitalize">
                                                {subscription.payment_method}
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Plan Features */}
                    <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
                        <div className="px-4 py-5 sm:p-6">
                            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                                Plan Features
                            </h3>
                            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                <div>
                                    <h4 className="text-sm font-medium text-gray-900 mb-2">Included Features</h4>
                                    <ul className="space-y-2">
                                        {subscription.plan.features?.map((feature, index) => (
                                            <li key={index} className="flex items-center">
                                                <svg className="flex-shrink-0 h-4 w-4 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                </svg>
                                                <span className="text-sm text-gray-600">{feature}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                                
                                {subscription.plan.limits && Object.keys(subscription.plan.limits).length > 0 && (
                                    <div>
                                        <h4 className="text-sm font-medium text-gray-900 mb-2">Usage Limits</h4>
                                        <ul className="space-y-2">
                                            {Object.entries(subscription.plan.limits).map(([key, value]) => (
                                                <li key={key} className="flex justify-between">
                                                    <span className="text-sm text-gray-600">
                                                        {key.replace('max_', '').replace('_', ' ')}:
                                                    </span>
                                                    <span className="text-sm font-medium text-gray-900">
                                                        {value === -1 ? 'Unlimited' : value}
                                                    </span>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Recent Invoices */}
                    <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
                        <div className="px-4 py-5 sm:p-6">
                            <div className="sm:flex sm:items-center sm:justify-between mb-4">
                                <h3 className="text-lg leading-6 font-medium text-gray-900">
                                    Recent Invoices
                                </h3>
                                <Link
                                    href={route('company.subscription.invoices')}
                                    className="text-sm text-indigo-600 hover:text-indigo-500"
                                >
                                    View all invoices
                                </Link>
                            </div>
                            
                            {recentInvoices.length > 0 ? (
                                <div className="overflow-hidden">
                                    <ul className="divide-y divide-gray-200">
                                        {recentInvoices.map((invoice) => (
                                            <li key={invoice.id} className="py-4">
                                                <div className="flex items-center justify-between">
                                                    <div>
                                                        <p className="text-sm font-medium text-gray-900">
                                                            Invoice #{invoice.invoice_number}
                                                        </p>
                                                        <p className="text-sm text-gray-500">
                                                            {formatDate(invoice.created_at)}
                                                        </p>
                                                    </div>
                                                    <div className="flex items-center space-x-4">
                                                        <span className="text-sm font-medium text-gray-900">
                                                            {invoice.formatted_amount_due}
                                                        </span>
                                                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                                            invoice.is_paid 
                                                                ? 'bg-green-100 text-green-800' 
                                                                : invoice.is_overdue
                                                                    ? 'bg-red-100 text-red-800'
                                                                    : 'bg-yellow-100 text-yellow-800'
                                                        }`}>
                                                            {invoice.status.toUpperCase()}
                                                        </span>
                                                    </div>
                                                </div>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            ) : (
                                <p className="text-sm text-gray-500">No invoices yet.</p>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    );
}
