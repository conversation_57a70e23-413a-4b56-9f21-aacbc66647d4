<!-- Dashboard.vue -->
<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { formatDistanceToNow } from 'date-fns'
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import ResponsiveNavLink from '../Components/ResponsiveNavLink.vue';
import { Head, usePage } from '@inertiajs/vue3';
import { Inertia } from '@inertiajs/inertia';

const pageTitle = ref('Chats')

const { botConverList, authDetail, chatData, sourceID, umid, userData, activeTab } = usePage().props;

let authUserid = authDetail.id;

let UsersList = botConverList;

var baseurl = window.location.origin;


const formatDate = (timestamp) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true })
}


const handleClick = (slug, user_id) => {
    const url = `?source=${slug}&umid=${user_id}`;
    Inertia.replace(url); // Use Inertia.replace instead of Inertia.visit
}


let currentTab = ref(activeTab);

const setActiveTab = (tab) => {
    currentTab.value = tab;
};

onMounted(() => {
    // Add class to hidden on mount
    const hiddenElementClass = `hidden_${umid}${sourceID}`;
    const hiddenElements = document.getElementsByClassName(hiddenElementClass);
    Array.from(hiddenElements).forEach(el => el.classList.add('hidden'));
});


const activeChat = ref({
    id: 1,
    name: 'Sarah Kortney',
    lastSeen: 'Last seen today 01:24',
    profileImg: '/images/profile.jpg'
});


/* detail chat messages */

const messages = ref([]);
const status_fallback = ref(0);
const messagesContainer = ref(null);
const inputText = ref('');
const selectedSlug = ref('');
const showInput = ref(true);

selectedSlug.value = sourceID;

const decodeToBase64 = (projectId) => {
    return atob(projectId); // Using atob() to decode from base64
}

const encodeToBase64 = (projectId) => {
    return btoa(projectId); // Using btoa() to encode to base64
}

let realuserid = decodeToBase64(umid);
let realsourceID = decodeToBase64(sourceID);


const scrollToBottom = () => {

    setTimeout(() => {

        let lastMessage = document.querySelector('#messagesContainer > div:nth-last-child(2)');
        if (lastMessage) {
            lastMessage.scrollIntoView({ behavior: 'smooth' });
        }

    }, 500);


}

onMounted(() => {


    if (Object.keys(chatData).length !== 0) {
        Object.values(chatData).forEach(item => {
            if (item.message.trim() !== '' && item.id == realuserid) {
                messages.value.push({ text: item.message, sender: 'user' });

            } else if (item.message.trim() !== '' && item.id != realuserid) {
                messages.value.push({ text: item.message, sender: 'server' });
            }
            status_fallback.value = item.status_fallback;
        });
    }
    scrollToBottom();

});


const sendMessage = async () => {
    try {

        if (inputText.value.trim() === '') {
            return false;
        }

        // Push the user message


        let inputMesage = inputText.value;
        if (inputText.value.trim() !== '') {
            messages.value.push({ text: inputText.value, sender: 'server' });
            scrollToBottom();
        }
        inputText.value = '';


        let token = document.head.querySelector('meta[name="csrf-token"]').content;

        let requestBody = {
            status_fallback: status_fallback.value,
            message: inputMesage,
            slug: selectedSlug.value, // Pass the selectedSlug
            type: sourceID, // Pass the type field
            userid: realuserid
        };

        const response = await fetch('/send-agent-message', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': token,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
        });

        if (response.status == 500) {
            let errorMessage = await response.json();
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: errorMessage.message,
            }).then(() => {
                var chatUrl = window.location.origin + '/chats';
                Inertia.replace(chatUrl);
            });
            return false;
        }



        const responseData = await response.json();


        setTimeout(function () {

            scrollToBottom();

            if (responseData.message.trim() !== '') {
                messages.value.push({ text: responseData.message, sender: 'server' });
            }


            nextTick(() => {
                let sourceMessage = document.querySelector('#inputsource');
                if (sourceMessage) {
                    sourceMessage.focus();
                }
            });

        }, 500);


    } catch (error) {
        console.error('Error sending message:', error);
    }
};

let matchFound = false;
let responseArray = 0;

var elements = document.getElementsByClassName('source_' + sourceID + 'umid_' + umid);
for (var i = 0; i < elements.length; i++) {
    elements[i].style.opacity = '0';
}


const fetchLatestConversations = async () => {
    try {
        const response = await fetch('/get-agent-last-conversation', { method: 'GET' });
        const responseData = await response.json();
        let conversactionList = responseData.botConverList;
        let counter1 = conversactionList.length;
        let counter = 0;
        conversactionList.forEach(conversation => {
            if (conversation.chatbot_project_id == realsourceID && conversation.user_id == realuserid) {

                if (conversation.status == 2) {
                    matchFound = true;
                    showInput.value = false;
                    messages.value.push({ text: 'User conversation is closed', sender: 'stop' });

                }

                if (conversation.agent_id != authUserid && conversation.status == 1) {
                    matchFound = true;
                    showInput.value = false;
                    messages.value.push({ text: 'The user is already associated with the agent (' + conversation.agent_name + ')', sender: 'stop' });
                }

            }
            counter++;
        });



        if (counter == counter1) {
            responseArray = 1;
            var elements = document.getElementsByClassName('source_' + sourceID + 'umid_' + umid);
            for (var i = 0; i < elements.length; i++) {
                elements[i].style.opacity = '1';
            }

            nextTick(() => {
                let sourceMessage = document.querySelector('#inputsource');
                if (sourceMessage) {
                    sourceMessage.focus();
                }
            });


        }




    } catch (error) {
        console.error('Error fetching latest conversations:', error);
    }
};

if (sourceID && umid) {
    fetchLatestConversations();
}


Echo.private('agent-channel').listen('ChatAgentMessage', (e) => {

    if (matchFound == false) {
        if (sourceID && umid) {
            fetchLatestConversations();
        }

    }


    let socketMessage = e.message;
    Object.values(socketMessage).forEach(item => {
        if (item.chat_uid === umid && item.chat_source == sourceID && item.sender == 'server') {
            if (item.text.trim() !== '') {
                messages.value.push({ text: item.text, sender: 'user' });
            }
            scrollToBottom();
        }
    });


})




const cancelConversationFun = async () => {
    try {

        let token = document.head.querySelector('meta[name="csrf-token"]').content;

        let requestBody = {
            umid: umid,
            sourceID: sourceID,
        };

        const response = await fetch('/cancel-agent-conversaction', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': token,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
        });

        const responseData = await response.json();
        var chatUrl = window.location.origin + '/chats';
        Inertia.replace(chatUrl);

    } catch (error) {
        console.error('Error fetching latest conversations:', error);
    }
};

const cancelConversation = () => {
    if (sourceID && umid) {
        cancelConversationFun();
    }

    // Add your cancel conversation logic here
};



</script>

<template>

    <Head :title="pageTitle" />

    <AuthenticatedLayout>
        <template #header>
            <h2 class="font-semibold text-slate-800 text-xl leading-tight">Dashboard</h2>
        </template>

        <div class="flex">
            <!-- Right Sidebar -->
            <div
                class="z-10 bg-white scrollbar-thumb-gray-700/20 p-6 rounded-md w-1/4 h-[calc(100vh-8rem)] overflow-y-auto scrollbar-thin scrollbar-track-gray-100">
                <div class="flex space-x-4 mb-4">
                    <button @click="setActiveTab('active')"
                        :class="currentTab === 'active' ? 'text-slate-800 border-t-4 border-black' : 'text-gray-600'"
                        class="pt-2 pb-2 w-1/2 focus:outline-none">
                        Active
                    </button>
                    <button @click="setActiveTab('inactive')"
                        :class="currentTab === 'inactive' ? 'text-slate-800 border-t-4 border-black' : 'text-gray-600'"
                        class="pt-2 pb-2 w-1/2 focus:outline-none">
                        Inactive
                    </button>
                    <button @click="setActiveTab('closed')"
                        :class="currentTab === 'closed' ? 'text-slate-800 border-t-4 border-black' : 'text-gray-600'"
                        class="pt-2 pb-2 w-1/2 focus:outline-none">
                        Close
                    </button>
                </div>

                <!-- Active Chats -->
                <ul class="space-y-4">

                    <template v-for="(chat, index) in UsersList" :key="index">

                        <li v-if="currentTab === 'active' && chat.status === 0"
                            class="flex items-center bg-white shadow p-4 rounded-md active_chat">

                            <ResponsiveNavLink
                                :href="baseurl + '/chats?tab=active&source=' + encodeToBase64(chat.chatbot_project_id) + '&umid=' + encodeToBase64(chat.user_id)"
                                class="relative flex items-center gap-5 hover:bg-gray-3 px-7.5 py-3 hover:cursor-pointer">

                                <img v-if="chat.profilePicture && chat.profilePicture.startsWith('http')"
                                    :src="chat.profilePicture" alt="Profile Picture"
                                    class="mr-4 rounded-full w-12 h-12">
                                <img v-else-if="chat.profilePicture" :src="baseurl + '/storage/' + chat.profilePicture"
                                    alt="Profile Picture" class="mr-4 rounded-full w-12 h-12">
                                <img v-else src="@/assets/images/user/user-profile.jpeg" alt="Profile Picture"
                                    class="mr-4 rounded-full w-12 h-12">
                                <span v-if="chat.last_msg_type === 'user'"
                                    class="-top-0.5 right-0 z-1 absolute bg-green-400 rounded-full w-2 h-2"
                                    :class="'hidden_' + encodeToBase64(chat.user_id) + encodeToBase64(chat.chatbot_project_id)">
                                    <span
                                        class="inline-flex -z-1 absolute bg-meta-1 opacity-75 rounded-full w-full h-full animate-ping"></span>
                                </span>

                                <div>
                                    <h4 class="font-semibold">{{ chat.name }}</h4>
                                    <p class="text-slate-500 text-sm">{{ chat.lastmessage }}
                                        <br>
                                        <span class="text-xs"> {{ formatDate(chat.updated_at) }} </span>
                                    </p>

                                </div>
                            </ResponsiveNavLink>

                        </li>

                        <!-- Inactive Chats -->

                        <li v-if="currentTab === 'active' && chat.status === 1 && chat.agent_id === authDetail.id"
                            class="flex items-center bg-white shadow p-4 rounded-md cursor-pointer inactive_chat">

                            <ResponsiveNavLink
                                :href="baseurl + '/chats?tab=active&source=' + encodeToBase64(chat.chatbot_project_id) + '&umid=' + encodeToBase64(chat.user_id)"
                                class="flex items-center gap-5 hover:bg-gray-3 px-7.5 py-3 hover:cursor-pointer">

                                <img v-if="chat.profilePicture && chat.profilePicture.startsWith('http')"
                                    :src="chat.profilePicture" alt="Profile Picture"
                                    class="mr-4 rounded-full w-12 h-12">
                                <img v-else-if="chat.profilePicture" :src="baseurl + '/storage/' + chat.profilePicture"
                                    alt="Profile Picture" class="mr-4 rounded-full w-12 h-12">
                                <img v-else src="@/assets/images/user/user-profile.jpeg" alt="Profile Picture"
                                    class="mr-4 rounded-full w-12 h-12">
                                <span v-if="chat.last_msg_type === 'user'"
                                    class="-top-0.5 right-0 z-1 absolute bg-green-400 rounded-full w-2 h-2"
                                    :class="'hidden_' + encodeToBase64(chat.user_id) + encodeToBase64(chat.chatbot_project_id)">
                                    <span
                                        class="inline-flex -z-1 absolute bg-meta-1 opacity-75 rounded-full w-full h-full animate-ping"></span>
                                </span>

                                <div>
                                    <h4 class="font-semibold">{{ chat.name }}</h4>
                                    <p class="text-slate-500 text-sm">{{ chat.lastmessage }}
                                        <br>
                                        <span class="text-xs"> . {{ formatDate(chat.updated_at) }} </span>
                                    </p>

                                </div>
                            </ResponsiveNavLink>

                        </li>

                        <!-- Inactive Chats (not the agent's) -->

                        <li v-if="currentTab === 'inactive' && chat.status === 1 && chat.agent_id !== authDetail.id"
                            class="flex items-center bg-white shadow p-4 rounded-md inactive_chat">

                            <ResponsiveNavLink
                                :href="baseurl + '/chats?tab=inactive&source=' + encodeToBase64(chat.chatbot_project_id) + '&umid=' + encodeToBase64(chat.user_id)"
                                class="flex items-center gap-5 hover:bg-gray-3 px-7.5 py-3 hover:cursor-pointer">

                                <img v-if="chat.profilePicture && chat.profilePicture.startsWith('http')"
                                    :src="chat.profilePicture" alt="Profile Picture"
                                    class="mr-4 rounded-full w-12 h-12">
                                <img v-else-if="chat.profilePicture" :src="baseurl + '/storage/' + chat.profilePicture"
                                    alt="Profile Picture" class="mr-4 rounded-full w-12 h-12">
                                <img v-else src="@/assets/images/user/user-profile.jpeg" alt="Profile Picture"
                                    class="mr-4 rounded-full w-12 h-12">
                                <span v-if="chat.last_msg_type === 'user'"
                                    class="-top-0.5 right-0 z-1 absolute bg-green-400 rounded-full w-2 h-2"
                                    :class="'hidden_' + encodeToBase64(chat.user_id) + encodeToBase64(chat.chatbot_project_id)">
                                    <span
                                        class="inline-flex -z-1 absolute bg-meta-1 opacity-75 rounded-full w-full h-full animate-ping"></span>
                                </span>

                                <div>
                                    <h4 class="font-semibold">{{ chat.name }}</h4>
                                    <p class="text-slate-500 text-sm">{{ chat.lastmessage }}
                                        <br>
                                        <span class="text-xs"> . {{ formatDate(chat.updated_at) }} </span>
                                    </p>

                                </div>
                            </ResponsiveNavLink>

                        </li>

                        <!-- Closed Chats -->

                        <li v-if="currentTab === 'closed' && chat.status === 2"
                            class="flex items-center bg-white shadow p-4 rounded-md closed_chat">

                            <ResponsiveNavLink
                                :href="baseurl + '/chats?tab=closed&source=' + encodeToBase64(chat.chatbot_project_id) + '&umid=' + encodeToBase64(chat.user_id)"
                                class="flex items-center gap-5 hover:bg-gray-3 px-7.5 py-3 hover:cursor-pointer">

                                <img v-if="chat.profilePicture && chat.profilePicture.startsWith('http')"
                                    :src="chat.profilePicture" alt="Profile Picture"
                                    class="mr-4 rounded-full w-12 h-12">
                                <img v-else-if="chat.profilePicture" :src="baseurl + '/storage/' + chat.profilePicture"
                                    alt="Profile Picture" class="mr-4 rounded-full w-12 h-12">
                                <img v-else src="@/assets/images/user/user-profile.jpeg" alt="Profile Picture"
                                    class="mr-4 rounded-full w-12 h-12">
                                <span v-if="chat.last_msg_type === 'user'"
                                    class="-top-0.5 right-0 z-1 absolute bg-green-400 rounded-full w-2 h-2"
                                    :class="'hidden_' + encodeToBase64(chat.user_id) + encodeToBase64(chat.chatbot_project_id)">
                                    <span
                                        class="inline-flex -z-1 absolute bg-meta-1 opacity-75 rounded-full w-full h-full animate-ping"></span>
                                </span>

                                <div>
                                    <h4 class="font-semibold">{{ chat.name }}</h4>
                                    <p class="text-slate-500 text-sm">{{ chat.lastmessage }}
                                        <br>
                                        <span class="text-xs"> . {{ formatDate(chat.updated_at) }} </span>
                                    </p>

                                </div>
                            </ResponsiveNavLink>

                        </li>


                    </template>




                </ul>

            </div>

            <!-- Main Content Area -->
            <div class="flex-1 pr-4">
                <div class="gap-6 grid mx-auto sm:px-6 lg:px-8">
                    <!-- Chat History -->
                    <div class="bg-white p-6">
                        <!-- Active Chat Information -->
                        <div class="flex items-center mb-6">
                            <img v-if="userData.profilePicture && userData.profilePicture.startsWith('http')"
                                :src="userData.profilePicture" alt="User" class="mr-4 rounded-full w-16 h-16" />
                            <img v-else-if="userData.profilePicture"
                                :src="baseurl + '/storage/' + userData.profilePicture" alt="User"
                                class="mr-4 rounded-full w-16 h-16" />
                            <img v-else src="@/assets/images/user/user-profile.jpeg" alt="User"
                                class="mr-4 rounded-full w-16 h-16" />

                            <div>
                                <h4 class="font-semibold text-lg">{{ userData.name }}</h4>
                                <p class="text-gray-600 text-sm">Replay to the Message</p>
                            </div>
                        </div>
                        <hr>
                        <!-- Chat Messages -->
                        <div class="z-10 flex flex-col space-y-4 scrollbar-thumb-gray-700/20 pt-8 rounded-md h-[calc(80vh-5rem)] overflow-y-auto scrollbar-thin scrollbar-track-gray-100"
                            :class="'source_' + sourceID + 'umid_' + umid" id="messagesContainer"
                            ref="messagesContainer">
                            <!-- Loop through each message -->

                            <div v-for="(message, index) in messages" :key="index" class="flex items-start space-x-4"
                                :class="[
                                    message.sender === 'user' ? 'justify-start' : 'justify-end',
                                    `${message.sender}_message`
                                ]">


                                <div v-if="message.sender != 'user'"
                                    class="flex items-center space-x-4 shadow p-4 rounded-md">

                                    <!-- Profile Picture -->
                                    <img src="@/assets/images/user/user-profile.jpeg" alt="Profile Picture"
                                        class="rounded-full w-10 h-10" />



                                    <!-- Message Container -->
                                    <div class="flex flex-col">
                                        <!-- Sender's Name -->
                                        <p class="font-semibold">{{ message.sender }}</p>
                                        <!-- Message Text -->
                                        <p>{{ message.text }}</p>
                                        <!-- Message Time -->
                                    </div>

                                </div>

                                <div v-else class="flex items-center space-x-4 shadow p-4 rounded-md">

                                    <!-- Message Container -->
                                    <div class="   ">
                                        <!-- Sender's Name -->
                                        <p class="font-semibold">{{ message.sender }}</p>
                                        <!-- Message Text -->
                                        <p class="text-slate-500">{{ message.text }}</p>
                                        <!-- Message Time -->
                                    </div>

                                    <!-- Profile Picture -->
                                    <img v-if="userData.profilePicture && userData.profilePicture.startsWith('http')"
                                        :src="userData.profilePicture" alt="Profile Picture"
                                        class="rounded-full w-10 h-10" />
                                    <img v-else-if="userData.profilePicture"
                                        :src="baseurl + '/storage/' + userData.profilePicture" alt="Profile Picture"
                                        class="rounded-full w-10 h-10" />
                                    <img v-else src="@/assets/images/user/user-profile.jpeg" alt="Profile Picture"
                                        class="rounded-full w-10 h-10" />


                                </div>


                            </div>

                        </div>
                    </div>

                    <!-- Chat Input -->
                    <div class="bottom-0 fixed flex items-center bg-white custom-width-62-5 border-t">
                        <input type="text"
                            class="flex-1 border-0 focus:border-cfp-500 mr-2 px-4 py-4 rounded-md focus:ring-cfp-500/50 focus:outline-none"
                            placeholder="Type your message...">
                        <button class="bg-gray-900 mt-1 rounded-full w-10 h-10 text-white"><i class="fa fa-paper-plane"
                                aria-hidden="true"></i></button>
                    </div>

                </div>
            </div>


        </div>
    </AuthenticatedLayout>
</template>
<style>
.custom-width-62-5 {
    width: 62.5%;
}
</style>