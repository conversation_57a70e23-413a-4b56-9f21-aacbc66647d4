<script setup>
import { ref } from "vue";
import { Link } from "@inertiajs/vue3";
import ResponsiveNavLink from "@/Components/ResponsiveNavLink.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import PageHeading from "../Global/PageHeading.vue";
import DtIconGlobal from "../DtIcon/DtIconGlobal.vue";

const props = defineProps(["category"]);

let categoryListData = props.category.data;

var adminBaseurl = window.location.origin + "/dtadmin";
var baseurl = window.location.origin + "/";

let category = props.category;

const pageTitle = ref("All Categories");

const formatDate = (dateString) => {
  const date = new Date(dateString);
  const formattedDate = `${date.getFullYear()}-${("0" + (date.getMonth() + 1)).slice(
    -2
  )}-${("0" + date.getDate()).slice(-2)} ${("0" + date.getHours()).slice(-2)}:${(
    "0" + date.getMinutes()
  ).slice(-2)}`;
  return formattedDate;
};

const deleteItem = async (item) => {
  try {
    // Show a SweetAlert confirmation popup
    const confirmation = await Swal.fire({
      title: "Are you sure?",
      text:
        "You are about to delete this item. This action cannot be undone. Are you sure you want to proceed?",
      icon: "warning",
      showCancelButton: true,

      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "Cancel",
      customClass: {
        confirmButton: "dk-update-btn",
        cancelButton: "dk-cancle-btn",
      },
      buttonsStyling: false,
    });

    // If user confirms deletion
    if (confirmation.isConfirmed) {
      const response = await axios.delete(route("category.delete", { id: item.catId }));
      if (response.data.status == true) {
        // Remove the deleted item from the allitems array
        const index = categoryListData.findIndex((a) => a.catId === item.catId);
        if (index !== -1) {
          categoryListData.splice(index, 1);
        }
        // Show success message
        // Swal.fire({
        //   title: "Deleted!",
        //   text: response.data.message,
        //   icon: "success",
        //   confirmButtonText: "OK",
        // });
      } else if (response.data.status == false) {
        Swal.fire({
          title: "Deletion Failed",
          text: response.data.message || "Unable to delete the category at this time.",
          icon: "error",
          confirmButtonText: "OK",
        });
      } else {
        throw new Error("Failed to delete item");
      }
    }
  } catch (error) {
    console.error(error);
    // Show error message if deletion fails
    Swal.fire("Error", "An error occurred while deleting the item.", "error");
  }
};
</script>

<template>
  <PageHeading :title="pageTitle" :href="route('category.create')" buttonLabel="Add Category"></PageHeading>

  <div class="border-stroke p-6 border rounded-md">
    <div class="max-w-full overflow-x-auto">
      <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
        leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
        <p v-if="$page.props.flash?.message" :class="$page.props.flash.class">
          {{ $page.props.flash.message }}
        </p>
      </Transition>

      <table class="dt-deals-table">
        <thead>
          <tr class="">
            <th class="hidden dt-deals-th">
              <div class="flex items-center gap-4">
                <label :for="'checkboxAll-data'" class="flex items-center font-medium cursor-pointer select-none">
                  <div class="relative">
                    <input type="checkbox" :id="'checkboxAll-data'" class="sr-only tableCheckbox" />
                    <div
                      class="flex justify-center items-center border-[.5px] border-stroke bg-gray-2 rounded-[3px] w-5 h-5 text-white 2 box">
                      <span class="opacity-0">
                        <svg width="14" height="14" viewBox="0 0 10 10">
                          <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M8.62796 2.20602C8.79068 2.36874 8.79068 2.63256 8.62796 2.79528L4.04463 7.37861C3.88191 7.54133 3.61809 7.54133 3.45537 7.37861L1.37204 5.29528C1.20932 5.13256 1.20932 4.86874 1.37204 4.70602C1.53476 4.5433 1.79858 4.5433 1.96129 4.70602L3.75 6.49473L8.03871 2.20602C8.20142 2.0433 8.46524 2.0433 8.62796 2.20602Z"
                            fill="currentColor"></path>
                        </svg>
                      </span>
                    </div>
                  </div>
                </label>
              </div>
            </th>
            <th class="dt-deals-th">Image</th>
            <th class="dt-deals-th">Name</th>

            <th class="dt-deals-th">Created at</th>
            <th class="dt-deals-th">Action</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in categoryListData" :key="index" class="dt-deals-tr">
            <td class="dt-deals-td">
              <img v-if="item.catPicture && item.catPicture.startsWith('http')" :src="item.catPicture" alt="User"
                class="rounded-full w-15" />
              <img v-else-if="item.catPicture" :src="baseurl + '/storage/' + item.catPicture" alt="User"
                class="rounded-full w-15" />
              <img v-else src="@/assets/images/unknown.png" alt="User" class="rounded-full w-15" />
            </td>
            <td class="px-4 py-5 pl-9 xl:pl-11">
              <h5 class="text-sm">{{ item.catName }}</h5>
            </td>

            <td class="dt-deals-td">
              <p>{{ formatDate(item.created_at) }}</p>
            </td>

            <td class="dt-deals-td">
              <div class="dt-deals-actions">
                <Link :href="adminBaseurl + `/category/edit/${item.catId}`" class="dt-deals-action-btn">
                <DtIconGlobal :type="'edit'" />
                </Link>

                <button v-if="item.catName != 'Other'" class="dt-deals-action-btn dt-deals-delete-btn"
                  @click="deleteItem(item)">
                  <DtIconGlobal :type="'delete'" />
                </button>
              </div>
            </td>
          </tr>

          <tr v-if="categoryListData.length == 0">
            <td colspan="7">
              <div class="my-4 text-danger">No record found</div>
            </td>
          </tr>
        </tbody>
      </table>

      <div v-if="categoryListData.length > 0" class="dt-table-pagi">
        <div class="content-center row-span-2 row-start-2">
          Showing <b>{{ (category.current_page - 1) * category.per_page + 1 }}</b>-
          <b>{{ Math.min(category.current_page * category.per_page, category.total) }}</b>
          from <b>{{ category.total }}</b> data
        </div>

        <div class="row-start-2 row-end-4 text-end">
          <div class="pagination-links">
            <ul class="flex justify-items-end place-content-end">
              <li v-for="page in category.links" :key="page.url">
                <button @click="$inertia.visit(page.url)" :class="{
                  'bg-cfp-500 text-white': page.active,
                  'hover:bg-cfp-500 hover:text-white': !page.active,
                }" class="px-3 py-1 rounded-full focus:outline-none mx-1" v-html="page.label"></button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
