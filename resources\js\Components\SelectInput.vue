<template>
  <select :class="[
    'w-full rounded-md border border-stroke bg-transparent px-4 py-2 outline-none focus:border-cfp-500 focus-visible:shadow-none focus:ring-cfp-500/50   text-slate-800  mt-1 block',
    classes
  ]" v-model="model" ref="selectRef" @change="$emit('update:modelValue', $event.target.value)">
    <slot></slot>
  </select>
</template>

<script setup>
import { defineProps, defineModel, defineExpose, ref, onMounted } from 'vue';

const { modelValue, classes } = defineProps(['modelValue', 'classes']);
const model = defineModel({
  prop: 'modelValue',
  event: 'update:modelValue'
});

const selectRef = ref(null);

onMounted(() => {
  if (selectRef.value.hasAttribute('autofocus')) {
    selectRef.value.focus();
  }
});

defineExpose({ focus: () => selectRef.value.focus() });
</script>