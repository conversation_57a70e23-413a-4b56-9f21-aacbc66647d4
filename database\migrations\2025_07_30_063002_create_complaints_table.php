<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('complaints', function (Blueprint $table) {
            $table->id();
            $table->string('cmp_id')->nullable();
            $table->unsignedBigInteger('companyId');
            $table->unsignedBigInteger('assignedToUserId');
            $table->enum('priority', ['0', '1', '2'])->default('0')->comment('0-low,1-medium,2-high');
            $table->enum('progressStatus', ['new', 'inprogress', 'need_user_input', 'resolved'])->default('new');
            $table->string('complainTitle', 255);
            $table->text('complainDetail');
            $table->enum('complainStatus', ['open', 'close'])->default('open');
            $table->timestamps();

            $table->foreign('companyId')->references('id')->on('companies')->onDelete('cascade');
            $table->foreign('assignedToUserId')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('complaints');
    }
};
