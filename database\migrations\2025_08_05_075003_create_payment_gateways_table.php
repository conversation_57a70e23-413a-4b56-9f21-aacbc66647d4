<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_gateways', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // stripe, paypal
            $table->string('display_name');
            $table->boolean('is_active')->default(false);
            $table->boolean('test_mode')->default(true);
            $table->json('test_config')->nullable(); // Test keys/settings
            $table->json('live_config')->nullable(); // Live keys/settings
            $table->string('webhook_secret')->nullable();
            $table->timestamps();

            $table->index(['is_active', 'test_mode']);
        });
    }
};
