<script setup>
import { ref } from 'vue'
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, usePage } from '@inertiajs/vue3';
import TableCategory from '@/Components/Tables/TableCategory.vue'
import BreadcrumbDefault from '@/Components/Breadcrumbs/BreadcrumbDefault.vue'
const pageTitle = ref('Category')

const { category } = usePage().props;

</script>

<template>

  <Head :title="pageTitle" />

  <AuthenticatedLayout>

    <div class="flex">


      <div class="flex-1">



        <TableCategory :category="category" />

      </div>

    </div>

  </AuthenticatedLayout>
</template>
