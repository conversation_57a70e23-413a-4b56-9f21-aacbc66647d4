@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

[type="text"]:focus,
input:where(:not([type])):focus,
[type="email"]:focus,
[type="url"]:focus,
[type="password"]:focus,
[type="number"]:focus,
[type="date"]:focus,
[type="datetime-local"]:focus,
[type="month"]:focus,
[type="search"]:focus,
[type="tel"]:focus,
[type="time"]:focus,
[type="week"]:focus,
[multiple]:focus,
textarea:focus,
select:focus {
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow);
    border-color: #337e81 !important;
}

.scrollbar-thin::-webkit-scrollbar {
    width: 4px;
    height: 4px;
}

#nprogress .bar {
    background: var(--primary-color, #337e81) !important;
    height: 5px !important;
}

#nprogress .spinner-icon {
    border-top-color: var(--primary-color, #337e81) !important;
    border-left-color: var(--primary-color, #337e81) !important;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 10px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: #555;
}
button.swal2-confirm.swal2-styled {
    background-color: var(--primary-color, #337e81) !important;
    align-items: center !important;
    border-radius: 0.375rem !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
    font-size: 0.75rem !important;
    line-height: 1rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.1em !important;
}

@layer base {
    body {
        @apply relative z-1 bg-whiten font-normal text-base text-slate-800;
    }
    input::placeholder {
        @apply font-normal text-gray-400 text-sm;
    }
}

@layer components {
    .main-logo-svg .logo-fillcolor {
        @apply fill-cfp-500;
    }
    .add-form-main-bcls {
        @apply gap-4 border-stroke grid grid-cols-12 bg-white border rounded-md;
    }
    .add-brad-btn {
        @apply flex px-4 py-2 items-center justify-center  rounded-full bg-cfp-500 hover:bg-cfp-500/85 font-medium text-white;
    }

    .dk-update-btn {
        @apply items-center border bg-cfp-500  leading-normal hover:bg-cfp-500/85  px-6 max-md:px-4 py-2.5 text-center  rounded-md  font-medium text-white text-sm   transition duration-150 ease-in-out;
    }
    .dk-cancle-btn {
        @apply !inline-flex items-center bg-gray-50 hover:bg-gray-200 mx-2 px-6 max-md:px-4 py-2.5 border rounded-md font-medium text-end text-sm leading-normal transition duration-150 ease-in-out;
    }
    a.dk-cancle-btn {
        @apply !px-6 max-md:!px-4 !py-2.5 !text-sm !leading-normal;
    }

    .swal2-confirm {
        @apply items-center border bg-cfp-500 leading-normal hover:bg-cfp-500/85  px-6 max-md:px-4 py-2.5 text-center  rounded-md  font-medium text-white text-sm   transition duration-150 ease-in-out;
    }

    button.swal2-cancel {
        @apply !inline-flex items-center !bg-transparent hover:bg-gray-50 mx-2 px-6 max-md:px-4 py-2.5 border rounded-md font-medium text-end text-sm leading-normal transition duration-150 ease-in-out;
    }
    .left-sidebar-menutext {
        @apply pt-1 font-normal text-slate-900 text-sm;
    }
    .list-unstyled li:hover {
        @apply bg-white;
    }
    .left-menuactive-effect {
        @apply bg-white;
    }
    .form-main-body {
        @apply w-full sm:w-full;
    }
    .form-main-body-90 {
        @apply w-full sm:w-full;
    }
    .chat-sender-cls {
        @apply bg-gray-200/80 text-gray-800 mb-0 px-5 py-1.5 rounded-md font-normal rounded-tl-none text-sm;
    }
    .chat-receiver-cls {
        @apply bg-cfp-500/10 mb-0 px-5 py-1.5 rounded-md rounded-br-none font-normal text-gray-800 text-sm;
    }
    .input-flt-datepicker {
        @apply border-stroke focus:border-cfp-500 bg-transparent focus-visible:shadow-none mt-1 px-4 py-2 border rounded-md w-full text-black outline-none;
    }
    .form-ttl-gap {
        @apply my-4;
    }
    .dash-box-main {
        @apply border-stroke bg-white px-7.5 py-6 border rounded-md;
    }
    .dash-box-main:hover {
        @apply shadow-default;
    }

    .dt-table-pagi {
        @apply content-center gap-2 grid grid-rows-3 grid-flow-col px-6 text-sm;
    }
    .login-body-cls {
        @apply m-4 max-md:m-2 p-7 max-md:p-5 border rounded-md max-w-xl text-left leading-loose;
    }

    /* table css */
    .dt-deals-container {
        @apply bg-white shadow-sm rounded-md w-full;
    }

    .dt-deals-header {
        @apply border-gray-200 p-6 border-b;
    }

    .dt-deals-title {
        @apply font-semibold text-gray-900 text-xl;
    }

    .dt-deals-wrapper {
        @apply overflow-x-auto;
    }

    .dt-deals-table {
        @apply border-b divide-y divide-gray-200 w-full;
    }

    .dt-deals-th {
        @apply px-6 py-3 font-semibold text-gray-900 text-left text-sm uppercase tracking-wider;
    }
    .dt-deals-table .dt-deals-th:last-child {
        @apply !text-right;
    }

    .dt-deals-tr {
        @apply hover:bg-gray-50;
    }

    .dt-deals-td {
        @apply px-6 py-4 text-gray-600 text-sm;
    }

    .dt-deals-th-checkbox,
    .dt-deals-td-checkbox {
        @apply px-6 py-4 whitespace-nowrap;
    }

    .dt-deals-checkbox {
        @apply border-gray-300 rounded focus:ring-cfp-500 w-4 h-4 text-cfp-500;
    }

    .dt-deals-title-wrap {
        @apply flex flex-col;
    }

    .dt-deals-title-link {
        @apply font-medium text-gray-900 text-sm hover:text-cfp-500;
    }

    .dt-deals-status {
        @apply inline-flex px-2 py-1 rounded-full font-semibold text-xs;
    }

    .dt-deals-status-pending {
        @apply bg-yellow-100 text-yellow-800;
    }

    .dt-deals-status-active {
        @apply bg-green-100 text-green-800;
    }

    .dt-deals-status-ended {
        @apply bg-red-100 text-red-800;
    }

    .dt-deals-actions {
        @apply flex justify-end items-center space-x-4;
    }

    .dt-deals-action-btn {
        @apply text-gray-600 hover:text-cfp-500 transition-colors;
    }

    .dt-deals-delete-btn {
        @apply hover:text-red-600;
    }

    .dt-deals-icon {
        @apply w-5 h-5 fill-none stroke-current;
    }

    .dt-deals-empty {
        @apply px-6 py-4 text-center text-red-500 text-sm;
    }
    /* end table css  */
}

@layer utilities {
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }
    ::-webkit-scrollbar-thumb {
        background-color: rgba(128, 128, 128, 0.5) !important;
        --scrollbar-track: #eef2ff !important;
        /* border-radius: 10px; */
    }
    ::-webkit-scrollbar-thumb:hover {
        background-color: rgba(128, 128, 128, 0.5) !important;
    }
    /* Chrome, Safari and Opera */
    .no-scrollbar::-webkit-scrollbar {
        display: none;
    }

    .no-scrollbar {
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }

    .chat-height {
        @apply h-[calc(100vh_-_8.125rem)] lg:h-[calc(100vh_-_5.625rem)];
    }

    .inbox-height {
        @apply h-[calc(100vh_-_8.125rem)] lg:h-[calc(100vh_-_5.625rem)];
    }
}

/* third-party libraries CSS */

.tableCheckbox:checked ~ div span {
    @apply opacity-100;
}

.tableCheckbox:checked ~ div {
    @apply bg-cfp-500 border-cfp-500;
}

.apexcharts-legend-text {
    @apply !text-body;
}

.apexcharts-text {
    @apply !fill-body;
}

.apexcharts-xcrosshairs {
    @apply !fill-stroke;
}

.apexcharts-gridline {
    @apply !stroke-stroke;
}

.apexcharts-legend-series {
    @apply !inline-flex gap-1.5;
}

.apexcharts-tooltip-series-group {
    @apply !pl-1.5;
}

.flatpickr-wrapper {
    @apply w-full;
}

.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg {
    @apply !fill-cfp-500;
}

.flatpickr-calendar {
    @apply !p-6 2xsm:!w-auto;
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
    @apply !top-7;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month.flatpickr-prev-month {
    @apply !left-7;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,
.flatpickr-months .flatpickr-next-month.flatpickr-next-month {
    @apply !right-7;
}

.flatpickr-day.inRange {
    box-shadow: -5px 0 0 #f3f4f6, 5px 0 0 #f3f4f6 !important;
}

.flatpickr-day.inRange,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.today.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day:hover,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day:focus,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.nextMonthDay:focus {
    @apply !border-[#F3F4F6] !bg-[#F3F4F6];
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
    background: var(--primary-color, #337e81);
    @apply !bg-cfp-500 hover:!bg-cfp-500 !border-cfp-500 hover:!border-cfp-500;
}

.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n + 1)) {
    box-shadow: -10px 0 0 var(--primary-color, #337e81);
}

.map-btn .jvm-zoom-btn {
    @apply flex h-7.5 w-7.5 items-center justify-center rounded border border-stroke bg-white px-0 pt-0 pb-0.5 text-2xl leading-none text-body hover:border-cfp-500 hover:bg-cfp-500/85 hover:text-white;
}

.mapOne .jvm-zoom-btn {
    @apply !top-auto bottom-0 left-auto;
}

.mapOne .jvm-zoom-btn.jvm-zoomin {
    @apply right-10;
}

.mapOne .jvm-zoom-btn.jvm-zoomout {
    @apply right-0;
}

.taskCheckbox:checked ~ .box span {
    @apply opacity-100;
}

.taskCheckbox:checked ~ p {
    @apply line-through;
}

.taskCheckbox:checked ~ .box {
    @apply bg-cfp-500 border-cfp-500;
}

.custom-input-date::-webkit-calendar-picker-indicator {
    background: transparent;
}

input[type="search"]::-webkit-search-cancel-button {
    @apply appearance-none;
}

.custom-input-date::-webkit-calendar-picker-indicator {
    background-position: center;
    background-repeat: no-repeat;
    background-size: 20px;
}

.custom-input-date-1::-webkit-calendar-picker-indicator {
    background-image: url(../images/icon/icon-calendar.svg);
}

.custom-input-date-2::-webkit-calendar-picker-indicator {
    background-image: url(../images/icon/icon-arrow-down.svg);
}

[x-cloak] {
    display: none !important;
}
