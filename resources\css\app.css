@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
  --color-cf-primary: oklch(54.79% 0.073 198.86);
  --color-cf-primary-50: oklch(95.19% 0.05 199.4);
  --color-cf-primary-100: oklch(90.22% 0.109 198.98);
  --color-cf-primary-200: oklch(80.65% 0.108 198.95);
  --color-cf-primary-300: oklch(72.29% 0.097 198.65);
  --color-cf-primary-400: oklch(63.7% 0.086 199.22);
  --color-cf-primary-500: oklch(54.79% 0.073 198.86);
  --color-cf-primary-600: oklch(46.78% 0.063 198.15);
  --color-cf-primary-700: oklch(38.13% 0.051 199.01);
  --color-cf-primary-800: oklch(29.68% 0.04 200.42);
  --color-cf-primary-900: oklch(22.1% 0.03 198.96);
  --color-cf-primary-950: oklch(17.32% 0.022 200.65);
}

/* Custom animations for contact page */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out;
}
