import React, { useState } from 'react';
import { Head, Link, useForm, router } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import Button from '@/Components/Button';
import FormError from '@/Components/FormError';

export default function Edit({ gateway }) {
    const [activeTab, setActiveTab] = useState(gateway.test_mode ? 'test' : 'live');
    
    const { data, setData, put, processing, errors } = useForm({
        is_active: gateway.is_active,
        test_mode: gateway.test_mode,
        test_config: gateway.test_config || {},
        live_config: gateway.live_config || {},
        webhook_secret: gateway.webhook_secret || '',
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        put(route('admin.payment-gateways.update', gateway.id));
    };

    const updateConfig = (mode, field, value) => {
        setData(`${mode}_config`, {
            ...data[`${mode}_config`],
            [field]: value
        });
    };

    const handleStripeConnect = () => {
        router.post(route('admin.stripe.connect'));
    };

    const handleStripeDisconnect = () => {
        if (confirm('Are you sure you want to disconnect your Stripe account? This will remove all stored credentials.')) {
            router.post(route('admin.stripe.disconnect'));
        }
    };

    const isStripeConnected = (mode) => {
        const config = data[`${mode}_config`];
        return config?.stripe_user_id && config?.publishable_key && config?.secret_key;
    };

    const renderStripeFields = (mode) => (
        <div className="space-y-6">
            {/* Stripe Connect Section */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div className="flex items-start">
                    <div className="flex-shrink-0">
                        <svg className="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                    <div className="ml-3 flex-1">
                        <h3 className="text-sm font-medium text-blue-800">
                            Quick Setup with Stripe Connect
                        </h3>
                        <div className="mt-2 text-sm text-blue-700">
                            <p>Connect your Stripe account automatically to configure all settings including webhooks.</p>
                        </div>
                        <div className="mt-4">
                            {isStripeConnected(mode) ? (
                                <div className="flex items-center space-x-4">
                                    <div className="flex items-center text-green-600">
                                        <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                        </svg>
                                        Connected to Stripe
                                    </div>
                                    <button
                                        type="button"
                                        onClick={handleStripeDisconnect}
                                        className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                    >
                                        Disconnect
                                    </button>
                                </div>
                            ) : (
                                <button
                                    type="button"
                                    onClick={handleStripeConnect}
                                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                >
                                    <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                                    </svg>
                                    Connect with Stripe
                                </button>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Manual Configuration Section */}
            <div className="border-t border-gray-200 pt-6">
                <h4 className="text-sm font-medium text-gray-900 mb-4">Manual Configuration</h4>
                <div className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Publishable Key
                        </label>
                        <input
                            type="text"
                            value={data[`${mode}_config`].publishable_key || ''}
                            onChange={(e) => updateConfig(mode, 'publishable_key', e.target.value)}
                            placeholder={`pk_${mode}_...`}
                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            disabled={isStripeConnected(mode)}
                        />
                        <FormError message={errors[`${mode}_config.publishable_key`]} />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Secret Key
                        </label>
                        <input
                            type="password"
                            value={data[`${mode}_config`].secret_key || ''}
                            onChange={(e) => updateConfig(mode, 'secret_key', e.target.value)}
                            placeholder={`sk_${mode}_...`}
                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            disabled={isStripeConnected(mode)}
                        />
                        <FormError message={errors[`${mode}_config.secret_key`]} />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Webhook Endpoint Secret
                        </label>
                        <input
                            type="password"
                            value={data[`${mode}_config`].webhook_secret || data[`${mode}_config`].webhook_endpoint_secret || ''}
                            onChange={(e) => updateConfig(mode, 'webhook_secret', e.target.value)}
                            placeholder="whsec_..."
                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            disabled={isStripeConnected(mode)}
                        />
                        <FormError message={errors[`${mode}_config.webhook_secret`]} />
                        {isStripeConnected(mode) && (
                            <p className="mt-1 text-xs text-gray-500">
                                Webhook endpoint automatically configured via Stripe Connect
                            </p>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );

    const renderPayPalFields = (mode) => (
        <div className="space-y-4">
            <div>
                <label className="block text-sm font-medium text-gray-700">
                    Client ID
                </label>
                <input
                    type="text"
                    value={data[`${mode}_config`].client_id || ''}
                    onChange={(e) => updateConfig(mode, 'client_id', e.target.value)}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
                <FormError message={errors[`${mode}_config.client_id`]} />
            </div>
            
            <div>
                <label className="block text-sm font-medium text-gray-700">
                    Client Secret
                </label>
                <input
                    type="password"
                    value={data[`${mode}_config`].client_secret || ''}
                    onChange={(e) => updateConfig(mode, 'client_secret', e.target.value)}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
                <FormError message={errors[`${mode}_config.client_secret`]} />
            </div>
            
            <div>
                <label className="block text-sm font-medium text-gray-700">
                    Webhook ID
                </label>
                <input
                    type="text"
                    value={data[`${mode}_config`].webhook_id || ''}
                    onChange={(e) => updateConfig(mode, 'webhook_id', e.target.value)}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
                <FormError message={errors[`${mode}_config.webhook_id`]} />
            </div>
        </div>
    );

    const renderConfigFields = (mode) => {
        if (gateway.name === 'stripe') {
            return renderStripeFields(mode);
        } else if (gateway.name === 'paypal') {
            return renderPayPalFields(mode);
        }
        return null;
    };

    return (
        <DashboardLayout>
            <Head title={`Configure ${gateway.display_name}`} />
            
            <div className="py-6">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="md:flex md:items-center md:justify-between">
                        <div className="flex-1 min-w-0">
                            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                                Configure {gateway.display_name}
                            </h2>
                            <p className="mt-1 text-sm text-gray-500">
                                Set up your {gateway.display_name} payment gateway configuration.
                            </p>
                        </div>
                        <div className="mt-4 flex md:mt-0 md:ml-4">
                            <Link
                                href={route('admin.payment-gateways.index')}
                                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                Back to Gateways
                            </Link>
                        </div>
                    </div>

                    <form onSubmit={handleSubmit} className="mt-8 space-y-6">
                        <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                            <div className="md:grid md:grid-cols-3 md:gap-6">
                                <div className="md:col-span-1">
                                    <h3 className="text-lg font-medium leading-6 text-gray-900">
                                        General Settings
                                    </h3>
                                    <p className="mt-1 text-sm text-gray-500">
                                        Basic configuration options for this payment gateway.
                                    </p>
                                </div>
                                <div className="mt-5 md:mt-0 md:col-span-2">
                                    <div className="space-y-4">
                                        <div className="flex items-center">
                                            <input
                                                id="test_mode"
                                                type="checkbox"
                                                checked={data.test_mode}
                                                onChange={(e) => setData('test_mode', e.target.checked)}
                                                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                            />
                                            <label htmlFor="test_mode" className="ml-2 block text-sm text-gray-900">
                                                Test Mode
                                            </label>
                                        </div>
                                        
                                        <div className="flex items-center">
                                            <input
                                                id="is_active"
                                                type="checkbox"
                                                checked={data.is_active}
                                                onChange={(e) => setData('is_active', e.target.checked)}
                                                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                            />
                                            <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                                                Active
                                            </label>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700">
                                                Webhook Secret
                                            </label>
                                            <input
                                                type="password"
                                                value={data.webhook_secret}
                                                onChange={(e) => setData('webhook_secret', e.target.value)}
                                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                            />
                                            <FormError message={errors.webhook_secret} />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white shadow sm:rounded-lg">
                            <div className="px-4 py-5 sm:p-6">
                                <h3 className="text-lg font-medium leading-6 text-gray-900 mb-4">
                                    API Configuration
                                </h3>
                                
                                <div className="border-b border-gray-200">
                                    <nav className="-mb-px flex space-x-8">
                                        <button
                                            type="button"
                                            onClick={() => setActiveTab('test')}
                                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                                activeTab === 'test'
                                                    ? 'border-indigo-500 text-indigo-600'
                                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                            }`}
                                        >
                                            Test Configuration
                                        </button>
                                        <button
                                            type="button"
                                            onClick={() => setActiveTab('live')}
                                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                                activeTab === 'live'
                                                    ? 'border-indigo-500 text-indigo-600'
                                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                            }`}
                                        >
                                            Live Configuration
                                        </button>
                                    </nav>
                                </div>

                                <div className="mt-6">
                                    {renderConfigFields(activeTab)}
                                </div>
                            </div>
                        </div>

                        <div className="flex justify-end">
                            <Button
                                type="submit"
                                disabled={processing}
                                className="ml-3"
                            >
                                {processing ? 'Saving...' : 'Save Configuration'}
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </DashboardLayout>
    );
}
