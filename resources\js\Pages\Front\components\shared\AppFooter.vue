<script setup>
import feather from 'feather-icons';
import { onMounted, onUpdated } from 'vue';
import FooterCopyright from './FooterCopyright.vue';

const socials = [
	{
		id: 1,
		name: 'Website',
		icon: 'globe',
		url: '#',
	},
	{
		id: 2,
		name: 'GitHub',
		icon: 'github',
		url: '#',
	},
	{
		id: 3,
		name: 'Twitter',
		icon: 'twitter',
		url: '#',
	},
	{
		id: 4,
		name: 'LinkedIn',
		icon: 'linkedin',
		url: '#',
	},
	{
		id: 5,
		name: 'YouTube',
		icon: 'youtube',
		url: '#',
	},
];

const replaceFeatherIcons = () => {
	feather.replace();
};

// Call replaceFeatherIcons on component mount and update
onMounted(replaceFeatherIcons);
onUpdated(replaceFeatherIcons);
</script>

<style scoped>
/* Your scoped styles here */
</style>

<template>
	<div class="mx-auto container">
		<div class="border-gray-200 mt-20 max-md:mt-5 pt-20 sm:pt-20 max-md:pt-6 pb-8 max-md:pb-6 border-t">
			<!-- Footer social links -->
			<div class="flex flex-col justify-center items-center mb-12 sm:mb-20 max-md:mb-5">
				<p class="mb-5 font-general-semibold font-semibold text-cfp-500-dark text-xl sm:text-4xl">
					Follow Us</p>
				<ul class="flex gap-4 sm:gap-8">
					<a v-for="social in socials" :key="social.id" :href="social.url" target="__blank"
						class="bg-gray-50 hover:bg-gray-200 px-4 max-md:px-2 py-2 max-md:py-1 border rounded-md text-gray-400 hover:text-gray-800 duration-500 cursor-pointer">
						<i :data-feather="social.icon" class="w-5 h-8"></i>
					</a>
				</ul>
			</div>

			<!-- Footer copyright -->
			<FooterCopyright />
		</div>
	</div>
</template>