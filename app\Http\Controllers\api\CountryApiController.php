<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Models\Country;
use Exception;

class CountryApiController extends Controller
{
    public function loadCountry()
    {
        try {
            $countries = Country::all();

            if ($countries->isNotEmpty()) {
                return response()->json(['status' => true, 'data' => $countries]);
            } else {
                return response()->json(['status' => false]);
            }
        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }
    }
}
