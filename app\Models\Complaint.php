<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Complaint extends Model
{
    use HasFactory;

    protected $table = 'complain';

    // Complaint.php
    public function company()
    {
        return $this->belongsTo(Company::class, 'companyId');
    }

    public function assignedUser()
    {
        return $this->belongsTo(User::class, 'assignedToUserId');
    }
}
