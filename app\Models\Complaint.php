<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Complaint extends Model
{
    use HasFactory;

    protected $table = 'complaints';

    protected $fillable = [
        'cmp_id',
        'companyId',
        'assignedToUserId',
        'priority',
        'progressStatus',
        'complainTitle',
        'complainDetail',
        'complainStatus',
    ];

    protected $casts = [
        'priority' => 'string',
        'progressStatus' => 'string',
        'complainStatus' => 'string',
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'companyId');
    }

    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assignedToUserId');
    }

    public function messages(): HasMany
    {
        return $this->hasMany(ComplaintMessage::class, 'complainId');
    }

    public function getPriorityLabelAttribute(): string
    {
        return match($this->priority) {
            '0' => 'Low',
            '1' => 'Medium',
            '2' => 'High',
            default => 'Low'
        };
    }

    public function getStatusLabelAttribute(): string
    {
        return match($this->progressStatus) {
            'new' => 'New',
            'inprogress' => 'In Progress',
            'need_user_input' => 'Need User Input',
            'resolved' => 'Resolved',
            default => 'New'
        };
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'cmp_id';
    }
}
