<script setup lang="ts">
import { ref } from 'vue'

const switcherToggle = ref<boolean>(false)
</script>

<template>
  <div>
    <label for="toggle1" class="flex items-center cursor-pointer select-none">
      <div class="relative">
        <input type="checkbox" id="toggle1" class="sr-only" @change="switcherToggle = !switcherToggle" />
        <div class="block bg-meta-9 rounded-full w-14 h-8"></div>
        <div :class="switcherToggle && '!right-1 !translate-x-full !bg-cfp-500'"
          class="top-1 left-1 absolute bg-white rounded-full w-6 h-6 transition"></div>
      </div>
    </label>
  </div>
</template>
