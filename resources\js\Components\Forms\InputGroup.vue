<script>
import { defineComponent } from 'vue'

export default defineComponent({
  props: {
    label: String,
    type: String,
    placeholder: String,
    customClasses: String,
    required: {
      type: Boolean,
      default: false
    }
  }
})
</script>

<template>
  <div :class="customClasses">
    <label class="block mb-2.5 text-slate-800">
      {{ label }}
      <span v-if="required" class="text-meta-1">*</span>
    </label>
    <input :type="type" :placeholder="placeholder"
      class="border-[1.5px] border-stroke focus:border-cfp-500 bg-transparent disabled:bg-whiter px-5 py-3 active:border-cfp-500 rounded-xl w-full font-normal text-slate-800 transition disabled:cursor-default outline-none" />
  </div>
</template>
