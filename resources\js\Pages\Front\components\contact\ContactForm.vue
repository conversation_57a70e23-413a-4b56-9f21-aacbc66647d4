<script setup>
import { ref } from 'vue';
import { useForm } from '@inertiajs/vue3';
import Button from '../reusable/Button.vue';
import FormInput from '../reusable/FormInput.vue';
import FormTextarea from '../reusable/FormTextarea.vue';
import InputError from '@/Components/InputError.vue';

const form = useForm({
    fullname: '',
    email: '',
    subject: '',
    message: ''
});

const submitForm = () => {
    form.post(route('send-contact-message'), {
        onSuccess: () => {
            form.reset(); // Optionally reset the form after successful submission
        }
    });
};
</script>

<template>
    <div class="w-full md:w-1/2">
        <div class="login-body-cls">
            <p class="mb-8 pb-2 border-b font-general-medium text-2xl text-cfp-500-dark">
                Contact Form
            </p>

            <Transition enter-active-class="transition ease-in-out" enter-from-class="opacity-0"
                leave-active-class="transition ease-in-out" leave-to-class="opacity-0">
                <p v-if="form.recentlySuccessful" class="text-green-400 text-large">
                    Your message has been sent successfully!
                </p>
            </Transition>


            <form @submit.prevent="submitForm" class="space-y-7 font-general-regular">
                <FormInput label="Full Name" inputIdentifier="fullname" v-model="form.fullname" />
                <InputError class="mt-2" :message="form.errors.fullname" />

                <FormInput label="Email" inputIdentifier="email" inputType="email" v-model="form.email" />
                <InputError class="mt-2" :message="form.errors.email" />

                <FormInput label="Subject" inputIdentifier="subject" v-model="form.subject" />
                <InputError class="mt-2" :message="form.errors.subject" />

                <FormTextarea label="Message" textareaIdentifier="message" v-model="form.message" />
                <InputError class="mt-2" :message="form.errors.message" />

                <div>
                    <Button title="Send Message"
                        class="bg-cfp-500 hover:bg-cfp-500/80 px-4 py-2.5 rounded-md focus:ring-1 text-sm text-white tracking-wider duration-500"
                        type="submit" aria-label="Send Message" />
                </div>
            </form>

        </div>
    </div>
</template>

<!-- Scoped styles block, if needed -->
<style lang="scss" scoped></style>
