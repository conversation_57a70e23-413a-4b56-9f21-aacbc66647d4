<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->unsignedBigInteger('price'); // Price in cents
            $table->enum('billing_interval', ['monthly', 'quarterly', 'semi_annual', 'annual']);
            $table->unsignedInteger('billing_interval_count')->default(1);
            $table->unsignedInteger('trial_days')->default(7);
            $table->json('features')->nullable(); // Plan features for display
            $table->json('limits')->nullable(); // Usage limits (max_agents, max_tickets, etc.)
            $table->boolean('is_active')->default(true);
            $table->boolean('is_popular')->default(false);
            $table->unsignedInteger('sort_order')->default(0);
            $table->string('stripe_price_id')->nullable();
            $table->string('paypal_plan_id')->nullable();
            $table->timestamps();

            $table->index(['is_active', 'sort_order']);
            $table->index('billing_interval');
        });
    }
};
