<script setup>
const props = defineProps(['contacts']);
</script>

<template>
	<div class="w-full md:w-1/2">
		<div class="px-6 max-w-xl text-left">
			<h2 class="mt-3 mb-8 pb-2 border-b font-general-medium text-2xl text-cfp-500-dark">
				Contact details
			</h2>
			<ul class="font-general-regular">
				<li class="flex" v-for="contact in contacts" :key="contact.id">
					<i :data-feather="contact.icon" class="mr-4 w-5 text-slate-500"></i>
					<a href="#" class="mb-4 text-lg text-ternary-dark max-md:text-sm" :class="contact.icon === 'mail' || contact.icon === 'phone'
						? 'hover:underline cursor-pointer'
						: ''
						" aria-label="Website and Phone">
						{{ contact.name }}
					</a>
				</li>
			</ul>
		</div>
	</div>
</template>

<!-- Scoped styles block, if needed -->
<style lang="scss" scoped></style>
