<script setup lang="ts">
import { ref } from 'vue'

import BrandOne from '@/assets/images/brand/brand-01.svg'
import BrandTwo from '@/assets/images/brand/brand-02.svg'
import BrandThree from '@/assets/images/brand/brand-03.svg'
import BrandFour from '@/assets/images/brand/brand-04.svg'
import BrandFive from '@/assets/images/brand/brand-05.svg'

const brandData = ref([
  {
    logo: BrandOne,
    name: 'Google',
    visitors: 3.5,
    revenues: '5,768',
    sales: 590,
    conversion: 4.8
  },
  {
    logo: BrandTwo,
    name: 'Twitter',
    visitors: 2.2,
    revenues: '4,635',
    sales: 467,
    conversion: 4.3
  },
  {
    logo: BrandThree,
    name: '<PERSON><PERSON><PERSON>',
    visitors: 2.1,
    revenues: '4,290',
    sales: 420,
    conversion: 3.7
  },
  {
    logo: BrandF<PERSON>,
    name: 'Vimeo',
    visitors: 1.5,
    revenues: '3,580',
    sales: 389,
    conversion: 2.5
  },
  {
    logo: BrandFive,
    name: 'Facebook',
    visitors: 3.5,
    revenues: '6,768',
    sales: 390,
    conversion: 4.2
  }
])
</script>

<template>
  <div class="border-stroke bg-white shadow-default px-5 sm:px-7.5 pt-6 pb-2.5 xl:pb-1 border rounded-sm">
    <h4 class="mb-6 font-semibold text-slate-800 text-xl">Top Channels</h4>

    <div class="flex flex-col">
      <div class="grid grid-cols-3 sm:grid-cols-5 bg-gray-2 rounded-sm">
        <div class="p-2.5 xl:p-5">
          <h5 class="font-medium text-sm xsm:text-base uppercase">Source</h5>
        </div>
        <div class="p-2.5 xl:p-5 text-center">
          <h5 class="font-medium text-sm xsm:text-base uppercase">Visitors</h5>
        </div>
        <div class="p-2.5 xl:p-5 text-center">
          <h5 class="font-medium text-sm xsm:text-base uppercase">Revenues</h5>
        </div>
        <div class="sm:block hidden p-2.5 xl:p-5 text-center">
          <h5 class="font-medium text-sm xsm:text-base uppercase">Sales</h5>
        </div>
        <div class="sm:block hidden p-2.5 xl:p-5 text-center">
          <h5 class="font-medium text-sm xsm:text-base uppercase">Conversion</h5>
        </div>
      </div>

      <div v-for="(brand, key) in brandData" :key="key" :class="`grid grid-cols-3 sm:grid-cols-5 ${key === brandData.length - 1 ? '' : 'border-b border-stroke '
        }`">
        <div class="flex items-center gap-3 p-2.5 xl:p-5">
          <div class="flex-shrink-0">
            <img :src="brand.logo" alt="Brand" />
          </div>
          <p class="sm:block hidden text-slate-800">{{ brand.name }}</p>
        </div>

        <div class="flex justify-center items-center p-2.5 xl:p-5">
          <p class="text-slate-800">{{ brand.visitors }}K</p>
        </div>

        <div class="flex justify-center items-center p-2.5 xl:p-5">
          <p class="text-meta-3">${{ brand.revenues }}</p>
        </div>

        <div class="sm:flex justify-center items-center hidden p-2.5 xl:p-5">
          <p class="text-slate-800">{{ brand.sales }}</p>
        </div>

        <div class="sm:flex justify-center items-center hidden p-2.5 xl:p-5">
          <p class="text-meta-5">{{ brand.conversion }}%</p>
        </div>
      </div>
    </div>
  </div>
</template>
