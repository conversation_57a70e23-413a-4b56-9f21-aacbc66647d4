<?php

namespace App\Listeners;

use Carbon\Carbon;
use Illuminate\Auth\Events\Logout;

class LogLastActivityOnLogout
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Logout $event): void
    {
        // Get the user who logged out
        $user = $event->user;

        if ($user) {
            $user->last_seen     = Carbon::now();
            $user->last_activity = null;
            $user->save();
        }
    }
}
