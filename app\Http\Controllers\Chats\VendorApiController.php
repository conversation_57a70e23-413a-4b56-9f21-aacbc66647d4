<?php

namespace App\Http\Controllers\Chats;

use App\Http\Controllers\Controller;
use App\Models\ApiAccessToken;
use App\Models\CbVendorApi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;
use Inertia\Inertia;

class VendorApiController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {

        CbVendorApi::checkorCreateDefaultApi(Auth::id());

        $title     = 'API Configuration';
        $actionBtn = ['title' => 'Add API', 'route' => route('vendor-apis.create')];
        $apis      = CbVendorApi::where('user_id', Auth::id())->orderBy('id', 'desc')->get();

        return Inertia::render('Chats/Api/List', ['title' => $title, 'apis' => $apis, 'actionBtn' => $actionBtn, 'success' => session('success')]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $title = 'Create API';

        return Inertia::render('Chats/Api/Add', ['title' => $title]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $userId = Auth::id();
        $request->validate([
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('cb_vendor_api')->where(function ($query) use ($userId) {
                    return $query->where('user_id', $userId);
                }),
            ],
            'url'                 => 'required|string|url|max:255',
            'method'              => 'required|string|max:255',
            'request_parameters'  => 'nullable|string',
            'response_parameters' => 'nullable|string',
            'description'         => 'nullable|string',
        ]);
        $data            = $request->all();
        $data['user_id'] = Auth::id();
        $api             = CbVendorApi::create($data);

        return Redirect::route('vendor-apis.edit', $api->id)->with([
            'status'  => true,
            'message' => 'API saved successfully',
            'class'   => 'flex items-center p-2 mb-4 text-green-700 rounded-md bg-green-100',
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $api   = CbVendorApi::find($id);
        $title = 'Show API';

        return Inertia::render('Chats/Api/Show', ['api' => $api, 'title' => $title]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $title = 'Edit API';
        $api   = CbVendorApi::find($id);

        if ($api->name === '@apiauth-get_api_token' && $api->request_parameters) {
            $params = json_decode($api->request_parameters, true);
            if ($params) {
                // Find username and password parameters
                $usernameParam = collect($params)->firstWhere('type', 'username');
                $passwordParam = collect($params)->firstWhere('type', 'password');

                if ($usernameParam && $passwordParam) {
                    $api->username       = $usernameParam['value'] ?? '';
                    $api->username_param = $usernameParam['param'] ?? '';
                    $api->password       = $passwordParam['value'] ?? '';
                    $api->password_param = $passwordParam['param'] ?? '';
                }
            }
        }

        return Inertia::render('Chats/Api/Edit', [
            'api'   => $api,
            'title' => $title,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $checkApiPre = CbVendorApi::find($id);
        $userId      = Auth::id();

        if ($checkApiPre->name == '@apiauth-get_api_token') {
            $request->validate([
                'name' => [
                    'required',
                    'string',
                    'max:255',
                    Rule::unique('cb_vendor_api')->where(function ($query) use ($userId, $id) {
                        return $query->where('user_id', $userId)->where('id', '!=', $id);
                    }),
                ],
                'username'    => 'required|string|max:180',
                'password'    => 'required|string|max:180',
                'url'         => 'required|string|url|max:255',
                'method'      => 'required|string|max:255',
                'description' => 'nullable|string',
            ]);

            $existingToken = ApiAccessToken::where('api_email', $request->username)->first();

            if ($existingToken && $existingToken->user_id !== Auth::id()) {
                return back()->withErrors(['username' => 'This API email is already used by another user.']);
            }

            $Data = ApiAccessToken::where('user_id',Auth::id())->first();
            $token = Str::random(60);
            if ($Data) {

                $checkExpired = ApiAccessToken::where('expires_at', '<', now())->first();
                if ($checkExpired) {

                    // Update the existing token
                    $Data->update([
                        'api_email'    => $request->username,
                        'api_password' => $request->password,
                        'token'      => $token,
                        'expires_at' => now()->addDay(),
                        'is_expired' => false,
                    ]);

                }

            } else {
                // Create a new token entry if none exists
                $Data = ApiAccessToken::create([
                    'user_id'      => Auth::id(),
                    'api_email'    => $request->username,
                    'api_password' => $request->password,
                    'token'        => $token,
                    'expires_at'   => now()->addDay(),
                    'is_expired'   => false,
                ]);
            }

            // Create request parameters array for authentication
            $requestParameters = [
                [
                    'label'    => 'Username',
                    'param'    => $request->username_param,
                    'value'    => $request->username,
                    'type'     => 'username',
                    'datatype' => 'string',
                ],
                [
                    'label'    => 'Password',
                    'param'    => $request->password_param,
                    'value'    => $request->password,
                    'type'     => 'password',
                    'datatype' => 'string',
                ],
            ];

            $data                       = $request->except(['name', 'username', 'password', 'username_param', 'password_param']);
            $data['user_id']            = Auth::id();
            $data['request_parameters'] = json_encode($requestParameters);

            $checkApiPre->update($data);


        } else {
            $request->validate([
                'name' => [
                    'required',
                    'string',
                    'max:255',
                    Rule::unique('cb_vendor_api')->where(function ($query) use ($userId, $id) {
                        return $query->where('user_id', $userId)->where('id', '!=', $id);
                    }),
                ],
                'url'                 => 'required|string|url|max:255',
                'method'              => 'required|string|max:255',
                'request_parameters'  => 'nullable|string',
                'response_parameters' => 'nullable|string',
                'description'         => 'nullable|string',
            ]);
            $api             = CbVendorApi::find($id);
            $data            = $request->all();
            $data['user_id'] = Auth::id();
            $api->update($data);
        }

        return Redirect::route('vendor-apis.edit', $id)->with([
            'status'  => true,
            'message' => 'API saved successfully',
            'class'   => 'flex items-center p-2 mb-4 text-green-700 rounded-md bg-green-100',
        ]);
    }

    public function searchApi(Request $request)
    {
        $search = $request->search;

        if (strpos($search, '.') !== false) {
            [$name, $param] = explode('.', $search);

            // Match the name and search for parameters that partially match the `param`
            $vendors = CbVendorApi::where('user_id', Auth::id())
                ->where('name', 'like', '%'.$name.'%')
                ->whereRaw("JSON_SEARCH(response_parameters, 'one', ?) IS NOT NULL", ['%'.$param.'%'])
                ->get();
        } else {
            // Search only by name if no dot is present
            $vendors = CbVendorApi::where('user_id', Auth::id())->where('name', 'like', '%'.$search.'%')->get();
        }

        return response()->json($vendors);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $api = CbVendorApi::find($id);
        if ($api->name == '@apiauth-get_api_token') {
            return response()->json(['success' => false, 'message' => 'You can not delete default api']);
        } else {
            $api->forceDelete();

            return response()->json(['success' => true, 'message' => 'API deleted successfully']);
        }

    }
}
