<script setup>
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  label: {
    type: String,
    default: '',
  },
  textareaIdentifier: {
    type: String,
    default: '',
  },
  modelValue: {
    type: [String, Number],
    default: '',
  },
});

const emit = defineEmits(['update:modelValue']);
</script>

<template>
  <div>
    <label class="block mb-2 text-cfp-500-dark text-sm" :for="textareaIdentifier">
      {{ label }}
    </label>
    <textarea
      class="border-gray-300 mt-1 px-4 py-2 border border-opacity-50 rounded-md w-full text-cfp-500-dark text-sm"
      :id="textareaIdentifier" :name="textareaIdentifier" :aria-label="textareaIdentifier" :placeholder="label"
      v-bind="$attrs" :value="modelValue" @input="emit('update:modelValue', $event.target.value)" cols="14"
      rows="6"></textarea>
  </div>
</template>

<!-- Scoped styles block, if needed -->
<style lang="scss" scoped></style>
