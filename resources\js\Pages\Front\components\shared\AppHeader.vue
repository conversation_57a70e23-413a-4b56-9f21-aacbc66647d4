<script setup>
import ThemeSwitcher from '../ThemeSwitcher.vue';
import AppHeaderLinks from './AppHeaderLinks.vue';
import { ref } from 'vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';

const isOpen = ref(false);
const modal = ref(false);
const theme = ref(localStorage.getItem('theme') || 'light');
const categories = [
	{
		id: 1,
		value: 'web',
		name: 'Web Application',
	},
	{
		id: 2,
		value: 'mobile',
		name: 'Mobile Application',
	},
	{
		id: 3,
		value: 'ui-ux',
		name: 'UI/UX Design',
	},
	{
		id: 4,
		value: 'branding',
		name: 'Branding & Anim',
	},
];

const updateTheme = (newTheme) => {
	theme.value = newTheme;
};

const toggleMobileMenu = () => {
	isOpen.value = !isOpen.value;
};


const showModal = () => {
	if (modal.value) {
		// Stop screen scrolling
		document.getElementsByTagName('html')[0].classList.remove('overflow-y-hidden');
		modal.value = false;
	} else {
		document.getElementsByTagName('html')[0].classList.add('overflow-y-hidden');
		modal.value = true;
	}
};

const isActive = (path) => {
	// Get the current URL path
	const currentPath = window.location.pathname;
	// Check if the current path matches the specified path
	return currentPath === path;
};

</script>

<template>
	<nav id="nav" class="relative z-10 mx-auto sm:mx-auto sm:container">
		<!-- Header start -->
		<div
			class="block z-10 sm:flex sm:justify-between sm:items-center border-gray-200 mx-6 sm:mx-0 xl:mx-auto my-6 mt-3 py-3 pr-3 sm:pr-8 pl-0 sm:pl-6 border rounded-md max-w-screen-lg xl:max-w-screen-xl">
			<!-- Header menu links and small screen hamburger menu -->
			<div class="flex justify-between items-center sm:px-0">
				<!-- Header logos -->
				<div class="w-50 max-md:w-35">
					<ResponsiveNavLink href="/" class="p-0">
						<img v-if="theme === 'light'" src="@/assets/images/logo-dark.svg"
							class="w-30 max-md:w-auto h-auto" alt="Dark Logo" />
						<img v-else src="@/assets/images/logo-light.svg" class="w-30 max-md:w-auto" alt="Light Logo" />
					</ResponsiveNavLink>
				</div>

				<!-- Theme switcher small screen -->
				<theme-switcher :theme="theme" @themeChanged="updateTheme"
					class="block sm:hidden bg-ternary-light hover:bg-hover-light hover:shadow-sm px-2.5 py-2 rounded-md" />

				<!-- Small screen hamburger menu -->
				<div class="sm:hidden">
					<button @click="toggleMobileMenu" type="button" class="focus:outline-none"
						aria-label="Hamburger Menu">
						<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
							class="w-7 h-7 text-secondary-dark fill-current">
							<path v-if="isOpen" fill-rule="evenodd"
								d="M18.278 16.864a1 1 0 0 1-1.414 1.414l-4.829-4.828-4.828 4.828a1 1 0 0 1-1.414-1.414l4.828-4.829-4.828-4.828a1 1 0 0 1 1.414-1.414l4.829 4.828 4.828-4.828a1 1 0 1 1 1.414 1.414l-4.828 4.829 4.828 4.828z"
								clip-rule="evenodd"></path>
							<path v-if="!isOpen" fill-rule="evenodd"
								d="M4 5h16a1 1 0 0 1 0 2H4a1 1 0 1 1 0-2zm0 6h16a1 1 0 0 1 0 2H4a1 1 0 0 1 0-2zm0 6h16a1 1 0 0 1 0 2H4a1 1 0 0 1 0-2z">
							</path>
						</svg>
					</button>
				</div>
			</div>

			<!-- Header links -->
			<app-header-links :showModal="showModal" :isOpen="isOpen" />

			<!-- Header right section buttons -->
			<div class="sm:flex md:flex-row flex-col justify-between items-center hidden">
				<!-- Hire me button -->
				<div class="md:block hidden">
					<ResponsiveNavLink :href="route('user-login')"
						:class="['font-general-medium block text-left text-base font-medium', isActive('/login') ? 'text-cfp-500' : 'text-cfp-500-dark ', 'hover:text-cfp-500  sm:mx-4 mb-2 sm:py-2']"
						aria-label="Contact">Login</ResponsiveNavLink>
				</div>

				<!-- Theme switcher large screen
		  <theme-switcher
			:theme="theme"
			@themeChanged="updateTheme"
			class="bg-cfp-500-light shadow-sm ml-8 px-3 py-2 rounded-xl cursor-pointer"
		  /> -->
			</div>
		</div>

		<!-- Hire me modal -->
		<hire-me-modal :showModal="showModal" :modal="modal" :categories="categories" aria-modal="Hire Me Modal" />
	</nav>
</template>