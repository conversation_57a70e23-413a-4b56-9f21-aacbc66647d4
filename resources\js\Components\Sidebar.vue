<script>
import { computed } from 'vue';
import { Link } from '@inertiajs/vue3';
</script>

<template>
    <div class="relative-">
        <button class="top-2 left-6 z-50 absolute lg:hidden text-slate-800" @click="toggleMenu">
            <i v-if="isMenuOpen" class="text-2xl fa fa-times"></i>
            <i v-else class="text-2xl fa fa-bars"></i>
        </button>

        <aside :class="{
            'translate-x-0': isMenuOpen || isDesktop,
            '-translate-x-full hidden': !isMenuOpen && !isDesktop,
            'block': isMenuOpen || isDesktop
        }"
            class="lg:block top-0 z-40 sticky !bg-gray-50 shadow-1 border-r w-39 h-full transition-all duration-300  overflow-y-hidden scrollbar-thin scrollbar-track-gray-100 ease-in-out">
            <div class="scroll">
                <ul class="list-unstyled">
                    <li :class="{ 'active left-menuactive-effect': isActive('/dtadmin/dashboard') }" class="border-b">
                        <Link :href="route('dashboard')" class="px-6 py-4 text-center">
                        <i class="block mr-4 w-full text-2xl fa fa-tachometer"></i>
                        <div class="left-sidebar-menutext">Dashboards</div>
                        </Link>
                    </li>
                    <li v-if="$page.props.auth.user.profileType == 'admin'"
                        :class="{ 'active left-menuactive-effect': isActive('/dtadmin/manage-users') }"
                        class="border-b">
                        <Link :href="route('manage-users')" class="px-6 py-4 text-center">
                            <i class="block mr-4 w-full font-normal text-2xl fa fa-users"></i>
                            <div class="left-sidebar-menutext">Users</div>
                        </Link>
                    </li>

                    <li v-if="$page.props.auth.user.profileType == 'user'"
                        :class="{ 'active left-menuactive-effect': isActive('/dtadmin/chatbot') }" class="border-b">
                        <Link :href="route('chatbot')" class="px-6 py-4 text-center">
                        <i class="block mr-4 w-full font-normal text-2xl fa fa-comments-o" aria-hidden="true"></i>
                        <div class="left-sidebar-menutext">Chatbot</div>
                        </Link>
                    </li>

                    <li v-if="$page.props.auth.user.profileType == 'agent' || $page.props.auth.user.profileType == 'company'"
                        :class="{ 'active left-menuactive-effect': isActive('/dtadmin/chats') }" class="border-b">
                        <Link :href="route('chats')" class="px-6 py-4 text-center">
                        <i class="block mr-4 w-full font-normal text-2xl fa fa-whatsapp" aria-hidden="true"></i>
                        <div class="left-sidebar-menutext">Chat</div>
                        </Link>
                    </li>
                    <li v-if="$page.props.auth.user.profileType == 'admin' || $page.props.auth.user.profileType == 'user' || $page.props.auth.user.profileType == 'company' || $page.props.auth.user.profileType == 'agent'"
                        :class="{ 'active left-menuactive-effect': isActive('/dtadmin/complaints') }" class="border-b">
                        <Link :href="route('complaints')" class="px-6 py-4 text-center">
                        <i class="block mr-4 w-full font-normal text-2xl fa-list-alt fa" aria-hidden="true"></i>
                        <div class="left-sidebar-menutext">Complaint</div>
                        </Link>
                    </li>

                    <!-- <li v-if="$page.props.auth.user.profileType == 'admin'"
                        :class="{ 'active left-menuactive-effect': isActive('/dtadmin/companies') }" class="border-b">
                        <Link :href="route('companies')" class="px-6 py-4 text-center">
                        <i class="block mr-4 w-full font-normal text-2xl fa fa-building-o" aria-hidden="true"></i>
                        <div class="left-sidebar-menutext">Companies</div>
                        </Link>
                    </li> -->

                    <li v-if="$page.props.auth.user.profileType == 'admin'"
                        :class="{ 'active left-menuactive-effect': isActive('/dtadmin/contacts') }" class="border-b">
                        <Link :href="route('contacts')" class="px-6 py-4 text-center">
                        <i class="block mr-4 w-full font-normal text-2xl fa fa-comments-o" aria-hidden="true"></i>
                        <div class="left-sidebar-menutext"> Inquiry</div>
                        </Link>
                    </li>

                    <!-- <li v-if="$page.props.auth.user.profileType == 'admin'"
                        :class="{ 'active left-menuactive-effect': isActive('/dtadmin/category/list') }"
                        class="border-b">
                        <Link :href="route('category.list')" class="px-6 py-4 text-center">
                        <i class="block mr-4 w-full font-normal text-2xl fa fa-certificate" aria-hidden="true"></i>
                        <div class="left-sidebar-menutext"> Category</div>
                        </Link>
                    </li> -->

                    <!-- <li v-if="$page.props.auth.user.profileType == 'company'"
                        :class="{ 'active left-menuactive-effect': isActive('/dtadmin/companies') }"
                        class="border-b">
                        <Link :href="route('companies')" class="px-6 py-4 text-center">
                            <i class="block mr-4 w-full font-normal text-2xl fa fa-building-o" aria-hidden="true"></i>
                            <div class="left-sidebar-menutext">Locations </div>
                        </Link>
                    </li> -->


                    <li v-if="$page.props.auth.user.profileType == 'admin' || $page.props.auth.user.profileType == 'company'"
                        :class="{ 'active left-menuactive-effect': isActive('/dtadmin/deals') }" class="border-b">
                        <Link :href="route('deals')" class="px-6 py-4 text-center">
                        <i class="block mr-4 w-full font-normal text-2xl fa fa-delicious" aria-hidden="true"></i>
                        <div class="left-sidebar-menutext">Deals</div>
                        </Link>
                    </li>

                    <li v-if="$page.props.auth.user.profileType == 'company'"
                        :class="{ 'active left-menuactive-effect': isActive('/dtadmin/settings/training/intents') }"
                        class="border-b">
                        <Link :href="route('intents.index')" class="px-6 py-4 text-center">
                        <i class="block mr-4 w-full font-normal text-2xl fa fa-indent" aria-hidden="true"></i>
                        <div class="left-sidebar-menutext">Intents</div>
                        </Link>
                    </li>

                    <li v-if="$page.props.auth.user.profileType == 'company'"
                        :class="{ 'active left-menuactive-effect': isActive('/dtadmin/settings/training/vendor-apis') }"
                        class="border-b">
                        <Link :href="route('vendor-apis.index')" class="px-6 py-4 text-center">
                        <i class="block mr-4 w-full font-normal text-2xl fa fa-american-sign-language-interpreting"
                            aria-hidden="true"></i>
                        <div class="left-sidebar-menutext">APIs</div>
                        </Link>
                    </li>

                    <!-- <li v-if="$page.props.auth.user.profileType == 'company'"
                        :class="{ 'active left-menuactive-effect': isActive('/dtadmin/manage-agent/list') }"
                        class="border-b">
                        <Link :href="route('manage-agent.list')" class="px-6 py-4 text-center">
                            <i class="block mr-4 w-full font-normal text-2xl fa fa-user" aria-hidden="true"></i>
                            <div class="left-sidebar-menutext">Agents</div>
                        </Link>
                    </li> -->

                    <!-- <li v-if="$page.props.auth.user.profileType == 'company'"
                        :class="{ 'active left-menuactive-effect': isActive('/dtadmin/settings/chatbots') }"
                        class="border-b">
                        <Link :href="route('chatbots.setting')" class="px-6 py-4 text-center">
                            <i class="block mr-4 w-full font-normal text-2xl fa fa-cog" aria-hidden="true"></i>
                            <div class="left-sidebar-menutext">Bots</div>
                        </Link>
                    </li> -->

                    <!-- Add other menu items here -->
                </ul>
            </div>
        </aside>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const isMenuOpen = ref(false);
const isDesktop = ref(false);

const toggleMenu = () => {
    isMenuOpen.value = !isMenuOpen.value;
};

const isActive = (path) => {
    const currentPath = window.location.pathname;
    return currentPath === path;
};

onMounted(() => {
    // Set initial desktop state
    isDesktop.value = window.innerWidth >= 1024;

    // Add event listener for window resizing
    window.addEventListener('resize', () => {
        isDesktop.value = window.innerWidth >= 1024;
        if (isDesktop.value) {
            isMenuOpen.value = true;
        } else {
            isMenuOpen.value = false;
        }
    });
});
</script>

<style scoped>
.scroll::-webkit-scrollbar {
    display: none;
}

button {
    background: none;
    border: none;
    cursor: pointer;
}

button:focus {
    outline: none;
}

aside {
    background-color: white;
}
</style>