import React, { useState } from 'react';
import { Link, useForm } from '@inertiajs/react';
import GuestLayout from '@/Layouts/GuestLayout';
import { Input } from '@/Components/FormElements';
import Button from '@/Components/Button';
import FormError from '@/Components/FormError';

export default function ForgotPassword() {
    const { data, setData, post, processing, errors } = useForm({
        email: '',
    });
    const [status, setStatus] = useState(null);
    const [formError, setFormError] = useState(null);

    function handleSubmit(e) {
        e.preventDefault();
        post('/forgot-password', {
            onSuccess: (res) => setStatus('Password reset link sent!'),
            onError: (err) => setFormError(err.email || 'Failed to send reset link'),
        });
    }

    return (
        <GuestLayout>
            <h2 className="text-2xl font-bold mb-6 text-center">Forgot your password?</h2>
            <form onSubmit={handleSubmit}>
                <Input
                    label="Email"
                    name="email"
                    type="email"
                    value={data.email}
                    onChange={e => setData('email', e.target.value)}
                    error={errors.email}
                    autoFocus
                />
                <FormError error={formError || errors.email} />
                <Button type="submit" loading={processing}>Send Reset Link</Button>
            </form>
            {status && <div className="text-green-600 text-sm mt-4 text-center">{status}</div>}
            <div className="mt-6 text-center text-sm">
                <Link href="/login" className="text-indigo-600 hover:underline">Back to login</Link>
            </div>
        </GuestLayout>
    );
} 