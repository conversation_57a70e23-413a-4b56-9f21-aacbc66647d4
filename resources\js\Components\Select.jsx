import clsx from 'clsx';
import React, { forwardRef } from 'react';

const Select = forwardRef(({ label, name, value, onChange, error, className = '', placeholder, children, ...props }, ref) => {
    return (
        <div className="flex flex-col gap-1 font-medium text-gray-700 leading-11 min-h-11">
            {label &&
                <label htmlFor={name} className="text-sm">
                    {label}
                </label>
            }
            <select
                ref={ref}
                id={name}
                name={name}
                value={value}
                onChange={onChange}
                className={clsx(
                    'w-full px-4 py-1 border rounded-lg focus:outline-none focus:ring-2 focus:ring-cf-primary-300 transition h-[54px]',
                    error ? 'border-red-500' : 'border-gray-300',
                    className
                )}
                {...props}
            >
                {placeholder && (
                    <option value="" disabled hidden>
                        {placeholder}
                    </option>
                )}
                {children}
            </select>
            {error && (
                <div id={`${name}-error`} className="text-red-500 text-xs">
                    {error}
                </div>
            )}
        </div>
    );
});

export default Select;