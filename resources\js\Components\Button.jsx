import React from 'react';
import clsx from 'clsx';

export default function Button({ type = 'button', variant="default", children, className = '', loading = false, ...props }) {

    const getButtonClasses = () => {
        const buttonDefaultClasses = "relative rounded py-2 px-4 text-sm cursor-pointer transition";
        switch (variant) {
            case 'secondary':
                return clsx(buttonDefaultClasses, "bg-gray-100 text-cf-primary fill-cf-primary hover:bg-gray-200");
            case 'outline':
                return clsx(buttonDefaultClasses, "border border-gray-300 text-gray-700 fill-gray-700 hover:bg-gray-100 hover:border-gray-400");
            case 'danger':
                return clsx(buttonDefaultClasses, "bg-red-600 text-white fill-red-600 hover:bg-red-700");
            case 'danger-outline':
                return clsx(buttonDefaultClasses, "border border-red-600 text-red-600 fill-red-600 hover:text-red-700 hover:border-red-700");
            case 'link':
                return clsx(buttonDefaultClasses, "text-black fill-black hover:text-cf-primary");
            case 'dark':
                return clsx(buttonDefaultClasses, "bg-gray-500 hover:bg-gray-600 text-white");
            default:
                return clsx(buttonDefaultClasses, "bg-cf-primary text-white fill-white hover:bg-cf-primary-700");
        }
    };

    return (
        <button
            type={type}
            className={clsx(getButtonClasses(), className)}
            disabled={loading || props.disabled}
            {...props}
        >
            {loading && (
                <svg className="animate-spin h-5 w-5 mr-2 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                </svg>
            )}
            {children}
        </button>
    );
}
