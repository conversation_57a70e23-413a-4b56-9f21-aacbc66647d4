<script setup>
import ApplicationLogo from '@/Components/ApplicationLogo.vue';
import { Link } from '@inertiajs/vue3';

const baseurl = window.location.origin;
const bgimg = `${baseurl}/images/balloon-lg.jpg`;
</script>

<template>
    <div class="flex flex-col sm:justify-center items-center bg-gray-100 bg-cover pt-6 sm:pt-0">
        <div>
            <Link href="/">
            <ApplicationLogo class="mb-3 w-52 h-auto " />
            </Link>
        </div>

        <div class="bg-white border mt-6 rounded-md w-full sm:max-w-screen-lg overflow-hidden">
            <slot />
        </div>
    </div>
</template>

<style>
.custom-bg-image {
    background-size: cover;
    background-position: center;
}
</style>
