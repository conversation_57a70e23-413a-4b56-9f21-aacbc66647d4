<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import ApplicationLogo from '@/Components/ApplicationLogo.vue';
import { Head } from '@inertiajs/vue3';
import axios from 'axios';
import { ref, watch, nextTick, onMounted } from 'vue'; // Import watch and nextTick
import { usePage } from '@inertiajs/vue3';
import BreadcrumbDefault from '@/Components/Breadcrumbs/BreadcrumbDefault.vue';
import { Inertia } from '@inertiajs/inertia';

const pageTitle = ref('Chatbot')

const { chatData, baseUrl, allAgents, agentType, authDetail, selectedAgent } = usePage().props;

const showInput = ref(true);
const selectedSlug = ref('');
const selectedSource = ref('Chatbot');
const inputText = ref(''); // Define a ref for input text
const messages = ref([]); // Define a ref for messages array
const status_fallback = ref(0);

selectedSlug.value = agentType;

onMounted(() => {
    // Check if the URL contains ?source=
    if (!window.location.search.includes('?source=')) {
        // If not, hide the input element
        showInput.value = false;
    } else {
        showInput.value = true;
    }

    if (Object.keys(chatData).length !== 0) {
        Object.values(chatData).forEach(item => {
            if (item.message.trim() !== '' && item.id == authDetail.id) {
                messages.value.push({ text: item.message, sender: 'user' });
            }
            if (item.message.trim() !== '' && item.id != authDetail.id) {
                messages.value.push({ text: item.message, sender: 'server' });
            }
            status_fallback.value = item.status_fallback;
        });
    }

    if (selectedAgent) {
        selectedSource.value = selectedAgent.chatbot_name;
    }

});


const handleClick = (slug, user_id) => {
    // Update the URL with the parameter without refreshing the page
    window.history.pushState(null, null, `?source=${slug}` + `&umid=${user_id}`);

    // Set the selectedSlug to the clicked agent's slug
    selectedSlug.value = slug;

    // Find the selected agent by its slug
    const selectedAgent = allAgents.find(agent => agent.slug === slug);

    // Get the name of the selected agent
    const selectedAgentName = selectedAgent ? selectedAgent.name : '';

    selectedSource.value = selectedAgentName;

    showInput.value = true;

    // Make a Get request to your Laravel backend with the parameter in the URL
    axios.get(`/chatbot-json?source=${slug}` + `&umid=${user_id}`)
        .then(response => {
            let chatDataNew = response.data;
            messages.value = [];
            if (Object.keys(chatDataNew).length !== 0) {
                Object.values(chatDataNew).forEach(item => {
                    if (item.message.trim() !== '' && item.id == authDetail.id) {
                        messages.value.push({ text: item.message, sender: 'user' });
                    }
                    if (item.message.trim() !== '' && item.id != authDetail.id) {
                        messages.value.push({ text: item.message, sender: 'server' });
                    }
                    status_fallback.value = item.status_fallback;
                    if (status_fallback.value == 1) {
                        showInput.value = false;
                    }
                });
            }

            setTimeout(function () {
                let lastMessage = document.querySelector('#messages > div:nth-last-child(2)');
                if (lastMessage) {
                    lastMessage.scrollIntoView({ behavior: 'smooth' });

                }
            }, 500); // 500 milliseconds = 1 second

            let sourceMessage = document.querySelector('#inputsource');
            if (sourceMessage) {
                sourceMessage.focus();
            }

        })
        .catch(error => {
            // Handle errors if the request fails
            console.error('Error:', error);
        });
}


const sendMessage = async () => {
    try {

        showInput.value = false;

        if (inputText.value.trim() === '') {
            return false;
        }

        let botTyping = document.querySelector('.botTyping');
        botTyping.classList.remove('hidden');
        botTyping.scrollIntoView();

        setTimeout(() => {
            botTyping.scrollIntoView();
        }, 1000);

        // Push the user message

        if (inputText.value.trim() !== '') {
            messages.value.push({ text: inputText.value, sender: 'user' });
        }
        let inputMesage = inputText.value;
        inputText.value = '';

        let token = document.head.querySelector('meta[name="csrf-token"]').content;

        let requestBody = {
            status_fallback: status_fallback.value,
            message: inputMesage,
            slug: selectedSlug.value, // Pass the selectedSlug
            type: document.querySelector('input[name="type"]').value // Pass the type field
        };



        const response = await fetch('/dtadmin/send-message', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': token,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
        });

        const responseData = await response.json();



        // Push the server response
        // if(responseData.message.trim() !== ''){
        //     messages.value.push({ text: responseData.message, sender: 'server' });
        // }


        setTimeout(function () {
            let lastMessage = document.querySelector('#messages > div:nth-last-child(2)');
            if (lastMessage) {
                lastMessage.scrollIntoView({ behavior: 'smooth' });
            }

            botTyping.classList.add('hidden');

            if (responseData.status_fallback == 1) {
                showInput.value = false;
                status_fallback.value = 1;
            } else {
                showInput.value = true;

            }

            if (responseData.message.trim() !== '') {
                messages.value.push({ text: responseData.message, sender: 'server' });
            }


            nextTick(() => {
                let sourceMessage = document.querySelector('#inputsource');
                if (sourceMessage) {
                    sourceMessage.focus();
                }
            });

        }, 500);




    } catch (error) {
        console.error('Error sending message:', error);
    }
};





let authUserid = authDetail.id;

Echo.private('agent-channel').listen('ChatAgentMessage', (e) => {

    let socketMessage = e.message;

    let sourceMessage2 = document.querySelector('#inputsource');
    if (sourceMessage2) {
        sourceMessage2.focus();
    }

    Object.values(socketMessage).forEach(item => {

        console.log(item);

        if (item.user_id === authUserid) {
        } else {
            if (item.text.trim() !== '') {
                let uid = encodeToBase64(authUserid);
                if ((item.chat_source.trim() === selectedSlug.value.trim()) && (item.chat_uid.trim() == uid)) {

                    if (item.status_fallback == 0) {
                        showInput.value = true;
                        status_fallback.value = 0;
                    }

                    messages.value.push({
                        text: item.text,
                        sender: 'server'
                    });
                }

                let lastMessage = document.querySelector('#messages > div:nth-last-child(2)');
                if (lastMessage) {
                    lastMessage.scrollIntoView({ behavior: 'smooth' });

                }


            }
        }

    });
})

const encodeToBase64 = (projectId) => {
    return btoa(projectId); // Using btoa() to encode to base64
}


</script>

<style>
#messages::-webkit-scrollbar {
    width: 1px;
    /* Adjust the width of the scrollbar */
    border-radius: 20px;
    /* Adjust the radius of the scrollbar */
}

#messages::-webkit-scrollbar-thumb {
    background-color: #999;
    /* Change the color of the scrollbar thumb */
    border-radius: 20px;
    /* Adjust the radius of the scrollbar thumb */
}

#messages::-webkit-scrollbar-track {
    background-color: #edf2f7;
    /* Change the color of the scrollbar track */
}
</style>



<template>

    <Head :title="pageTitle" />

    <AuthenticatedLayout>
        <!-- <template #header>
           
        </template> -->

        <BreadcrumbDefault :pageTitle="pageTitle" />

        <div class="flex h-screen">
            <div class="flex-1 py-12 p-4">

                <div class="gap-4 md:gap-6 2xl:gap-7.5 grid grid-cols-12 mt-4 md:mt-6 2xl:mt-7.5">
                    <div class="col-span-12 xl:col-span-4 rounded-md">
                        <div id="agents"
                            class="border-stroke col-span-12 xl:col-span-4 bg-white shadow-default py-6 p-4 border rounded-md">
                            <!-- Agent list -->
                            <div v-for="(agent, index) in allAgents" :key="index"
                                class="border-gray-800 dark:border-gray-600 mb-4 pb-4 border-b cursor-pointer">
                                <div @click="handleClick(agent.slug, encodeToBase64($page.props.auth.user.id))"
                                    class="flex items-center space-x-2">
                                    <div>
                                        <img :src="baseUrl + '/robot.png'" alt="" class="order-1 rounded-full w-8 h-10">
                                    </div>
                                    <div>
                                        <p class="font-semibold text-lg">{{ agent.name }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div
                        class="border-stroke dark:border-strokedark col-span-12 xl:col-span-8 bg-white dark:bg-boxdark shadow-default px-5 sm:px-7.5 pt-6 pb-2.5 xl:pb-1 border rounded-md w-full d-blo">

                        <div class="flex items-center">
                            <div class="mr-4.5 rounded-full w-full max-w-13 h-13 overflow-hidden">
                                <img :src="baseUrl + '/robot.png'" alt="User"
                                    class="w-full h-full object-center object-cover" />
                            </div>
                            <div>
                                <h5 class="font-medium text-slate-800 dark:text-white"><span class="source_name">{{
                                    selectedSource }}</span></h5>
                                <p class="font-medium text-sm">start to chatbot</p>
                            </div>
                        </div>

                        <div v-if="selectedSource != 'Chatbot'"
                            class="flex flex-col flex-1 justify-between p-2 sm:p-6 h-screen">
                            <div id="messages"
                                class="flex flex-col space-y-4 scrollbar-thumb-blue p-3 scrollbar-thumb-rounded scrollbar-w-2 overflow-y-auto scrollbar-track-blue-lighter scrolling-touch">
                                <!-- Message display -->
                                <div v-for="(message, index) in messages" :key="index"
                                    :class="message.sender + '_message'">
                                    <div class="flex"
                                        :class="message.sender === 'user' ? 'items-end justify-end' : 'items-start justify-left'">
                                        <template v-if="message.sender === 'user'">
                                            <div
                                                class="flex flex-col items-end space-y-2 bg-cfp-500 mx-2 px-4 py-3 rounded-xl max-w-lg text-md text-white leading-tight">
                                                <div>
                                                    <span class="">{{ message.text }}</span>
                                                </div>
                                            </div>

                                        </template>
                                        <template v-else>
                                            <div
                                                class="flex flex-col items-start space-y-2 bg-green-500 mx-2 px-4 py-3 rounded-xl max-w-lg text-md text-white leading-tight">
                                                <div>
                                                    <span class="">{{ message.text }}</span>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                                <!-- Typing indicator -->
                                <div class="hidden botTyping">
                                    <div class="flex items-end">
                                        <div
                                            class="flex flex-col items-start space-y-2 order-2 mx-2 text-md leading-tight">
                                            <div><img :src="baseUrl + '/microsoft-microsoft365.gif'" alt="..."
                                                    class="ml-6 pb-5 w-16"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Input area -->
                            <div class="border-gray-200 mb-2 sm:mb-0 px-4 pt-4 border-t-2">
                                <div class="relative flex">
                                    <input type="hidden" v-model="status_fallback"
                                        class="w-full text-md focus:outline-none focus:placeholder-gray-400 border-2 border-gray-200 focus:border-cfp-500 bg-gray-100 py-2 pr-16 pl-5 rounded-full text-gray-600 placeholder-gray-600" />
                                    <input ref="textInput" v-model="inputText" @keyup.enter="sendMessage" type="text"
                                        placeholder="Say something..." autocomplete="off" autofocus="true"
                                        id="inputsource" :disabled="!showInput"
                                        class="w-full text-md focus:outline-none focus:placeholder-gray-400 border-2 border-gray-200 focus:border-cfp-500 bg-gray-100 py-2 pr-16 pl-5 rounded-full text-gray-600 placeholder-gray-600" />
                                    <input type="hidden" name="type" :value="selectedSlug">
                                    <div class="right-2 absolute inset-y-1 sm:flex items-center">
                                        <button @click="sendMessage" type="button"
                                            class="inline-flex justify-center items-center bg-cfp-500 hover:bg-cfp-600 rounded-full w-8 h-8 text-white transition duration-200 ease-in-out focus:outline-none"><i
                                                class="fa-arrow-right text-xl leading-none fa"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>






    </AuthenticatedLayout>
</template>
