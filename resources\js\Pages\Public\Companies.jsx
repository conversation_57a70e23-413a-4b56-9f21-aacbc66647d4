import React, { useState, useEffect } from 'react';
import { Link } from '@inertiajs/react';
import PublicLayout from '@/Components/PublicLayout';

export default function Companies() {
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [companies, setCompanies] = useState([]);
    const [filteredCompanies, setFilteredCompanies] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const companiesPerPage = 10;

    // Mock company data - in real app this would come from API
    const mockCompanies = [
        {
            id: 1,
            name: "ICICI Bank",
            description: "Leading private sector bank providing comprehensive financial services and digital banking solutions",
            established: "Est. 1994",
            website: "https://www.icicibank.com",
            logo: "https://images.unsplash.com/photo-**********-b33ff0c44a43?w=80&h=80&fit=crop&crop=center",
            category: "Banking",
            employees: "100,000+",
            location: "Mumbai, India"
        },
        {
            id: 2,
            name: "HDFC Bank",
            description: "India's largest private sector bank by assets, offering innovative banking solutions",
            established: "Est. 1994",
            website: "https://www.hdfcbank.com",
            logo: "https://images.unsplash.com/photo-*************-f4d9a9f9297f?w=80&h=80&fit=crop&crop=center",
            category: "Banking",
            employees: "120,000+",
            location: "Mumbai, India"
        },
        {
            id: 3,
            name: "TechCorp Solutions",
            description: "Innovative technology solutions provider specializing in AI and machine learning",
            established: "Est. 2010",
            website: "https://www.techcorp.com",
            logo: "https://images.unsplash.com/photo-*************-4e9042af2176?w=80&h=80&fit=crop&crop=center",
            category: "Technology",
            employees: "5,000+",
            location: "Bangalore, India"
        },
        {
            id: 4,
            name: "Global Consulting",
            description: "Strategic business consulting firm helping companies transform and grow",
            established: "Est. 2005",
            website: "https://www.globalconsulting.com",
            logo: "https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=80&h=80&fit=crop&crop=center",
            category: "Consulting",
            employees: "2,500+",
            location: "New Delhi, India"
        },
        {
            id: 5,
            name: "MediCare Plus",
            description: "Comprehensive healthcare services with state-of-the-art medical facilities",
            established: "Est. 2008",
            website: "https://www.medicareplus.com",
            logo: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=80&h=80&fit=crop&crop=center",
            category: "Healthcare",
            employees: "15,000+",
            location: "Chennai, India"
        },
        {
            id: 6,
            name: "Sajal Organization",
            description: "Innovative business solutions and strategic consulting for enterprise growth",
            established: "Est. 2018",
            website: "https://www.sajal.com",
            logo: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=80&h=80&fit=crop&crop=center",
            category: "Business",
            employees: "1,500+",
            location: "Kolkata, India"
        },
        {
            id: 7,
            name: "DigiChat Solutions",
            description: "Next-generation B2B communication platform with AI-powered features",
            established: "Est. 2021",
            website: "https://www.digichat.com",
            logo: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=80&h=80&fit=crop&crop=center",
            category: "Technology",
            employees: "300+",
            location: "Gurgaon, India"
        },
        {
            id: 8,
            name: "Ivato Bruco Consulting",
            description: "Global consulting services specializing in digital transformation and strategy",
            established: "Est. 2012",
            website: "https://www.ivatobruco.com",
            logo: "https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=80&h=80&fit=crop&crop=center",
            category: "Consulting",
            employees: "2,000+",
            location: "London, UK"
        },
        {
            id: 9,
            name: "ChatHi Enterprise",
            description: "Advanced enterprise communication solutions with cutting-edge technology",
            established: "Est. 2023",
            website: "https://www.chathi2.com",
            logo: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=80&h=80&fit=crop&crop=center",
            category: "Technology",
            employees: "750+",
            location: "Seattle, USA"
        },
        {
            id: 10,
            name: "Luxury Hospitality Group",
            description: "Premium hospitality services with world-class hotels and exceptional experiences",
            established: "Est. 2008",
            website: "https://www.luxuryhospitality.com",
            logo: "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=80&h=80&fit=crop&crop=center",
            category: "Hospitality",
            employees: "25,000+",
            location: "Dubai, UAE"
        }
    ];

    useEffect(() => {
        // Simulate API loading
        setTimeout(() => {
            setCompanies(mockCompanies);
            setFilteredCompanies(mockCompanies);
            setIsLoading(false);
        }, 1000);
    }, []);

    useEffect(() => {
        const filtered = companies.filter(company =>
            company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            company.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
            company.category.toLowerCase().includes(searchTerm.toLowerCase())
        );
        setFilteredCompanies(filtered);
        setCurrentPage(1);
    }, [searchTerm, companies]);

    const totalPages = Math.ceil(filteredCompanies.length / companiesPerPage);
    const startIndex = (currentPage - 1) * companiesPerPage;
    const currentCompanies = filteredCompanies.slice(startIndex, startIndex + companiesPerPage);

    const handlePageChange = (page) => {
        setCurrentPage(page);
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    return (
        <PublicLayout currentPage="companies">
            {/* Header Section */}

            {/* Header Section */}
            <div className="bg-white px-6 py-12">
                <div className="max-w-7xl mx-auto text-center">
                    <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                        Discover Amazing Companies
                    </h1>
                    <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                        Explore our curated list of innovative companies across various industries
                    </p>
                    
                    {/* Search Bar */}
                    <div className="max-w-2xl mx-auto relative">
                        <div className="relative">
                            <svg className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                            <input
                                type="text"
                                placeholder="Search companies by name, description, or category..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-cf-primary-500 focus:border-transparent text-lg"
                            />
                        </div>
                    </div>
                </div>
            </div>

            {/* Companies Grid */}
            <div className="px-6 py-8">
                <div className="max-w-7xl mx-auto">
                    {isLoading ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {[...Array(6)].map((_, index) => (
                                <div key={index} className="bg-white rounded-xl p-6 shadow-sm animate-pulse">
                                    <div className="flex items-start gap-4">
                                        <div className="w-20 h-20 bg-gray-200 rounded-lg"></div>
                                        <div className="flex-1">
                                            <div className="h-6 bg-gray-200 rounded mb-2"></div>
                                            <div className="h-4 bg-gray-200 rounded mb-2"></div>
                                            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <>
                            <div className="mb-6 flex items-center justify-between">
                                <p className="text-gray-600">
                                    Showing {startIndex + 1}-{Math.min(startIndex + companiesPerPage, filteredCompanies.length)} of {filteredCompanies.length} companies
                                </p>
                                {searchTerm && (
                                    <button
                                        onClick={() => setSearchTerm('')}
                                        className="text-cf-primary-600 hover:text-cf-primary-700 font-medium"
                                    >
                                        Clear search
                                    </button>
                                )}
                            </div>

                            {filteredCompanies.length === 0 ? (
                                <div className="text-center py-12">
                                    <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                    </svg>
                                    <h3 className="text-xl font-medium text-gray-900 mb-2">No companies found</h3>
                                    <p className="text-gray-600">Try adjusting your search terms</p>
                                </div>
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    {currentCompanies.map((company, index) => (
                                        <div 
                                            key={company.id} 
                                            className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100 hover:border-cf-primary-200 group"
                                        >
                                            <div className="flex items-start gap-4">
                                                <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 bg-gray-100">
                                                    <img 
                                                        src={company.logo} 
                                                        alt={company.name}
                                                        className="w-full h-full object-cover"
                                                    />
                                                </div>
                                                
                                                <div className="flex-1 min-w-0">
                                                    <div className="flex items-start justify-between mb-2">
                                                        <h3 className="text-xl font-bold text-gray-900 group-hover:text-cf-primary-600 transition-colors">
                                                            {company.name}
                                                        </h3>
                                                        <span className="text-sm text-gray-500 whitespace-nowrap ml-2">
                                                            {company.established}
                                                        </span>
                                                    </div>
                                                    
                                                    <p className="text-gray-600 mb-3 line-clamp-2">
                                                        {company.description}
                                                    </p>

                                                    {/* Company Details */}
                                                    <div className="flex flex-wrap gap-2 mb-3 text-sm text-gray-500">
                                                        {company.employees && (
                                                            <div className="flex items-center gap-1">
                                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                                                </svg>
                                                                <span>{company.employees}</span>
                                                            </div>
                                                        )}
                                                        {company.location && (
                                                            <div className="flex items-center gap-1">
                                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                                                </svg>
                                                                <span>{company.location}</span>
                                                            </div>
                                                        )}
                                                    </div>

                                                    <div className="flex items-center justify-between">
                                                        <span className="inline-block bg-cf-primary-100 text-cf-primary-700 px-3 py-1 rounded-full text-sm font-medium">
                                                            {company.category}
                                                        </span>
                                                        
                                                        <div className="flex items-center gap-3">
                                                            <a 
                                                                href={company.website} 
                                                                target="_blank" 
                                                                rel="noopener noreferrer"
                                                                className="text-gray-400 hover:text-cf-primary-600 transition-colors"
                                                            >
                                                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                                                </svg>
                                                            </a>
                                                            
                                                            <button className="bg-cf-primary-600 hover:bg-cf-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                                                More Info
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </>
                    )}
                </div>
            </div>

            {/* Pagination */}
            {!isLoading && filteredCompanies.length > companiesPerPage && (
                <div className="px-6 pb-12">
                    <div className="max-w-7xl mx-auto">
                        <div className="flex items-center justify-center gap-2">
                            <button
                                onClick={() => handlePageChange(currentPage - 1)}
                                disabled={currentPage === 1}
                                className="px-4 py-2 text-gray-600 hover:text-cf-primary-600 disabled:text-gray-300 disabled:cursor-not-allowed transition-colors"
                            >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                </svg>
                            </button>

                            {[...Array(totalPages)].map((_, index) => {
                                const page = index + 1;
                                const isCurrentPage = page === currentPage;
                                const isNearCurrentPage = Math.abs(page - currentPage) <= 2;
                                const isFirstOrLast = page === 1 || page === totalPages;

                                if (!isNearCurrentPage && !isFirstOrLast) {
                                    if (page === currentPage - 3 || page === currentPage + 3) {
                                        return <span key={page} className="px-2 text-gray-400">...</span>;
                                    }
                                    return null;
                                }

                                return (
                                    <button
                                        key={page}
                                        onClick={() => handlePageChange(page)}
                                        className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                                            isCurrentPage
                                                ? 'bg-cf-primary-600 text-white'
                                                : 'text-gray-600 hover:text-cf-primary-600 hover:bg-cf-primary-50'
                                        }`}
                                    >
                                        {page}
                                    </button>
                                );
                            })}

                            <button
                                onClick={() => handlePageChange(currentPage + 1)}
                                disabled={currentPage === totalPages}
                                className="px-4 py-2 text-gray-600 hover:text-cf-primary-600 disabled:text-gray-300 disabled:cursor-not-allowed transition-colors"
                            >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                            </button>
                        </div>

                        <div className="text-center mt-4 text-gray-600">
                            Page {currentPage} of {totalPages}
                        </div>
                    </div>
                </div>
            )}

        </PublicLayout>
    );
}
