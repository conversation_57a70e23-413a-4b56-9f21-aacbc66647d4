import React, { useState } from 'react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import Button from '@/Components/Button';
import { Card, CardContent, PageHeader, StatusBadge } from '@/Components/UI';

const chats = [
    {
        id: 1,
        name: 'Qa',
        avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
        lastMessage: 'hi',
        lastTime: '2025-07-11 19:13',
        active: true,
    },
    {
        id: 2,
        name: 'Chatfil User2',
        avatar: 'https://randomuser.me/api/portraits/men/33.jpg',
        lastMessage: 'hey',
        lastTime: '2025-05-31 19:23',
        active: false,
    },
    {
        id: 3,
        name: 'Qa',
        avatar: 'https://randomuser.me/api/portraits/men/34.jpg',
        lastMessage: 'hey',
        lastTime: '2025-05-31 19:22',
        active: false,
    },
];

const messages = [
    { id: 1, text: 'how can i help you?', time: '2025-07-11 19:23', fromMe: true },
    { id: 2, text: 'hey', time: '2025-07-11 19:30', fromMe: false },
    { id: 3, text: 'hello', time: '2025-07-11 19:33', fromMe: true },
    { id: 4, text: 'hey', time: '2025-07-11 19:48', fromMe: false },
    { id: 5, text: 'hey', time: '2025-07-11 19:53', fromMe: true },
    { id: 6, text: 'ba bla', time: '2025-07-11 19:59', fromMe: true },
    { id: 7, text: 'hi', time: '2025-07-11 19:13', fromMe: false },
];

export default function Chats() {
    const [selectedChat, setSelectedChat] = useState(chats[0]);
    const [messageText, setMessageText] = useState('');
    const [activeFilter, setActiveFilter] = useState('all');

    const filteredChats = chats.filter(chat => {
        if (activeFilter === 'active') return chat.active;
        if (activeFilter === 'inactive') return !chat.active;
        return true;
    });

    return (
        <DashboardLayout title="Chats">
            <title>Chats</title>

            <div className="space-y-6">
                <PageHeader
                    title="Chat Management"
                    subtitle="Communicate with users and manage conversations"
                />

                <div className="h-[calc(100vh-12rem)] flex gap-6">
                    {/* Chat List Sidebar */}
                    <Card className="w-80 flex flex-col p-0 overflow-hidden">
                        <div className="p-4 border-b border-gray-200">
                            <h3 className="font-semibold text-gray-900 mb-3">Conversations</h3>
                            <input
                                type="text"
                                placeholder="Search conversations..."
                                className="w-full px-3 py-2 rounded-lg border border-gray-300 text-sm focus:outline-none focus:ring-2 focus:ring-cf-primary-300 bg-white"
                            />
                            <div className="flex gap-2 mt-3">
                                <button
                                    onClick={() => setActiveFilter('all')}
                                    className={`px-3 py-1 text-xs rounded-full transition ${activeFilter === 'all' ? 'bg-cf-primary text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
                                >
                                    All
                                </button>
                                <button
                                    onClick={() => setActiveFilter('active')}
                                    className={`px-3 py-1 text-xs rounded-full transition ${activeFilter === 'active' ? 'bg-green-500 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
                                >
                                    Active
                                </button>
                                <button
                                    onClick={() => setActiveFilter('inactive')}
                                    className={`px-3 py-1 text-xs rounded-full transition ${activeFilter === 'inactive' ? 'bg-gray-500 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
                                >
                                    Inactive
                                </button>
                            </div>
                        </div>
                        <div className="flex-1 overflow-y-auto">
                            {filteredChats.map(chat => (
                                <div
                                    key={chat.id}
                                    onClick={() => setSelectedChat(chat)}
                                    className={`flex items-center gap-3 px-4 py-3 cursor-pointer hover:bg-gray-50 border-b border-gray-100 transition ${selectedChat?.id === chat.id ? 'bg-cf-primary-50 border-cf-primary-200' : ''}`}
                                >
                                    <div className="relative">
                                        <img src={chat.avatar} alt={chat.name} className="w-10 h-10 rounded-full object-cover" />
                                        {chat.active && (
                                            <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                                        )}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <div className="flex items-center justify-between">
                                            <h4 className="font-medium text-gray-900 text-sm truncate">{chat.name}</h4>
                                            <StatusBadge variant={chat.active ? 'success' : 'secondary'} size="xs">
                                                {chat.active ? 'Online' : 'Offline'}
                                            </StatusBadge>
                                        </div>
                                        <p className="text-xs text-gray-500 truncate">{chat.lastMessage}</p>
                                        <p className="text-xs text-gray-400">{chat.lastTime}</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </Card>
                    {/* Chat Main Area */}
                    <Card className="flex-1 flex flex-col p-0 overflow-hidden">
                        {selectedChat ? (
                            <>
                                {/* Chat Header */}
                                <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4 bg-white">
                                    <div className="flex items-center gap-3">
                                        <div className="relative">
                                            <img src={selectedChat.avatar} alt={selectedChat.name} className="w-12 h-12 rounded-full object-cover" />
                                            {selectedChat.active && (
                                                <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                                            )}
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-gray-900">{selectedChat.name}</h3>
                                            <p className="text-sm text-gray-500">ID: #{selectedChat.id}</p>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <StatusBadge variant={selectedChat.active ? 'success' : 'secondary'}>
                                            {selectedChat.active ? 'Online' : 'Offline'}
                                        </StatusBadge>
                                        <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition">
                                            <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                {/* Messages */}
                                <div className="flex-1 overflow-y-auto p-6 space-y-4 bg-gray-50">
                                    {messages.map(msg => (
                                        <div key={msg.id} className={`flex ${msg.fromMe ? 'justify-end' : 'justify-start'}`}>
                                            <div className={`max-w-[70%] rounded-2xl px-4 py-3 text-sm shadow-sm ${
                                                msg.fromMe
                                                    ? 'bg-cf-primary text-white rounded-br-md'
                                                    : 'bg-white text-gray-700 rounded-bl-md border border-gray-200'
                                            }`}>
                                                <p>{msg.text}</p>
                                                <p className={`text-xs mt-1 ${msg.fromMe ? 'text-cf-primary-100' : 'text-gray-400'}`}>
                                                    {new Date(msg.time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                                </p>
                                            </div>
                                        </div>
                                    ))}
                                    <div className="flex justify-center">
                                        <StatusBadge variant="success" size="sm">
                                            Conversation is closed
                                        </StatusBadge>
                                    </div>
                                </div>

                                {/* Message Input */}
                                <div className="p-4 border-t border-gray-200 bg-white">
                                    <div className="flex items-center gap-3">
                                        <input
                                            type="text"
                                            value={messageText}
                                            onChange={(e) => setMessageText(e.target.value)}
                                            placeholder="Type your message..."
                                            className="flex-1 px-4 py-3 rounded-lg border border-gray-300 text-sm focus:outline-none focus:ring-2 focus:ring-cf-primary-300 bg-white"
                                        />
                                        <button className="bg-cf-primary p-3 rounded-lg text-white hover:bg-cf-primary-600 transition shadow-sm">
                                            <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </>
                        ) : (
                            <div className="flex-1 flex items-center justify-center text-gray-500">
                                <div className="text-center">
                                    <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" strokeWidth="1" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.77 9.77 0 01-4-.8L3 21l1.8-4A8.96 8.96 0 013 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                    </svg>
                                    <p className="text-lg font-medium">Select a conversation</p>
                                    <p className="text-sm">Choose a chat from the sidebar to start messaging</p>
                                </div>
                            </div>
                        )}
                    </Card>
                </div>
            </div>
        </DashboardLayout>
    );
}