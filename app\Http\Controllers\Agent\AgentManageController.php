<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password as FacadesPassword;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;
use Inertia\Inertia;

class AgentManageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {

        $title     = 'Manage Agent';
        $actionBtn = ['title' => 'Add Agent', 'route' => route('manage-agent.add')];

        $allAgents = User::where('users.profileType', 'agent')
            ->where('users.parent_id', Auth::id())
            ->orderBy('users.id', 'desc')
            ->paginate(env('PAGE_LIMIT'));

        return Inertia::render('Agent/List', ['allAgents' => $allAgents, 'title' => $title, 'actionBtn' => $actionBtn]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $title = 'Add Agent';

        return Inertia::render('Agent/Add', ['title' => $title]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $request->validate([
            'name'  => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'phone' => 'required|string|regex:/^\+?[0-9]{10,15}$/',
        ]);

        $randomPass = Str::random(10);

        $user                     = new User;
        $user->name               = $request->name;
        $user->email              = $request->email;
        $user->phone              = $request->phone ?? ''; // Optional phone field
        $user->password           = Hash::make($randomPass);
        $user->profileType        = 'agent';
        $user->verificationStatus = 'not_verified';
        $user->status             = 'active';
        $user->profilePicture     = ''; // No profile picture initially
        $user->parent_id          = Auth::id();
        $user->save();

        // $user = User::create([
        //     'name' => $request->name,
        //     'email' => $request->email,
        //     'password' => Hash::make($randomPass),
        //     'phone' => $request->phone,
        //     'profileType' => 'agent',
        //     'status' => 'inactive',
        //     'parent_id' => Auth::id()
        // ]);

        // Generate a password reset token
        $token = FacadesPassword::broker()->createToken($user);

        try {
            $sendMail = User::sendActiveAgentMail($token, $request->email);
        } catch (Exception $ex) {

        }

        return Redirect::route('manage-agent.add')->with([
            'status'  => true,
            'message' => 'Agent saved successfully',
            'class'   => 'flex items-center p-2 mb-4 text-green-700 rounded-md bg-green-100',
        ]);

    }

    public function ResetAgentPassword()
    {
        $searchField = [
            'email'    => $_REQUEST['emailAddress'],
            'identity' => $_REQUEST['identity'],
        ];

        $email = $_REQUEST['emailAddress'] ?? null;
        $token = $_REQUEST['identity']     ?? null;

        // Check if email and token exist in password_reset_tokens table
        $validToken = DB::table('password_reset_tokens')
            ->where('email', $email)
            ->first();

        // If token is invalid or expired, abort
        if (! $validToken) {
            return redirect()->route('user-login');
        }

        return Inertia::render('Front/views/ResetPassword', ['searchField' => $searchField]);
    }

    public function resetCustomPassword(Request $request)
    {

        // Validate the incoming request data
        $request->validate([
            'email'    => 'required|email',
            'token'    => 'required',
            'password' => 'required|min:6|confirmed',
        ]);

        // Attempt to reset the password
        $status = FacadesPassword::broker()->reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($user, $password) {
                // Set the new password for the user
                $user->forceFill([
                    'verificationStatus' => 'verified',
                    'status'             => 'active',
                    'password'           => bcrypt($password),
                ])->save();

                // Redirect the user or return a response based on the result
            }
        );

        if ($status == FacadesPassword::PASSWORD_RESET) {
            return redirect()->back()->with('success', 'Password has been reset successfully');
            echo $status;
            exit;
        }

        // If the password was not reset successfully, redirect back with errors
        return back()->withInput($request->only('password'))->withErrors(['password' => trans($status)]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {

        $agentDetail = User::where('users.profileType', 'agent')
            ->where('users.parent_id', Auth::id())
            ->where('id', $id)
            ->orderBy('users.id', 'desc')->first();
        $title = 'Edit Agent';

        return Inertia::render('Agent/Edit', ['agentDetail' => $agentDetail, 'title' => $title]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $user = User::findOrFail($id);

        // Validate the request data
        $request->validate([
            'name'  => 'required|string|max:255',
            'email' => [
                'required',
                'string',
                'lowercase',
                'email',
                'max:255',
                Rule::unique(User::class)->ignore($user->id),
            ],
            'phone' => 'required|string|regex:/^\+?[0-9]{10,15}$/',
        ]);

        $statusUpdateMail = 0;
        $oldMail          = '';
        if (trim($user->email) == trim($request->email)) {
        } else {
            $oldMail          = $user->email;
            $statusUpdateMail = 1;
        }

        // Update the user data
        $user->name  = $request->name;
        $user->email = $request->email;
        $user->phone = $request->phone;

        $user->save();

        if ($statusUpdateMail == 1) {

            $checkOldmail = DB::table('password_reset_tokens')->where('email', $oldMail)->first();
            if ($checkOldmail) {
                DB::table('password_reset_tokens')->where('email', $oldMail)->delete();
            }

            $user->status             = 'inactive';
            $user->verificationStatus = 'not_verified';
            $user->save();

            $token    = FacadesPassword::broker()->createToken($user);
            $sendMail = User::sendActiveAgentMail($token, $request->email);

        }

        return Redirect::route('manage-agent.edit', [$id])->with([
            'status'  => true,
            'message' => 'Agent updated successfully',
            'class'   => 'flex items-center p-2 mb-4 text-green-700 rounded-md bg-green-100',
        ]);

        // return redirect()->route('manage-agent.list')->with('success', 'Agent updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $userData = User::where('id', $id)->first();
        if ($userData) {
            $userData->delete();

            return response()->json(['success' => true]);
        }

        return response()->json(['success' => true]);
    }
}
