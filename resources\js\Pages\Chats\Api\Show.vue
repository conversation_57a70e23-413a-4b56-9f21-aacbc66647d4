<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import { usePage } from '@inertiajs/vue3';
import PageHeading from '@/Components/Global/PageHeading.vue';

const { api } = usePage().props;
const pageTitle = ref('API Details');

const requestParams = ref([]);
const responseParams = ref([]);

onMounted(() => {
    if (api.request_parameters) {
        requestParams.value = JSON.parse(api.request_parameters);
    }
    if (api.response_parameters) {
        responseParams.value = JSON.parse(api.response_parameters);
    }
});
</script>

<template>
    <Head :title="pageTitle" />

    <AuthenticatedLayout>
        <PageHeading :title="pageTitle"></PageHeading>

        <div class="flex">
            <div class="flex-1">
                <div class="p-6 border rounded-md">
                    <div class="space-y-4">
                        <div>
                            <h3 class="font-semibold">Name</h3>
                            <p>{{ api.name }}</p>
                        </div>
                        <div>
                            <h3 class="font-semibold">URL</h3>
                            <p>{{ api.url }}</p>
                        </div>
                        <div>
                            <h3 class="font-semibold">Method</h3>
                            <p>{{ api.method }}</p>
                        </div>
                        
                        <!-- Request Parameters Table -->
                        <div>
                            <h3 class="font-semibold mb-2">Request Parameters</h3>
                            <table class="min-w-full divide-y divide-gray-200" v-if="requestParams.length">
                                <thead>
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Label</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Parameter</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Data Type</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(param, index) in requestParams" :key="index">
                                        <td class="px-6 py-4">{{ param.label }}</td>
                                        <td class="px-6 py-4">{{ param.param }}</td>
                                        <td class="px-6 py-4">{{ param.datatype }}</td>
                                    </tr>
                                </tbody>
                            </table>
                            <p v-else class="text-gray-500">No request parameters defined</p>
                        </div>

                        <!-- Response Parameters Table -->
                        <div>
                            <h3 class="font-semibold mb-2">Response Parameters</h3>
                            <table class="min-w-full divide-y divide-gray-200" v-if="responseParams.length">
                                <thead>
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Label</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Parameter</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Data Type</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(param, index) in responseParams" :key="index">
                                        <td class="px-6 py-4">{{ param.label }}</td>
                                        <td class="px-6 py-4">{{ param.param }}</td>
                                        <td class="px-6 py-4">{{ param.datatype }}</td>
                                    </tr>
                                </tbody>
                            </table>
                            <p v-else class="text-gray-500">No response parameters defined</p>
                        </div>

                        <div>
                            <h3 class="font-semibold">Description</h3>
                            <p>{{ api.description }}</p>
                        </div>
                        
                        <div class="mt-6">
                            <ResponsiveNavLink :href="route('vendor-apis.index')" class="dk-cancle-btn">
                                Back to List
                            </ResponsiveNavLink>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
