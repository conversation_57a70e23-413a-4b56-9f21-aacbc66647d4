import React, { useState, useEffect, useRef } from 'react';
import { FiSearch, FiX } from 'react-icons/fi';

export default function SearchBar({
    value,
    onChange,
    onClear,
    placeholder = "Search complaints...",
    className = ""
}) {
    const [localValue, setLocalValue] = useState(value || '');
    const [showDropdown, setShowDropdown] = useState(false);
    const [isFocused, setIsFocused] = useState(false);
    const dropdownRef = useRef(null);
    const inputRef = useRef(null);

    useEffect(() => {
        setLocalValue(value || '');
    }, [value]);

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setShowDropdown(false);
                setIsFocused(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Debounced search
    useEffect(() => {
        if (localValue && localValue.length > 0) {
            const timeoutId = setTimeout(() => {
                onChange(localValue);
            }, 300);

            return () => clearTimeout(timeoutId);
        } else if (localValue === '') {
            onChange('');
        }
    }, [localValue, onChange]);

    const handleSubmit = (e) => {
        e.preventDefault();
        onChange(localValue);
        setShowDropdown(false);
        inputRef.current?.blur();
    };

    const handleClear = () => {
        setLocalValue('');
        onClear();
        setShowDropdown(false);
        inputRef.current?.focus();
    };

    const handleInputChange = (e) => {
        const newValue = e.target.value;
        setLocalValue(newValue);
        setShowDropdown(newValue.length > 0 && isFocused);
    };

    const handleFocus = () => {
        setIsFocused(true);
        if (localValue.length > 0) {
            setShowDropdown(true);
        }
    };

    const handleBlur = () => {
        // Delay hiding dropdown to allow for clicks
        setTimeout(() => {
            setIsFocused(false);
            setShowDropdown(false);
        }, 150);
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            onChange(localValue);
            setShowDropdown(false);
            inputRef.current?.blur();
        } else if (e.key === 'Escape') {
            setShowDropdown(false);
            inputRef.current?.blur();
        }
    };

    return (
        <div className={`relative ${className}`} ref={dropdownRef}>
            <form onSubmit={handleSubmit} className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FiSearch className="h-5 w-5 text-gray-400" />
                </div>
                <input
                    ref={inputRef}
                    type="text"
                    value={localValue}
                    onChange={handleInputChange}
                    onFocus={handleFocus}
                    onBlur={handleBlur}
                    onKeyDown={handleKeyDown}
                    placeholder={placeholder}
                    className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-cf-primary focus:border-transparent"
                    autoComplete="off"
                />
                {localValue && (
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <button
                            type="button"
                            onClick={handleClear}
                            className="text-gray-400 hover:text-gray-600 focus:outline-none transition-colors"
                            title="Clear search"
                        >
                            <FiX className="h-5 w-5" />
                        </button>
                    </div>
                )}
            </form>

            {/* Search suggestions dropdown */}
            {showDropdown && localValue && (
                <div className="absolute z-50 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg">
                    <div className="p-3 text-sm text-gray-600">
                        <p className="font-medium mb-2 text-gray-800">Search in:</p>
                        <ul className="text-xs space-y-1 text-gray-600">
                            <li className="flex items-center">
                                <span className="w-1 h-1 bg-gray-400 rounded-full mr-2"></span>
                                Complaint titles
                            </li>
                            <li className="flex items-center">
                                <span className="w-1 h-1 bg-gray-400 rounded-full mr-2"></span>
                                Complaint IDs (e.g., CP2025123)
                            </li>
                            <li className="flex items-center">
                                <span className="w-1 h-1 bg-gray-400 rounded-full mr-2"></span>
                                Complaint descriptions
                            </li>
                        </ul>
                        <div className="mt-3 pt-2 border-t border-gray-200">
                            <p className="text-xs text-gray-500 flex items-center">
                                <kbd className="px-1.5 py-0.5 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded mr-1">Enter</kbd>
                                to search
                                <span className="mx-2">•</span>
                                <kbd className="px-1.5 py-0.5 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded mr-1">Esc</kbd>
                                to close
                            </p>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
