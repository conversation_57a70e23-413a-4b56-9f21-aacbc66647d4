<script setup>
import { ref } from 'vue'

import UserOne from '@/assets/images/user/user-01.png'
import UserTwo from '@/assets/images/user/user-02.png'
import UserThree from '@/assets/images/user/user-03.png'
import UserFour from '@/assets/images/user/user-04.png'
import UserFive from '@/assets/images/user/user-05.png'

const chatData = ref([
  {
    avatar: UserOne,
    name: '<PERSON><PERSON> <PERSON><PERSON>',
    text: 'How are you?',
    time: 12,
    textCount: 3,
    color: '#10B981'
  },
  {
    avatar: UserTwo,
    name: '<PERSON>',
    text: 'Waiting for you!',
    time: 12,
    textCount: 0,
    color: '#DC3545'
  },
  {
    avatar: UserFour,
    name: '<PERSON><PERSON>',
    text: "What's up?",
    time: 32,
    textCount: 0,
    color: '#10B981'
  },
  {
    avatar: UserFive,
    name: '<PERSON>',
    text: 'Great',
    time: 32,
    textCount: 2,
    color: '#FFBA00'
  },
  {
    avatar: User<PERSON>ne,
    name: '<PERSON><PERSON>',
    text: 'How are you?',
    time: 32,
    textCount: 0,
    color: '#10B981'
  },
  {
    avatar: UserThree,
    name: 'Jhon Doe',
    text: 'How are you?',
    time: 32,
    textCount: 3,
    color: '#FFBA00'
  }
])
</script>

<template>
  <div class="border-stroke col-span-12 xl:col-span-4 bg-white shadow-default py-6 border rounded-md">
    <h4 class="mb-6 px-7.5 font-semibold text-slate-800 text-xl">Chats</h4>

    <div>
      <template v-for="(chat, index) in chatData" :key="index">
        <RouterLink to="/" class="flex items-center gap-5 hover:bg-gray-3 px-7.5 py-3">
          <div class="relative rounded-full w-14 h-14">
            <img :src="chat.avatar" alt="User" />
            <span class="right-0 bottom-0 absolute border-2 border-white rounded-full w-3.5 h-3.5"
              :style="{ backgroundColor: chat.color }"></span>
          </div>

          <div class="flex flex-1 justify-between items-center">
            <div>
              <h5 class="font-medium text-slate-800">{{ chat.name }}</h5>
              <p>
                <span class="text-slate-800 text-sm">{{ chat.text }}</span>
                <span class="text-xs"> . {{ chat.time }} min</span>
              </p>
            </div>
            <div v-if="chat.textCount !== 0" class="flex justify-center items-center bg-cfp-500 rounded-full w-6 h-6">
              <span class="font-medium text-sm text-white"> {{ chat.textCount }}</span>
            </div>
          </div>
        </RouterLink>
      </template>
    </div>
  </div>
</template>
