<?php

namespace App\Http\Controllers;

use App\Models\Company;
use App\Models\Complaint;
use App\Models\ComplaintMessages;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class Complaincontroller extends Controller
{
    /**
     * Display a listing of the resource.
     */
    // public function index()
    // {
    //     if (Auth::user()->profileType == 'admin') {
    //         $Complaints = Complaint::join('company', 'company.id', '=', 'complain.companyId')
    //             ->join('users', 'users.id', '=', 'company.userId')
    //             ->select('users.profilePicture', 'company.companyName', 'company.city', 'company.state', 'complain.*')
    //             ->orderBy('complain.id', 'desc')->get();
    //     } elseif (Auth::user()->profileType == 'company') {
    //         $companyIds = Company::where('userId', Auth::id())->pluck('id')->toArray();
    //         $Complaints = Complaint::join('company', 'company.id', '=', 'complain.companyId')
    //             ->whereIn('company.id', $companyIds)
    //             ->join('users', 'users.id', '=', 'company.userId')
    //             ->select('users.profilePicture', 'company.companyName', 'company.city', 'company.state', 'complain.*')
    //             ->orderBy('complain.id', 'desc')->get();
    //     } elseif (Auth::user()->profileType == 'agent') {
    //         $companyIds = Company::where('userId', Auth::user()->parent_id)->pluck('id')->toArray();
    //         $Complaints = Complaint::join('company', 'company.id', '=', 'complain.companyId')
    //             ->whereIn('company.id', $companyIds)
    //             ->join('users', 'users.id', '=', 'company.userId')
    //             ->select('users.profilePicture', 'company.companyName', 'company.city', 'company.state', 'complain.*')
    //             ->orderBy('complain.id', 'desc')->get();
    //     } else {
    //         $Complaints = Complaint::join('company', 'company.id', '=', 'complain.companyId')
    //             ->join('users', 'users.id', '=', 'company.userId')
    //             ->where('assignedToUserId', Auth::id())
    //             ->select('users.profilePicture', 'company.companyName', 'company.city', 'company.state', 'complain.*')
    //             ->orderBy('complain.id', 'desc')->get();
    //     }
        
    //     return Inertia::render('Complain/Complaints', [
    //         'Complaints' => $Complaints
    //     ]);
    // }

    public function index(Request $request)
    {
        $query = Complaint::join('company', 'company.id', '=', 'complain.companyId')
            ->join('users', 'users.id', '=', 'company.userId')
            ->select('users.profilePicture', 'company.companyName', 'company.city', 'company.state', 'complain.*');

        // Base query for counts
        $baseQuery = clone $query;

        // Apply user type filters for counts
        if (Auth::user()->profileType == 'company') {
            $companyIds = Company::where('userId', Auth::id())->pluck('id')->toArray();
            $baseQuery->whereIn('company.id', $companyIds);
        } elseif (Auth::user()->profileType == 'agent') {
            $companyIds = Company::where('userId', Auth::user()->parent_id)->pluck('id')->toArray();
            $baseQuery->whereIn('company.id', $companyIds);
        } elseif (Auth::user()->profileType != 'admin') {
            $baseQuery->where('assignedToUserId', Auth::id());
        }

        // Get total counts
        $totalOpen = (clone $baseQuery)->where('complain.complainStatus', 'open')->count();
        $totalResolved = (clone $baseQuery)->where('complain.complainStatus', 'close')->count();

        // Apply search filter
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('complain.complainTitle', 'like', "%{$search}%")
                  ->orWhere('complain.cmp_id', 'like', "%{$search}%")
                  ->orWhere('complain.complainDetail', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if ($request->has('status') && !empty($request->status)) {
            $query->where('complain.complainStatus', $request->status);
        } else {
            // Default to 'open' status if no status is provided
            $query->where('complain.complainStatus', 'open');
        }

        // Apply user type filters
        if (Auth::user()->profileType == 'admin') {
            // No additional where conditions for admin
        } elseif (Auth::user()->profileType == 'company') {
            $companyIds = Company::where('userId', Auth::id())->pluck('id')->toArray();
            $query->whereIn('company.id', $companyIds);
        } elseif (Auth::user()->profileType == 'agent') {
            $companyIds = Company::where('userId', Auth::user()->parent_id)->pluck('id')->toArray();
            $query->whereIn('company.id', $companyIds);
        } else {
            $query->where('assignedToUserId', Auth::id());
        }

        $Complaints = $query->orderBy('complain.id', 'desc')->paginate(env('PAGE_LIMIT'));

        return Inertia::render('Complain/Complaints', [
            'Complaints' => $Complaints,
            'totalOpen' => $totalOpen,
            'totalResolved' => $totalResolved
        ]);
    }

    public function storeguestComplain(Request $request)
    {

        // Manual validation using Validator
        $validator = Validator::make($request->all(), [
            'name'             => 'required|string|max:255',
            'email'            => 'required|email',
            'complaint_detail' => 'required|string',
            'company_id'       => 'required|integer',
        ]);

        // Check if validation fails
        if ($validator->fails()) {
            // Return errors with 422 Unprocessable Entity status
            return response()->json([
                'success' => false,
                'errors'  => $validator->errors(),
            ], 422);
        }

        $userExist = User::where('email', $request->email);
        if ($userExist) {
            $userid = $userExist->id;
        } else {

            $password                 = time().'_'.rand(1111, 9999);
            $user                     = new User;
            $user->name               = $request->first_name;
            $user->email              = $request->email;
            $user->phone              = '';
            $user->password           = Hash::make($password);
            $user->profileType        = 'user';
            $user->verificationStatus = 'not_verified';
            $user->status             = 'active';
            $user->profilePicture     = '';
            $user->otp                = '';
            $user->save();
            $userid = $user->id;
        }

        $company_id       = $request->company_id;
        $priority         = 2;
        $complaint_title  = $request->complaint_title;
        $complaint_detail = $request->complaint_detail;

        // Create new Complaint instance
        $saveData                   = new Complaint;
        $saveData->companyId        = $company_id;
        $saveData->assignedToUserId = $userid;
        $saveData->priority         = $priority;
        $saveData->complainTitle    = $complaint_title;
        $saveData->complainDetail   = $complaint_detail;
        $saveData->save();

        $cmpID            = $saveData->id;
        $curentYear       = date('Y');
        $finalString      = 'CP'.$curentYear.$cmpID;
        $saveData->cmp_id = $finalString;
        $saveData->save();

        $allDetailData = Complaint::find($saveData->id);

        return response()->json(['status' => true, 'data' => $allDetailData, 'message' => 'Thank you! Your ticket has been successfully submitted. Our service agent will contact you shortly.']);

    }

    public function addComplain()
    {
        $companyList = Company::join('users', 'users.id', '=', 'company.userId')->where('userId', '<>', Auth::id())->select('company.*', 'users.email')->get();

        $title = 'Add Complaint';

        return Inertia::render('Complain/AddComplain', ['companyList' => $companyList, 'title' => $title]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $user        = Auth::user();
        $profileType = $user->profileType;

        if ($profileType == 'user') {

        } else {

            if ((trim($request->selectedUserId) == '') && (trim($request->name) == '') && (trim($request->email) == '') && (trim($request->phone) == '')) {

                return Redirect::route('addcomplain')->with([
                    'status'  => false,
                    'message' => 'Please select or add user',
                    'class'   => 'text-sm text-red-700',
                ]);

            }

        }

        $customMessages = [
            'required'       => 'The :attribute field is required.',
            'email'          => 'The :attribute must be a valid email address.',
            'unique'         => 'The :attribute has already been taken.',
            'in'             => 'The :attribute field must be 0, 1, or 2.',
            'exists'         => 'The selected :attribute is invalid.',
            'phone.regex'    => 'The :attribute must be a valid phone number.',
            'numeric'        => 'The :attribute must be a valid phone number without alphabetic characters.',
            'digits_between' => 'The :attribute must be between :min and :max digits.',
        ];

        // Define common validation rules
        $commonRules = [
            'complain_title'       => 'required',
            'complain_description' => 'required',
            'priority'             => 'required|in:0,1,2',
        ];

        if ($profileType == 'admin' || $profileType == 'user') {
            $commonRules['company'] = 'required';
        }

        // Define specific validation rules based on conditions
        $specificRules = [];

        if ($profileType == 'admin' || $profileType == 'company' || $profileType == 'agent') {

            if (empty(trim($request->selectedUserId))) {

                $specificRules = [
                    'name'  => 'required|string|max:255',
                    'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
                    'phone' => ['required', 'numeric', 'digits_between:8,15'],
                ];

            }

        }

        $validatedData = $request->validate(array_merge($commonRules, $specificRules), $customMessages);

        if ($profileType == 'user') {

            $userId = Auth::user()->id;

        } else {

            if (empty(trim($request->selectedUserId))) {

                $user = User::create([
                    'name'        => $request->name,
                    'phone'       => $request->phone,
                    'profileType' => 'user',
                    'email'       => $request->email,
                    'password'    => Hash::make(rand(11111, 99999)),
                ]);

                $userId = $user->id;

            } else {

                $userId = $request->selectedUserId;

                if (User::find($userId)) {
                } else {
                    return Redirect::route('addcomplain')->with([
                        'status'  => true,
                        'message' => 'Invalid User selection.',
                        'class'   => 'text-sm text-green-400 ',
                    ]);
                }

            }

        }

        if ($profileType == 'admin' || $profileType == 'user') {

            $companyQry = Company::where('id', $request->company)->first();

        } elseif ($profileType == 'company') {

            $companyUseriD = Auth::id();
            $companyQry    = Company::where('userId', $companyUseriD)->first();

        } elseif ($profileType == 'agent') {

            $companyUseriD = Auth::user()->parent_id;
            $companyQry    = Company::where('userId', $companyUseriD)->first();

        }

        if ($companyQry) {

            $saveData                   = new Complaint;
            $saveData->companyId        = $companyQry->id;
            $saveData->assignedToUserId = $userId;
            $saveData->priority         = $request->priority;
            $saveData->complainTitle    = $request->complain_title;
            $saveData->complainDetail   = $request->complain_description;
            $saveData->save();

            $cmpID            = $saveData->id;
            $curentYear       = date('Y');
            $finalString      = 'CP'.$curentYear.$cmpID;
            $saveData->cmp_id = $finalString;
            $saveData->save();

            return Redirect::route('complaints')->with([
                'status'  => true,
                'message' => 'Complain sent successfully.',
                'class'   => 'text-sm text-green-400 ',
            ]);

        } else {

            return Redirect::route('addcomplain')->with([
                'status'  => false,
                'message' => 'Invalid request to add complain',
                'class'   => 'text-sm text-red-700',
            ]);

        }

    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $user        = Auth::user();
        $profileType = $user->profileType;

        $complaintQuery = Complaint::join('company', 'company.id', '=', 'complain.companyId')
            ->join('users as complain_user', 'complain_user.id', '=', 'complain.assignedToUserId')
            ->select(
                'company.companyName',
                'company.city',
                'company.state',
                'complain.*',
                'complain_user.email as complainant_email',
                'complain_user.name as complainant_name',
                'complain_user.phone as complainant_phone',
                'company.userId',
                'complain_user.profilePicture as complainant_profilePicture'
            )
            ->where('complain.id', $id)
            ->orderBy('complain.id', 'desc');

        if ($profileType == 'admin') {
            // No additional where conditions for admin
        } elseif ($profileType == 'company') {
            $complaintQuery->where('company.userId', $user->id);
        } elseif ($profileType == 'agent') {
            $complaintQuery->where('company.userId', Auth::user()->parent_id);
        } else {
            $complaintQuery->where('complain.assignedToUserId', $user->id);
        }

        $ComplaintsSingle = $complaintQuery->first();

        if ($ComplaintsSingle) {

            if ($profileType == 'admin' || $profileType == 'company') {
                $senderID = $ComplaintsSingle->assignedToUserId;
            } else {
                $senderID = $ComplaintsSingle->userId;
            }

            $senderComplainID      = $ComplaintsSingle->id;
            $complaintConversation = ComplaintMessages::join('users', 'users.id', '=', 'complainmessage.senderId')
                ->where('complainmessage.complainId', $ComplaintsSingle->id)
                ->select('complainmessage.*', 'users.name', 'users.email')
                ->get();
        }

        if ($ComplaintsSingle) {

            if ($profileType == 'admin' || $profileType == 'company') {
                $senderID = $ComplaintsSingle->assignedToUserId;
            } else {
                $senderID = $ComplaintsSingle->userId;
            }

            $senderComplainID      = $ComplaintsSingle->id;
            $complaintConversation = ComplaintMessages::join('users', 'users.id', '=', 'complainmessage.senderId')
                ->where('complainmessage.complainId', $ComplaintsSingle->id)
                ->select('complainmessage.*', 'users.name', 'users.email')
                ->get();

            return Inertia::render('Complain/ViewComplain', [
                'ComplaintsSingle'       => $ComplaintsSingle,
                'complaintConversation' => $complaintConversation,
                'senderID'              => $senderID,
                'senderComplainID'      => $senderComplainID,
            ]);
        } else {
            return Redirect::route('complaints');
        }

    }

    public function sendComplainMessage(Request $request)
    {

        $user        = Auth::user();
        $profileType = $user->profileType;
        $id          = $request->complain_id;
        $senderUid   = $request->senderUid;
        $message     = ($request->message) ? $request->message : '';

        $complaintQuery = Complaint::join('company', 'company.id', '=', 'complain.companyId')
            ->join('users as complain_user', 'complain_user.id', '=', 'complain.assignedToUserId')
            ->select(
                'company.companyName',
                'company.city',
                'company.state',
                'complain.*',
                'complain_user.email as complainant_email',
                'complain_user.name as complainant_name',
                'complain_user.phone as complainant_phone',
                'complain_user.profilePicture as complainant_profilePicture'
            )
            ->where('complain.id', $id)
            ->orderBy('complain.id', 'desc');

        if ($profileType == 'admin') {
            // No additional where conditions for admin
        } elseif ($profileType == 'company') {
            $complaintQuery->where('company.userId', $user->id);
        } elseif ($profileType == 'agent') {
            $complaintQuery->where('company.userId', Auth::user()->parent_id);
        } else {
            $complaintQuery->where('complain.assignedToUserId', $user->id);
        }

        $Complaints = $complaintQuery->first();

        if ($Complaints) {

            $senderId = Auth::user()->id;

            // Handle file upload
            $contentFilePath = '';
            if ($request->hasFile('attachment')) {
                $file              = $request->file('attachment');
                $allowedExtensions = ['jpeg', 'jpg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt'];
                if (! in_array($file->getClientOriginalExtension(), $allowedExtensions)) {
                    return response()->json([
                        'status'  => false,
                        'message' => 'Invalid file type.',
                    ], 400);
                }

                $folder_name     = 'images/complaints';
                $contentFilePath = $file->store($folder_name, 'public');
            }

            if ($contentFilePath == '' && $message == '') {
                return response()->json(['status' => false], 200, [], JSON_PRETTY_PRINT);
            }

            $saveComplains               = new ComplaintMessages;
            $saveComplains->complainId   = $id;
            $saveComplains->senderId     = $senderId;
            $saveComplains->content      = $message;
            $saveComplains->content_file = $contentFilePath;
            $saveComplains->save();

            $complaintConversation = ComplaintMessages::join('users', 'users.id', '=', 'complainmessage.senderId')
                ->where('complainmessage.complainId', $Complaints->id)
                ->select('complainmessage.*', 'users.name', 'users.email')
                ->get();

            return response()->json([
                'status' => true,
                'data'   => $complaintConversation,
            ], 200, [], JSON_PRETTY_PRINT);

        } else {
            return response()->json(['status' => false], 200, [], JSON_PRETTY_PRINT);
        }

    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $user        = Auth::user();
        $profileType = $user->profileType;

        if ($profileType == 'user') {

            return Redirect::route('complaints')->with([
                'status'  => true,
                'message' => 'Invalid Authentication.',
                'class'   => 'text-sm text-green-400 ',
            ]);

        }

        $complaintQuery = Complaint::join('company', 'company.id', '=', 'complain.companyId')
            ->join('users as complain_user', 'complain_user.id', '=', 'complain.assignedToUserId')
            ->select(
                'company.companyName',
                'company.city',
                'company.state',
                'complain.*',
                'complain_user.email as complainant_email',
                'complain_user.name as complainant_name',
                'complain_user.phone as complainant_phone',
                'complain_user.profilePicture as complainant_profilePicture'
            )
            ->where('complain.id', $id)
            ->orderBy('complain.id', 'desc');

        if ($profileType == 'admin') {
            // No additional where conditions for admin
        } elseif ($profileType == 'company') {
            $complaintQuery->where('company.userId', $user->id);
        } elseif ($profileType == 'agent') {
            $complaintQuery->where('company.userId', Auth::user()->parent_id);
        }

        $Complaints = $complaintQuery->first();

        return Inertia::render('Complain/EditComplain', [
            'Complaints' => $Complaints,
        ]);

    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $user        = Auth::user();
        $profileType = $user->profileType;

        if ($profileType == 'user') {

            return Redirect::route('complaints')->with([
                'status'  => true,
                'message' => 'Invalid Authentication',
                'class'   => 'text-sm text-green-400 ',
            ]);

        }

        $customMessages = [
            'required'       => 'The :attribute field is required.',
            'email'          => 'The :attribute must be a valid email address.',
            'unique'         => 'The :attribute has already been taken.',
            'in'             => 'The :attribute field must be 0, 1, or 2.',
            'exists'         => 'The selected :attribute is invalid.',
            'phone.regex'    => 'The :attribute must be a valid phone number.',
            'numeric'        => 'The :attribute must be a valid phone number without alphabetic characters.',
            'digits_between' => 'The :attribute must be between :min and :max digits.',
        ];

        // Define common validation rules
        $commonRules = [
            'complain_title'       => 'required',
            'complain_description' => 'required',
            'priority'             => 'required|in:0,1,2',
            'complainStatus'       => 'required|in:open,close',
        ];

        $validatedData = $request->validate(array_merge($commonRules), $customMessages);

        $complainQry = Complaint::find($request->selectedComplainID);
        if ($complainQry) {

            if ($profileType == 'company') {
                $checkCompany = Company::where('id', $complainQry->companyId)->where('userId', Auth::id())->first();
                if ($checkCompany) {
                } else {
                    return Redirect::route('complaints')->with([
                        'status'  => true,
                        'message' => 'Invalid Authentication',
                        'class'   => 'text-sm text-green-400 ',
                    ]);
                }
            }

            if ($profileType == 'agent') {
                $checkCompany = Company::where('id', $complainQry->companyId)->where('userId', Auth::user()->parent_id)->first();
                if ($checkCompany) {
                } else {
                    return Redirect::route('complaints')->with([
                        'status'  => true,
                        'message' => 'Invalid Authentication',
                        'class'   => 'text-sm text-green-400 ',
                    ]);
                }
            }

            $saveData                 = Complaint::find($request->selectedComplainID);
            $saveData->complainTitle  = $request->complain_title;
            $saveData->complainDetail = $request->complain_description;
            $saveData->priority       = $request->priority;
            $saveData->complainStatus = $request->complainStatus;
            $saveData->save();

            return Redirect::route('complaints')->with([
                'status'  => true,
                'message' => 'Complain saved successfully.',
                'class'   => 'text-sm text-green-400 ',
            ]);

        } else {
            return Redirect::route('complaints')->with([
                'status'  => true,
                'message' => 'Invalid request',
                'class'   => 'text-sm text-green-400 ',
            ]);

        }

    }

    public function updateProgressStatus(Request $request)
    {
        $user        = Auth::user();
        $profileType = $user->profileType;

        if ($profileType == 'user') {

            return response()->json([
                'success' => false,
                'message' => 'Invalid authentication',
            ]);
        }

        // Manual validation using Validator
        $validator = Validator::make($request->all(), [
            'progressStatus' => 'required|in:new,inprogress,need_user_input,resolved',
        ]);

        // Check if validation fails
        if ($validator->fails()) {
            // Return errors with 422 Unprocessable Entity status
            return response()->json([
                'success' => false,
                'errors'  => $validator->errors(),
                'message' => 'validation error',
            ], 422);
        }

        $complainQry = Complaint::find($request->complaint_id);
        if ($complainQry) {

            if ($profileType == 'company') {
                $checkCompany = Company::where('id', $complainQry->companyId)->where('userId', Auth::id())->first();
                if ($checkCompany) {
                } else {

                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid Authentication',
                    ]);
                }
            }

            if ($profileType == 'agent') {
                $checkCompany = Company::where('id', $complainQry->companyId)->where('userId', Auth::user()->parent_id)->first();
                if ($checkCompany) {
                } else {

                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid Authentication',
                    ]);

                }
            }

            $saveData                 = Complaint::find($request->complaint_id);
            $saveData->progressStatus = $request->progressStatus;
            if ($request->progressStatus == 'resolved') {
                $saveData->complainStatus = 'close';
            }
            $saveData->save();

            return response()->json([
                'success'        => true,
                'message'        => 'Complaint progress status saved successfully.',
                'complainStatus' => $saveData->complainStatus,
                'saveData'       => $saveData,
            ]);

        } else {

            return response()->json([
                'success' => false,
                'message' => 'Invalid request',
            ]);

        }

    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    public function getComplaintDetails(Request $request)
    {
        $user        = Auth::user();
        $profileType = $user->profileType;
        $id          = $request->id;

        $complaintQuery = Complaint::join('company', 'company.id', '=', 'complain.companyId')
            ->join('users as complain_user', 'complain_user.id', '=', 'complain.assignedToUserId')
            ->select(
                'company.companyName',
                'company.city',
                'company.state',
                'complain.*',
                'complain_user.email as complainant_email',
                'complain_user.name as complainant_name',
                'complain_user.phone as complainant_phone',
                'company.userId',
                'complain_user.profilePicture as complainant_profilePicture'
            )
            ->where('complain.id', $id)
            ->orderBy('complain.id', 'desc');

        if ($profileType == 'admin') {
            // No additional where conditions for admin
        } elseif ($profileType == 'company') {
            $complaintQuery->where('company.userId', $user->id);
        } elseif ($profileType == 'agent') {
            $complaintQuery->where('company.userId', Auth::user()->parent_id);
        } else {
            $complaintQuery->where('complain.assignedToUserId', $user->id);
        }

        $ComplaintsSingle = $complaintQuery->first();

        $senderID              = '';
        $senderComplainID      = '';
        $complaintConversation = '';

        if ($ComplaintsSingle) {
            if ($profileType == 'admin' || $profileType == 'company') {
                $senderID = $ComplaintsSingle->assignedToUserId;
            } else {
                $senderID = $ComplaintsSingle->userId;
            }

            $senderComplainID      = $ComplaintsSingle->id;
            $complaintConversation = ComplaintMessages::join('users', 'users.id', '=', 'complainmessage.senderId')
                ->where('complainmessage.complainId', $ComplaintsSingle->id)
                ->select('complainmessage.*', 'users.name', 'users.email')
                ->get();
        }

        return response()->json([
            'ComplaintsSingle'      => $ComplaintsSingle,
            'complaintConversation' => $complaintConversation,
            'senderID'              => $senderID,
            'senderComplainID'      => $senderComplainID,
        ]);
    }
}
