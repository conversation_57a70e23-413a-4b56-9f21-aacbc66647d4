<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cb_intents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('name');
            $table->string('context');
            $table->text('description')->nullable();
            $table->longText('training_phrases')->nullable();
            $table->longText('responses')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        // Schema::create('cb_training_phrases', function (Blueprint $table) {
        //     $table->id();
        //     $table->foreignId('intent_id')->constrained('cb_intents')->onDelete('cascade');
        //     $table->text('phrase');
        //     $table->softDeletes();
        //     $table->timestamps();
        // });

        // Schema::create('cb_response_text', function (Blueprint $table) {
        //     $table->id();
        //     $table->foreignId('intent_id')->constrained('cb_intents')->onDelete('cascade');
        //     $table->text('phrase');
        //     $table->softDeletes();
        //     $table->timestamps();
        // });

        Schema::create('cb_vendor_api', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('name');
            $table->text('url');
            $table->enum('method', ['GET', 'POST']);
            $table->json('request_parameters')->nullable();
            $table->json('response_parameters')->nullable();
            $table->text('description')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cb_vendor_api');
        Schema::dropIfExists('cb_response_text');
        Schema::dropIfExists('cb_training_phrases');
        Schema::dropIfExists('cb_intents');
    }
};
