<script setup>
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import { Head, Link, useForm, usePage } from "@inertiajs/vue3";
import { ref, computed } from "vue";
import TextInput from "@/Components/TextInput.vue";
import TextAreaInput from "@/Components/TextAreaInput.vue";
import InputError from "@/Components/InputError.vue";
import InputLabel from "@/Components/InputLabel.vue";
import DefaultCard from "@/Components/Forms/DefaultCard.vue";
import SelectInput from "@/Components/SelectInput.vue";
import ResponsiveNavLink from "@/Components/ResponsiveNavLink.vue";
import FileInput from "@/Components/FileInput.vue";
import { Inertia } from "@inertiajs/inertia";
import BreadcrumbDefault from "@/Components/Breadcrumbs/BreadcrumbDefault.vue";
import PageHeading from "@/Components/Global/PageHeading.vue";

const pageTitle = ref("Edit Category");

const { itemDetail } = usePage().props;
var baseurl = window.location.origin;

const formatDate = (dateString) => {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const form = useForm({
  catId: itemDetail.catId,
  catPicture: null,
  itemViewImage: itemDetail.catPicture,
  catName: itemDetail.catName,
  response: "",
});

const onFileChange = (event) => {
  const file = event.target.files[0];
  form.catPicture = file;
};

const resetForm = () => {
  // Clear file input by replacing it with a new one
  const fileInput = document.getElementById("catPicture");
  fileInput.value = ""; // Clear the value for better browser compatibility
  const newFileInput = document.createElement("input");
  newFileInput.type = "file";
  newFileInput.id = "catPicture";
  newFileInput.classList.add("mt-1", "block", "w-full");
  newFileInput.addEventListener("change", onFileChange); // Reattach the change event listener
  fileInput.parentNode.replaceChild(newFileInput, fileInput);

  //form.catName = itemDetail.catName;
};

const submitForm = () => {
  form.post(route("category.update"), {
    onSuccess: (response) => {
      form.itemViewImage = response.props.itemDetail.catPicture;
      form.catName = response.props.itemDetail.catName;
      resetForm();
    },
    onError: (errors) => {},
  });
};
</script>

<template>
  <Head :title="pageTitle" />

  <AuthenticatedLayout>
    <PageHeading :title="pageTitle"></PageHeading>
    <form
      @submit.prevent="submitForm"
      enctype="multipart/form-data"
      class="form-main-body"
    >
      <TextInput
        id="catId"
        name="catId"
        class="hidden"
        type="hidden"
        v-model="form.catId"
      />

      <div class="add-form-main-bcls">
        <div class="flex flex-col gap-4 col-span-12">
          <!-- Contact Form Start -->
          <DefaultCard cardTitle="Update Category">
            <div class="p-6">
              <Transition
                enter-active-class="transition ease-in-out"
                enter-from-class="opacity-0"
                leave-active-class="transition ease-in-out"
                leave-to-class="opacity-0"
              >
                <p
                  class="bg-green-100 mb-4 p-2 rounded-md text-green-700"
                  v-if="$page.props.flash?.message"
                  :class="$page.props.flash.class"
                >
                  {{ $page.props.flash.message }}
                </p>
              </Transition>

              <div class="mt-2 mb-4">
                <InputLabel for="catName" value="Title" />
                <TextInput
                  id="catName"
                  name="catName"
                  type="text"
                  placeholder="Title"
                  class="block mt-1 w-full"
                  v-model="form.catName"
                  :disabled="form.catName == 'Other'"
                />
                <InputError :message="form.errors.catName" class="mt-2" />
              </div>

              <div class="mt-2 mb-4">
                <InputLabel for="catPicture" value="Upload Image" />
                <FileInput
                  id="catPicture"
                  type="file"
                  class="block mt-1 w-full"
                  v-model="form.catPicture"
                  @input="onFileChange"
                  autocomplete="catPicture"
                />
                <InputError class="mt-2" :message="form.errors.catPicture" />

                <div
                  class="my-2"
                  v-if="form.itemViewImage != '' && form.itemViewImage != null"
                >
                  <img
                    :src="baseurl + '/storage/' + form.itemViewImage"
                    alt="Category"
                    class="rounded-full w-15 h-15"
                  />
                </div>
              </div>

              <div class="flex">
                <button class="dk-update-btn">Update</button>

                <ResponsiveNavLink :href="route('category.list')" class="dk-cancle-btn">
                  Cancel
                </ResponsiveNavLink>
              </div>
            </div>
          </DefaultCard>

          <!-- Contact Form End -->
        </div>
      </div>
    </form>
  </AuthenticatedLayout>
</template>
