import React, { useState } from 'react';
import { Link, useForm } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { Input, Select, Form, FormRow, Checkbox } from '@/Components/FormElements';
import Button from '@/Components/Button';
import FormError from '@/Components/FormError';
import { Card, CardContent, PageHeader } from '@/Components/UI';

export default function AgentCreate() {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        email: '',
        phone: '',
        role: '',
        department: '',
        password: '',
        password_confirmation: '',
        status: 'active',
        skills: [],
        bio: '',
        emergency_contact: '',
        emergency_phone: '',
        start_date: '',
        salary: '',
        employee_id: '',
        manager_id: '',
        work_schedule: 'full_time',
        permissions: {
            can_view_all_tickets: false,
            can_assign_tickets: false,
            can_delete_tickets: false,
            can_manage_users: false,
            can_view_reports: false,
            can_export_data: false
        }
    });

    const [selectedSkills, setSelectedSkills] = useState([]);
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    const availableSkills = [
        'Technical Support',
        'Customer Service',
        'Product Knowledge',
        'Billing Support',
        'Technical Troubleshooting',
        'Software Support',
        'Hardware Issues',
        'Order Management',
        'Account Management',
        'Payment Processing',
        'Team Leadership',
        'Training',
        'Customer Relations',
        'Live Chat Support',
        'Email Support',
        'Phone Support',
        'Social Media Support',
        'Escalation Management'
    ];

    const departments = [
        'Customer Support',
        'Technical Support',
        'Billing Support',
        'Sales Support',
        'Product Support',
        'IT Support'
    ];

    const roles = [
        'Junior Support Agent',
        'Support Agent',
        'Senior Support Agent',
        'Lead Support Agent',
        'Support Specialist',
        'Technical Support Specialist',
        'Customer Success Manager'
    ];

    const handleSkillToggle = (skill) => {
        const updatedSkills = selectedSkills.includes(skill)
            ? selectedSkills.filter(s => s !== skill)
            : [...selectedSkills, skill];
        
        setSelectedSkills(updatedSkills);
        setData('skills', updatedSkills);
    };

    const handlePermissionChange = (permission, value) => {
        setData('permissions', {
            ...data.permissions,
            [permission]: value
        });
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        post('/agents', {
            onSuccess: () => {
                // Handle success
            },
            onError: () => {
                // Handle error
            }
        });
    };

    return (
        <DashboardLayout title="Create Agent">
            <title>Create New Agent</title>
            
            <div className="space-y-6">
                {/* Header */}
                <div className="bg-gradient-to-r from-cf-primary-600 to-blue-600 rounded-2xl p-8 text-white relative overflow-hidden">
                    <div className="absolute inset-0 opacity-10">
                        <img
                            src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?w=800&h=400&fit=crop&crop=center"
                            alt="Team management"
                            className="w-full h-full object-cover"
                        />
                    </div>
                    <div className="relative">
                        <div className="flex items-center gap-4 mb-4">
                            <Link
                                href="/agents"
                                className="p-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                </svg>
                            </Link>
                            <div>
                                <h1 className="text-3xl md:text-4xl font-bold">
                                    Create New Agent
                                </h1>
                                <p className="text-xl opacity-90 mt-2">
                                    Add a new support agent to your team
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <PageHeader
                    title="Agent Information"
                    subtitle="Enter the details for the new agent"
                />

                {/* Form */}
                <form onSubmit={handleSubmit} className="space-y-8">
                    {/* Personal Information */}
                    <Card>
                        <CardContent className="p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                                <svg className="w-5 h-5 text-cf-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                Personal Information
                            </h3>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <FormRow label="Full Name" error={errors.name}>
                                    <Input
                                        type="text"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="Enter full name"
                                    />
                                </FormRow>
                                
                                <FormRow label="Email Address" error={errors.email}>
                                    <Input
                                        type="email"
                                        value={data.email}
                                        onChange={(e) => setData('email', e.target.value)}
                                        placeholder="Enter email address"
                                    />
                                </FormRow>
                                
                                <FormRow label="Phone Number" error={errors.phone}>
                                    <Input
                                        type="tel"
                                        value={data.phone}
                                        onChange={(e) => setData('phone', e.target.value)}
                                        placeholder="Enter phone number"
                                    />
                                </FormRow>
                                
                                <FormRow label="Bio" error={errors.bio}>
                                    <textarea
                                        value={data.bio}
                                        onChange={(e) => setData('bio', e.target.value)}
                                        placeholder="Brief description of the agent's background and expertise"
                                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cf-primary-500 focus:border-cf-primary-500"
                                        rows={3}
                                    />
                                </FormRow>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Account Information */}
                    <Card>
                        <CardContent className="p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                                <svg className="w-5 h-5 text-cf-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                                Account Information
                            </h3>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <FormRow label="Password" error={errors.password}>
                                    <div className="relative">
                                        <Input
                                            type={showPassword ? "text" : "password"}
                                            value={data.password}
                                            onChange={(e) => setData('password', e.target.value)}
                                            placeholder="Enter password"
                                        />
                                        <button
                                            type="button"
                                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                                            onClick={() => setShowPassword(!showPassword)}
                                        >
                                            {showPassword ? (
                                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                                                </svg>
                                            ) : (
                                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                </svg>
                                            )}
                                        </button>
                                    </div>
                                </FormRow>
                                
                                <FormRow label="Confirm Password" error={errors.password_confirmation}>
                                    <div className="relative">
                                        <Input
                                            type={showConfirmPassword ? "text" : "password"}
                                            value={data.password_confirmation}
                                            onChange={(e) => setData('password_confirmation', e.target.value)}
                                            placeholder="Confirm password"
                                        />
                                        <button
                                            type="button"
                                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                        >
                                            {showConfirmPassword ? (
                                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                                                </svg>
                                            ) : (
                                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                </svg>
                                            )}
                                        </button>
                                    </div>
                                </FormRow>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Job Information */}
                    <Card>
                        <CardContent className="p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                                <svg className="w-5 h-5 text-cf-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                Job Information
                            </h3>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <FormRow label="Role" error={errors.role}>
                                    <Select
                                        value={data.role}
                                        onChange={(e) => setData('role', e.target.value)}
                                    >
                                        <option value="">Select a role</option>
                                        {roles.map(role => (
                                            <option key={role} value={role}>{role}</option>
                                        ))}
                                    </Select>
                                </FormRow>
                                
                                <FormRow label="Department" error={errors.department}>
                                    <Select
                                        value={data.department}
                                        onChange={(e) => setData('department', e.target.value)}
                                    >
                                        <option value="">Select a department</option>
                                        {departments.map(dept => (
                                            <option key={dept} value={dept}>{dept}</option>
                                        ))}
                                    </Select>
                                </FormRow>
                                
                                <FormRow label="Status" error={errors.status}>
                                    <Select
                                        value={data.status}
                                        onChange={(e) => setData('status', e.target.value)}
                                    >
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                    </Select>
                                </FormRow>
                                
                                <FormRow label="Start Date" error={errors.start_date}>
                                    <Input
                                        type="date"
                                        value={data.start_date}
                                        onChange={(e) => setData('start_date', e.target.value)}
                                    />
                                </FormRow>
                                
                                <FormRow label="Employee ID" error={errors.employee_id}>
                                    <Input
                                        type="text"
                                        value={data.employee_id}
                                        onChange={(e) => setData('employee_id', e.target.value)}
                                        placeholder="Enter employee ID"
                                    />
                                </FormRow>
                                
                                <FormRow label="Salary" error={errors.salary}>
                                    <Input
                                        type="text"
                                        value={data.salary}
                                        onChange={(e) => setData('salary', e.target.value)}
                                        placeholder="Enter salary"
                                    />
                                </FormRow>
                                
                                <FormRow label="Manager" error={errors.manager_id}>
                                    <Select
                                        value={data.manager_id}
                                        onChange={(e) => setData('manager_id', e.target.value)}
                                    >
                                        <option value="">No Manager</option>
                                        <option value="1">David Wilson (Lead Support Agent)</option>
                                        <option value="2">Sarah Johnson (Senior Support Agent)</option>
                                    </Select>
                                </FormRow>
                                
                                <FormRow label="Work Schedule" error={errors.work_schedule}>
                                    <Select
                                        value={data.work_schedule}
                                        onChange={(e) => setData('work_schedule', e.target.value)}
                                    >
                                        <option value="full_time">Full Time</option>
                                        <option value="part_time">Part Time</option>
                                        <option value="contract">Contract</option>
                                        <option value="remote">Remote</option>
                                    </Select>
                                </FormRow>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Skills */}
                    <Card>
                        <CardContent className="p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                                <svg className="w-5 h-5 text-cf-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                </svg>
                                Skills
                            </h3>
                            
                            <div className="space-y-4">
                                <p className="text-sm text-gray-600">Select the skills that this agent possesses:</p>
                                
                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                                    {availableSkills.map(skill => (
                                        <div key={skill} className="flex items-center">
                                            <Checkbox
                                                id={`skill-${skill}`}
                                                checked={selectedSkills.includes(skill)}
                                                onChange={() => handleSkillToggle(skill)}
                                            />
                                            <label htmlFor={`skill-${skill}`} className="ml-2 text-sm text-gray-700">
                                                {skill}
                                            </label>
                                        </div>
                                    ))}
                                </div>
                                
                                {errors.skills && <FormError>{errors.skills}</FormError>}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Emergency Contact */}
                    <Card>
                        <CardContent className="p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                                <svg className="w-5 h-5 text-cf-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                                </svg>
                                Emergency Contact
                            </h3>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <FormRow label="Emergency Contact Name" error={errors.emergency_contact}>
                                    <Input
                                        type="text"
                                        value={data.emergency_contact}
                                        onChange={(e) => setData('emergency_contact', e.target.value)}
                                        placeholder="Enter emergency contact name"
                                    />
                                </FormRow>
                                
                                <FormRow label="Emergency Contact Phone" error={errors.emergency_phone}>
                                    <Input
                                        type="tel"
                                        value={data.emergency_phone}
                                        onChange={(e) => setData('emergency_phone', e.target.value)}
                                        placeholder="Enter emergency contact phone"
                                    />
                                </FormRow>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Permissions */}
                    <Card>
                        <CardContent className="p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                                <svg className="w-5 h-5 text-cf-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                                </svg>
                                Permissions
                            </h3>
                            
                            <div className="space-y-4">
                                <p className="text-sm text-gray-600">Set the permissions for this agent:</p>
                                
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                    <div className="flex items-center">
                                        <Checkbox
                                            id="perm-view-tickets"
                                            checked={data.permissions.can_view_all_tickets}
                                            onChange={(e) => handlePermissionChange('can_view_all_tickets', e.target.checked)}
                                        />
                                        <label htmlFor="perm-view-tickets" className="ml-2 text-sm text-gray-700">
                                            Can view all tickets
                                        </label>
                                    </div>
                                    
                                    <div className="flex items-center">
                                        <Checkbox
                                            id="perm-assign-tickets"
                                            checked={data.permissions.can_assign_tickets}
                                            onChange={(e) => handlePermissionChange('can_assign_tickets', e.target.checked)}
                                        />
                                        <label htmlFor="perm-assign-tickets" className="ml-2 text-sm text-gray-700">
                                            Can assign tickets
                                        </label>
                                    </div>
                                    
                                    <div className="flex items-center">
                                        <Checkbox
                                            id="perm-delete-tickets"
                                            checked={data.permissions.can_delete_tickets}
                                            onChange={(e) => handlePermissionChange('can_delete_tickets', e.target.checked)}
                                        />
                                        <label htmlFor="perm-delete-tickets" className="ml-2 text-sm text-gray-700">
                                            Can delete tickets
                                        </label>
                                    </div>
                                    
                                    <div className="flex items-center">
                                        <Checkbox
                                            id="perm-manage-users"
                                            checked={data.permissions.can_manage_users}
                                            onChange={(e) => handlePermissionChange('can_manage_users', e.target.checked)}
                                        />
                                        <label htmlFor="perm-manage-users" className="ml-2 text-sm text-gray-700">
                                            Can manage users
                                        </label>
                                    </div>
                                    
                                    <div className="flex items-center">
                                        <Checkbox
                                            id="perm-view-reports"
                                            checked={data.permissions.can_view_reports}
                                            onChange={(e) => handlePermissionChange('can_view_reports', e.target.checked)}
                                        />
                                        <label htmlFor="perm-view-reports" className="ml-2 text-sm text-gray-700">
                                            Can view reports
                                        </label>
                                    </div>
                                    
                                    <div className="flex items-center">
                                        <Checkbox
                                            id="perm-export-data"
                                            checked={data.permissions.can_export_data}
                                            onChange={(e) => handlePermissionChange('can_export_data', e.target.checked)}
                                        />
                                        <label htmlFor="perm-export-data" className="ml-2 text-sm text-gray-700">
                                            Can export data
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Submit Buttons */}
                    <div className="flex justify-end gap-4">
                        <Link
                            href="/agents"
                            className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                            Cancel
                        </Link>
                        <Button
                            type="submit"
                            disabled={processing}
                            className="px-6 py-2 bg-cf-primary-600 text-white rounded-lg hover:bg-cf-primary-700 transition-colors"
                        >
                            {processing ? 'Creating...' : 'Create Agent'}
                        </Button>
                    </div>
                </form>
            </div>
        </DashboardLayout>
    );
}