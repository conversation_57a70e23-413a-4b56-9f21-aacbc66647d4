document.addEventListener("DOMContentLoaded", () => {
    const button = document.querySelector("[data-oauth-login]");
    if (!button) return;

    const config = {
        clientId: button.getAttribute("data-client-id"),
        clientSecret: button.getAttribute("data-client-secret"),
        redirectUri: button.getAttribute("data-redirect-uri"),
        authUrl: button.getAttribute("data-auth-url"),
        tokenUrl: button.getAttribute("data-token-url"),
        vuserid: button.getAttribute("data-user-id"),
        popupOptions: button.getAttribute("data-popup-options") || "width=500,height=600"
    };

    Object.assign(button.style, {
        display: "flex",
        alignItems: "center",
        gap: "10px",
        padding: "10px 20px",
        border: "1px solid #dcdcdc",
        borderRadius: "5px",
        background: "white",
        fontSize: "16px",
        fontWeight: "500",
        cursor: "pointer",
        transition: "all 0.2s ease-in-out",
        boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)"
    });

    const icon = document.createElement("img");
    icon.src = button.getAttribute("data-icon") || "https://chatfil.com/favicon.ico";
    Object.assign(icon.style, { width: "20px", height: "20px" });
    button.prepend(icon);
    
    if (!config.vuserid) {
        button.style.display = "none";
    }

    // Inject CSS dynamically
    const style = document.createElement('style');
    style.textContent = `
        .loading-container { display: flex; align-items: center; gap: 5px; }
        .loading-dots { display: flex; }
        .loading-dot { width: 8px; height: 8px; background-color: #007bff; border-radius: 50%; margin-left: 4px; animation: loading-animation 1s infinite; }
        .loading-dot:nth-child(1) { animation-delay: 0s; }
        .loading-dot:nth-child(2) { animation-delay: 0.2s; }
        .loading-dot:nth-child(3) { animation-delay: 0.4s; }
        @keyframes loading-animation { 0% { transform: scale(0); opacity: 0.5; } 50% { transform: scale(1); opacity: 1; } 100% { transform: scale(0); opacity: 0.5; } }
        button:disabled { opacity: 0.7; cursor: not-allowed; }
    `;
    document.head.appendChild(style);

    button.addEventListener("click", () => {
        const loadingContainer = document.createElement("span");
        loadingContainer.classList.add("loading-container");
        loadingContainer.textContent = "Loading";
        const dotsContainer = document.createElement("span");
        dotsContainer.classList.add("loading-dots");
        loadingContainer.appendChild(dotsContainer);
        for (let i = 0; i < 3; i++) {
            const dot = document.createElement("span");
            dot.classList.add("loading-dot");
            dotsContainer.appendChild(dot);
        }

        button.textContent = "";
        button.appendChild(loadingContainer);
        button.disabled = true;

        const authWindow = window.open(
            `${config.authUrl}?client_id=${config.clientId}&redirect_uri=${config.redirectUri}&vuserid=${config.vuserid}`,
            "OAuthLoginPopup",
            config.popupOptions
        );

        window.addEventListener("message", ({ origin, data }) => {
            if (origin !== new URL(config.authUrl).origin || !data.code) return;

            fetch(config.tokenUrl, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    client_id: config.clientId,
                    client_secret: config.clientSecret,
                    code: data.code,
                    vuserid: config.vuserid
                })
            })
                .then(res => res.json())
                .then(response => {
                    if (response.access_token) {
                        const form = document.createElement("form");
                        form.method = "GET";
                        form.action = config.redirectUri;

                        for (const key in response) {
                            if (response.hasOwnProperty(key)) {
                                const input = document.createElement("input");
                                input.type = "hidden";
                                input.name = key;
                                input.value = response[key];
                                form.appendChild(input);
                            }
                        }

                        document.body.appendChild(form);
                        form.submit();
                    } else {
                        console.error("OAuth Error");
                        button.disabled = false;
                        button.textContent = "";
                        button.prepend(icon);
                        button.insertAdjacentText("beforeend", "Login with Chatfil");
                    }
                })
                .catch(error => {
                    console.error(error);
                    button.disabled = false;
                    button.textContent = "";
                    button.prepend(icon);
                    button.insertAdjacentText("beforeend", "Login with Chatfil");
                });
        }, false);
    });
});