<?php

namespace App\Actions\Subscription;

use App\Models\Invoice;
use App\Models\PaymentGateway;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class HandleWebhooksAction
{
    /**
     * Handle webhook from payment gateway.
     */
    public function handle(string $gateway, Request $request): array
    {
        try {
            $paymentGateway = PaymentGateway::where('name', $gateway)
                ->where('is_active', true)
                ->first();

            if (!$paymentGateway) {
                throw new \Exception("Payment gateway {$gateway} not found or inactive.");
            }

            // Verify webhook signature
            $this->verifyWebhookSignature($paymentGateway, $request);

            // Process webhook based on gateway
            return match ($gateway) {
                'stripe' => $this->handleStripeWebhook($request),
                'paypal' => $this->handlePayPalWebhook($request),
                default => throw new \Exception("Unsupported gateway: {$gateway}"),
            };
        } catch (\Exception $e) {
            Log::error('Webhook processing failed', [
                'gateway' => $gateway,
                'error' => $e->getMessage(),
                'payload' => $request->all(),
            ]);

            throw $e;
        }
    }

    /**
     * Verify webhook signature.
     */
    private function verifyWebhookSignature(PaymentGateway $gateway, Request $request): void
    {
        if ($gateway->name === 'stripe') {
            $this->verifyStripeSignature($gateway, $request);
        } elseif ($gateway->name === 'paypal') {
            $this->verifyPayPalSignature($gateway, $request);
        }
    }

    /**
     * Verify Stripe webhook signature.
     */
    private function verifyStripeSignature(PaymentGateway $gateway, Request $request): void
    {
        $config = $gateway->getCurrentConfig();
        $webhookSecret = $config['webhook_endpoint_secret'] ?? $gateway->webhook_secret;

        if (!$webhookSecret) {
            throw new \Exception('Stripe webhook secret not configured.');
        }

        $signature = $request->header('Stripe-Signature');
        $payload = $request->getContent();

        try {
            \Stripe\Webhook::constructEvent($payload, $signature, $webhookSecret);
        } catch (\Stripe\Exception\SignatureVerificationException) {
            throw new \Exception('Invalid Stripe webhook signature.');
        }
    }

    /**
     * Verify PayPal webhook signature.
     */
    private function verifyPayPalSignature(PaymentGateway $gateway, Request $request): void
    {
        // PayPal webhook signature verification would go here
        // This is a placeholder for the actual PayPal webhook verification
        Log::info('PayPal webhook signature verification', [
            'gateway_id' => $gateway->id,
        ]);
    }

    /**
     * Handle Stripe webhook.
     */
    private function handleStripeWebhook(Request $request): array
    {
        $payload = json_decode($request->getContent(), true);
        $event = $payload['type'];
        $data = $payload['data']['object'];

        Log::info('Processing Stripe webhook', [
            'event' => $event,
            'object_id' => $data['id'] ?? null,
        ]);

        return match ($event) {
            'invoice.payment_succeeded' => $this->handleInvoicePaymentSucceeded($data),
            'invoice.payment_failed' => $this->handleInvoicePaymentFailed($data),
            'customer.subscription.updated' => $this->handleSubscriptionUpdated($data),
            'customer.subscription.deleted' => $this->handleSubscriptionDeleted($data),
            'customer.subscription.trial_will_end' => $this->handleTrialWillEnd($data),
            default => ['status' => 'ignored', 'event' => $event],
        };
    }

    /**
     * Handle PayPal webhook.
     */
    private function handlePayPalWebhook(Request $request): array
    {
        $payload = json_decode($request->getContent(), true);
        $eventType = $payload['event_type'] ?? null;

        Log::info('Processing PayPal webhook', [
            'event_type' => $eventType,
        ]);

        // PayPal webhook handling would go here
        return ['status' => 'processed', 'event_type' => $eventType];
    }

    /**
     * Handle successful invoice payment.
     */
    private function handleInvoicePaymentSucceeded(array $data): array
    {
        $subscriptionId = $data['subscription'] ?? null;

        if (!$subscriptionId) {
            return ['status' => 'ignored', 'reason' => 'No subscription ID'];
        }

        $subscription = Subscription::where('stripe_subscription_id', $subscriptionId)->first();

        if (!$subscription) {
            return ['status' => 'ignored', 'reason' => 'Subscription not found'];
        }

        // Find or create invoice
        $invoice = Invoice::where('stripe_invoice_id', $data['id'])->first();

        if (!$invoice) {
            $invoice = Invoice::create([
                'subscription_id' => $subscription->id,
                'user_id' => $subscription->user_id,
                'invoice_number' => Invoice::generateInvoiceNumber(),
                'stripe_invoice_id' => $data['id'],
                'status' => 'paid',
                'amount_due' => $data['amount_due'],
                'amount_paid' => $data['amount_paid'],
                'currency' => strtoupper($data['currency']),
                'billing_reason' => $data['billing_reason'] ?? 'subscription_cycle',
                'paid_at' => now(),
            ]);
        } else {
            $invoice->markAsPaid($data['amount_paid']);
        }

        // Update subscription status
        if ($subscription->status !== 'active') {
            $subscription->update(['status' => 'active']);
        }

        return ['status' => 'processed', 'invoice_id' => $invoice->id];
    }

    /**
     * Handle failed invoice payment.
     */
    private function handleInvoicePaymentFailed(array $data): array
    {
        $subscriptionId = $data['subscription'] ?? null;

        if (!$subscriptionId) {
            return ['status' => 'ignored', 'reason' => 'No subscription ID'];
        }

        $subscription = Subscription::where('stripe_subscription_id', $subscriptionId)->first();

        if (!$subscription) {
            return ['status' => 'ignored', 'reason' => 'Subscription not found'];
        }

        // Update subscription status to past due
        $subscription->update(['status' => 'past_due']);

        return ['status' => 'processed', 'subscription_id' => $subscription->id];
    }

    /**
     * Handle subscription updated.
     */
    private function handleSubscriptionUpdated(array $data): array
    {
        $subscription = Subscription::where('stripe_subscription_id', $data['id'])->first();

        if (!$subscription) {
            return ['status' => 'ignored', 'reason' => 'Subscription not found'];
        }

        // Update subscription details
        $subscription->update([
            'status' => $data['status'],
            'current_period_start' => \Carbon\Carbon::createFromTimestamp($data['current_period_start']),
            'current_period_end' => \Carbon\Carbon::createFromTimestamp($data['current_period_end']),
        ]);

        return ['status' => 'processed', 'subscription_id' => $subscription->id];
    }

    /**
     * Handle subscription deleted.
     */
    private function handleSubscriptionDeleted(array $data): array
    {
        $subscription = Subscription::where('stripe_subscription_id', $data['id'])->first();

        if (!$subscription) {
            return ['status' => 'ignored', 'reason' => 'Subscription not found'];
        }

        $subscription->update([
            'status' => 'canceled',
            'canceled_at' => now(),
            'ends_at' => now(),
        ]);

        return ['status' => 'processed', 'subscription_id' => $subscription->id];
    }

    /**
     * Handle trial will end notification.
     */
    private function handleTrialWillEnd(array $data): array
    {
        $subscription = Subscription::where('stripe_subscription_id', $data['id'])->first();

        if (!$subscription) {
            return ['status' => 'ignored', 'reason' => 'Subscription not found'];
        }

        // Here you could send notification emails, etc.
        Log::info('Trial ending soon', [
            'subscription_id' => $subscription->id,
            'user_id' => $subscription->user_id,
            'trial_ends_at' => $subscription->trial_ends_at,
        ]);

        return ['status' => 'processed', 'subscription_id' => $subscription->id];
    }
}
