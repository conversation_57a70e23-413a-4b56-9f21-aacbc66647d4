<!-- Dashboard.vue -->
<script setup>
import { ref } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head } from '@inertiajs/vue3';

// State for the active tab
const activeTab = ref('messages');

const activeChat = ref({
    id: 1,
    name: '<PERSON>',
    lastSeen: 'Last seen today 01:24',
    profileImg: '/images/profile.jpg'
});

const chatMessages = ref([
    { id: 1, sender: '<PERSON>', profileImg: '/images/profile.jpg', text: 'Hello! How can I help you?', time: '10:30 AM' },
    { id: 2, sender: 'Customer', profileImg: '/images/profile2.jpg', text: 'I need assistance with my order.', time: '10:35 AM' },
    { id: 2, sender: 'Customer', profileImg: '/images/profile2.jpg', text: 'Lorem ipsum dj dshjhoe hsdah.', time: '10:35 AM' },
    { id: 1, sender: '<PERSON>', profileImg: '/images/profile.jpg', text: 'I need purchase thus ne inortesr', time: '10:35 AM' },
    { id: 1, sender: '<PERSON> Kortney', profileImg: '/images/profile.jpg', text: 'I need purchase Loikush huiokm', time: '10:35 AM' },
    { id: 1, sender: 'Sarah Kortney', profileImg: '/images/profile.jpg', text: 'I need purchase Loikush huiokm', time: '10:35 AM' },
    { id: 1, sender: 'Sarah Kortney', profileImg: '/images/profile.jpg', text: 'I need purchase Loikush huiokm', time: '10:35 AM' },
    { id: 1, sender: 'Sarah Kortney', profileImg: '/images/profile.jpg', text: 'I need purchase Loikush huiokm', time: '10:35 AM' },
    { id: 1, sender: 'Sarah Kortney', profileImg: '/images/profile.jpg', text: 'I need purchase Loikush huiokm', time: '10:35 AM' },
    // Add more messages as needed
]);
// Sample data for messages and contacts
const messages = [
    { id: 1, sender: 'Agent 1', text: '19:20', profileImg: '/images/profile.jpg' },
    { id: 2, sender: 'Agent 2', text: '04:02', profileImg: '/images/profile2.jpg' },

    // Add more messages
];

const closes = [
    { id: 1, sender: 'Sumil', text: '19:20', profileImg: '/images/profile.jpg' },
    { id: 2, sender: 'Dipesh', text: '04:02', profileImg: '/images/profile.jpg' },
    { id: 2, sender: 'Roshan', text: '14:02', profileImg: '/images/profile2.jpg' },
    { id: 2, sender: 'Sahil', text: '08:20', profileImg: '/images/profile2.jpg' },

    // Add more messages
];

const contacts = [
    { id: 1, name: 'Agent 1', img: '/images/profile.jpg' },
    { id: 2, name: 'Agent 2', img: '/images/profile2.jpg' },
    { id: 3, name: 'Agent 3', img: '/images/profile.jpg' },
    { id: 4, name: 'Agent 4', img: '/images/profile.jpg' },
    { id: 5, name: 'Agent 5', img: '/images/profile.jpg' },
    { id: 6, name: 'Agent 6', img: '/images/profile.jpg' },
    { id: 7, name: 'Agent 7', img: '/images/profile.jpg' },
    { id: 8, name: 'Agent 8', img: '/images/profile.jpg' },
    { id: 9, name: 'Agent 9', img: '/images/profile.jpg' },
    { id: 10, name: 'Agent 10', img: '/images/profile.jpg' },
    { id: 11, name: 'Agent 11', img: '/images/profile.jpg' },
    { id: 12, name: 'Agent 12', img: '/images/profile.jpg' },
    { id: 13, name: 'Agent 13', img: '/images/profile.jpg' },
    { id: 14, name: 'Agent 14', img: '/images/profile.jpg' },
    { id: 15, name: 'Agent 15', img: '/images/profile.jpg' },
    // Add more contacts
];

// Function to change the active tab
const setActiveTab = (tab) => {
    activeTab.value = tab;
};
</script>

<template>

    <Head title="Dashboard" />

    <AuthenticatedLayout>
        <template #header>
            <h2 class="font-semibold text-slate-800 text-xl leading-tight">Dashboard</h2>
        </template>

        <div class="flex">
            <!-- Right Sidebar -->
            <div
                class="z-10 bg-white scrollbar-thumb-gray-700/20 p-6 rounded-md w-1/4 h-[calc(100vh-8rem)] overflow-y-auto scrollbar-thin scrollbar-track-gray-100">
                <div class="flex space-x-4 mb-4">
                    <button @click="setActiveTab('messages')"
                        :class="activeTab === 'messages' ? 'text-slate-800 border-t-4 border-black' : 'text-gray-600'"
                        class="pt-2 pb-2 w-1/2 focus:outline-none">
                        Active
                    </button>
                    <button @click="setActiveTab('contacts')"
                        :class="activeTab === 'contacts' ? 'text-slate-800 border-t-4 border-black' : 'text-gray-600'"
                        class="pt-2 pb-2 w-1/2 focus:outline-none">
                        Inactive
                    </button>
                    <button @click="setActiveTab('closes')"
                        :class="activeTab === 'closes' ? 'text-slate-800 border-t-4 border-black' : 'text-gray-600'"
                        class="pt-2 pb-2 w-1/2 focus:outline-none">
                        Close
                    </button>
                </div>
                <div v-if="activeTab === 'messages'">
                    <!-- Messages List -->
                    <ul class="space-y-4">
                        <li v-for="message in messages" :key="message.id"
                            class="flex items-center bg-white shadow p-4 rounded-md">
                            <img :src="message.profileImg" alt="Profile Picture" class="mr-4 rounded-full w-12 h-12">
                            <div>
                                <h4 class="font-semibold">{{ message.sender }}</h4>
                                <p class="text-slate-500 text-sm">{{ message.text }}</p>
                            </div>
                        </li>
                    </ul>
                </div>
                <div v-else-if="activeTab === 'contacts'">
                    <!-- Contacts List -->
                    <ul class="space-y-4">
                        <li v-for="contact in contacts" :key="contact.id"
                            class="flex items-center bg-white shadow p-4 rounded-md cursor-pointer">
                            <img :src="contact.img" alt="Profile Picture" class="mr-2 rounded-full w-12 h-12">
                            <span class="font-semibold">{{ contact.name }}</span>
                        </li>
                    </ul>
                </div>

                <div v-if="activeTab === 'closes'">
                    <!-- Close List -->
                    <ul class="space-y-4">
                        <li v-for="close in closes" :key="close.id"
                            class="flex items-center bg-white shadow p-4 rounded-md">
                            <img :src="close.profileImg" alt="Profile Picture" class="mr-4 rounded-full w-12 h-12">
                            <div>
                                <h4 class="font-semibold">{{ close.sender }}</h4>
                                <p class="text-slate-500 text-sm">{{ close.text }}</p>
                            </div>
                        </li>
                    </ul>
                </div>

            </div>

            <!-- Main Content Area -->
            <div class="flex-1 pr-4">
                <div class="gap-6 grid mx-auto sm:px-6 lg:px-8">
                    <!-- Chat History -->
                    <div class="bg-white p-6">
                        <!-- Active Chat Information -->
                        <div class="flex items-center mb-6">
                            <img :src="activeChat.profileImg" alt="Profile Picture" class="mr-4 rounded-full w-16 h-16">
                            <div>
                                <h4 class="font-semibold text-lg">{{ activeChat.name }}</h4>
                                <p class="text-gray-600 text-sm">{{ activeChat.lastSeen }}</p>
                            </div>
                        </div>
                        <hr>
                        <!-- Chat Messages -->
                        <div
                            class="z-10 flex flex-col space-y-4 scrollbar-thumb-gray-700/20 pt-8 rounded-md h-[calc(80vh-5rem)] overflow-y-auto scrollbar-thin scrollbar-track-gray-100">
                            <!-- Loop through each message -->
                            <div v-for="message in chatMessages" :key="message.id" class="flex items-start space-x-4"
                                :class="{ 'justify-start': message.sender === 'Agent', 'justify-end': message.sender === 'Customer' }">

                                <!-- Agent Message -->
                                <div v-if="message.sender === 'Agent'"
                                    class="flex items-center space-x-4 shadow p-4 rounded-md">
                                    <!-- Profile Picture -->
                                    <img :src="message.profileImg" alt="Profile Picture" class="rounded-full w-10 h-10">

                                    <!-- Message Container -->
                                    <div class="flex flex-col">
                                        <!-- Sender's Name -->
                                        <p class="font-semibold">{{ message.sender }}</p>
                                        <!-- Message Text -->
                                        <p>{{ message.text }}</p>
                                        <!-- Message Time -->
                                        <p class="text-slate-500 text-xs">{{ message.time }}</p>
                                    </div>
                                </div>


                                <!-- Customer Message -->
                                <div v-else class="flex items-center space-x-4 shadow p-4 rounded-md">
                                    <!-- Message Container -->
                                    <div class="   ">
                                        <!-- Sender's Name -->
                                        <p class="font-semibold">{{ message.sender }}</p>
                                        <!-- Message Text -->
                                        <p class="text-slate-500">{{ message.text }}</p>
                                        <!-- Message Time -->
                                        <p class="text-slate-500 text-xs">{{ message.time }}</p>
                                    </div>

                                    <!-- Profile Picture -->
                                    <img :src="message.profileImg" alt="Profile Picture" class="rounded-full w-10 h-10">
                                </div>

                            </div>

                        </div>
                    </div>

                    <!-- Chat Input -->
                    <div class="bottom-0 fixed flex items-center bg-white custom-width-62-5 border-t">
                        <input type="text"
                            class="flex-1 border-0 focus:border-cfp-500 mr-2 px-4 py-4 rounded-md focus:ring-cfp-500/50 focus:outline-none"
                            placeholder="Type your message...">
                        <button class="bg-gray-900 mt-1 rounded-full w-10 h-10 text-white"><i class="fa fa-paper-plane"
                                aria-hidden="true"></i></button>
                    </div>

                </div>
            </div>


        </div>
    </AuthenticatedLayout>
</template>
<style>
.custom-width-62-5 {
    width: 62.5%;
}
</style>