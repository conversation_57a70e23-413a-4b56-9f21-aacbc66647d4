<script setup>
import feather from 'feather-icons';
import LoginForm from '../components/login/LoginForm.vue';
import App from '../App.vue';
import { ref } from 'vue';
import { Head, usePage } from '@inertiajs/vue3';
const pageTitle = ref('Login');


feather.replace();
</script>


<template>
	<Head :title="pageTitle" />
	<App>
		<div
	  class="container mx-auto flex flex-col-reverse md:flex-row py-5 md:py-10 md:mt-10"
	>
	  <!-- Login form -->
	  <LoginForm />
  
	  <!-- Contact details -->
	</div>
	</App>
	
  </template>
  
 
  